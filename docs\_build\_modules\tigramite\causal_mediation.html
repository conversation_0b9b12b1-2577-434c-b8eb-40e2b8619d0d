<!DOCTYPE html>

<html lang="en" data-content_root="../../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.causal_mediation &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=db26dd79" />
    <link rel="stylesheet" type="text/css" href="../../_static/alabaster.css?v=19da42e6" />
    <script src="../../_static/documentation_options.js?v=625b3a9a"></script>
    <script src="../../_static/doctools.js?v=aa79a7b1"></script>
    <script src="../../_static/sphinx_highlight.js?v=4825356b"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
   
  <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.causal_mediation</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite causal inference for time series.&quot;&quot;&quot;</span>

<span class="c1"># Authors: <AUTHORS>
<span class="c1">#</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">numpy</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">np</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sklearn.neighbors</span><span class="w"> </span><span class="kn">import</span> <span class="n">KNeighborsRegressor</span><span class="p">,</span> <span class="n">KernelDensity</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">functools</span><span class="w"> </span><span class="kn">import</span> <span class="n">partial</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.toymodels.non_additive</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">toy_setup</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.causal_effects</span><span class="w"> </span><span class="kn">import</span> <span class="n">CausalEffects</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.toymodels.non_additive</span><span class="w"> </span><span class="kn">import</span> <span class="n">_Fct_on_grid</span><span class="p">,</span> <span class="n">_Fct_smoothed</span>



<span class="sd">&quot;&quot;&quot;-------------------------------------------------------------------------------------------</span>
<span class="sd">-----------------------------   Helpers for Mixed Data Fitting   -----------------------------</span>
<span class="sd">-------------------------------------------------------------------------------------------&quot;&quot;&quot;</span>


<span class="k">class</span><span class="w"> </span><span class="nc">MixedData</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Namescope for mixed-data helpers.</span>

<span class="sd">    Mostly for internal use.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">IsPurelyCategorical</span><span class="p">(</span><span class="n">vars_and_data</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check if all variables are categorical</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        vars_and_data : dictionary&lt; toy_setup.VariableDescription, np.array(N) &gt;</span>
<span class="sd">            Samples with meta-data.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        purely categorical : bool</span>
<span class="sd">            If all variables in vars_and_data are categorical, return true, otherwise false.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">for</span> <span class="n">var</span> <span class="ow">in</span> <span class="n">vars_and_data</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">var</span><span class="o">.</span><span class="n">Id</span><span class="p">()</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
                <span class="k">return</span> <span class="kc">False</span>
        <span class="k">return</span> <span class="kc">True</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">_FixZeroDim</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;[INTERNAL] Ensure consistent shape (0,N) for batches containing zero categorical or continuous variables&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">0</span>
            <span class="n">continuous_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">([</span><span class="mi">0</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">)[</span><span class="mi">1</span><span class="p">]],</span> <span class="n">dtype</span><span class="o">=</span><span class="s2">&quot;float64&quot;</span><span class="p">)</span>
            <span class="n">categorical_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">)</span>
        <span class="k">elif</span> <span class="nb">len</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">categorical_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">([</span><span class="mi">0</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">)[</span><span class="mi">1</span><span class="p">]],</span> <span class="n">dtype</span><span class="o">=</span><span class="s2">&quot;int32&quot;</span><span class="p">)</span>
            <span class="n">continuous_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">continuous_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">)</span>
            <span class="n">categorical_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">Get_data_via_var_ids</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">var_ids</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;For a subset of variables, get separate batches for continuous and categorical data, and category-shape</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : dictionary&lt; toy_setup.VariableDescription, np.array(N) &gt;</span>
<span class="sd">            Samples and meta-data.</span>
<span class="sd">        var_ids : *iterable* &lt;toy_setup.VariableDescription&gt;</span>
<span class="sd">            Subset of variables to extract data for.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        continuous_data : np.array( # continuous variables in var_ids, # samples )</span>
<span class="sd">            Continuous entries of the data.</span>
<span class="sd">        categorical_data : np.array( # categorical variables in var_ids, # samples )</span>
<span class="sd">            Categorical entries of the data.</span>
<span class="sd">        category_shape : list &lt;uint&gt;</span>
<span class="sd">            List of the number of categories for each categorical variable in var_ids in the same ordering.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">category_shape</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">categorical_data</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">continuous_data</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">desc</span> <span class="ow">in</span> <span class="n">var_ids</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">desc</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
                <span class="n">category_shape</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">desc</span><span class="o">.</span><span class="n">categories</span><span class="p">)</span>
                <span class="n">categorical_data</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">x</span><span class="p">[</span><span class="n">desc</span><span class="p">])</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">continuous_data</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">x</span><span class="p">[</span><span class="n">desc</span><span class="p">])</span>
        <span class="k">return</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">_FixZeroDim</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span><span class="p">)</span> <span class="o">+</span> <span class="p">(</span><span class="n">category_shape</span><span class="p">,)</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">Split_data_into_categorical_and_continuous</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get separate batches for continuous and categorical data, and category-shape</span>

<span class="sd">        (see Get_data_via_var_ids)</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="o">.</span><span class="n">Get_data_via_var_ids</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">x</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">CategoryCount</span><span class="p">(</span><span class="n">category_shape</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get total number of categories in shape.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        category_shape : list&lt;uint&gt;</span>
<span class="sd">            Number of categories per variable.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        total : uint</span>
<span class="sd">            Total number of categories in the product.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">result</span> <span class="o">=</span> <span class="mi">1</span>
        <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">category_shape</span><span class="p">:</span>
            <span class="n">result</span> <span class="o">*=</span> <span class="n">c</span>
        <span class="k">return</span> <span class="n">result</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">Get_Len</span><span class="p">(</span><span class="n">continuous</span><span class="p">,</span> <span class="n">categorical</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Extract number of samples from data&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">continuous</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">continuous</span><span class="p">)[</span><span class="mi">1</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">assert</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">categorical</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span> <span class="o">!=</span> <span class="mi">0</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">categorical</span><span class="p">)[</span><span class="mi">1</span><span class="p">]</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">SimplifyIfTrivialVector</span><span class="p">(</span><span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Make shapes consistent.&quot;&quot;&quot;</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">squeeze</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">result</span><span class="p">)</span> <span class="o">==</span> <span class="p">():</span>
            <span class="k">return</span> <span class="n">result</span><span class="p">[()]</span>  <span class="c1"># turn a scalar from array(x) of shape () to an actual scalar</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">result</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">Call_map</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">f</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Execute call consistently.&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">f</span><span class="p">,</span> <span class="s1">&#39;__iter__&#39;</span><span class="p">):</span>
            <span class="n">result</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="n">coord</span> <span class="ow">in</span> <span class="n">f</span><span class="p">:</span>
                <span class="n">result</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">cls</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="n">coord</span><span class="p">,</span> <span class="n">x</span><span class="p">))</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">result</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">callable</span><span class="p">(</span><span class="n">f</span><span class="p">):</span>
            <span class="k">return</span> <span class="bp">cls</span><span class="o">.</span><span class="n">SimplifyIfTrivialVector</span><span class="p">(</span><span class="n">f</span><span class="p">(</span><span class="n">x</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">cls</span><span class="o">.</span><span class="n">SimplifyIfTrivialVector</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">predict</span><span class="p">(</span><span class="n">x</span><span class="p">))</span>


<span class="k">class</span><span class="w"> </span><span class="nc">FitProvider_Continuous_Default</span><span class="p">:</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Helper for fitting continuous maps.</span>

<span class="sd">    See &quot;Technical Appendix B&quot; of the Causal Mediation Tutorial.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    fit_map : *callable* (np.array(N, dim x), np.array(N))</span>
<span class="sd">        A callable, that given data (x,y) fits a regressor f s.t. y = f(x)</span>
<span class="sd">    fit_map_1d : *None* or *callable* (np.array(N, 1), np.array(N))</span>
<span class="sd">        overwrite fit_map in the case of a 1-dimensional domain.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">fit_map</span><span class="p">,</span> <span class="n">fit_map_1d</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_map</span> <span class="o">=</span> <span class="n">fit_map</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_map_1d</span> <span class="o">=</span> <span class="n">fit_map_1d</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">UseSklearn</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">neighbors</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span> <span class="n">weights</span><span class="o">=</span><span class="s1">&#39;uniform&#39;</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Use an sci-kit learn KNeighborsRegressor</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        neighbors : uint</span>
<span class="sd">            The number of neighbors to consider</span>
<span class="sd">        weights : string</span>
<span class="sd">            Either &#39;uniform&#39; or &#39;radius&#39;, see sci-kit learn.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        K-Neighbors Fit-Provider : FitProvider_Continuous_Default</span>
<span class="sd">            Fit-Provider for use with FitSetup.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">:</span> <span class="n">KNeighborsRegressor</span><span class="p">(</span><span class="n">n_neighbors</span><span class="o">=</span><span class="n">neighbors</span><span class="p">,</span> <span class="n">weights</span><span class="o">=</span><span class="n">weights</span><span class="p">)</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">))</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">UseSklearn_GC</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Use an sci-kit learn GaussianProcessRegressor</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        ... : any-type</span>
<span class="sd">            forwarded to sci-kit</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        GC Fit-Provider : FitProvider_Continuous_Default</span>
<span class="sd">            Fit-Provider for use with FitSetup.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="kn">from</span><span class="w"> </span><span class="nn">sklearn.gaussian_process</span><span class="w"> </span><span class="kn">import</span> <span class="n">GaussianProcessRegressor</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">:</span> <span class="n">GaussianProcessRegressor</span><span class="p">(</span><span class="o">**</span><span class="n">kwargs</span><span class="p">)</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">))</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">Get_Fit_Continuous</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Produce a fit</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : np.array(N, dim x)</span>
<span class="sd">            Predictor values</span>
<span class="sd">        y : np.array(N)</span>
<span class="sd">            Training values</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Continuous Fit : *callable* (x)</span>
<span class="sd">            The fitted predictor.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">dim_of_domain</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">x</span><span class="p">)[</span><span class="mi">1</span><span class="p">]</span>
        <span class="k">if</span> <span class="n">dim_of_domain</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="k">lambda</span> <span class="n">x_dim_zero</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">y</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">dim_of_domain</span> <span class="o">==</span> <span class="mi">1</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_map_1d</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_map_1d</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>  <span class="c1"># use splines?</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_map</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>


<span class="k">class</span><span class="w"> </span><span class="nc">FitProvider_Density_Default</span><span class="p">:</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Helper for fitting continuous densities.</span>

<span class="sd">   See &quot;Technical Appendix B&quot; of the Causal Mediation Tutorial.</span>

<span class="sd">   Parameters</span>
<span class="sd">   ----------</span>
<span class="sd">   fit_density : *callable* (np.array(N, dim x))</span>
<span class="sd">       A callable, that given data (x) fits a density estimator p s.t. x ~ p</span>
<span class="sd">   &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">fit_density</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_density</span> <span class="o">=</span> <span class="n">fit_density</span>

    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">UseSklearn</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">kernel</span><span class="o">=</span><span class="s1">&#39;gaussian&#39;</span><span class="p">,</span> <span class="n">bandwidth</span><span class="o">=</span><span class="mf">0.2</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Use an sci-kit learn KernelDensity</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        kernel : string</span>
<span class="sd">            E.g. &#39;gaussian&#39;, see sci-kit learn documentation.</span>
<span class="sd">        bandwidth : float</span>
<span class="sd">            See sci-kit learn documentation.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Density Estimator : FitProvider_Density_Default</span>
<span class="sd">            Fit-Provider for use with FitSetup.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">cls</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">KernelDensity</span><span class="p">(</span><span class="n">kernel</span><span class="o">=</span><span class="n">kernel</span><span class="p">,</span> <span class="n">bandwidth</span><span class="o">=</span><span class="n">bandwidth</span><span class="p">)</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">x</span><span class="p">))</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">Get_Fit_Density</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x_train</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Produce a fit</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x_train : np.array(N, dim x)</span>
<span class="sd">            Training samples.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Density Estimate : *callable* (x)</span>
<span class="sd">            The fitted predictor.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">dim_of_domain</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">x_train</span><span class="p">)[</span><span class="mi">1</span><span class="p">]</span>
        <span class="k">if</span> <span class="n">dim_of_domain</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="k">lambda</span> <span class="n">x_dim_zero</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">x_dim_zero</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
        <span class="k">elif</span> <span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">x_train</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">model</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_density</span><span class="p">(</span><span class="n">x_train</span><span class="p">)</span>
            <span class="k">return</span> <span class="k">lambda</span> <span class="n">x_predict</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">exp</span><span class="p">(</span><span class="n">model</span><span class="o">.</span><span class="n">score_samples</span><span class="p">(</span><span class="n">x_predict</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="k">lambda</span> <span class="n">x_predict</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">x_predict</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>


<span class="k">class</span><span class="w"> </span><span class="nc">MixedMap</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Helper to evaluate fitted maps on mixed data.</span>

<span class="sd">    Used internally, should not normally be required in user-code.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">error_value</span> <span class="o">=</span> <span class="nb">float</span><span class="p">(</span><span class="s1">&#39;nan&#39;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">maps</span><span class="p">,</span> <span class="n">var_ids</span><span class="p">,</span> <span class="n">dtype</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">maps</span> <span class="o">=</span> <span class="n">maps</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">var_ids</span> <span class="o">=</span> <span class="n">var_ids</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dtype</span> <span class="o">=</span> <span class="n">dtype</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">predict</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Given x, predict y.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__call__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Given x, predict y.&quot;&quot;&quot;</span>
        <span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span><span class="p">,</span> <span class="n">category_shape</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Get_data_via_var_ids</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">var_ids</span><span class="p">)</span>
        <span class="n">category_count</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">CategoryCount</span><span class="p">(</span><span class="n">category_shape</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">category_count</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>  <span class="c1"># avoid errors in ravel_multi_index and improve performance by treating separately</span>
            <span class="k">return</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">maps</span><span class="p">,</span> <span class="n">continuous_data</span><span class="o">.</span><span class="n">T</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">categories</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ravel_multi_index</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">,</span> <span class="n">category_shape</span><span class="p">)</span>
            <span class="n">result</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">(</span><span class="n">MixedData</span><span class="o">.</span><span class="n">Get_Len</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">dtype</span><span class="p">)</span>
            <span class="k">for</span> <span class="n">cX</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">category_count</span><span class="p">):</span>
                <span class="n">filter_x</span> <span class="o">=</span> <span class="p">(</span><span class="n">categories</span> <span class="o">==</span> <span class="n">cX</span><span class="p">)</span>
                <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">count_nonzero</span><span class="p">(</span><span class="n">filter_x</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="c1"># Avoid errors if asking for prediction on data with cX not occurring</span>
                    <span class="c1"># (Queries must be validated elsewhere)</span>
                    <span class="n">filtered_x</span> <span class="o">=</span> <span class="n">continuous_data</span><span class="p">[:,</span> <span class="n">filter_x</span><span class="p">]</span>
                    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">maps</span><span class="p">[</span><span class="n">cX</span><span class="p">]</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                        <span class="n">result</span><span class="p">[</span><span class="n">filter_x</span><span class="p">]</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">maps</span><span class="p">[</span><span class="n">cX</span><span class="p">],</span> <span class="n">filtered_x</span><span class="o">.</span><span class="n">T</span><span class="p">)</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="n">result</span><span class="p">[</span><span class="n">filter_x</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">error_value</span>
            <span class="k">return</span> <span class="n">result</span>


<span class="k">class</span><span class="w"> </span><span class="nc">MixedMarkovKernel</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Helper to evaluate fitted densities on mixed data.</span>

<span class="sd">    Used internally, should not normally be required in user-code.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">transfers</span><span class="p">,</span> <span class="n">var_ids</span><span class="p">,</span> <span class="n">dtype</span><span class="p">,</span> <span class="n">output_category_count</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">transfers</span> <span class="o">=</span> <span class="n">transfers</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">var_ids</span> <span class="o">=</span> <span class="n">var_ids</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dtype</span> <span class="o">=</span> <span class="n">dtype</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">output_category_count</span> <span class="o">=</span> <span class="n">output_category_count</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">predict</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Given np.array x, predict probability of each sample.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__call__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Given np.array x, predict probability of each sample.&quot;&quot;&quot;</span>
        <span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span><span class="p">,</span> <span class="n">category_shape</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Get_data_via_var_ids</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">var_ids</span><span class="p">)</span>
        <span class="n">category_count</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">CategoryCount</span><span class="p">(</span><span class="n">category_shape</span><span class="p">)</span>
        <span class="n">categories</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="n">category_count</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>  <span class="c1"># avoid errors in ravel_multi_index and improve performance by treating separately</span>
            <span class="n">categories</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">)[</span><span class="mi">1</span><span class="p">])</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">categories</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ravel_multi_index</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">,</span> <span class="n">category_shape</span><span class="p">)</span>

        <span class="n">values</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">([</span><span class="bp">self</span><span class="o">.</span><span class="n">output_category_count</span><span class="p">,</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Get_Len</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span><span class="p">)])</span>
        <span class="k">for</span> <span class="n">cX</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">category_count</span><span class="p">):</span>
            <span class="n">filter_x</span> <span class="o">=</span> <span class="p">(</span><span class="n">categories</span> <span class="o">==</span> <span class="n">cX</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">count_nonzero</span><span class="p">(</span><span class="n">filter_x</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="c1"># Avoid errors if asking for prediction on data with cX not occurring</span>
                <span class="c1"># (Queries must be validated elsewhere)</span>
                <span class="n">filtered_x</span> <span class="o">=</span> <span class="n">continuous_data</span><span class="p">[:,</span> <span class="n">filter_x</span><span class="p">]</span>
                <span class="k">for</span> <span class="n">cY</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">output_category_count</span><span class="p">):</span>
                    <span class="n">transfer</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">transfers</span><span class="p">[</span><span class="n">cX</span><span class="p">][</span><span class="n">cY</span><span class="p">]</span>
                    <span class="n">values</span><span class="p">[</span><span class="n">cY</span><span class="p">][</span><span class="n">filter_x</span><span class="p">]</span> <span class="o">=</span> <span class="n">transfer</span><span class="p">(</span><span class="n">filtered_x</span><span class="o">.</span><span class="n">T</span><span class="p">)</span>
        <span class="n">normalize</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">values</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
        <span class="k">return</span> <span class="p">(</span><span class="n">values</span> <span class="o">/</span> <span class="n">normalize</span><span class="p">)</span><span class="o">.</span><span class="n">T</span>


<span class="k">class</span><span class="w"> </span><span class="nc">FitSetup</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Helper to fit mixed data.</span>

<span class="sd">   See &quot;Technical Appendix B&quot; of the Causal Mediation Tutorial.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">fit_map</span><span class="o">=</span><span class="n">FitProvider_Continuous_Default</span><span class="o">.</span><span class="n">UseSklearn</span><span class="p">(),</span>
                 <span class="n">fit_density</span><span class="o">=</span><span class="n">FitProvider_Density_Default</span><span class="o">.</span><span class="n">UseSklearn</span><span class="p">()):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_map</span> <span class="o">=</span> <span class="n">fit_map</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_density</span> <span class="o">=</span> <span class="n">fit_density</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">Fit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">domain</span><span class="p">,</span> <span class="n">target</span><span class="p">,</span> <span class="n">dont_wrap_1d_y_in_vector</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Fit a mixed data mapping.</span>

<span class="sd">        See &quot;Technical Appendix B&quot; of the Causal Mediation Tutorial.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">dont_wrap</span> <span class="o">=</span> <span class="n">dont_wrap_1d_y_in_vector</span> <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span><span class="n">target</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span>
        <span class="n">result</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">y</span><span class="p">,</span> <span class="n">train</span> <span class="ow">in</span> <span class="n">target</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="n">_next</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="k">if</span> <span class="n">y</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
                <span class="n">_next</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">Fit_CategoricalTarget_MarkovKernel</span><span class="p">(</span><span class="n">domain</span><span class="p">,</span> <span class="n">train</span><span class="p">,</span> <span class="n">y</span><span class="o">.</span><span class="n">categories</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">_next</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">Fit_ContinuousTarget</span><span class="p">(</span><span class="n">domain</span><span class="p">,</span> <span class="n">train</span><span class="p">,</span> <span class="n">y</span><span class="o">.</span><span class="n">dimension</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">dont_wrap</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">_next</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">result</span><span class="p">[</span><span class="n">y</span><span class="p">]</span> <span class="o">=</span> <span class="n">_next</span>
        <span class="k">return</span> <span class="n">result</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">Fit_ContinuousTarget</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">dim_y</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Fit a mixed-domain mapping with continuous target.&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">dim_y</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">Fit_ContinuousTarget_1D</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">result</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">dim_y</span><span class="p">):</span>
                <span class="n">result</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Fit_ContinuousTarget_1D</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]))</span>
            <span class="k">return</span> <span class="n">result</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">Fit_ContinuousTarget_1D</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Fit a mixed-domain mapping with 1-dimensional continuous target.&quot;&quot;&quot;</span>
        <span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span><span class="p">,</span> <span class="n">category_shape</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Split_data_into_categorical_and_continuous</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>

        <span class="n">category_count</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">CategoryCount</span><span class="p">(</span><span class="n">category_shape</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">category_count</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="c1"># Avoid errors in np.ravel_multi_index and improve performance by treating this case separately</span>
            <span class="n">_map</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_map</span><span class="o">.</span><span class="n">Get_Fit_Continuous</span><span class="p">(</span><span class="n">continuous_data</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">MixedMap</span><span class="p">(</span><span class="n">_map</span><span class="p">,</span> <span class="n">var_ids</span><span class="o">=</span><span class="n">x</span><span class="o">.</span><span class="n">keys</span><span class="p">(),</span> <span class="n">dtype</span><span class="o">=</span><span class="n">y</span><span class="o">.</span><span class="n">dtype</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Fit once per category</span>
            <span class="n">category_index</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ravel_multi_index</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">,</span> <span class="n">category_shape</span><span class="p">)</span>
            <span class="n">maps</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="k">for</span> <span class="n">cX</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">category_count</span><span class="p">):</span>
                <span class="n">filter_x</span> <span class="o">=</span> <span class="p">(</span><span class="n">category_index</span> <span class="o">==</span> <span class="n">cX</span><span class="p">)</span>
                <span class="n">filtered_y</span> <span class="o">=</span> <span class="n">y</span><span class="p">[</span><span class="n">filter_x</span><span class="p">]</span>
                <span class="n">filtered_x</span> <span class="o">=</span> <span class="n">continuous_data</span><span class="p">[:,</span> <span class="n">filter_x</span><span class="p">]</span>
                <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">count_nonzero</span><span class="p">(</span><span class="n">filter_x</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
                    <span class="n">maps</span><span class="p">[</span><span class="n">cX</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_map</span><span class="o">.</span><span class="n">Get_Fit_Continuous</span><span class="p">(</span><span class="n">filtered_x</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">filtered_y</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">maps</span><span class="p">[</span><span class="n">cX</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="k">return</span> <span class="n">MixedMap</span><span class="p">(</span><span class="n">maps</span><span class="p">,</span> <span class="n">var_ids</span><span class="o">=</span><span class="n">x</span><span class="o">.</span><span class="n">keys</span><span class="p">(),</span> <span class="n">dtype</span><span class="o">=</span><span class="n">y</span><span class="o">.</span><span class="n">dtype</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">Fit_CategoricalTarget_MarkovKernel</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">y_category_count</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Fit a mixed-domain probabilistic mapping (a Markov-kernel) with categorical target.&quot;&quot;&quot;</span>
        <span class="n">continuous_data</span><span class="p">,</span> <span class="n">categorical_data</span><span class="p">,</span> <span class="n">category_shape</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Split_data_into_categorical_and_continuous</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>

        <span class="n">category_count</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">CategoryCount</span><span class="p">(</span><span class="n">category_shape</span><span class="p">)</span>
        <span class="n">category_index</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="n">category_count</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="c1"># Avoid errors in np.ravel_multi_index and improve performance by treating this case separately</span>
            <span class="n">category_index</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">)[</span><span class="mi">1</span><span class="p">])</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">category_index</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ravel_multi_index</span><span class="p">(</span><span class="n">categorical_data</span><span class="p">,</span> <span class="n">category_shape</span><span class="p">)</span>

        <span class="c1"># Compute the transfer-matrix</span>
        <span class="n">transfer_matrix</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_Fit_Enriched_TransferMatrix</span><span class="p">(</span><span class="n">continuous_data</span><span class="p">,</span>
                                                            <span class="n">category_index</span><span class="p">,</span> <span class="n">category_count</span><span class="p">,</span>
                                                            <span class="n">y</span><span class="p">,</span> <span class="n">y_category_count</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">MixedMarkovKernel</span><span class="p">(</span><span class="n">transfer_matrix</span><span class="p">,</span> <span class="n">var_ids</span><span class="o">=</span><span class="n">x</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="n">y</span><span class="o">.</span><span class="n">dtype</span><span class="p">,</span> <span class="n">output_category_count</span><span class="o">=</span><span class="n">y_category_count</span><span class="p">)</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">_Fit_TransferMatrix</span><span class="p">(</span><span class="n">x_categorical</span><span class="p">,</span> <span class="n">x_category_count</span><span class="p">,</span> <span class="n">y_categorical</span><span class="p">,</span> <span class="n">y_category_count</span><span class="p">):</span>
        <span class="n">transfers</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">([</span><span class="n">x_category_count</span><span class="p">,</span> <span class="n">y_category_count</span><span class="p">])</span>
        <span class="k">for</span> <span class="n">cX</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">x_category_count</span><span class="p">):</span>
            <span class="n">filter_x</span> <span class="o">=</span> <span class="p">(</span><span class="n">x_categorical</span> <span class="o">==</span> <span class="n">cX</span><span class="p">)</span>
            <span class="n">filtered_y</span> <span class="o">=</span> <span class="n">y_categorical</span><span class="p">[</span><span class="n">filter_x</span><span class="p">]</span>
            <span class="n">normalization</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">count_nonzero</span><span class="p">(</span><span class="n">filter_x</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">normalization</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">cY</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">y_category_count</span><span class="p">):</span>
                    <span class="n">transfers</span><span class="p">[</span><span class="n">cX</span><span class="p">,</span> <span class="n">cY</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">count_nonzero</span><span class="p">(</span><span class="n">filtered_y</span> <span class="o">==</span> <span class="n">cY</span><span class="p">)</span> <span class="o">/</span> <span class="n">normalization</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">transfers</span><span class="p">[</span><span class="n">cX</span><span class="p">,</span> <span class="n">cY</span><span class="p">]</span> <span class="o">=</span> <span class="n">MixedMap</span><span class="o">.</span><span class="n">error_value</span>
        <span class="k">return</span> <span class="n">transfers</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_Fit_Enriched_TransferMatrix</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x_continuous</span><span class="p">,</span>
                                     <span class="n">x_categorical</span><span class="p">,</span> <span class="n">x_category_count</span><span class="p">,</span>
                                     <span class="n">y_categorical</span><span class="p">,</span> <span class="n">y_category_count</span><span class="p">):</span>
        <span class="n">transfers</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">cX</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">x_category_count</span><span class="p">):</span>
            <span class="n">filter_x</span> <span class="o">=</span> <span class="p">(</span><span class="n">x_categorical</span> <span class="o">==</span> <span class="n">cX</span><span class="p">)</span>
            <span class="n">normalization</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">count_nonzero</span><span class="p">(</span><span class="n">filter_x</span><span class="p">)</span>
            <span class="n">transfers_from_cX</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="n">transfers</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">transfers_from_cX</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">normalization</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">cY</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">y_category_count</span><span class="p">):</span>
                    <span class="c1"># Use Bayes&#39; Theorem to avoid fitting densities conditional on continuous variables</span>
                    <span class="n">filter_both</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">logical_and</span><span class="p">(</span><span class="n">x_categorical</span> <span class="o">==</span> <span class="n">cX</span><span class="p">,</span> <span class="n">y_categorical</span> <span class="o">==</span> <span class="n">cY</span><span class="p">)</span>
                    <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">filter_both</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span>
                    <span class="n">p_y_given_discrete_x</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">count_nonzero</span><span class="p">(</span><span class="n">filter_both</span><span class="p">)</span> <span class="o">/</span> <span class="n">normalization</span>
                    <span class="n">p_continuous_x_given_discrete_x_and_y</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_density</span><span class="o">.</span><span class="n">Get_Fit_Density</span><span class="p">(</span>
                        <span class="n">x_continuous</span><span class="p">[:,</span> <span class="n">filter_both</span><span class="p">]</span><span class="o">.</span><span class="n">T</span><span class="p">)</span>
                    <span class="c1"># Avoid weird bugs in lambda-captures combined with for-loop scopes by using &quot;functools-&gt;partial&quot;</span>
                    <span class="n">transfers_from_cX</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">partial</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_EvalFromBayes</span><span class="p">,</span> <span class="n">p_y_given_discrete_x</span><span class="o">=</span><span class="n">p_y_given_discrete_x</span><span class="p">,</span>
                                                     <span class="n">p_continuous_x_given_discrete_x_and_y</span><span class="o">=</span><span class="n">p_continuous_x_given_discrete_x_and_y</span><span class="p">))</span>
                    <span class="c1"># transfers_from_cX.append(lambda x_c: self._EvalFromBayes(x_c, p_y_given_discrete_x,</span>
                    <span class="c1">#                                                        p_continuous_x_given_discrete_x_and_y))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">cY</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">y_category_count</span><span class="p">):</span>
                    <span class="n">transfers_from_cX</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="k">lambda</span> <span class="n">x_c</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">shape</span><span class="p">(</span><span class="n">x_c</span><span class="p">)[</span><span class="mi">0</span><span class="p">],</span> <span class="n">MixedMap</span><span class="o">.</span><span class="n">error_value</span><span class="p">))</span>
        <span class="k">return</span> <span class="n">transfers</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">_EvalFromBayes</span><span class="p">(</span><span class="n">x_c</span><span class="p">,</span> <span class="n">p_y_given_discrete_x</span><span class="p">,</span> <span class="n">p_continuous_x_given_discrete_x_and_y</span><span class="p">):</span>
        <span class="c1"># P( y | x_c, x_d ) = P( x_c | x_d, y ) P( Y | x_d ) / normalization independent of y</span>
        <span class="k">return</span> <span class="n">p_continuous_x_given_discrete_x_and_y</span><span class="p">(</span><span class="n">x_c</span><span class="p">)</span> <span class="o">*</span> <span class="n">p_y_given_discrete_x</span>





<span class="sd">&quot;&quot;&quot;-------------------------------------------------------------------------------------------</span>
<span class="sd">--------------------------------   Natural Effect Estimation   -------------------------------</span>
<span class="sd">-------------------------------------------------------------------------------------------&quot;&quot;&quot;</span>

<span class="k">class</span><span class="w"> </span><span class="nc">NaturalEffects_StandardMediationSetup</span><span class="p">:</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Setup for estimating natural effects in a &quot;standard&quot; mediation (triangle) graph.</span>

<span class="sd">    Methods for the non-parametric estimation of mediation effects.</span>
<span class="sd">    For (more) general graphs, see NaturalEffects_GraphMediation.</span>

<span class="sd">    Actual fit-models can be chosen independently, for details see</span>
<span class="sd">    technical appendix B in the mediation-tutorial.</span>

<span class="sd">    See references and tigramite tutorial for an in-depth introduction.</span>

<span class="sd">    References</span>
<span class="sd">    ----------</span>

<span class="sd">    J. Pearl. Direct and indirect effects. Proceedings of the Seventeenth Conference</span>
<span class="sd">    on Uncertainty in Artificial intelligence, 2001.</span>

<span class="sd">    J. Pearl. Interpretation and identification of causal mediation. Psychological</span>
<span class="sd">    methods, 19(4):459, 2014.</span>

<span class="sd">    I. Shpitser and T. J. VanderWeele. A complete graphical criterion for the adjust-</span>
<span class="sd">    ment formula in mediation analysis. The international journal of biostatistics,</span>
<span class="sd">    7(1), 2011.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    fit_setup : fit model</span>
<span class="sd">        A fit model to use internally, e.g. mixed_fit.FitSetup(). See there or technical</span>
<span class="sd">        appendix B of the tutorial.</span>
<span class="sd">    source : toy_setup.VariableDescription</span>
<span class="sd">        The (description of, e.g. toy_setup.ContinuousVariable()) the effect-source.</span>
<span class="sd">    target : toy_setup.VariableDescription</span>
<span class="sd">        The (description of, e.g. toy_setup.ContinuousVariable()) the effect-target.</span>
<span class="sd">    mediator : toy_setup.VariableDescription</span>
<span class="sd">        The (description of, e.g. toy_setup.ContinuousVariable()) the effect-mediator.</span>
<span class="sd">    data : dictionary ( keys=toy_setup.VariableDescription, values=np.array(N) )</span>
<span class="sd">        The data as map variable-description -&gt; samples; e.g. toy_setup.world.Observables(),</span>
<span class="sd">        or toy_setup.VariablesFromDataframe( tigramite dataframe )</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">fit_setup</span><span class="p">,</span> <span class="n">source</span><span class="p">,</span> <span class="n">target</span><span class="p">,</span> <span class="n">mediator</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span> <span class="o">=</span> <span class="n">fit_setup</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">X</span> <span class="o">=</span> <span class="n">source</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">Y</span> <span class="o">=</span> <span class="n">target</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">M</span> <span class="o">=</span> <span class="n">mediator</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">x</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">source</span><span class="p">]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">y</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">target</span><span class="p">]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">m</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">mediator</span><span class="p">]</span>
        <span class="n">X</span> <span class="o">=</span> <span class="n">source</span>
        <span class="n">Y</span> <span class="o">=</span> <span class="n">target</span>
        <span class="n">M</span> <span class="o">=</span> <span class="n">mediator</span>
        <span class="n">x</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">source</span><span class="p">]</span>
        <span class="n">y</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">target</span><span class="p">]</span>
        <span class="n">m</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">mediator</span><span class="p">]</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XM</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_P_Y_XM</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_P_M_X</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">E_Y_XM_fixed_x_obs_m</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;[internal] Provide samples Y( fixed x, observed m ) from fit of E[Y|X,M]</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            The fixed value of X.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Y( fixed x, observed m ) : np.array(N)</span>
<span class="sd">            Samples of Y estimated from observations and fit.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XM</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XM</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">m</span><span class="p">},</span> <span class="p">{</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">y</span><span class="p">})</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XM</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full_like</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="n">x</span><span class="p">),</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">m</span><span class="p">})</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">E_Y_XM_fixed_x_all_m</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;For categorical M, provide samples Y( fixed x, m_i ) for all categories m_i of M from fit of E[Y|X,M]</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            The fixed value of X.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Y( fixed x, all  m_i ) : np.array(# of categories of M)</span>
<span class="sd">            Samples of Y estimated from fit for all categories of the mediator M.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">assert</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="o">.</span><span class="n">is_categorical</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XM</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XM</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">m</span><span class="p">},</span> <span class="p">{</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">y</span><span class="p">})</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XM</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="o">.</span><span class="n">categories</span><span class="p">,</span> <span class="n">x</span><span class="p">),</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="o">.</span><span class="n">categories</span><span class="p">)})</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">P_Y_XM_fixed_x_obs_m</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Provide for all samples of M the likelihood P( Y | fixed x, observed m ) from fit of P[Y|X,M]</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            The fixed value of X.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        P( Y | fixed x, observed m ) : np.array(N, # categories of Y)</span>
<span class="sd">            Likelihood of each category of Y given x and observations of m from fit.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_P_Y_XM</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_P_Y_XM</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">m</span><span class="p">},</span> <span class="p">{</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">y</span><span class="p">})</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_P_Y_XM</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full_like</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">,</span> <span class="n">x</span><span class="p">),</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">m</span><span class="p">})</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">P_M_X</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Provide the likelihood P( M | fixed x ) from fit of P[M|X]</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            The fixed value of X.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        P( M | fixed x ) : np.array( # categories of M )</span>
<span class="sd">            Likelihood of each category of M given x and observations of m from fit.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_P_M_X</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_P_M_X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">},</span> <span class="p">{</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">m</span><span class="p">})</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_P_M_X</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">x</span><span class="p">})</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_ValidateRequest</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Sanity-check parameters passed to effect-estimation</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        cf. NDE, NIE</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
            <span class="c1"># both_int = isinstance(change_from, int) and isinstance(change_to, int) does not work with numpy</span>
            <span class="n">both_int</span> <span class="o">=</span> <span class="n">change_from</span> <span class="o">%</span> <span class="mi">1</span> <span class="o">==</span> <span class="mf">0.0</span> <span class="ow">and</span> <span class="n">change_to</span> <span class="o">%</span> <span class="mi">1</span> <span class="o">==</span> <span class="mf">0.0</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">both_int</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;Categorical variables can only be intervened to integer values.&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">change_from</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">categories</span> <span class="ow">or</span> <span class="n">change_to</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">categories</span> <span class="ow">or</span> <span class="n">change_from</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="ow">or</span> <span class="n">change_to</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;Intervention on categorical variable was outside of valid category-range.&quot;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">NDE</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">,</span> <span class="n">fct_of_nde</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute Natural Direct Effect (NDE)</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        change_from : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            Reference-value to which X is set by intervention in the world seen by the mediator.</span>

<span class="sd">        change_to : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            Post-intervention-value to which X is set by intervention in the world seen by the effect (directly).</span>

<span class="sd">        fct_of_nde : *callable* or None</span>
<span class="sd">            Also in the case of a continuous Y the distribution, not just the expectation is identified.</span>
<span class="sd">            However, a density-fit is not usually reasonable to do in practise. However, instead of</span>
<span class="sd">            computing E[Y] can compute E[f(Y)] efficiently for any f. Assume f=id if None.</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NDE : If Y is categorical -&gt; np.array( # categories Y, 2 )</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping M as if X remained at change_from.</span>

<span class="sd">        NDE : If Y is continuous -&gt; float</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping M as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_ValidateRequest</span><span class="p">(</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
            <span class="k">assert</span> <span class="n">fct_of_nde</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">,</span> <span class="s2">&quot;Categorical estimate returns full density-estimate anyway.&quot;</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_NDE_categorical_target</span><span class="p">(</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_NDE_continuous_target</span><span class="p">(</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">,</span> <span class="n">fct_of_nde</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">NIE</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute Natural Indirect Effect (NIE)</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        change_from : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">           Reference-value to which X is set by intervention in the world seen by the effect (directly).</span>

<span class="sd">        change_to : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            Post-intervention-value to which X is set by intervention in the world seen by the mediator.</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NIE : If Y is categorical -&gt; np.array( # categories Y, 2 )</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by M from change_from to change_to, while keeping the value as (directly) seen by Y,</span>
<span class="sd">            as if X remained at change_from.</span>

<span class="sd">        NIE : If Y is continuous -&gt; float</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by M from change_from to change_to, while keeping the value as (directly) seen by Y,</span>
<span class="sd">            as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_ValidateRequest</span><span class="p">(</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_NIE_categorical_target</span><span class="p">(</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_NIE_continuous_target</span><span class="p">(</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_NDE_continuous_target</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">,</span> <span class="n">fct_of_nde</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE (continuous Y)</span>

<span class="sd">        See &#39;NDE&#39; above.</span>

<span class="sd">        Computed from mediation-formula</span>
<span class="sd">        (see [Pearl 2001] or [Shpitser, VanderWeele], see references above)</span>
<span class="sd">        by &quot;double&quot;-regression.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">difference</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">E_Y_XM_fixed_x_obs_m</span><span class="p">(</span><span class="n">change_to</span><span class="p">)</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">E_Y_XM_fixed_x_obs_m</span><span class="p">(</span><span class="n">change_from</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">fct_of_nde</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">difference</span> <span class="o">=</span> <span class="n">fct_of_nde</span><span class="p">(</span><span class="n">difference</span><span class="p">)</span>

        <span class="c1"># sklearn predicts nan if too far from data ... (but might be irrelevant, check separately)</span>
        <span class="n">valid_samples</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">isfinite</span><span class="p">(</span><span class="n">difference</span><span class="p">)</span>
        <span class="n">Difference</span> <span class="o">=</span> <span class="n">toy_setup</span><span class="o">.</span><span class="n">ContinuousVariable</span><span class="p">()</span>
        <span class="n">X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span>
        <span class="n">x</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span>
        <span class="n">E_Difference_X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="n">X</span><span class="p">:</span> <span class="n">x</span><span class="p">[</span><span class="n">valid_samples</span><span class="p">]},</span> <span class="p">{</span><span class="n">Difference</span><span class="p">:</span> <span class="n">difference</span><span class="p">[</span><span class="n">valid_samples</span><span class="p">]})</span>
        <span class="k">return</span> <span class="n">E_Difference_X</span><span class="p">({</span><span class="n">X</span><span class="p">:</span> <span class="p">[</span><span class="n">change_from</span><span class="p">]})</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_NDE_categorical_target_full_density</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">actual_value</span><span class="p">,</span> <span class="n">counterfactual_value</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE as full density (categorical Y)</span>

<span class="sd">        See &#39;NDE&#39; above.</span>

<span class="sd">        Computed from mediation-formula</span>
<span class="sd">        (see [Pearl 2001] or [Shpitser, VanderWeele], see references above)</span>
<span class="sd">        by &quot;double&quot;-regression.</span>

<span class="sd">        Note: According to (see p.13)</span>
<span class="sd">        [Shpitser, VanderWeele: A Complete Graphical Criterion for theAdjustment Formula in Mediation Analysis]</span>
<span class="sd">        not just the expectation-value, but the full counterfactual distribution can be obtained via mediation-formula.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">P_Y</span> <span class="o">=</span> <span class="n">toy_setup</span><span class="o">.</span><span class="n">ContinuousVariable</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">categories</span><span class="p">)</span>
        <span class="n">P_Y_samples</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">P_Y_XM_fixed_x_obs_m</span><span class="p">(</span><span class="n">actual_value</span><span class="p">)</span>
        <span class="n">P_Y_X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span><span class="p">},</span> <span class="p">{</span><span class="n">P_Y</span><span class="p">:</span> <span class="n">P_Y_samples</span><span class="p">})</span>

        <span class="k">return</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="n">P_Y_X</span><span class="p">,</span> <span class="p">{</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="p">[</span><span class="n">counterfactual_value</span><span class="p">]})</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_NDE_categorical_target</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">actual_value</span><span class="p">,</span> <span class="n">counterfactual_value</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE by evaluating density (categorical Y)</span>

<span class="sd">        See &#39;NDE&#39; and &#39;_NDE_categorical_target_full_density&#39; above.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># returns (counterfactual probabilities, total effect)</span>
        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="bp">self</span><span class="o">.</span><span class="n">_NDE_categorical_target_full_density</span><span class="p">(</span><span class="n">counterfactual_value</span><span class="p">,</span> <span class="n">actual_value</span><span class="p">),</span>
                         <span class="bp">self</span><span class="o">.</span><span class="n">_NDE_categorical_target_full_density</span><span class="p">(</span><span class="n">actual_value</span><span class="p">,</span> <span class="n">actual_value</span><span class="p">)])</span><span class="o">.</span><span class="n">T</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_NIE_continuous_target</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NIE (continuous Y)</span>

<span class="sd">        See &#39;NIE&#39; above.</span>

<span class="sd">        Computed from mediation-formula</span>
<span class="sd">        (see [Pearl 2001] or [Shpitser, VanderWeele], see references above)</span>
<span class="sd">        by &quot;double&quot;-regression.</span>

<span class="sd">        If M is categorical, after fixing X=x, the fitted P( Y | X=x, M=m ), is actually</span>
<span class="sd">        categorical (a transfer matrix), because it takes values only in</span>
<span class="sd">        im( P ) = { P( Y | X=x, M=m_0 ), ...,  P( Y | X=x, M=m_k ) } where</span>
<span class="sd">        m_0, ..., m_k are the categories of M. This is clearly a finite set.</span>
<span class="sd">        Since the distribution over this finite subset of the continuous Val(Y)</span>
<span class="sd">        is very non-gaussian, &quot;max likelihood by least square estimation&quot; can fail horribly.</span>
<span class="sd">        Hence we fit a transfer-matrix instead.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span>
        <span class="n">M</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span>
        <span class="n">x</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span>
        <span class="n">m</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">m</span>

        <span class="k">if</span> <span class="n">M</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
            <span class="c1"># if the image of the mapping in double-regression is finite, use a density fit instead</span>
            <span class="n">p_M_X01</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">P_M_X</span><span class="p">([</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">])</span>
            <span class="n">E_YX0</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">E_Y_XM_fixed_x_all_m</span><span class="p">(</span><span class="n">change_from</span><span class="p">)</span>
            <span class="c1"># sum over finite M:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">dot</span><span class="p">(</span><span class="n">E_YX0</span><span class="p">,</span> <span class="n">p_M_X01</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">-</span> <span class="n">p_M_X01</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="n">Estimate</span> <span class="o">=</span> <span class="n">toy_setup</span><span class="o">.</span><span class="n">ContinuousVariable</span><span class="p">()</span>
            <span class="n">y_at_original_x</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">E_Y_XM_fixed_x_obs_m</span><span class="p">(</span><span class="n">change_from</span><span class="p">)</span>
            <span class="n">ModifiedY</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="n">X</span><span class="p">:</span> <span class="n">x</span><span class="p">},</span> <span class="p">{</span><span class="n">Estimate</span><span class="p">:</span> <span class="n">y_at_original_x</span><span class="p">})</span>
            <span class="k">return</span> <span class="p">(</span><span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="n">ModifiedY</span><span class="p">,</span> <span class="p">{</span><span class="n">X</span><span class="p">:</span> <span class="p">[</span><span class="n">change_to</span><span class="p">]})</span>
                    <span class="o">-</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="n">ModifiedY</span><span class="p">,</span> <span class="p">{</span><span class="n">X</span><span class="p">:</span> <span class="p">[</span><span class="n">change_from</span><span class="p">]}))</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_NIE_categorical_target</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NIE (continuous Y)</span>

<span class="sd">        See &#39;NIE&#39; above.</span>

<span class="sd">        Computed from mediation-formula</span>
<span class="sd">        (see [Pearl 2001] or [Shpitser, VanderWeele], see references above)</span>
<span class="sd">        by &quot;double&quot;-regression.</span>

<span class="sd">        Similar to before (see _NIE_continuous_target), treat categorical M differently.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span>
        <span class="n">M</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span>
        <span class="n">x</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">x</span>
        <span class="n">m</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">m</span>
        <span class="n">py_at_original_x</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">P_Y_XM_fixed_x_obs_m</span><span class="p">(</span><span class="n">change_from</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">M</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
            <span class="c1"># if the image of the mapping in double-regression is finite, use a density fit instead</span>
            <span class="n">p_M_X01</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">P_M_X</span><span class="p">([</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">])</span>
            <span class="c1"># sum over finite M:</span>
            <span class="n">result</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">([</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">categories</span><span class="p">,</span> <span class="mi">2</span><span class="p">])</span>  <span class="c1"># 2 is for TE &amp; CF</span>
            <span class="k">for</span> <span class="n">cM</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">M</span><span class="o">.</span><span class="n">categories</span><span class="p">):</span>
                <span class="n">result</span> <span class="o">+=</span> <span class="n">np</span><span class="o">.</span><span class="n">outer</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">py_at_original_x</span><span class="p">[</span><span class="n">m</span> <span class="o">==</span> <span class="n">cM</span><span class="p">],</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">),</span> <span class="n">p_M_X01</span><span class="p">[:,</span> <span class="n">cM</span><span class="p">])</span>
            <span class="k">return</span> <span class="n">result</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="n">Estimate</span> <span class="o">=</span> <span class="n">toy_setup</span><span class="o">.</span><span class="n">ContinuousVariable</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">categories</span><span class="p">)</span>
            <span class="n">ModifiedY</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="n">X</span><span class="p">:</span> <span class="n">x</span><span class="p">},</span> <span class="p">{</span><span class="n">Estimate</span><span class="p">:</span> <span class="n">py_at_original_x</span><span class="p">})</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="n">ModifiedY</span><span class="p">,</span> <span class="p">{</span><span class="n">X</span><span class="p">:</span> <span class="p">[</span><span class="n">change_to</span><span class="p">]}),</span>
                             <span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="n">ModifiedY</span><span class="p">,</span> <span class="p">{</span><span class="n">X</span><span class="p">:</span> <span class="p">[</span><span class="n">change_from</span><span class="p">]})])</span><span class="o">.</span><span class="n">T</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">NDE_grid</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">list_of_points</span><span class="p">,</span> <span class="n">cf_delta</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">normalize_by_delta</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">fct_of_nde</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE as grided (unsmoothed) function</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        list_of_points : np.array( K )</span>
<span class="sd">            List of reference-values at which to estimate NDEs</span>

<span class="sd">        cf_delta : float</span>
<span class="sd">            The change from reference-value to effect-value (change_from=reference, change_to=ref + delta)</span>

<span class="sd">        normalize_by_delta : bool</span>
<span class="sd">            Normalize the effect by dividing by cf_delta.</span>

<span class="sd">        fct_of_nde : *callable* or None</span>
<span class="sd">            Also in the case of a continuous Y the distribution, not just the expectation is identified.</span>
<span class="sd">            However, a density-fit is not usually reasonable to do in practise. However, instead of</span>
<span class="sd">            computing E[Y] can compute E[f(Y)] efficiently for any f. Assume f=id if None.</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NDE : If Y is categorical -&gt; np.array( K = # grid points, # categories Y, 2 )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping M as if X remained at change_from.</span>

<span class="sd">        NDE : If Y is continuous -&gt; np.array( K = # grid points )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping M as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">_Fct_on_grid</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">NDE</span><span class="p">,</span> <span class="n">list_of_points</span><span class="p">,</span> <span class="n">cf_delta</span><span class="p">,</span> <span class="n">normalize_by_delta</span><span class="p">,</span> <span class="n">fct_of_nde</span><span class="o">=</span><span class="n">fct_of_nde</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">NIE_grid</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">list_of_points</span><span class="p">,</span> <span class="n">cf_delta</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">normalize_by_delta</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NIE as grided (unsmoothed) function</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        list_of_points : np.array( K )</span>
<span class="sd">            List of reference-values at which to estimate NIEs</span>

<span class="sd">        cf_delta : float</span>
<span class="sd">            The change from reference-value to effect-value (change_from=reference, change_to=ref + delta)</span>

<span class="sd">        normalize_by_delta : bool</span>
<span class="sd">            Normalize the effect by dividing by cf_delta.</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NIE : If Y is categorical -&gt; np.array( K = # grid points, # categories Y, 2 )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by M from change_from to change_to, while keeping the value of X &quot;seen&quot; (directly)</span>
<span class="sd">            by Y as if X remained at change_from.</span>

<span class="sd">        NIE : If Y is continuous -&gt; np.array( K = # grid points )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by M from change_from to change_to, while keeping the value of X &quot;seen&quot; (directly)</span>
<span class="sd">            by Y as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">_Fct_on_grid</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">NIE</span><span class="p">,</span> <span class="n">list_of_points</span><span class="p">,</span> <span class="n">cf_delta</span><span class="p">,</span> <span class="n">normalize_by_delta</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">NDE_smoothed</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">min_x</span><span class="p">,</span> <span class="n">max_x</span><span class="p">,</span> <span class="n">cf_delta</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">steps</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">smoothing_gaussian_sigma_in_steps</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span>
                     <span class="n">normalize_by_delta</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">fct_of_nde</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE as smoothed function</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        min_x : float</span>
<span class="sd">            Lower bound of interval on which reference-values for X are taken</span>

<span class="sd">        max_x : float</span>
<span class="sd">            Upper bound of interval on which reference-values for X are taken</span>

<span class="sd">        cf_delta : float</span>
<span class="sd">            The change from reference-value to effect-value (change_from=reference, change_to=ref + delta)</span>

<span class="sd">        steps : uint</span>
<span class="sd">            Number of intermediate values to compute in the interval [min_x, max_x]</span>

<span class="sd">        smoothing_gaussian_sigma_in_steps : uint</span>
<span class="sd">            The width of the Gauß-kernel used for smoothing, given in steps.</span>

<span class="sd">        normalize_by_delta : bool</span>
<span class="sd">            Normalize the effect by dividing by cf_delta.</span>

<span class="sd">        fct_of_nde : *callable* or None</span>
<span class="sd">            Also in the case of a continuous Y the distribution, not just the expectation is identified.</span>
<span class="sd">            However, a density-fit is not usually reasonable to do in practise. However, instead of</span>
<span class="sd">            computing E[Y] can compute E[f(Y)] efficiently for any f. Assume f=id if None.</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NDE : If Y is categorical -&gt; np.array( # steps, # categories Y, 2 )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping M as if X remained at change_from.</span>

<span class="sd">        NDE : If Y is continuous -&gt; np.array( # steps )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping M as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">_Fct_smoothed</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">NDE</span><span class="p">,</span> <span class="n">min_x</span><span class="p">,</span> <span class="n">max_x</span><span class="p">,</span> <span class="n">cf_delta</span><span class="p">,</span> <span class="n">steps</span><span class="p">,</span> <span class="n">smoothing_gaussian_sigma_in_steps</span><span class="p">,</span>
                             <span class="n">normalize_by_delta</span><span class="p">,</span> <span class="n">fct_of_nde</span><span class="o">=</span><span class="n">fct_of_nde</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">NIE_smoothed</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">min_x</span><span class="p">,</span> <span class="n">max_x</span><span class="p">,</span> <span class="n">cf_delta</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">steps</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">smoothing_gaussian_sigma_in_steps</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span>
                     <span class="n">normalize_by_delta</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NIE as smoothed function</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        min_x : float</span>
<span class="sd">            Lower bound of interval on which reference-values for X are taken</span>

<span class="sd">        max_x : float</span>
<span class="sd">            Upper bound of interval on which reference-values for X are taken</span>

<span class="sd">        cf_delta : float</span>
<span class="sd">            The change from reference-value to effect-value (change_from=reference, change_to=ref + delta)</span>

<span class="sd">        steps : uint</span>
<span class="sd">            Number of intermediate values to compute in the interval [min_x, max_x]</span>

<span class="sd">        smoothing_gaussian_sigma_in_steps : uint</span>
<span class="sd">            The width of the Gauß-kernel used for smoothing, given in steps.</span>

<span class="sd">        normalize_by_delta : bool</span>
<span class="sd">            Normalize the effect by dividing by cf_delta.</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NIE : If Y is categorical -&gt; np.array( # steps, # categories Y, 2 )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by M from change_from to change_to, while keeping the value of X &quot;seen&quot; (directly)</span>
<span class="sd">            by Y as if X remained at change_from.</span>

<span class="sd">        NDE : If Y is continuous -&gt; np.array( # steps )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by M from change_from to change_to, while keeping the value of X &quot;seen&quot; (directly)</span>
<span class="sd">            by Y as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">_Fct_smoothed</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">NIE</span><span class="p">,</span> <span class="n">min_x</span><span class="p">,</span> <span class="n">max_x</span><span class="p">,</span> <span class="n">cf_delta</span><span class="p">,</span> <span class="n">steps</span><span class="p">,</span> <span class="n">smoothing_gaussian_sigma_in_steps</span><span class="p">,</span>
                             <span class="n">normalize_by_delta</span><span class="p">)</span>


<span class="k">class</span><span class="w"> </span><span class="nc">NaturalEffects_GraphMediation</span><span class="p">:</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Setup for estimating natural effects in a (general) causal graph.</span>

<span class="sd">    Methods for the non-parametric estimation of mediation effects by adjustment.</span>

<span class="sd">    Actual fit-models can be chosen independently, for details see</span>
<span class="sd">    technical appendix B in the mediation-tutorial.</span>

<span class="sd">    See references and tigramite tutorial for an in-depth introduction.</span>

<span class="sd">    References</span>
<span class="sd">    ----------</span>

<span class="sd">    J. Pearl. Direct and indirect effects. Proceedings of the Seventeenth Conference</span>
<span class="sd">    on Uncertainty in Artificial intelligence, 2001.</span>

<span class="sd">    J. Pearl. Interpretation and identification of causal mediation. Psychological</span>
<span class="sd">    methods, 19(4):459, 2014.</span>

<span class="sd">    I. Shpitser and T. J. VanderWeele. A complete graphical criterion for the adjust-</span>
<span class="sd">    ment formula in mediation analysis. The international journal of biostatistics,</span>
<span class="sd">    7(1), 2011.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    graph : np.array( [N, N] or [N, N, tau_max+1] depending on graph_type ) of 3-character patterns</span>
<span class="sd">        The causal graph, see &#39;Causal Effects&#39; tutorial. E.g. returned by causal discovery method</span>
<span class="sd">        (see &quot;Tutorials/Causal Discovery/CD Overview&quot;) or by a toymodel (see toy_setup.Model.GetGroundtruthGraph or</span>
<span class="sd">        the Mediation tutorial).</span>
<span class="sd">    graph_type : string</span>
<span class="sd">        The type of graph, tested for &#39;dag&#39; and &#39;stationary_dag&#39; (time-series). See &#39;Causal Effects&#39; tutorial.</span>
<span class="sd">    tau_max : uint</span>
<span class="sd">        Maximum lag to be considered (can be relevant for adjustment sets, passed to &#39;Causal Effects&#39; class).</span>
<span class="sd">    fit_setup : fit model</span>
<span class="sd">        A fit model to use internally, e.g. mixed_fit.FitSetup(). See there or technical</span>
<span class="sd">        appendix B of the tutorial.</span>
<span class="sd">    observations_data : dictionary ( keys=toy_setup.VariableDescription, values=np.array(N) )</span>
<span class="sd">        The data as map variable-description -&gt; samples; e.g. toy_setup.world.Observables(),</span>
<span class="sd">        or toy_setup.VariablesFromDataframe( tigramite dataframe )</span>
<span class="sd">    effect_source : toy_setup.VariableDescription or (idx, -lag)</span>
<span class="sd">        The (description of, e.g. toy_setup.ContinuousVariable()) the effect-source.</span>
<span class="sd">    effect_target : toy_setup.VariableDescriptionor (idx, -lag)</span>
<span class="sd">        The (description of, e.g. toy_setup.ContinuousVariable()) the effect-target.</span>
<span class="sd">    blocked_mediators : iterable of Variable-descriptions or &#39;all&#39;</span>
<span class="sd">        Which mediators to &#39;block&#39; (consider indirect), *un*\ blocked mediators are considered as</span>
<span class="sd">        contributions to the *direct* effect.</span>
<span class="sd">    adjustment_set : iterable of Variable-descriptions or &#39;auto&#39;</span>
<span class="sd">        Adjustment-set to use. Will be validated if specified explicitly, if &#39;auto&#39;, will try</span>
<span class="sd">        to use an &#39;optimal&#39; set, fall back to [Perkovic et al]&#39;s adjustment-set (which should always</span>
<span class="sd">        work if single-set adjustment as in [Shpitser, VanderWeele] is possible; this follows</span>
<span class="sd">        from combining results of [Shpitser, VanderWeele] and [Perkovic et al]).</span>
<span class="sd">        See &#39;Causal Effects&#39; and its tutorial for more info and references on (optimal) adjustment.</span>
<span class="sd">    only_check_validity : bool</span>
<span class="sd">        If True, do not set up an estimator, only check if an optimal adjustment-set exists (or the</span>
<span class="sd">        explicitly specified one is valid). Call this.Valid() to extract the result.</span>
<span class="sd">    fall_back_to_total_effect : bool</span>
<span class="sd">        If True, if no mediators are blocked, use mediation implementation to estimate the total effect.</span>
<span class="sd">        In this case, estimating the total effect through the &#39;Causal Effects&#39; class might be easier,</span>
<span class="sd">        however, for comparison to other estimates, using this option might yield more consistent results.</span>
<span class="sd">    _internal_provide_cfx : *None* or tigramite.CausalEffects</span>
<span class="sd">        Set to None. Used when called from CausalMediation, which already has a causal-effects class.</span>
<span class="sd">    enable_dataframe_based_preprocessing : bool</span>
<span class="sd">        Enable (and enforce) data-preprocessing through the tigramite::dataframe, makes missing-data</span>
<span class="sd">        and other features available to the mediation analysis. Custom (just in time) handling</span>
<span class="sd">        of missing data might be more sample-efficient.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">fit_setup</span><span class="p">,</span> <span class="n">observations_data</span><span class="p">,</span> <span class="n">effect_source</span><span class="p">,</span> <span class="n">effect_target</span><span class="p">,</span>
                 <span class="n">blocked_mediators</span><span class="o">=</span><span class="s2">&quot;all&quot;</span><span class="p">,</span> <span class="n">adjustment_set</span><span class="o">=</span><span class="s2">&quot;auto&quot;</span><span class="p">,</span> <span class="n">only_check_validity</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                 <span class="n">fall_back_to_total_effect</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">_internal_provide_cfx</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">enable_dataframe_based_preprocessing</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>

        <span class="n">data</span> <span class="o">=</span> <span class="n">toy_setup</span><span class="o">.</span><span class="n">DataHandler</span><span class="p">(</span><span class="n">observations_data</span><span class="p">,</span> <span class="n">dataframe_based_preprocessing</span><span class="o">=</span><span class="n">enable_dataframe_based_preprocessing</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">Source</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">GetVariableAuto</span><span class="p">(</span><span class="n">effect_source</span><span class="p">,</span> <span class="s2">&quot;Source&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">Target</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">GetVariableAuto</span><span class="p">(</span><span class="n">effect_target</span><span class="p">,</span> <span class="s2">&quot;Target&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">blocked_mediators</span> <span class="o">!=</span> <span class="s2">&quot;all&quot;</span><span class="p">:</span>
            <span class="n">blocked_mediators</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">GetVariablesAuto</span><span class="p">(</span><span class="n">blocked_mediators</span><span class="p">,</span> <span class="s2">&quot;Mediator&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">adjustment_set</span> <span class="o">!=</span> <span class="s2">&quot;auto&quot;</span><span class="p">:</span>
            <span class="n">adjustment_set</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">GetVariablesAuto</span><span class="p">(</span><span class="n">adjustment_set</span><span class="p">,</span> <span class="s2">&quot;Adjustment&quot;</span><span class="p">)</span>

        <span class="n">X</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">Source</span><span class="p">]</span>
        <span class="n">Y</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">Target</span><span class="p">]</span>

        <span class="c1"># Use tigramite&#39;s total effect estimation utility to help generate adjustment-sets and mediators</span>
        <span class="n">cfx_xy</span> <span class="o">=</span> <span class="n">_internal_provide_cfx</span> <span class="k">if</span> <span class="n">_internal_provide_cfx</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="k">else</span>\
            <span class="n">CausalEffects</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="o">=</span><span class="n">graph_type</span><span class="p">,</span> <span class="n">X</span><span class="o">=</span><span class="p">[</span><span class="n">X</span><span class="p">],</span> <span class="n">Y</span><span class="o">=</span><span class="p">[</span><span class="n">Y</span><span class="p">])</span>

        <span class="c1"># ----- MEDIATORS -----</span>
        <span class="c1"># Validate &quot;blocked mediators&quot; are actually mediators, or find &quot;all&quot;</span>
        <span class="n">all_mediators</span> <span class="o">=</span> <span class="n">blocked_mediators</span> <span class="o">==</span> <span class="s2">&quot;all&quot;</span>
        <span class="k">if</span> <span class="n">all_mediators</span><span class="p">:</span>
            <span class="n">M</span> <span class="o">=</span> <span class="n">cfx_xy</span><span class="o">.</span><span class="n">M</span>
            <span class="n">blocked_mediators</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">ReverseLookupMulti</span><span class="p">(</span><span class="n">M</span><span class="p">,</span> <span class="s2">&quot;Mediator&quot;</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">M</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">blocked_mediators</span><span class="p">]</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="nb">set</span><span class="p">(</span><span class="n">M</span><span class="p">)</span> <span class="o">&lt;=</span> <span class="nb">set</span><span class="p">(</span><span class="n">cfx_xy</span><span class="o">.</span><span class="n">M</span><span class="p">):</span>
                <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;Blocked mediators, if specified, must actually be mediators, try using&quot;</span>
                                <span class="s2">&quot;set-intersection with causal_effects_instance_xy.M instead.&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">M</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">fall_back_to_total_effect</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;There are no mediators, use total-effect estimation instead or set &quot;</span>
                            <span class="s2">&quot;fall_back_to_total_effect=True!&quot;</span><span class="p">)</span>

        <span class="c1"># ----- ADJUSTMENT -----</span>
        <span class="c1"># Use tigramite&#39;s total effect estimation utility to help validate adjustment-sets and mediators</span>
        <span class="n">cfx_xm</span> <span class="o">=</span> <span class="n">CausalEffects</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="o">=</span><span class="n">graph_type</span><span class="p">,</span> <span class="n">X</span><span class="o">=</span><span class="p">[</span><span class="n">X</span><span class="p">],</span> <span class="n">Y</span><span class="o">=</span><span class="n">M</span><span class="p">)</span>
        <span class="n">cfx_xm_y</span> <span class="o">=</span> <span class="n">CausalEffects</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="o">=</span><span class="n">graph_type</span><span class="p">,</span> <span class="n">X</span><span class="o">=</span><span class="p">[</span><span class="n">X</span><span class="p">]</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">M</span><span class="p">),</span> <span class="n">Y</span><span class="o">=</span><span class="p">[</span><span class="n">Y</span><span class="p">])</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">valid</span><span class="p">(</span><span class="n">S</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">cfx_xm</span><span class="o">.</span><span class="n">_check_validity</span><span class="p">(</span><span class="n">S</span><span class="p">)</span> <span class="ow">and</span> <span class="n">cfx_xm_y</span><span class="o">.</span><span class="n">_check_validity</span><span class="p">(</span><span class="n">S</span><span class="p">)</span>

        <span class="n">adjustment_set_auto</span> <span class="o">=</span> <span class="p">(</span><span class="n">adjustment_set</span> <span class="o">==</span> <span class="s2">&quot;auto&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">adjustment_set_auto</span><span class="p">:</span>
            <span class="c1"># first try optimal set (for small cf_delta, optimality for the causal effect should imply</span>
            <span class="c1"># optimality for the nde by continuity for the estimator variance)</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="n">cfx_xy</span><span class="o">.</span><span class="n">get_optimal_set</span><span class="p">()</span>

            <span class="k">if</span> <span class="ow">not</span> <span class="n">valid</span><span class="p">(</span><span class="n">Z</span><span class="p">)</span> <span class="ow">and</span> <span class="n">adjustment_set_auto</span><span class="p">:</span>
                <span class="c1"># fall back to adjust, which should work if any single adjustmentset works</span>
                <span class="n">Z</span> <span class="o">=</span> <span class="n">cfx_xy</span><span class="o">.</span><span class="n">_get_adjust_set</span><span class="p">()</span>

            <span class="n">adjustment_set</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">ReverseLookupMulti</span><span class="p">(</span><span class="n">Z</span><span class="p">,</span> <span class="s2">&quot;Adjustment&quot;</span><span class="p">)</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="n">adjustment_set</span><span class="p">]</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">valid</span> <span class="o">=</span> <span class="n">valid</span><span class="p">(</span><span class="n">Z</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">only_check_validity</span><span class="p">:</span>
            <span class="k">return</span>

        <span class="c1"># Output appropriate error msgs if not valid</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">valid</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">adjustment_set_auto</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span>
                    <span class="s2">&quot;The graph-effect you are trying to estimate is not identifiable via one-step adjustment, &quot;</span>
                    <span class="s2">&quot;try using a different query &quot;</span>
                    <span class="s2">&quot;or refine the causal graph by expert knowledge. &quot;</span>
                    <span class="s2">&quot;For the implemented method, there must be an adjustment-set, valid for both X u M -&gt; Y and &quot;</span>
                    <span class="s2">&quot;X -&gt; Y. If such a set exists, Perkovic&#39;s Adjust(X,Y) is valid, which was tried as &quot;</span>
                    <span class="s2">&quot;fallback because adjustment-set=&#39;auto&#39; was used.&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span>
                    <span class="s2">&quot;The adjustment-set specified is not valid for one-step adjustment, &quot;</span>
                    <span class="s2">&quot;try using or a different adjustment-set (or set it to &#39;auto&#39;), a different query &quot;</span>
                    <span class="s2">&quot;or refine the causal graph by expert knowledge. &quot;</span>
                    <span class="s2">&quot;For the implemented method, there must be an adjustment-set, valid for both X u M -&gt; Y and &quot;</span>
                    <span class="s2">&quot;X -&gt; Y. If such a set exists, Perkovic&#39;s Adjust(X,Y) is valid, which is tried as &quot;</span>
                    <span class="s2">&quot;fallback if adjustment-set=&#39;auto&#39; is used.&quot;</span><span class="p">)</span>

        
        <span class="c1"># lock in mediators and adjustment for preprocessing</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">BlockedMediators</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">ReverseLookupMulti</span><span class="p">(</span><span class="n">M</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">AdjustmentSet</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">ReverseLookupMulti</span><span class="p">(</span><span class="n">Z</span><span class="p">)</span>

        <span class="c1"># ----- STORE RESULTS ON INSTANCE -----</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">sources</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">Get</span><span class="p">(</span><span class="s2">&quot;Source&quot;</span><span class="p">,</span> <span class="p">[</span><span class="n">X</span><span class="p">],</span> <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>  <span class="c1"># currently univariate anyway</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">Get</span><span class="p">(</span><span class="s2">&quot;Target&quot;</span><span class="p">,</span> <span class="p">[</span><span class="n">Y</span><span class="p">],</span> <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">Y</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">M</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">M_ids</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">mediators</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">Get</span><span class="p">(</span><span class="s2">&quot;Mediator&quot;</span><span class="p">,</span> <span class="n">M</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">M_ids</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">mediators</span> <span class="o">=</span> <span class="p">{}</span>


        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">Z</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">Z_ids</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">Get</span><span class="p">(</span><span class="s2">&quot;Adjustment&quot;</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">Z_ids</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span> <span class="o">=</span> <span class="p">{}</span>


        <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span> <span class="o">=</span> <span class="n">fit_setup</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XMZ</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_P_Y_XMZ</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">E_Y_XMZ_fixed_x_obs_mz</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Provide samples Y( fixed x, observed m, z ) from fit of E[Y|X,M,Z]</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            The fixed value of X.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Y( fixed x, observed m, z ) : np.array(N)</span>
<span class="sd">            Samples of Y estimated from observations and fit.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">assert</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">is_categorical</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XMZ</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XMZ</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">mediators</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">},</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">)</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_E_Y_XMZ</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full_like</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">],</span> <span class="n">x</span><span class="p">),</span>
                              <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">mediators</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">})</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">P_Y_XMZ_fixed_x_obs_mz</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Provide for all samples of M, Z the likelihood P( Y | fixed x, observed m, z ) from fit of P[Y|X,M,Z]</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            The fixed value of X.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        P( Y | fixed x, observed m, z ) : np.array(N, # categories of Y)</span>
<span class="sd">            Likelihood of each category of Y given x and observations of m, z from fit.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">assert</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">is_categorical</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_P_Y_XMZ</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_P_Y_XMZ</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">mediators</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">},</span> <span class="bp">self</span><span class="o">.</span><span class="n">targets</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_P_Y_XMZ</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full_like</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">],</span> <span class="n">x</span><span class="p">),</span>
                              <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">mediators</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">})</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">Valid</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get validity of adjustment-set</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Valid : bool</span>
<span class="sd">            Validity of adjustment set. (see constructor-parameter &#39;only_check_validity&#39;)</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">valid</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">NDE</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute Natural Direct Effect (NDE)</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        change_from : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            Reference-value to which X is set by intervention in the world seen by the (blocked) mediators.</span>

<span class="sd">        change_to : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            Post-intervention-value to which X is set by intervention in the world seen by the effect (directly).</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful or if adjustment-set is not valid.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NDE : If Y is categorical -&gt; np.array( # categories Y, 2 )</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping (blocked) M as if X remained at change_from.</span>

<span class="sd">        NDE : If Y is continuous -&gt; float</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping (blocked) M as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">valid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;Valid adjustment-set is required!&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">ValidValue</span><span class="p">(</span><span class="n">change_from</span><span class="p">)</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">ValidValue</span><span class="p">(</span><span class="n">change_to</span><span class="p">)):</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;NDE change must be at valid values of the source-variable (e.g. categorical, and within &quot;</span>
                            <span class="s2">&quot;the range [0,num-categories).&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">is_categorical</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="bp">self</span><span class="o">.</span><span class="n">_NDE_categorical_target_full_density</span><span class="p">(</span><span class="n">change_to</span><span class="p">,</span> <span class="n">change_from</span><span class="p">),</span>
                             <span class="bp">self</span><span class="o">.</span><span class="n">_NDE_categorical_target_full_density</span><span class="p">(</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_from</span><span class="p">)])</span><span class="o">.</span><span class="n">T</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_NDE_continuous_target</span><span class="p">(</span><span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_NDE_continuous_target</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">change_from</span><span class="p">,</span> <span class="n">change_to</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE (continuous Y)</span>

<span class="sd">        See &#39;NDE&#39; above.</span>

<span class="sd">        Computed from mediation-formula</span>
<span class="sd">        (see [Pearl 2001] or [Shpitser, VanderWeele], see references above)</span>
<span class="sd">        by &quot;triple&quot;-regression.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">difference</span> <span class="o">=</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">E_Y_XMZ_fixed_x_obs_mz</span><span class="p">(</span><span class="n">change_to</span><span class="p">)</span>
                      <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">E_Y_XMZ_fixed_x_obs_mz</span><span class="p">(</span><span class="n">change_from</span><span class="p">))</span>

        <span class="n">Difference</span> <span class="o">=</span> <span class="n">toy_setup</span><span class="o">.</span><span class="n">ContinuousVariable</span><span class="p">()</span>
        <span class="n">E_Difference_X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">},</span>
                                            <span class="p">{</span><span class="n">Difference</span><span class="p">:</span> <span class="n">difference</span><span class="p">})</span>

        <span class="n">E_NDE_per_c</span> <span class="o">=</span> <span class="n">E_Difference_X</span><span class="p">({</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full_like</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">],</span> <span class="n">change_from</span><span class="p">),</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">})</span>

        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">E_NDE_per_c</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_NDE_categorical_target_full_density</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">cf_x</span><span class="p">,</span> <span class="n">reference_x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE as full density (categorical Y)</span>

<span class="sd">        See &#39;NDE&#39; above.</span>

<span class="sd">        Computed from mediation-formula</span>
<span class="sd">        (see [Pearl 2001] or [Shpitser, VanderWeele], see references above)</span>
<span class="sd">        by &quot;double&quot;-regression.</span>

<span class="sd">        Note: According to (see p.13)</span>
<span class="sd">        [Shpitser, VanderWeele: A Complete Graphical Criterion for theAdjustment Formula in Mediation Analysis]</span>
<span class="sd">        not just the expectation-value, but the full counterfactual distribution can be obtained via mediation-formula.</span>

<span class="sd">        Note: If all M and Z are categorical, after fixing X=x, the fitted P( Y | X=x, M=m ), is actually</span>
<span class="sd">        categorical (a transfer matrix), because it takes values only in</span>
<span class="sd">        im( P ) = { P( Y | X=x, M=m_0 ), ...,  P( Y | X=x, M=m_k ) } where</span>
<span class="sd">        m_0, ..., m_k are the categories of M. This is clearly a finite set.</span>
<span class="sd">        Since the distribution over this finite subset of the continuous Val(Y)</span>
<span class="sd">        is very non-gaussian, &quot;max likelihood by least square estimation&quot; can fail horribly.</span>
<span class="sd">        Hence we fit a transfer-matrix instead.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">p_y_values</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">P_Y_XMZ_fixed_x_obs_mz</span><span class="p">(</span><span class="n">cf_x</span><span class="p">)</span>

        <span class="k">if</span> <span class="p">(</span><span class="n">MixedData</span><span class="o">.</span><span class="n">IsPurelyCategorical</span><span class="p">({</span><span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">mediators</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">})</span>
                <span class="ow">and</span> <span class="nb">len</span><span class="p">({</span><span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">mediators</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">})</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">):</span>
            <span class="c1"># If there are mediators or adjustment</span>
            <span class="c1"># and they are purely categorical, then the mapping (M u Z) -&gt; P_Y</span>
            <span class="c1"># has finite image, treating it as categorical gives better results</span>

            <span class="c1"># different numpy-versions behave differently wrt this call:</span>
            <span class="c1"># https://numpy.org/devdocs/release/2.0.0-notes.html#np-unique-return-inverse-shape-for-multi-dimensional-inputs</span>
            <span class="c1"># see also https://github.com/numpy/numpy/issues/26738</span>
            <span class="n">labels_y</span><span class="p">,</span> <span class="n">transformed_y_numpy_version_dependent</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">unique</span><span class="p">(</span><span class="n">p_y_values</span><span class="p">,</span> <span class="n">return_inverse</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="n">transformed_y</span> <span class="o">=</span> <span class="n">transformed_y_numpy_version_dependent</span><span class="o">.</span><span class="n">squeeze</span><span class="p">()</span>

            <span class="n">P_Y</span> <span class="o">=</span> <span class="n">toy_setup</span><span class="o">.</span><span class="n">CategoricalVariable</span><span class="p">(</span><span class="n">categories</span><span class="o">=</span><span class="n">labels_y</span><span class="p">)</span>
            <span class="n">P_P_Y_xz</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">},</span> <span class="p">{</span><span class="n">P_Y</span><span class="p">:</span> <span class="n">transformed_y</span><span class="p">})</span>

            <span class="n">C_NDE_per_c</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="n">P_P_Y_xz</span><span class="p">,</span>
                                                       <span class="p">{</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full_like</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">],</span> <span class="n">reference_x</span><span class="p">),</span>
                                                        <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">})</span>
            <span class="nb">print</span><span class="p">(</span><span class="n">labels_y</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="n">C_NDE_per_c</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="n">labels_y</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>
            <span class="n">P_NDE_per_c</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">matmul</span><span class="p">(</span><span class="n">C_NDE_per_c</span><span class="p">,</span> <span class="n">labels_y</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">P_NDE_per_c</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>  <span class="c1"># Axis 1 is p of different categories</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="n">P_Y</span> <span class="o">=</span> <span class="n">toy_setup</span><span class="o">.</span><span class="n">ContinuousVariable</span><span class="p">(</span><span class="n">dimension</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">categories</span><span class="p">)</span>
            <span class="n">E_P_Y_xz</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_setup</span><span class="o">.</span><span class="n">Fit</span><span class="p">({</span><span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">},</span> <span class="p">{</span><span class="n">P_Y</span><span class="p">:</span> <span class="n">p_y_values</span><span class="p">})</span>

            <span class="n">P_NDE_per_c</span> <span class="o">=</span> <span class="n">MixedData</span><span class="o">.</span><span class="n">Call_map</span><span class="p">(</span><span class="n">E_P_Y_xz</span><span class="p">,</span>
                                                       <span class="p">{</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">full_like</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">sources</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">],</span> <span class="n">reference_x</span><span class="p">),</span>
                                                        <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment</span><span class="p">})</span>

            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">P_NDE_per_c</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>  <span class="c1"># Axis 0 is p of different categories</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">NDE_smoothed</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">min_x</span><span class="p">,</span> <span class="n">max_x</span><span class="p">,</span> <span class="n">cf_delta</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">steps</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">smoothing_gaussian_sigma_in_steps</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span>
                     <span class="n">normalize_by_delta</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE as smoothed function</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        min_x : float</span>
<span class="sd">            Lower bound of interval on which reference-values for X are taken</span>

<span class="sd">        max_x : float</span>
<span class="sd">            Upper bound of interval on which reference-values for X are taken</span>

<span class="sd">        cf_delta : float</span>
<span class="sd">            The change from reference-value to effect-value (change_from=reference, change_to=ref + delta)</span>

<span class="sd">        steps : uint</span>
<span class="sd">            Number of intermediate values to compute in the interval [min_x, max_x]</span>

<span class="sd">        smoothing_gaussian_sigma_in_steps : uint</span>
<span class="sd">            The width of the Gauß-kernel used for smoothing, given in steps.</span>

<span class="sd">        normalize_by_delta : bool</span>
<span class="sd">            Normalize the effect by dividing by cf_delta.</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful, adjustment-set is not valid or</span>
<span class="sd">        normalization requested makes no sense (normalizing probabilites by delta).</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NDE : If Y is categorical -&gt; np.array( # steps, # categories Y, 2 )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping (blocked) M as if X remained at change_from.</span>

<span class="sd">        NDE : If Y is continuous -&gt; np.array( # steps )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping (blocked) M as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">is_categorical</span> <span class="ow">and</span> <span class="n">normalize_by_delta</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;Do not normalize categorical output-probabilities by delta. (They are probabilities, &quot;</span>
                            <span class="s2">&quot;so normalizing them in this way makes no sense.) Normalize the difference instead.&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">_Fct_smoothed</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">NDE</span><span class="p">,</span> <span class="n">min_x</span><span class="p">,</span> <span class="n">max_x</span><span class="p">,</span> <span class="n">cf_delta</span><span class="p">,</span> <span class="n">steps</span><span class="p">,</span> <span class="n">smoothing_gaussian_sigma_in_steps</span><span class="p">,</span>
                             <span class="n">normalize_by_delta</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">PrintInfo</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Print info about estimator.</span>

<span class="sd">        Helper to quickly print blocked mediators, adjustment-set used and source-&gt;target.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Estimator for the effect of </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">Source</span><span class="o">.</span><span class="n">Info</span><span class="p">(</span><span class="n">detail</span><span class="p">)</span><span class="si">}</span><span class="s2"> on </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">Target</span><span class="o">.</span><span class="n">Info</span><span class="p">(</span><span class="n">detail</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">PrintMediators</span><span class="p">(</span><span class="n">detail</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">PrintAdjustmentSet</span><span class="p">(</span><span class="n">detail</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">PrintMediators</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot; Print info about blocked mediators. &quot;&quot;&quot;</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Blocked Mediators:&quot;</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">BlockedMediators</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot; - &quot;</span> <span class="o">+</span> <span class="n">m</span><span class="o">.</span><span class="n">Info</span><span class="p">(</span><span class="n">detail</span><span class="p">))</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">PrintAdjustmentSet</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot; Print info about adjustment-set. &quot;&quot;&quot;</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Adjustment Set:&quot;</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">z</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">AdjustmentSet</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot; - &quot;</span> <span class="o">+</span> <span class="n">z</span><span class="o">.</span><span class="n">Info</span><span class="p">(</span><span class="n">detail</span><span class="p">))</span>




<span class="sd">&quot;&quot;&quot;-------------------------------------------------------------------------------------------</span>
<span class="sd">-------------------------------   Expose Tigramite Interface   -------------------------------</span>
<span class="sd">-------------------------------------------------------------------------------------------&quot;&quot;&quot;</span>



<div class="viewcode-block" id="CausalMediation">
<a class="viewcode-back" href="../../index.html#tigramite.causal_mediation.CausalMediation">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">CausalMediation</span><span class="p">(</span><span class="n">CausalEffects</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Non-linear, non-additive causal mediation analysis.</span>

<span class="sd">    See the tutorial on Causal Mediation.</span>

<span class="sd">    Extends the tigramite.CausalEffects class by natural-effect estimation for counter-factual mediation analysis.</span>
<span class="sd">    Effects are estimated by adjustment, where adjustment-sets can be generated automatically (if they exist).</span>

<span class="sd">    Actual fit-models can be chosen independently, for details see</span>
<span class="sd">    technical appendix B in the mediation-tutorial.</span>

<span class="sd">    See references and tigramite tutorial for an in-depth introduction.</span>

<span class="sd">    References</span>
<span class="sd">    ----------</span>
<span class="sd">    J. Pearl. Direct and indirect effects. Proceedings of the Seventeenth Conference</span>
<span class="sd">    on Uncertainty in Artificial intelligence, 2001.</span>

<span class="sd">    J. Pearl. Interpretation and identification of causal mediation. Psychological</span>
<span class="sd">    methods, 19(4):459, 2014.</span>

<span class="sd">    I. Shpitser and T. J. VanderWeele. A complete graphical criterion for the adjust-</span>
<span class="sd">    ment formula in mediation analysis. The international journal of biostatistics,</span>
<span class="sd">    7(1), 2011.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    graph : np.array( [N, N] or [N, N, tau_max+1] depending on graph_type ) of 3-character patterns</span>
<span class="sd">        The causal graph, see &#39;Causal Effects&#39; tutorial. E.g. returned by causal discovery method</span>
<span class="sd">        (see &quot;Tutorials/Causal Discovery/CD Overview&quot;) or by a toymodel (see toy_setup.Model.GetGroundtruthGraph or</span>
<span class="sd">        the Mediation tutorial).</span>
<span class="sd">    graph_type : string</span>
<span class="sd">        The type of graph, tested for &#39;dag&#39; and &#39;stationary_dag&#39; (time-series). See &#39;Causal Effects&#39; tutorial.</span>
<span class="sd">    X : (idx, -lag)</span>
<span class="sd">        Index of the effect-source.</span>
<span class="sd">    Y : (idx, -lag)</span>
<span class="sd">        Index of the effect-target.</span>
<span class="sd">    S : None</span>
<span class="sd">        Reserved. Must be None in current version.</span>
<span class="sd">    hidden_variables : None</span>
<span class="sd">        Reserved. Must be None in current version.</span>
<span class="sd">    verbosity : uint</span>
<span class="sd">        Tigramite.CausalEffects verbosity setting.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">S</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">hidden_variables</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">S</span><span class="p">,</span> <span class="n">hidden_variables</span><span class="p">,</span> <span class="kc">False</span><span class="p">,</span> <span class="n">verbosity</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">BlockedMediators</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">MediationEstimator</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">assert</span> <span class="n">hidden_variables</span> <span class="ow">is</span> <span class="kc">None</span>

<div class="viewcode-block" id="CausalMediation.fit_natural_direct_effect">
<a class="viewcode-back" href="../../index.html#tigramite.causal_mediation.CausalMediation.fit_natural_direct_effect">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">fit_natural_direct_effect</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">dataframe</span><span class="p">,</span> <span class="n">mixed_data_estimator</span><span class="o">=</span><span class="n">FitSetup</span><span class="p">(),</span>
                                  <span class="n">blocked_mediators</span><span class="o">=</span><span class="s1">&#39;all&#39;</span><span class="p">,</span> <span class="n">adjustment_set</span><span class="o">=</span><span class="s1">&#39;auto&#39;</span><span class="p">,</span>
                                  <span class="n">use_mediation_impl_for_total_effect_fallback</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                  <span class="n">enable_dataframe_based_preprocessing</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Fit a natural direct effect.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        dataframe : tigramite.Dataframe</span>
<span class="sd">            Observed data.</span>
<span class="sd">        mixed_data_estimator : mixed_fit.FitSetup</span>
<span class="sd">            The fit-configuration to use. See mixed_fit.FitSetup and the Mediation tutorial, Appendix B.</span>
<span class="sd">        blocked_mediators : &#39;all&#39; or *iterable* of &lt; (idx, -lag) &gt;</span>
<span class="sd">            Which mediators to &#39;block&#39; (consider indirect), *un*\ blocked mediators are considered as</span>
<span class="sd">            contributions to the *direct* effect.</span>
<span class="sd">        adjustment_set : &#39;auto&#39; or None or *iterable* &lt; (idx, -lag) &gt;</span>
<span class="sd">            Adjustment-set to use. Will be validated if specified explicitly, if &#39;auto&#39; or None, will try</span>
<span class="sd">            to use an &#39;optimal&#39; set, fall back to [Perkovic et al]&#39;s adjustment-set (which should always</span>
<span class="sd">            work if single-set adjustment as in [Shpitser, VanderWeele] os possible; this follows</span>
<span class="sd">            from combining results of [Shpitser, VanderWeele] and [Perkovic et al]).</span>
<span class="sd">            See &#39;Causal Effects&#39; and its tutorial for more info and references on (optimal) adjustment.</span>
<span class="sd">        use_mediation_impl_for_total_effect_fallback : bool</span>
<span class="sd">            If True, if no mediators are blocked, use mediation implementation to estimate the total effect.</span>
<span class="sd">            In this case, estimating the total effect through the &#39;Causal Effects&#39; class might be easier,</span>
<span class="sd">            however, for comparison to other estimates, using this option might yield more consistent results.        </span>
<span class="sd">        enable_dataframe_based_preprocessing : bool</span>
<span class="sd">            Enable (and enforce) data-preprocessing through the tigramite::dataframe, makes missing-data</span>
<span class="sd">            and other features available to the mediation analysis. Custom (just in time) handling</span>
<span class="sd">            of missing data might be more sample-efficient.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        estimator : NaturalEffects_GraphMediation</span>
<span class="sd">            Typically, use predict_natural_direct_effect or predict_natural_direct_effect_function</span>
<span class="sd">            to use the fitted estimator.</span>
<span class="sd">            The internal NaturalEffects_GraphMediation (if needed).</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">adjustment_set</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">adjustment_set</span> <span class="o">=</span> <span class="s1">&#39;auto&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">BlockedMediators</span> <span class="o">=</span> <span class="n">blocked_mediators</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">NotImplementedError</span><span class="p">(</span><span class="s2">&quot;Currently only implemented for univariate effects (source).&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">NotImplementedError</span><span class="p">(</span><span class="s2">&quot;Currently only implemented for univariate effects (target).&quot;</span><span class="p">)</span>
        <span class="p">[</span><span class="n">source</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span>
        <span class="p">[</span><span class="n">target</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">MediationEstimator</span> <span class="o">=</span> <span class="n">NaturalEffects_GraphMediation</span><span class="p">(</span>
            <span class="n">graph</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
            <span class="n">fit_setup</span><span class="o">=</span><span class="n">mixed_data_estimator</span><span class="p">,</span> <span class="n">observations_data</span><span class="o">=</span><span class="n">dataframe</span><span class="p">,</span>
            <span class="n">effect_source</span><span class="o">=</span><span class="n">source</span><span class="p">,</span> <span class="n">effect_target</span><span class="o">=</span><span class="n">target</span><span class="p">,</span>
            <span class="n">blocked_mediators</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">BlockedMediators</span><span class="p">,</span> <span class="n">adjustment_set</span><span class="o">=</span><span class="n">adjustment_set</span><span class="p">,</span> <span class="n">only_check_validity</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
            <span class="n">fall_back_to_total_effect</span><span class="o">=</span><span class="n">use_mediation_impl_for_total_effect_fallback</span><span class="p">,</span>
            <span class="n">_internal_provide_cfx</span><span class="o">=</span><span class="bp">self</span><span class="p">,</span> <span class="n">enable_dataframe_based_preprocessing</span><span class="o">=</span><span class="n">enable_dataframe_based_preprocessing</span><span class="p">)</span>
        <span class="c1"># return a NDE_Graph Estimator, but also remember it for predict_nde</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">MediationEstimator</span></div>


<div class="viewcode-block" id="CausalMediation.predict_natural_direct_effect">
<a class="viewcode-back" href="../../index.html#tigramite.causal_mediation.CausalMediation.predict_natural_direct_effect">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">predict_natural_direct_effect</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">reference_value_x</span><span class="p">,</span> <span class="n">cf_intervention_value_x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;*After fitting* a natural direct effect, predict its value at a specific point.</span>
<span class="sd">        See also predict_natural_direct_effect_function.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        reference_value_x : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            Reference-value to which X is set by intervention in the world seen by the (blocked) mediators.</span>

<span class="sd">        cf_intervention_value_x : single value of same type as single sample for X (float, int, or bool)</span>
<span class="sd">            Post-intervention-value to which X is set by intervention in the world seen by the effect (directly).</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful or if adjustment-set is not valid.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NDE : If Y is categorical -&gt; np.array( # categories Y, 2 )</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping (blocked) M as if X remained at change_from.</span>

<span class="sd">        NDE : If Y is continuous -&gt; float</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping (blocked) M as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">MediationEstimator</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;Call fit_natural_direct_effect_x before using predict_natural_direct_effect.&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">MediationEstimator</span><span class="o">.</span><span class="n">NDE</span><span class="p">(</span><span class="n">change_from</span><span class="o">=</span><span class="n">reference_value_x</span><span class="p">,</span> <span class="n">change_to</span><span class="o">=</span><span class="n">cf_intervention_value_x</span><span class="p">)</span></div>


<div class="viewcode-block" id="CausalMediation.predict_natural_direct_effect_function">
<a class="viewcode-back" href="../../index.html#tigramite.causal_mediation.CausalMediation.predict_natural_direct_effect_function">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">predict_natural_direct_effect_function</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">min_x</span><span class="p">,</span> <span class="n">max_x</span><span class="p">,</span> <span class="n">cf_delta</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span>
                                               <span class="n">steps</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">smoothing_gaussian_sigma_in_steps</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span>
                                               <span class="n">normalize_by_delta</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Compute NDE as *smoothed* function</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        min_x : float</span>
<span class="sd">            Lower bound of interval on which reference-values for X are taken</span>

<span class="sd">        max_x : float</span>
<span class="sd">            Upper bound of interval on which reference-values for X are taken</span>

<span class="sd">        cf_delta : float</span>
<span class="sd">            The change from reference-value to effect-value (change_from=reference, change_to=ref + delta)</span>

<span class="sd">        steps : uint</span>
<span class="sd">            Number of intermediate values to compute in the interval [min_x, max_x]</span>

<span class="sd">        smoothing_gaussian_sigma_in_steps : uint</span>
<span class="sd">            The width of the Gauß-kernel used for smoothing, given in steps.</span>

<span class="sd">        normalize_by_delta : bool</span>
<span class="sd">            Normalize the effect by dividing by cf_delta.</span>

<span class="sd">        Throws</span>
<span class="sd">        ------</span>
<span class="sd">        Raises and exception if parameters are not meaningful, adjustment-set is not valid or</span>
<span class="sd">        normalization requested makes no sense (normalizing probabilites by delta).</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        NDE : If Y is categorical -&gt; np.array( # steps, # categories Y, 2 )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The probabilities the categories of Y (after, before) changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping (blocked) M as if X remained at change_from.</span>

<span class="sd">        NDE : If Y is continuous -&gt; np.array( # steps )</span>
<span class="sd">            For each grid-point:</span>
<span class="sd">            The change in the expectation-value of Y induced by changing the interventional value of X</span>
<span class="sd">            as &quot;seen&quot; by Y from change_from to change_to, while keeping (blocked) M as if X remained at change_from.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">MediationEstimator</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">Exception</span><span class="p">(</span><span class="s2">&quot;Call fit_natural_direct_effect before using predict_natural_direct_effect_x.&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">MediationEstimator</span><span class="o">.</span><span class="n">NDE_smoothed</span><span class="p">(</span><span class="n">min_x</span><span class="p">,</span> <span class="n">max_x</span><span class="p">,</span> <span class="n">cf_delta</span><span class="p">,</span>
                                                    <span class="n">steps</span><span class="p">,</span> <span class="n">smoothing_gaussian_sigma_in_steps</span><span class="p">,</span>
                                                    <span class="n">normalize_by_delta</span><span class="p">)</span></div>
</div>

</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../index.html">Documentation overview</a><ul>
  <li><a href="../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2023, Jakob Runge.
      
      |
      Powered by <a href="https://www.sphinx-doc.org/">Sphinx 8.2.3</a>
      &amp; <a href="https://alabaster.readthedocs.io">Alabaster 0.7.16</a>
      
    </div>

    

    
  </body>
</html>