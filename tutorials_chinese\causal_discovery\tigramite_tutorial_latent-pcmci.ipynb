{"cells": [{"cell_type": "markdown", "id": "7154ddb6", "metadata": {}, "source": ["# 潜在 因果发现 with TIGRAMITE"]}, {"cell_type": "markdown", "id": "54852686", "metadata": {}, "source": ["TIGRAMITE is a time series analysis python module. It allows to reconstruct causal graphical models from discrete or continuously-valued time series based on the PCMCI framework and create high-quality plots of the results.\n", "\n", "The following Nature Review Earth and Environment paper provides an overview of causal inference for time series in general: https://github.com/jakobrunge/tigramite/blob/master/tutorials/Runge_Causal_Inference_for_Time_Series_NREE.pdf\n", "\n", "This tutorial explains the **Latent-PCMCI (LPCMCI) algorithm**, which is implemented as the function `LPCMCI.run_lpcmci`. In contrast to the [PCMCI](https://github.com/jakobrunge/tigramite/blob/master/tutorials/tigramite_tutorial_basics.ipynb) and [PCMCIplus](https://github.com/jakobrunge/tigramite/blob/master/tutorials/tigramite_tutorial_pcmciplus.ipynb) algorithms, respectively implemented as `PCMCI.run_pcmci` and `PCMCI.run_pcmciplus`, LPCMCI allows for unobserved (aka latent) time series.\n", "\n", "**注意:**\n", "This 方法 is still experimental since the default 设置 of hyperparameters are still being fine-tuned. Feedback on this matter is kindly appreciated.\n", "\n", "---\n", "**Publication on LPCMCI:**\n", "<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> (2020). High-recall causal discovery for autocorrelated time series with latent confounders. In Laro<PERSON>le, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, <PERSON>, editors, *Advances in Neural Information Processing Systems*, volume 33, pages 12615–12625. Curran Associates, Inc. [https://proceedings.neurips.cc/paper/2020/file/94e70705efae423efda1088614128d0b-Paper.pdf](https://proceedings.neurips.cc/paper/2020/file/94e70705efae423efda1088614128d0b-Paper.pdf).\n", "\n", "---\n", "\n", "The structure of 本教程 is as follows:\n", "1. Section 1 explains the interpretation of the causal 图形模型 that are being learned by LPCMCI.\n", "2. Section 2 gives an introduction into how LPCMCI works and explains its essential 参数 and 输出.\n", "3. Section 3 explains the practical use of LPCMCI by showing an 示例 application on synthetic 数据."]}, {"cell_type": "code", "execution_count": 1, "id": "54ce75b6", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline     \n", "\n", "import tigramite\n", "from tigramite import data_processing as pp\n", "from tigramite.toymodels import structural_causal_processes as toys\n", "from tigramite import plotting as tp\n", "from tigramite.lpcmci import LPCMCI\n", "from tigramite.pcmci import PCMCI\n", "from tigramite.independence_tests.parcorr import ParCorr\n", "# from tigramite.independence_tests.gpdc import GPDC\n", "# from tigramite.independence_tests.cmiknn import CMIknn\n", "# from tigramite.independence_tests.cmisymb import CMIsymb"]}, {"cell_type": "markdown", "id": "08d1aca7", "metadata": {}, "source": ["## 1 Structural causal processes and their graphical representation"]}, {"cell_type": "markdown", "id": "f13e019e", "metadata": {}, "source": ["We are interested in learning the causal structure underlying complex dynamical systems. The corresponding time series $\\mathbf{V}_t=(V^1_t,\\ldots,V^N_t)$ is assumed to follow a structural causal process, i.e., to be of the form\n", "\n", "$$\\begin{align} \\label{eq:causal_model} V^j_t &= f_j\\left(\\mathcal{P}(V^j_t),\\,\\eta^j_t\\right) \\end{align}$$\n", "\n", "where $f_j$ is some arbitrary measurable function with non-trivial dependencies on all its arguments, $\\eta^j_t$ represents mutually ($i\\neq j$) and serially ($t'\\neq t$) independent dynamical noise, and $\\mathcal{P}(V^j_t) \\subset \\mathbf{V}^-_{t+1}=(\\mathbf{V}_{t}, \\mathbf{V}_{t-1},\\ldots){\\setminus} \\{V^j_t\\}$. Imporantly, the equation is asserted to have causal meaning in the sense that it represents the physical mechanism by which the value of $V^j_t$ is determined from the values of variables in $\\mathcal{P}(V^j_t)$ together with the value of the dynamical noise $\\eta^j_t$.$^a$ This is why variables variables in $\\mathcal{P}(V^j_t)$ are referred to as *causal parents* of $V^j_t$. We further require that $V^i_{t-\\tau} \\in \\mathcal{P}(V^j_{t})$ if and only if $V^i_{t-\\tau - \\Delta t} \\in \\mathcal{P}(V^j_{t-\\Delta t})$, a property referred to as *causal stationarity*, and that there are no cyclic causal relationships (both these properties have an intuitive graphical meaning, see subsection 1.1 below).\n", "\n", "The goal of LPCMCI is to learn the 时间序列 DPAG of the 数据-generating structural causal process, which is a graph that represents partial knowledge of the causal ancestral relationships among only the observed variables. These graphs are introduced and explained in the remainder of this section.\n", "\n", "Footnotes:\\\n", "$^a$For a more detailed explanation of this point we refer to the literature on structural causal models, see e.g. the textbooks [1] and [2] (the references are given at the bottom of this notebook)."]}, {"cell_type": "markdown", "id": "c56f4a71", "metadata": {}, "source": ["### 1.1 时间序列 DAGs"]}, {"cell_type": "markdown", "id": "f03fbdae", "metadata": {}, "source": ["The causal *parentships* specified by a structural causal process can conveniently be represented by a directed acyclic graph (DAG) $\\mathcal{G}$ that\n", "1. has one vertex (aka node) per 变量 $V^j_t$ and\n", "2. an edge (aka link) $V^i_{t-\\tau} {\\rightarrow} V^j_t$ if and only if $V^i_{t-\\tau} \\in \\mathcal{P}(V^j_t)$.\n", "\n", "It is acyclic precisely because by assumption there are no cyclic causal relationships, and its structure is repetitive in time due to the causal stationarity. In other tutorials in TIGRAMITE $\\mathcal{G}$ is 也 referred to as *时间序列 graph*.\n", "\n", "An edge $V^i_{t-\\tau} {\\rightarrow} V^j_t$ with $\\tau > 0$ is referred to as *lagged* and the integer $\\tau$ is its *滞后*. Edges $V^i_{t} {\\rightarrow} V^j_t$ are called *contemporaneous*. The *order* of a process, denoted by $p_{\\text{ts}}$, is the maximum 滞后.\n", "\n", "For illustration, here and in the subsequent discussions, we consider the following linear structural causal process of order $p_{\\text{ts}}= 2$ with four component time series as a running example:\n", "\n", "$$\n", "\\begin{align}\n", "V^1_t &= 0.9 V^1_{t-1} + 0.6 V^2_{t} + \\eta^1_t\\\\\n", "V^2_t &= \\eta^2_t \\\\\n", "V^3_t &= 0.9 V^3_{t-1} + 0.4 V^2_{t-1} + \\eta^3_t\\\\\n", "V^4_t &= 0.9 V^4_{t-1} - 0.4 V^3_{t-2} + \\eta^4_T\n", "\\end{align}\n", "$$\n", "\n", "The 以下 figure shows the associated 时间序列 DAG $\\mathcal{G}$:\n", "\n", "<img src=\"figures/ts_DAG.png\" width=300 height=300 />\n", "\n", "The horizontal dots on the left and right indicate that this graph in principle extends to the infinity past and future. However, due to the repetitive structure as imposed by causal stationarity it is sufficient to restrict to a time window at least as large as $[t-p_{\\text{ts}}, t]$ (for the above example this means it would have been sufficient to show one time step less). By convention we only draw those edges that are fully contained within the shown time window: For example, the edge $V^1_{t-1} {\\rightarrow} V^3_{t+1}$ is not drawn despite $V^1_{t-1}$ being in the shown time window $[t-3, t]$ because $V^3_{t+1}$ is outside this time window."]}, {"cell_type": "markdown", "id": "693747bd", "metadata": {}, "source": ["### 1.2 时间序列 DMAGs"]}, {"cell_type": "markdown", "id": "cc216c40", "metadata": {}, "source": ["The setting of LPCMCI, which distinguishes it from PCMCI and PCMCIplus, is that a subset of the component time series is allowed to be unobserved. In other words: The set of component time series $V^1, \\ldots, V^N$ splits into a set of observed time series $X^1, \\ldots, X^{N_X}$ with $N_X \\geq 1$ and a set of unobserved time series $L^1, \\ldots, L^{N_L}$ with $N_L \\geq 0$ and $N = N_X + N_L$.\n", "\n", "This raises the question as how to represent the causal relationships of the underlying process in a graph with vertices only for the observed variables. One approach, which LPCMCI is based on, is to employ *directed maximal ancestral graphs (DMAGs)*. These are a type of directed mixed graphs and, thus, can have directed edges $X {\\rightarrow} Y$ and bidirected edges $X {\\leftrightarrow} Y$. They are a specialization of the yet more general class of maximal ancestral graphs (MAGs) introduced in [3], which in addition allow to represent selection bias. For LPCMCI, however, the absence of selection bias is assumed.\n", "\n", "The basic idea is as follows:\\\n", "To a given DAG $\\mathcal{G}$ with a given subset of unobserved variables one can associate a unique DMAG $\\mathcal{M}(\\mathcal{G})$ over the observed variables that has the 以下 properties:\n", "\n", "1. **Adjacencies:**\\\n", "There is an edge between vertices $X$ and $Y$, i.e., $X {\\rightarrow} Y$ or $X {\\leftarrow} Y$ or $X {\\leftrightarrow} X$, if and only if the information flow between them cannot be blocked by conditioning on any subset of observed variables, i.e., if and only if there is no subset of observed variables conditional on which $X$ and $Y$ become independent.\n", "2. **Edge types:**\n", "    1. $X {\\rightarrow} Y$ implies that in $\\mathcal{G}$ there is a directed path from $X$ to $Y$. Because $\\mathcal{G}$ is acyclic, this further implies that there is no directed path from $Y$ to $X$.\n", "    2. $X {\\leftrightarrow} Y$ implies that in $\\mathcal{G}$ there neither is a directed path from $X$ to $Y$ nor a directed path from $Y$ to $X$.\n", "    \n", "Since the DAG $\\mathcal{G}$ carries causal meaning, in the sense that a directed edge signifies causal parentship, 也 the associated DMAG $\\mathcal{M}(\\mathcal{G})$ carries causal meaning:\n", "1. $X {\\rightarrow} Y$ says that\n", "    1. $X$ is a (potentially indirect) cause of $Y$\n", "    2. $Y$ does not cause $X$\n", "2. $X {\\leftrightarrow} Y$ says that\n", "    1. $X$ does not cause $Y$\n", "    2. $Y$ does not cause $X$\n", "    2. $X$ and $Y$ are subject to unobserved confounding, i.e., there is an unobserved 变量 $Z$ that causes both $X$ and $Y$ (this follows because else there would not be any edge between $X$ and $Y$).\n", "\n", "In other words: The DMAG $\\mathcal{M}(\\mathcal{G})$ represents the *causal ancestral relationships* of the underlying 数据 generating process. The absence and presence of an edge between a pair of variables do, however, not have a straightforward causal interpretation.\n", "\n", "In the 时间序列 setting considered here one additional aspect comes into play: Not the entire past and future but only a *finite* number of time steps can be observed. This amounts to the choice of an observed time window $[t-\\tau_{\\text{max}}, t]$, where $\\tau_{\\text{max}} \\geq 0$ is referred to as *maximum considered time 滞后*.$^a$ Moreover, the repetitive structure of the underyling DAG $\\mathcal{G}$ can be used to 也 impose a repetitive structure on the associated DMAG which is then extrapolated to the time steps outside of the observed time window. As will be explained in more detail in subsection 1.2.2, the resulting DMAG in 一般 depends on the choice of $\\tau_{\\text{max}}$. For this reason we employ the notation $\\mathcal{M}^{\\tau_{\\text{max}}}(\\mathcal{G})$ when specifically referring to the 时间序列 setting.\n", "\n", "To illustrate the previous discussion, let us return to the running example and say that the component time series $V^2$ is unobserved while $V^1$, $V^3$, and $V^4$ are observed. The corresponding time series DMAG $\\mathcal{M}^2(\\mathcal{G})$, so with the choice $\\tau_{\\text{max}} = 2$, is shown on the right-hand-side of the following figure:\n", "\n", "<img src=\"figures/ts_DMAG.png\" width=600 height=300 />\n", "\n", "Note that the unobserved variable $L^1_{t-1}$ confounds the observed variables $X^1_{t-1}$ and $X^2_t$ by means of the path $X^1_{t-1} {\\leftarrow} L^1_{t-1} {\\rightarrow} X^2_{t}$. This introduces a dependence between $X^1_{t-1}$ and $X^2_t$ that could only be blocked by conditionig on $L^1_{t-1}$, which cannot be done because $L^1_{t-1}$ is unobserved. Hence, $X^1_{t-1}$ and $X^2_t$ are adjacent in $\\mathcal{M}(\\mathcal{G})$. Since in $\\mathcal{G}$ there neither is a directed path from $X^1_{t-1}$ to $X^2_t$ nor from $X^2_t$ to $X^1_{t-1}$ --- the latter is impossible anyway because there is no causal influence backwards in time as enforced from the outset by restricting the set $\\mathcal{P}(V^j_t)$ to the present and past of $V^j_t$ --- the edge between them is bidirected, i.e., $X^1_{t-1} {\\leftrightarrow} X^2_t$ in $\\mathcal{M}^2(\\mathcal{G})$.\n", "\n", "Footnotes:\\\n", "$^a$注意 that $\\tau_{\\text{max}}$ is NOT equivalent to the process order $p_{\\text{ts}}$. The word \"considered\" in \"maximum considered time 滞后\" is supposed to stress the distinction."]}, {"cell_type": "markdown", "id": "5b10d299", "metadata": {}, "source": ["#### 1.2.1 More details on the interpretation of DMAGs"]}, {"cell_type": "markdown", "id": "21b458c9", "metadata": {}, "source": ["The interpretation of DMAGs can sometimes be difficult, especially concerning directed edges. To avoid confusion, we stress what a DMAG $\\mathcal{M}(\\mathcal{G})$ does NOT entail:\n", "1. A directed edge $X {\\rightarrow} Y$ does NOT say that $X$ is a causal parent of $Y$ in $\\mathcal{G}$. Rather the directed path from $X$ to $Y$ may contain more than a single edge, i.e., the causal influence of $X$ on $Y$ may be indirect.\n", "2. A directed edge $X {\\rightarrow} Y$ does NOT say that $X$ and $Y$ are not subject to unobserved confounding. Rather, in addition to the directed path(s) from $X$ to $Y$ in $\\mathcal{G}$ there may be one or more unobserved variables $Z$ that cause both $X$ and $Y$. (However: For certain directed edges it *is* possible to infer the absence of unobserved confounding, see further below in the current subsection 1.2.1).\n", "\n", "Both these points are illustrated by the 以下 示例, 其中 left-hand side shows a 时间序列 DAG $\\mathcal{G}$ and the right-hand side its corresponding 时间序列 DMAG $\\mathcal{M}^2(\\mathcal{G})$:\n", "\n", "<img src=\"figures/ts_DMAG_2.png\" width=600 height=300 />\n", "\n", "Blocking the dependence between $X^3_{t-1}$ and $X^1_t$ introduced by the path $X^3_{t-1} {\\rightarrow} X^2_t {\\rightarrow} X^1_t$ requires to condition on $X^2_t$. This, however, introduces a dependence along the path $X^3_{t-1} {\\rightarrow} X^2_t {\\leftarrow} L^1_{t-1} {\\rightarrow} X^1_t$. Therefore, there is no set of observed variables conditional on which $X^3_{t-1}$ and $X^1_t$ become independent and, thus, they are adjacent in $\\mathcal{M}^2(\\mathcal{G})$. Due to the directed path $X^3_{t-1} {\\rightarrow} X^2_t {\\rightarrow} X^1_t$ this edge is directed, i.e., $X^3_{t-1} {\\rightarrow} X^1_t$ in $\\mathcal{M}^2(\\mathcal{G})$. 注意, however, that $X^3_{t-1}$ is not a causal parent of $X^1_t$ in $\\mathcal{G}$, thus illustrating the first point in above list. The second point is illustrated by the edge $X^2_t {\\rightarrow} X^1_t$, which is confounded by the unobserved 变量 $L^1_{t-1}$ through the path $X^2_t {\\leftarrow} L^1_{t-1} {\\rightarrow} X^1_t$.\n", "\n", "However, *under certain conditions it is possible* to infer from $X {\\rightarrow} Y$ in $\\mathcal{M}(\\mathcal{G})$ that $X$ and $Y$ are not subject to unobserved confounding. Such directed edges were termed *visible* in [4]. A sufficient but not necessary condition for visibility is the following:\n", "1. If there is a third 变量 $Z$ such that $Z {\\rightarrow} X {\\rightarrow} Y$ or $Z {\\leftrightarrow} X {\\rightarrow} Y$ in $\\mathcal{M}(\\mathcal{G})$ and $Z$ and $Y$ are not adjacent in $\\mathcal{M}(\\mathcal{G})$, then the edge $X {\\rightarrow} Y$ is visible, i.e., $X$ and $Y$ are not subject to unobserved confounding.\n", "\n", "Note that for the edge $X^2_{t} {\\rightarrow} X^1_t$ in the previous example this condition is indeed not met."]}, {"cell_type": "markdown", "id": "5f170a7d", "metadata": {}, "source": ["#### 1.2.2 Effect of the maximum considered time 滞后 $\\tau_{\\text{max}}$"]}, {"cell_type": "markdown", "id": "b6c44c91", "metadata": {}, "source": ["As mentioned above, the time series DMAG $\\mathcal{M}^{\\tau_{\\text{max}}}(\\mathcal{G})$ can depend on the choice of the maximum considered time lag $\\tau_{\\text{max}}$. To illustrate this, the right-hand side of the following figure shows the time series DMAG $\\mathcal{M}^1(\\mathcal{G})$, so for $\\tau_{\\text{max}} = 1$, associated to our running example.\n", "\n", "<img src=\"figures/ts_DMAG_a.png\" width=600 height=300 />\n", "\n", "Compare this with the corresponding time series DMAG $\\mathcal{M}^2(\\mathcal{G})$, which is depicted in section 1.2 before subsection 1.2.1. One difference is that in $\\mathcal{M}^1(\\mathcal{G})$ there is no edge between $X^2_{t-2}$ and $X^3_t$, while in $\\mathcal{M}^2(\\mathcal{G})$ there is $X^2_{t-2} {\\rightarrow} X^3_t$. The reason is clear: Since $\\mathcal{M}^1(\\mathcal{G})$ is based on an observed time window $[t-\\tau_{\\text{max}}, 1] = [t-1, 1]$, it cannot contain edges with a lag larger than $\\tau_{\\text{max}} = 1$ (conforming with the term \"maximum considered time lag\"). Moreover, the smaller observed time window leads to more dependence-inducing path that cannot be blocked: First, the path $X^2_{t-1} {\\leftarrow} X^2_{t-2} {\\rightarrow} X^3_t$ cannot be blocked because this would require to condition on $X^2_{t-2}$. This can, however, not be done because $X^2_{t-2}$ is not within the observed time window $[t-1, t]$. This leads to the edge $X^2_{t-1} {\\leftrightarrow} X^3_t$ in $\\mathcal{M}^1(\\mathcal{G})$. Second and similarly, the path $X^1_{t-1} {\\leftarrow} X^1_{t-2} {\\leftarrow} X^1_{t-3} {\\leftarrow} L^1_{t-3} {\\rightarrow} X^2_{t-2} {\\rightarrow} X^3_t$ cannot be blocked and hence there is the edge $X^1_{t-1} {\\leftrightarrow} X^3_t$ in $\\mathcal{M}^1(\\mathcal{G})$.\n", "\n", "While in this example $\\tau_{\\text{max}} = 1 < p_{\\text{ts}}$, we stress that this condition is not necessary to observe changes in $\\mathcal{M}^{\\tau_{\\text{max}}}(\\mathcal{G})$ with $\\tau_{\\text{max}}$. For more complicated $\\mathcal{G}$ there may be difference between $\\mathcal{M}^{\\tau_{\\text{max}}}(\\mathcal{G})$ and $\\mathcal{M}^{\\tau^\\prime_{\\text{max}}}(\\mathcal{G})$ even though $\\tau_{\\text{max}} \\geq p_{\\text{ts}}$ and $\\tau^\\prime_{\\text{max}} \\geq p_{\\text{ts}}$."]}, {"cell_type": "markdown", "id": "066b6084", "metadata": {}, "source": ["### 1.3 时间序列 DPAGs"]}, {"cell_type": "markdown", "id": "5dd03585", "metadata": {}, "source": ["At this point the task can be phrased as learning time series DMAGs from observations of the observed time series. LPCMCI does this within the constraint-based approach to causal discovery that utilizes (conditional) independencies in the data, which are tested for by statistical means.\n", "\n", "This is, however, an under-determined problem because distinct DMAGs can give rise to the exact same set of (conditional) independencies --- a phenomenon referred to as *Markov equivalence*, which is not specific to DMAGs but rather applies to most probabilistic graphical models. As a consequence, it is without further assumptions in general not possible to uniquely learn the DMAG $\\mathcal{M}(\\mathcal{G})$ that represents the causal ancestral relationships of the underlying data generating process but rather only a set of candidate time series DMAGs $\\mathcal{M}_1, \\ldots, \\mathcal{M}_m$ that includes $\\mathcal{M}(\\mathcal{G})$ and which constitutes the *Markov equivalence class* of $\\mathcal{M}(\\mathcal{G})$. This means that only those features shared by all members of the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$ can be learned. These shared features can in turn be represented by *directed partial ancestral graphs (DPAGs)*, which are a specialization$^a$ of partial ancestral graphs (PAGs) that are used to represent Markov equivalence classes of MAGs [5, 6].\n", "\n", "The basic idea is as follows:\\\n", "All members of the Markov equivalence class agree on adjacencies --- i.e., $X$ and $Y$ are connected by an edge in $\\mathcal{M}(\\mathcal{G})$ if and only if they are connected by an edge in all members its Markov equivalence class --- but the members may differ with regard to the edge types: For example, if $X {\\rightarrow} Y$ is in $\\mathcal{M}(\\mathcal{G})$ then there could be a member in the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$ in which $X {\\leftarrow} Y$ or $X {\\leftrightarrow} Y$ instead. These ambiguities are represented explicitly by two new edge types, namely partially directed edges $X {\\circ\\!{\\rightarrow}} Y$ and non-directed edges $X {\\circ\\!{-}\\!\\circ} Y$. The DPAG $\\mathcal{P}(\\mathcal{G})$ corresponding to $\\mathcal{M}(\\mathcal{G})$ is constructed as follows:\n", "1. **Adjacencies:**\\\n", "There is an edge between vertices $X$ and $Y$ if and only if there is an edge between them in $\\mathcal{M}(\\mathcal{G})$.\n", "2. **Edge types:**\n", "    1. $X {\\rightarrow} Y$ implies that $X {\\rightarrow} Y$ in all members of the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$. In particular, $X {\\rightarrow} Y$ in $\\mathcal{M}(\\mathcal{G})$.\n", "    2. $X {\\leftrightarrow} Y$ implies that $X {\\leftrightarrow} Y$ in all members of the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$. In particular, $X {\\leftrightarrow} Y$ in $\\mathcal{M}(\\mathcal{G})$.\n", "    3. $X {\\circ\\!{\\rightarrow}} Y$ implies that all of the 以下 holds:\n", "        1. In all members of the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$ there is $X {\\rightarrow} Y$ or $X {\\leftrightarrow} Y$. In particular, $X {\\rightarrow} Y$ or $X {\\leftrightarrow} Y$ in $\\mathcal{M}(\\mathcal{G})$.\n", "        2. There is a member $\\mathcal{M}_1$ of the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$ such that $X {\\rightarrow} Y$ in $\\mathcal{M}_1$.\n", "        3. There is a member $\\mathcal{M}_2$ of the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$ such that $X {\\leftrightarrow} Y$ in $\\mathcal{M}_2$.\n", "    4. $X {\\circ\\!{-}\\!\\circ} Y$ implies that all of the 以下 holds:\n", "        1. There is a member $\\mathcal{M}_1$ of the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$ such that $X {\\rightarrow} Y$ in $\\mathcal{M}_1$.\n", "        2. There is a member $\\mathcal{M}_2$ of the Markov equivalence class of $\\mathcal{M}(\\mathcal{G})$ such that $X {\\leftarrow} Y$ in $\\mathcal{M}_2$.\n", "    \n", "Since $\\mathcal{M}(\\mathcal{G})$ carries causal meaning, in the sense that its edges signify causal ancestral relationships, 也 the associated DPAG $\\mathcal{P}(\\mathcal{G})$ carries causal meaning:\n", "1. $X \\rightarrow Y$ says the same as in $\\mathcal{M}(\\mathcal{G})$, i.e.,\n", "    1. $X$ is a (potentially indirect) cause of $Y$\n", "    2. $Y$ does not cause $X$\n", "2. $X \\leftrightarrow Y$ says the same as in $\\mathcal{M}(\\mathcal{G})$, i.e.,\n", "    1. $X$ does not cause $Y$\n", "    2. $Y$ does not cause $X$\n", "    2. $X$ and $Y$ are subject to unobserved confounding, i.e., there is an unobserved 变量 $Z$ that causes both $X$ and $Y$.\n", "3. $X {\\circ\\!{\\rightarrow}} Y$ says that\n", "    1. $X$ may or may not cause $Y$\n", "    2. $Y$ does not cause $X$\n", "4. $X {\\circ\\!{-}\\!\\circ} Y$ says that\n", "    1. $X$ may or may not cause $Y$\n", "    2. $Y$ may or may not cause $X$\n", "\n", "In other words: The DPAG $\\mathcal{P}(\\mathcal{G})$ represents *partial knowledge of the causal ancestral relationships* of the underlying data generating process. As for $\\mathcal{M}(\\mathcal{G})$, the absence and presence of an edge between a pair of variables do not have a straightforward causal interpretation.\n", "\n", "To illustrate the previous discussion, let us return to our running example. The time series DPAG $\\mathcal{P}^{2}(\\mathcal{G})$ associated to $\\mathcal{M}^{2}(\\mathcal{G})$ is shown on the right-hand-side of the following figure:\n", "\n", "<img src=\"figures/ts_DPAG.png\" width=800 height=400 />\n", "\n", "Footnotes:\\\n", "$^a$Specialization to the case of no selection bias."]}, {"cell_type": "markdown", "id": "2a8b50cd", "metadata": {}, "source": ["## 2 LPCMCI: Causal discovery for time series with unobserved confounders"]}, {"cell_type": "markdown", "id": "f5286b5c", "metadata": {}, "source": ["The goal of LPCMCI is to learn 时间序列 DPAGs $\\mathcal{P}^{\\tau_{\\text{max}}}(\\mathcal{G})$, which as explained in section 1 represent partial knowledge of the causal ancestral relationships of the underlying structural causal process. The value of $\\tau_{\\text{max}}$ is an 输入 parameter chosen by the user.\n", "\n", "This section gives an 概述 of how the 算法 works. Those eager to get a quick start with applying LPCMCI may skip subsections 2.1 through 2.4 and move to subsections 2.5 and 2.6 directly."]}, {"cell_type": "markdown", "id": "dd6f7027", "metadata": {}, "source": ["### 2.1 Fundamental basis: The FCI 算法"]}, {"cell_type": "markdown", "id": "16ff5eec", "metadata": {}, "source": ["The fundamental basis of LPCMCI is the FCI 算法 [7, 8, 6]. This 算法 was developed in the non-temporal setting and learns PAGs, i.e. graphs which represent partial knowledge of causal ancestral relationships in the potential presence of both unobserved confounders and selection bias. The possibility of selection bias can, however, easily be excluded by a few simple modifications. The 算法 works in four steps:\n", "1. **First edge removal phase:**\\\n", "Starting from a fully connected graph, where all edges are of the type $X {\\circ\\!{-}\\!\\circ} Y$, the algorithm iterates through all pairs of adjacent variables $(X, Y)$ and tests whether $X$ and $Y$ are (conditionally) independent given some subset of other variables. If this is the case, the edge $X {\\circ\\!{-}\\!\\circ} Y$ is removed. The tested conditioning sets are subsets of the adjacencies of $X$ and subsets of the adjacencies of $Y$.\n", "2. **Premature collider orientation phase:**\\\n", "Based on a certain orientation rule some of the remaining links are turned from $X {\\circ\\!{-}\\!\\circ} Y$  into $X {\\circ\\!{\\rightarrow}} Y$ or $X {{\\leftarrow}\\!\\circ} Y$ or $X {\\leftrightarrow} Y$.\n", "3. **Second edge remove phase:**\\\n", "The 算法 once more iterates through all pairs of adjacent variables $(X, Y)$ and tests whether $X$ and $Y$ are (conditionally) independent given some subset of other variables. This time, however, the tested conditioning sets are subsets not of the adjacencies of $X$ or the adjacencies of $Y$ but rather of larger sets referred to as $\\text{Possible-D-Sep}(X, Y)$ and $\\text{Possible-D-Sep}(Y, X)$. The identification of these sets rests on the edge type updates made in the previous step.\n", "4. **Rule application phase:**\n", "    1. All remaining edges are turned into $X {\\circ\\!{-}\\!\\circ} Y$ (i.e., all edge type updates of step 2. are undone).\n", "    2. The same orientation rule as in step 2. is applied.\n", "    3. A list of other orientation rules, which turn some of the edges into $X {\\circ\\!{\\rightarrow}} Y$ or $X {{\\leftarrow}\\!\\circ} Y$ or $X {\\rightarrow} Y$ or $X {\\leftarrow} Y$ or $X {\\leftrightarrow} Y$, is exhaustively applied.\n", "\n", "For another, slightly more precise and detailed explanation of the FCI 算法 see section S2 in the [supplementary material to the 论文 on LPCMCI](https://papers.nips.cc/论文/2020/file/94e70705efae423efda1088614128d0b-Supplemental.pdf).\n", "\n", "Importantly, the constraint-based approach to 因果发现 relies on a one-to-one correspondence between (conditional) independencies in the 数据 and the graphical notion of *$d$-separation* in the DAG representing the causal parentships of the 数据-generating process. That $d$-separations implies the corresponding (conditional) independencies, a property known as the *causal Markov condition*, is already implied by the 数据 being generated according to a structural causal 模型 --- or, in the 时间序列 setting considered here, according to a structural causal process. The opposite implication, namely that (conditional) independencies imply the corresponding $d$-separations, is referred to as the *causal faithfulness condition* and amounts to an additional assumption. Intuitively, this excludes *accidental* (conditional) independencies due to counteracting mechanisms whose effects cancel out exactly."]}, {"cell_type": "markdown", "id": "f1eec18e", "metadata": {}, "source": ["### 2.2 Basic modifications for the time series setting"]}, {"cell_type": "markdown", "id": "88b1ba4d", "metadata": {}, "source": ["In the 时间序列 setting, which we consider here, several basic modifications may be applied to the FCI 算法. Among these are:\n", "1. All lagged edges $X^i_{t-\\tau} {\\circ\\!{-}\\!\\circ} X^j_t$, where we recall that \"lagged\" means $\\tau > 0$, can be turned into $X^i_{t-\\tau} {\\circ\\!{\\rightarrow}} X^j_t$. This is so because $X^i_{t-\\tau} {\\circ\\!{\\rightarrow}} X^j_t$ says that $X^j_t$ does not cause $X^i_{t-\\tau}$, which simply reflects the absence of causal influences backwards in time.\n", "2. Whenever a lagged edge $X^i_{t-\\tau} {\\circ\\!{\\rightarrow}} X^j_t$ is turned into $X^i_{t-\\tau} {\\rightarrow} X^j_t$ or $X^i_{t-\\tau} {\\leftarrow} X^j_t$ the same edge type update is applied to the edges $X^i_{t-\\tau - \\Delta t} {\\circ\\!{\\rightarrow}} X^j_{t-\\Delta t}$ for all $\\Delta t$. Similarly, whenever a contemporaneous edge $X^i_{t} {\\circ\\!{-}\\!\\circ} X^j_t$ is turned into or $X^i_{t} {{\\leftarrow}\\!\\circ} X^j_t$ or $X^i_{t} {\\leftarrow} X^j_t$ or $X^i_{t} {\\circ\\!{\\rightarrow}} X^j_t$ or $X^i_{t} {\\rightarrow} X^j_t$ or $X^i_{t} {\\leftrightarrow} X^j_t$ the same edge type update is applied to the edges $X^i_{t - \\Delta t} {\\circ\\!{\\rightarrow}} X^j_{t-\\Delta t}$ for all $\\Delta t$. This reflects causal stationarity.\n", "3. Whenever a lagged or contemporaneous edge between $X^i_{t-\\tau}$ and $X^j_t$ is removed then also the edge between $X^i_{t - \\tau - \\Delta t}$ and $X^j_{t-\\Delta t}$ for all $\\Delta t$ are removed. This is allowed by causal stationarity. "]}, {"cell_type": "markdown", "id": "62581840", "metadata": {}, "source": ["### 2.3 Incorporation of the PCMCI idea"]}, {"cell_type": "markdown", "id": "43b65703", "metadata": {}, "source": ["The central feature of LPCMCI its incorporation of the PCMCI idea, which is 也 used in the PCMCI [9] and PCMCIplus [10] algorithms. It rests on the observation that strong autocorrelations tend to reduce the effect sizes of (conditional) independence tests, thereby reducing the statistical power of these tests and thus degrading the 算法's overvall statistical performance. The idea is to alleviate this problem by *conditioning the autocorrelation away* (at least partially). This is achieved by extending the 标准 conditioning sets $\\mathcal{S}$ of (conditional) independence tests with 所谓的 *default conditions* $\\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$, schematically\n", "\n", "$$\n", "\\begin{align}\n", "\\text{测试 whether } X^i_{t-\\tau} \\perp X^j_t ~|~ \\mathcal{S}\n", "\\qquad \\longrightarrow \\qquad\n", "\\text{测试 whether } X^i_{t-\\tau} \\perp X^j_t ~|~ \\mathcal{S} \\cup \\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t) \\ .\n", "\\end{align}\n", "$$\n", "\n", "\n", "In the setting of PCMCI and PCMCIplus, where by assumption there are no unobserved variables, the default conditions $\\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$ would for this purpose ideally be the union of causal parents of $X^i_{t-\\tau}$ and $X^j_t$. Initially, however, the causal parentships are unknown --- after all, the very purpose of these algorithms is to learn the causal parentships. PCMCI and PCMCIplus get around this complication by noting that in the absence of unobserved variables the conditioning sets may in principle be extended with all lagged variables without the danger of introducing spurious dependencies. In other words: The default conditions $\\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$ could in principle be chosen as the set of all variables at $t-1$ or earlier. Such a high-dimensional conditioning set would, however, significantly increase the estimation dimensions of the (conditional) independence tests and thereby again reduce their statistical power. A compromise is drawn by first running a greedy version of the PC algorithm [11] on lagged links and then using the remaining lagged adjacencies as default conditions, see references [9] and [10] for more details.\n", "\n", "In the setting of LPCMCI, where the existence of unobserved variables is allowed, the situation is yet more complicated: Conditioning on lagged variables *may* introduce spurious dependencies, such that the approach of PCMCI and PCMCIplus can NOT simply be copied. However, conditioning sets can still be extended with *causal ancestors* without the danger of introducing spurious dependencies. The PCMCI idea can thus be implemented if definite knowledge of (some) causal ancestorships can be deduced before all (conditional) independence tests have already been made. This is, precisely, what LPCMCI does: It utilizes a novel set of orientation rules, extending those of the FCI algorithm, that allow to learn causal ancestral relationships while the algorithm still tests for further (conditional) independencies. The causal ancestors identified in this way are then used as the default conditions $\\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$ for the remaining tests. Because the novel orientation rules are able to identify the more causal ancestral relationships the more edges have been removed already, the algorithm iterates between performing (conditional) independence tests and applying the orientation rules."]}, {"cell_type": "markdown", "id": "3535f9d1", "metadata": {}, "source": ["### 2.4 The LPCMCI 算法"]}, {"cell_type": "markdown", "id": "c5447d0d", "metadata": {}, "source": ["The basic structure of LPCMCI is as follows:\n", "\n", "---\n", "\n", "1. Initialize a fully connected graph with edges $X^i_{t-\\tau} {\\circ\\!{\\rightarrow}} X^j_t$ for $0 < \\tau \\leq \\tau_{\\text{max}}$ and $X^i_{t} {\\circ\\!{-}\\!\\circ} X^j_t$\n", "2. Repeat $k$ times:\n", "    1. Iterate until convergence between testing (conditional) independencies $X^i_{t-\\tau} \\perp X^j_t ~|~ \\mathcal{S} \\cup \\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$ of adjacent variables and applying the novel orientation rules. The 标准 conditioning set $\\mathcal{S}$ runs through subsets of restricted versions of the adjacencies of $X^i_{t-\\tau}$ and $X^j_t$, and as default conditions $\\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$ the already identified causal ancestors of $X^i_{t-\\tau}$ and $X^j_t$ are used. Whenver $X^i_{t-\\tau}$ and $X^j_t$ are found to be (conditionally) independent, the edge between them is removed.\n", "    2. <PERSON><PERSON> all removed edges while remembering the identified causal ancestorships.\n", "3. Once more 运行 step 2.A.\n", "4. 运行 a modified version of step 2.A., the difference being that $\\mathcal{S}$ here runs through subsets of restricted versions of $\\text{Possible-D-Sep}(X, Y)$ and $\\text{Possible-D-Sep}(Y, X)$.\n", "\n", "---\n", "\n", "To understand this, first assume step 2. is skipped because $k = 0$ is chosen (the value of $k$ is an argument to `LPCMCI.run_lpcmci`, see subsection 2.5 below). The combinations of steps 1. and 3. of LPCMCI then correspond to the combination of steps 1. and 2. of FCI (for FCI see subsection 2.1 above) with modifications according to the discussions in subsections 2.2 and 2.3. Similarly, step 4. of LPCMCI corresponds to the combination of steps 3. and 4. of FCI with according modifications. This is a valid application of LPCMCI and in theory --- i.e., if all assumptions are met in combination with the infinite sample limit in which all statistical decisions about (conditional) independencies are correct --- learns the true time series DPAG $\\mathcal{P}^{\\tau_{\\text{max}}}(\\mathcal{G})$.\n", "\n", "The purpose of step 2. with $k > 0$ is to further improve the *finite-sample* performance of LPCMCI. This has been demonstrated in numerical experiments in the [paper on LPCMCI](https://proceedings.neurips.cc/paper/2020/file/94e70705efae423efda1088614128d0b-Paper.pdf). The reasoning behind this is follows: If $k = 0$ is chosen, then step 3. of LPCMCI begins with having no knowledge about causal ancestral relationships. The first (conditional) independence tests thus have empty default conditions $\\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$, which means they suffer from a low statistical power and are therefore prone to erroneous decisions. In the course of step 3. some causal ancestral relationships will be identified and used as default conditions for subsequent (conditional) independence tests, but that does not repair erroneous decisions of previous tests. And this is precisely why step 2. with $k > 0$ is useful: Once step 2.A. is completed, several causal ancestral relationships have been identified. The algorithm then starts over in so far as that it restores all removed edges but remembers the identified causal ancestral relationships. This results in non-empty default conditions $\\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$ already in the first (conditional) independence tests of step 3., thus increasing their statistical power and making this step more robust against erroneous test decisions. For $k > 1$ the process of restoring the edges is even repeated more than once, such that also step 2.A. (except for the first iteration) starts with non-empty default conditions.\n", "\n", "In the [论文 on LPCMCI](https://proceedings.neurips.cc/论文/2020/file/94e70705efae423efda1088614128d0b-论文.pdf) as well the verbose 输出 of LPCMCI the iterations in step 2. are referred to as *preliminary 阶段*, step 3. as *(final) ancestral phase* and step 4. as *non-ancestral phase*."]}, {"cell_type": "markdown", "id": "270bca3e", "metadata": {}, "source": ["### 2.5 参数 of LPCMCI"]}, {"cell_type": "markdown", "id": "48c4f399", "metadata": {}, "source": ["LPCMCI can be flexibly combined with any kind of (conditional) independence 测试 statistic and therefore adapt to the 数据 type (continuous or discrete) 以及 the assumed dependency forms (e.g. 线性 or non-线性). The (conditional) independence tests are available in `tigramite.independence_tests`.\n", "\n", "The main free 参数 of LPCMCI, 此外 to those of the (conditional) independence 测试, are:\n", "1. The maximum considered time 滞后 $\\tau_{\\max}$ (`tau_max`). This determines the observed time window $[t-\\tau_{\\max}, t]$ and thereby 也 the 时间序列 DPAG $\\mathcal{P}^{\\tau_{\\max}}(\\mathcal{G})$ that is supposed to be learned.\n", "2. The 显著性 threshold $\\alpha_{\\rm PC}$ (`pc_alpha`) of the individual (conditional) independence tests. For higher $\\alpha_{\\rm PC}$ the estimated graph tends to be denser.\n", "3. The number $k$ of preliminary iterations (`n_preliminary_iterations`) in step 2. of the algorithm. By default `n_preliminary_iterations = 1`.\n", "\n", "The non-ancestral phase of LPCMCI (step 4.) can sometimes be very slow because the 标准 conditioning set $\\mathcal{S}$ runs through subsets of large sets, which means that a large number of (conditional) independence tests may be conducted. In this case one can compromise on the provable asymptotic correctness of LPCMCI and restrict the cardinality of $\\mathcal{S}$, and hence the runtime of step 4., by using the `max_p_non_ancestral` argument."]}, {"cell_type": "markdown", "id": "11d2c197", "metadata": {}, "source": ["### 2.6 输出 of LPCMCI"]}, {"cell_type": "markdown", "id": "19017690", "metadata": {}, "source": ["The 函数 `LPCMCI.run_lpcmci` 返回 a dictionary with three entries, which are respectively accessed with the strings `'graph'`, `'p_matrix'`, and `'val_matrix'`. We now in turn discuss the three entries and refer to them by their key.\n", "\n", "**`graph`:**\\\n", "This is the central output of LPCMCI. It is a three-dimensional array of shape `(N_X, N_X, tau_max + 1)`, where `graph[i, j, tau]` is a string that symbolically represents the edge from $X^i_{t-\\tau}$ to $X^j_t$. To be specific:\n", "1. `graph[i, j, tau] = '-->'` says that $X^i_{t-\\tau} {\\rightarrow} X^j_t$.\n", "2. `graph[i, j, tau] = '<->'` says that $X^i_{t-\\tau} {\\leftrightarrow} X^j_t$.\n", "3. `graph[i, j, tau] = 'o->'` says that $X^i_{t-\\tau} {\\circ\\!{\\rightarrow}} X^j_t$.\n", "4. `graph[i, j, tau] = ''` (empty string) says that there is no edge between $X^i_{t-\\tau}$ and $X^j_t$.\n", "5. `graph[i, j, 0] = '-->'` and `graph[j, i, 0] = '<--'` say that $X^i_{t} {\\rightarrow} X^j_t$.\n", "6. `graph[i, j, 0] = '<--'` and `graph[j, i, 0] = '-->'` say that $X^i_{t} {\\leftarrow} X^j_t$.\n", "7. `graph[i, j, 0] = '<->'` and `graph[j, i, 0] = '<->'` say that $X^i_{t} {\\leftrightarrow} X^j_t$.\n", "8. `graph[i, j, 0] = 'o->'` and `graph[j, i, 0] = '<-o'` say that $X^i_{t} {\\circ\\!{\\rightarrow}} X^j_t$.\n", "9. `graph[i, j, 0] = '<-o'` and `graph[j, i, 0] = 'o->'` say that $X^i_{t} {{\\leftarrow}\\!\\circ} X^j_t$.\n", "10. `graph[i, j, 0] = 'o-o'` and `graph[j, i, 0] = 'o-o'` say that $X^i_{t} {\\circ\\!{-}\\!\\circ} X^j_t$.\n", "11. `graph[i, j, 0] = ''` and `graph[j, i, 0] = ''` (empty strings) say that there is no edge between $X^i_{t}$ and $X^j_t$.\n", "\n", "Due to erroneous test decisions about (conditional) independencies it may happen that the edge orientation rules infer conflicting edge type updates. For example, one of the orientation rules may say that the edge $X^i_{t-\\tau} {\\circ\\!{\\rightarrow}} X^j_t$ can be updated to $X^i_{t-\\tau} {\\rightarrow} X^j_t$ while at the same time another orientation rules says it can be updated to $X^i_{t-\\tau} {\\leftrightarrow} X^j_t$. Such conflicts are dealt with by explictly marking them through the introduction of the additional edge types $X^i_{t-\\tau} {x\\!{\\rightarrow}} X^j_t$ and $X^i_{t-\\tau} {x\\!{-}\\!x} X^j_t$ in which the symbol $x$ denotes the conflict. In the example, the edge would be updated to $X^i_{t-\\tau} {x\\!{\\rightarrow}} X^j_t$ because the conflict only concerns whether or not $X^i_{t-\\tau}$ causes $X^j_t$ while there is consensus about $X^j_t$ not causing $X^i_{t-\\tau}$. To be specific:\n", "1. `graph[i, j, tau] = 'x->'` says that both $X^i_{t-\\tau} {\\rightarrow} X^j_t$ and $X^i_{t-\\tau} {\\leftrightarrow} X^j_t$ were proposed.\n", "2. `graph[i, j, 0] = 'x->'` and `graph[j, i, 0] = '<-x'` say that one of these combinations were proposed:\n", "    1. $X^i_{t} {\\rightarrow} X^j_t$ and $X^i_{t} {\\leftrightarrow} X^j_t$\n", "    2. $X^i_{t} {\\rightarrow} X^j_t$ and $X^i_{t} {{\\leftarrow}\\!\\circ} X^j_t$\n", "3. `graph[i, j, 0] = '<-x'` and `graph[j, i, 0] = 'x->'` say that one of these combinations were proposed:\n", "    1. $X^i_{t} {\\leftarrow} X^j_t$ and $X^i_{t} {\\leftrightarrow} X^j_t$\n", "    2. $X^i_{t} {\\leftarrow} X^j_t$ and $X^i_{t} {\\circ\\!{\\rightarrow}} X^j_t$\n", "4. `graph[i, j, 0] = 'x-x'` and `graph[j, i, 0] = 'x-x'` say that both $X^i_{t} {\\rightarrow} X^j_t$ and $X^i_{t} {\\leftarrow} X^j_t$ were proposed.\n", "\n", "The graph can be visualized with the 绘图 functionality of TIGRAMITE, see the application 示例 in section 3 下面的.\n", "\n", "**`p_matrix`:**\\\n", "A three-dimensional array of shape `(N_X, N_X, tau_max + 1)`, where `p_matrix[i, j, tau]` is the maximum across the p-values of all (conditional) independence tests of the pair of variables $X^i_{t-\\tau}$ and $X^j_{t}$. For $\\tau = 0$ the symmetry `p_matrix[i, j, 0] = p_matrix[j, i, 0]` holds.\n", "\n", "The maximum p值 rather than the minimum p值 is being recorded because of the way in which (conditional) independence tests are used in the constraint-based paradigm: A p值 larger than $\\alpha_{\\rm PC}$ means that the null hypothesis of independence is not rejected, in case of which the corresponding edge is removed.\n", "\n", "**`val_matrix`:**\\\n", "A three-dimensional array of shape `(N_X, N_X, tau_max + 1)`, where `val_matrix[i, j, tau]` is the test statistic of that particular (conditional) independence test whose p-value is stored in `p_matrix[i, j, tau]`. Since for the same pair of variables $X^i_{t-\\tau}$ and $X^j_{t}$ conditioning sets of different cardinalities can be tested, thus leading to different null distributions, this value is NOT necessarily equal to the minimum test statistic across all (conditional) independence tests. For $\\tau = 0$ the symmetry `val_matrix[i, j, 0] = val_matrix[j, i, 0]` holds.\n", "\n", "One may pass `val_matrix` to the 绘图 functions `tp.plot_graph` and `tp.plot_time_series_graph` in order to accordingly color the edges, see the application 示例 in section 3 下面的."]}, {"cell_type": "markdown", "id": "91f2eef9", "metadata": {}, "source": ["## 3 Application 示例"]}, {"cell_type": "markdown", "id": "3903e5dc", "metadata": {}, "source": ["This section demonstrates and explains the application of LPCMCI on synthetic 数据. Subsection 3.7 may be skipped on a first read."]}, {"cell_type": "markdown", "id": "abba662f", "metadata": {}, "source": ["### 3.1 数据 generation"]}, {"cell_type": "markdown", "id": "78cd49a7", "metadata": {}, "source": ["We return to the structural causal process that in section 1 has served as running example, where also as above we treat the second component time series as unobserved. Making this explicit by using the symbols $X^i$ for observed time series and $L^i$ for unobserved time series the process reads:\n", "\n", "\\begin{align}\n", "X^1_t &= 0.9 X^1_{t-1} + 0.6 L^2_{t} + \\eta^1_t\\\\\n", "L^2_t &= \\eta^2_t \\\\\n", "X^3_t &= 0.9 X^3_{t-1} + 0.4 L^2_{t-1} + \\eta^3_t\\\\\n", "X^4_t &= 0.9 X^4_{t-1} - 0.4 X^3_{t-2} + \\eta^4_T\n", "\\end{align}\n", "\n", "The 函数 `toys.structural_causal_process` allows to 生成 a realization of this proces as follows:"]}, {"cell_type": "code", "execution_count": 2, "id": "5b5f18e8", "metadata": {}, "outputs": [], "source": ["# Set a seed for reproducibility\n", "seed = 19\n", "\n", "# Choose the 时间序列 length\n", "T = 500\n", "\n", "# Specify the 模型 (注意 that here, unlike in the typed equations, variables\n", "# are indexed starting from 0)\n", "def lin(x): return x\n", "\n", "links = {0: [((0, -1), 0.9, lin), ((1, 0), 0.6, lin)],\n", "         1: [],\n", "         2: [((2, -1), 0.9, lin), ((1, -1), 0.4, lin)],\n", "         3: [((3, -1), 0.9, lin), ((2, -2), -0.5, lin)]                                    \n", "        }\n", "\n", "# Specify dynamical noise term distributions, here unit variance Gaussians\n", "random_state = np.random.RandomState(seed)\n", "noises = noises = [random_state.randn for j in links.keys()]\n", "\n", "# 生成 数据 according to the full structural causal process\n", "data_full, nonstationarity_indicator = toys.structural_causal_process(\n", "    links=links, T=T, noises=noises, seed=seed)\n", "assert not nonstationarity_indicator\n", "\n", "# Remove the unobserved component 时间序列\n", "data_obs = data_full[:, [0, 2, 3]]\n", "\n", "# Number of observed variables\n", "N = data_obs.shape[1]\n", "\n", "# Initialize dataframe object, specify 变量 names\n", "var_names = [r'$X^{%d}$' % j for j in range(N)]\n", "dataframe = pp.DataFrame(data_obs, var_names=var_names)"]}, {"cell_type": "markdown", "id": "0d664d58", "metadata": {}, "source": ["### 3.2 绘图 the 时间序列"]}, {"cell_type": "markdown", "id": "5fcc1c05", "metadata": {}, "source": ["The 时间序列 can be plotted with the 函数 `tp.plot_timeseries`:"]}, {"cell_type": "code", "execution_count": 3, "id": "ce3fec24", "metadata": {}, "outputs": [{"data": {"image/png": "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*****************************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tp.plot_timeseries(dataframe, figsize=(15, 5));\n", "plt.show()"]}, {"cell_type": "markdown", "id": "ec37148c", "metadata": {}, "source": ["### 3.3 Exploration: Bivariate lagged 条件独立性"]}, {"cell_type": "markdown", "id": "98ad8267", "metadata": {}, "source": ["In order to obtain an idea which `tau_max` to choose one may 运行 the `run_bivci` 函数, which implements a bivariate lagged 条件独立性 测试 (similar to bivariate Granger causality, but 滞后-specific). This 函数 is implemented by the `PCMCI` 类."]}, {"cell_type": "code", "execution_count": 4, "id": "e154b4a1", "metadata": {}, "outputs": [], "source": ["# 创建 a (conditional) independence 测试 object\n", "# Here, the partial 相关性 测试 is used\n", "parcorr = ParCorr(significance='analytic')\n", "\n", "# 创建 a PCMCI object, passing the the dataframe and (conditional)\n", "# independence 测试 object.\n", "pcmci = PCMCI(dataframe=dataframe, \n", "              cond_ind_test=parcorr,\n", "              verbosity=1)"]}, {"cell_type": "code", "execution_count": 5, "id": "f7e6a101", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "##\n", "## Running Tigramite BivCI algorithm\n", "##\n", "\n", "Parameters:\n", "\n", "independence test = par_corr\n", "tau_min = 0\n", "tau_max = 20\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 运行 the `PCMCI.run_bivci` 函数\n", "correlations = pcmci.run_bivci(tau_max=20, val_only=True)['val_matrix']\n", "\n", "# 绘制 the 结果\n", "setup_args = {'var_names':var_names,\n", "              'figsize':(10, 6),\n", "              'x_base':5,\n", "              'y_base':.5}\n", "lag_func_matrix = tp.plot_lagfuncs(val_matrix=correlations, \n", "                                   setup_args=setup_args)"]}, {"cell_type": "markdown", "id": "5113f33b", "metadata": {}, "source": ["### 3.4 Application of LPCMCI"]}, {"cell_type": "markdown", "id": "38e0421f", "metadata": {}, "source": ["Based on the results of `run_bivci`, we choose to apply LPCMCI with `tau_max = 5` since after that lag, the lag functions decay to zero. Note that this is only a heuristic. In addition, you may also check scatter plots as done in the PCMCI tutorial in order to choose an appropriate conditional independence test. We further choose `pc_alpha = 0.01` and apart from that do not modify the default hyperparameter settings."]}, {"cell_type": "code", "execution_count": 6, "id": "4f9c8c46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=======================================================\n", "=======================================================\n", "Starting preliminary phase  1\n", "\n", "Starting test phase\n", "\n", "p = 0\n", "\n", "Test phase complete\n", "p = 1\n", "(0,-5) independent (0, 0) given ((0, -1),) union set()\n", "(0,-4) independent (0, 0) given ((0, -1),) union set()\n", "(0,-3) independent (0, 0) given ((0, -1),) union set()\n", "(0,-2) independent (0, 0) given ((0, -1),) union set()\n", "(1,-5) independent (1, 0) given ((1, -1),) union set()\n", "(1,-4) independent (1, 0) given ((1, -1),) union set()\n", "(1,-3) independent (1, 0) given ((1, -1),) union set()\n", "(1,-2) independent (1, 0) given ((1, -1),) union set()\n", "Writing:   (0,-5) oL> (0, 0) ==> (0,-5)     (0, 0) \n", "Writing:   (0,-4) oL> (0, 0) ==> (0,-4)     (0, 0) \n", "Writing:   (0,-3) oL> (0, 0) ==> (0,-3)     (0, 0) \n", "Writing:   (0,-2) oL> (0, 0) ==> (0,-2)     (0, 0) \n", "Writing:   (1,-5) oL> (1, 0) ==> (1,-5)     (1, 0) \n", "Writing:   (1,-4) oL> (1, 0) ==> (1,-4)     (1, 0) \n", "Writing:   (1,-3) oL> (1, 0) ==> (1,-3)     (1, 0) \n", "Writing:   (1,-2) oL> (1, 0) ==> (1,-2)     (1, 0) \n", "(0, 0) independent (1, 0) given ((0, -1),) union set()\n", "(0, 0) independent (1, 0) given ((0, -1),) union set()\n", "(0, 0) independent (2, 0) given ((2, -1),) union set()\n", "(0, 0) independent (2, 0) given ((0, -1),) union set()\n", "Writing:   (0, 0) o?o (1, 0) ==> (0, 0)     (1, 0) \n", "Writing:   (1, 0) o?o (0, 0) ==> (1, 0)     (0, 0) \n", "Writing:   (0, 0) o?o (2, 0) ==> (0, 0)     (2, 0) \n", "Writing:   (2, 0) o?o (0, 0) ==> (2, 0)     (0, 0) \n", "(0,-1) independent (1, 0) given ((0, -2),) union set()\n", "(0,-1) independent (2, 0) given ((2, -1),) union set()\n", "(1,-1) independent (0, 0) given ((0, -1),) union set()\n", "(2,-1) independent (0, 0) given ((0, -1),) union set()\n", "Writing:   (1,-1) oL> (0, 0) ==> (1,-1)     (0, 0) \n", "Writing:   (2,-1) oL> (0, 0) ==> (2,-1)     (0, 0) \n", "Writing:   (0,-1) oL> (1, 0) ==> (0,-1)     (1, 0) \n", "Writing:   (0,-1) oL> (2, 0) ==> (0,-1)     (2, 0) \n", "(0,-2) independent (1, 0) given ((1, -1),) union set()\n", "(0,-2) independent (2, 0) given ((2, -1),) union set()\n", "(1,-2) independent (0, 0) given ((0, -1),) union set()\n", "(2,-2) independent (0, 0) given ((0, -1),) union set()\n", "Writing:   (1,-2) oL> (0, 0) ==> (1,-2)     (0, 0) \n", "Writing:   (2,-2) oL> (0, 0) ==> (2,-2)     (0, 0) \n", "Writing:   (0,-2) oL> (1, 0) ==> (0,-2)     (1, 0) \n", "Writing:   (0,-2) oL> (2, 0) ==> (0,-2)     (2, 0) \n", "(0,-3) independent (1, 0) given ((1, -1),) union set()\n", "(0,-3) independent (2, 0) given ((1, -5),) union set()\n", "(1,-3) independent (0, 0) given ((0, -1),) union set()\n", "(2,-3) independent (0, 0) given ((0, -1),) union set()\n", "(2,-3) independent (1, 0) given ((1, -1),) union set()\n", "Writing:   (1,-3) oL> (0, 0) ==> (1,-3)     (0, 0) \n", "Writing:   (2,-3) oL> (0, 0) ==> (2,-3)     (0, 0) \n", "Writing:   (0,-3) oL> (1, 0) ==> (0,-3)     (1, 0) \n", "Writing:   (2,-3) oL> (1, 0) ==> (2,-3)     (1, 0) \n", "Writing:   (0,-3) oL> (2, 0) ==> (0,-3)     (2, 0) \n", "(0,-4) independent (1, 0) given ((1, -1),) union set()\n", "(0,-4) independent (2, 0) given ((2, -1),) union set()\n", "(1,-4) independent (0, 0) given ((0, -1),) union set()\n", "(2,-4) independent (0, 0) given ((0, -1),) union set()\n", "(2,-4) independent (1, 0) given ((1, -1),) union set()\n", "Writing:   (1,-4) oL> (0, 0) ==> (1,-4)     (0, 0) \n", "Writing:   (2,-4) oL> (0, 0) ==> (2,-4)     (0, 0) \n", "Writing:   (0,-4) oL> (1, 0) ==> (0,-4)     (1, 0) \n", "Writing:   (2,-4) oL> (1, 0) ==> (2,-4)     (1, 0) \n", "Writing:   (0,-4) oL> (2, 0) ==> (0,-4)     (2, 0) \n", "(0,-5) independent (1, 0) given ((1, -1),) union set()\n", "(0,-5) independent (2, 0) given ((2, -1),) union set()\n", "(1,-5) independent (0, 0) given ((0, -1),) union set()\n", "(2,-5) independent (0, 0) given ((0, -1),) union set()\n", "(2,-5) independent (1, 0) given ((1, -1),) union set()\n", "Writing:   (1,-5) oL> (0, 0) ==> (1,-5)     (0, 0) \n", "Writing:   (2,-5) oL> (0, 0) ==> (2,-5)     (0, 0) \n", "Writing:   (0,-5) oL> (1, 0) ==> (0,-5)     (1, 0) \n", "Writing:   (2,-5) oL> (1, 0) ==> (2,-5)     (1, 0) \n", "Writing:   (0,-5) oL> (2, 0) ==> (0,-5)     (2, 0) \n", "\n", "Test phase complete\n", "\n", "Starting orientation phase\n", "with rule list:  [['APR'], ['ER-08'], ['ER-02'], ['ER-01'], ['ER-09'], ['ER-10']]\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Marked:    (0,-1) oL> (0, 0) ==> (0,-1) -L> (0, 0) \n", "Marked:    (1,-1) oL> (1, 0) ==> (1,-1) -L> (1, 0) \n", "Writing:   (0,-1) oL> (0, 0) ==> (0,-1) -L> (0, 0) \n", "Update:    Mark<PERSON> (0, -1) as anc of (0, 0)\n", "Writing:   (1,-1) oL> (1, 0) ==> (1,-1) -L> (1, 0) \n", "Update:    Mark<PERSON> (1, -1) as anc of (1, 0)\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "Orientation phase complete\n", "\n", "Middle mark updates\n", "\n", "\n", "Starting test phase\n", "\n", "p = 0\n", "\n", "Test phase complete\n", "p = 1\n", "Writing:   (0,-1) -L> (0, 0) ==> (0,-1) -!> (0, 0) \n", "(1, 0) independent (2, 0) given ((2, -1),) union {(1, -1)}\n", "(1, 0) independent (2, 0) given ((2, -2),) union {(1, -1)}\n", "Writing:   (1, 0) o?o (2, 0) ==> (1, 0)     (2, 0) \n", "Writing:   (2, 0) o?o (1, 0) ==> (2, 0)     (1, 0) \n", "(1,-1) independent (2, 0) given ((2, -1),) union {(1, -2)}\n", "(2,-1) independent (1, 0) given ((2, -2),) union {(1, -1)}\n", "Writing:   (2,-1) oL> (1, 0) ==> (2,-1)     (1, 0) \n", "Writing:   (1,-1) oL> (2, 0) ==> (1,-1)     (2, 0) \n", "Writing:   (2,-2) oL> (1, 0) ==> (2,-2) o!> (1, 0) \n", "(1,-3) independent (2, 0) given ((1, -2),) union {(1, -4)}\n", "Writing:   (1,-3) oL> (2, 0) ==> (1,-3)     (2, 0) \n", "\n", "Test phase complete\n", "\n", "Starting orientation phase\n", "with rule list:  [['APR'], ['ER-08'], ['ER-02'], ['ER-01'], ['ER-09'], ['ER-10']]\n", "\n", "APR:\n", "Marked:    (0,-1) -!> (0, 0) ==> (0,-1) --> (0, 0) \n", "Writing:   (0,-1) -!> (0, 0) ==> (0,-1) --> (0, 0) \n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Marked:    (1,-2) oL> (2, 0) ==> (1,-2) -L> (2, 0) \n", "Writing:   (1,-2) oL> (2, 0) ==> (1,-2) -L> (2, 0) \n", "Update:    Mark<PERSON> (1, -2) as anc of (2, 0)\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-09:\n", "Marked:    (1,-4) oL> (2, 0) ==> (1,-4) -L> (2, 0) \n", "Writing:   (1,-4) oL> (2, 0) ==> (1,-4) -L> (2, 0) \n", "Update:    Mark<PERSON> (1, -4) as anc of (2, 0)\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Marked:    (1,-5) oL> (2, 0) ==> (1,-5) -L> (2, 0) \n", "Writing:   (1,-5) oL> (2, 0) ==> (1,-5) -L> (2, 0) \n", "Update:    Mark<PERSON> (1, -5) as anc of (2, 0)\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "Orientation phase complete\n", "\n", "Middle mark updates\n", "\n", "\n", "Starting test phase\n", "\n", "p = 0\n", "\n", "Test phase complete\n", "p = 1\n", "(2,-5) independent (2, 0) given ((2, -1),) union {(1, -5), (1, -4), (1, -2)}\n", "(2,-4) independent (2, 0) given ((2, -1),) union {(1, -5), (1, -4), (1, -2)}\n", "(2,-3) independent (2, 0) given ((2, -1),) union {(1, -5), (1, -4), (1, -2)}\n", "(2,-2) independent (2, 0) given ((2, -1),) union {(1, -5), (1, -4), (1, -2)}\n", "Writing:   (2,-5) oL> (2, 0) ==> (2,-5)     (2, 0) \n", "Writing:   (2,-4) oL> (2, 0) ==> (2,-4)     (2, 0) \n", "Writing:   (2,-3) oL> (2, 0) ==> (2,-3)     (2, 0) \n", "Writing:   (2,-2) oL> (2, 0) ==> (2,-2)     (2, 0) \n", "(1,-4) independent (2, 0) given ((2, -1),) union {(1, -5), (1, -2)}\n", "Writing:   (1,-4) -L> (2, 0) ==> (1,-4)     (2, 0) \n", "(1,-5) independent (2, 0) given ((2, -1),) union {(1, -2)}\n", "Writing:   (1,-5) -L> (2, 0) ==> (1,-5)     (2, 0) \n", "\n", "Test phase complete\n", "\n", "Starting orientation phase\n", "with rule list:  [['APR'], ['ER-08'], ['ER-02'], ['ER-01'], ['ER-09'], ['ER-10']]\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Marked:    (2,-1) oL> (2, 0) ==> (2,-1) -L> (2, 0) \n", "Writing:   (2,-1) oL> (2, 0) ==> (2,-1) -L> (2, 0) \n", "Update:    Mark<PERSON> (2, -1) as anc of (2, 0)\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "Orientation phase complete\n", "\n", "Middle mark updates\n", "\n", "\n", "Starting test phase\n", "\n", "p = 0\n", "\n", "Test phase complete\n", "p = 1\n", "Writing:   (2,-1) -L> (2, 0) ==> (2,-1) -!> (2, 0) \n", "Writing:   (1,-2) -L> (2, 0) ==> (1,-2) -!> (2, 0) \n", "\n", "Test phase complete\n", "p = 2\n", "Writing:   (1,-1) -L> (1, 0) ==> (1,-1) -!> (1, 0) \n", "\n", "Test phase complete\n", "\n", "Starting orientation phase\n", "with rule list:  [['APR'], ['ER-08'], ['ER-02'], ['ER-01'], ['ER-00-d'], ['ER-00-c'], ['ER-03'], ['R-04'], ['ER-09'], ['ER-10'], ['ER-00-b'], ['ER-00-a']]\n", "\n", "APR:\n", "Marked:    (1,-1) -!> (1, 0) ==> (1,-1) --> (1, 0) \n", "Marked:    (1,-2) -!> (2, 0) ==> (1,-2) --> (2, 0) \n", "Marked:    (2,-1) -!> (2, 0) ==> (2,-1) --> (2, 0) \n", "Writing:   (1,-1) -!> (1, 0) ==> (1,-1) --> (1, 0) \n", "Writing:   (1,-2) -!> (2, 0) ==> (1,-2) --> (2, 0) \n", "Writing:   (2,-1) -!> (2, 0) ==> (2,-1) --> (2, 0) \n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-00-d:\n", "Found nothing\n", "\n", "ER-00-c:\n", "Marked:    (2,-2) o!> (1, 0) ==> (2,-2) <!> (1, 0) \n", "Writing:   (2,-2) o!> (1, 0) ==> (2,-2) <!> (1, 0) \n", "Update:    Marking (2, -2) as non-anc of (1, 0)\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-00-d:\n", "Found nothing\n", "\n", "ER-00-c:\n", "Found nothing\n", "\n", "ER-03:\n", "Found nothing\n", "\n", "R-04:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "ER-00-b:\n", "Found nothing\n", "\n", "ER-00-a:\n", "Marked:    (2,-2) <!> (1, 0) ==> (2,-2)     (1, 0) \n", "Writing:   (2,-2) <!> (1, 0) ==> (2,-2)     (1, 0) \n", "\n", "Links were removed by rules\n", "\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-00-d:\n", "Found nothing\n", "\n", "ER-00-c:\n", "Found nothing\n", "\n", "ER-03:\n", "Found nothing\n", "\n", "R-04:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "ER-00-b:\n", "Found nothing\n", "\n", "ER-00-a:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Found nothing\n", "\n", "Orientation phase complete\n", "\n", "Middle mark updates\n", "\n", "\n", "Preliminary phase  1 complete\n", "\n", "Graph:\n", "--------------------------------\n", "(0,-1) --> (0, 0)\n", "(1,-1) --> (1, 0)\n", "(1,-2) --> (2, 0)\n", "(2,-1) --> (2, 0)\n", "--------------------------------\n", "Writing:   (0,-1) oL> (0, 0) ==> (0,-1) -L> (0, 0) \n", "Update:    Mark<PERSON> (0, -1) as anc of (0, 0)\n", "Writing:   (1,-1) oL> (1, 0) ==> (1,-1) -L> (1, 0) \n", "Update:    Mark<PERSON> (1, -1) as anc of (1, 0)\n", "Writing:   (2,-1) oL> (2, 0) ==> (2,-1) -L> (2, 0) \n", "Update:    Mark<PERSON> (2, -1) as anc of (2, 0)\n", "Writing:   (1,-2) oL> (2, 0) ==> (1,-2) -L> (2, 0) \n", "Update:    Mark<PERSON> (1, -2) as anc of (2, 0)\n", "\n", "=======================================================\n", "=======================================================\n", "Starting final ancestral phase\n", "\n", "Starting test phase\n", "\n", "p = 0\n", "(0,-5) independent (0, 0) given () union {(0, -1)}\n", "(0,-4) independent (0, 0) given () union {(0, -5), (0, -1)}\n", "(0,-3) independent (0, 0) given () union {(0, -4), (0, -1)}\n", "(0,-2) independent (0, 0) given () union {(0, -3), (0, -1)}\n", "(1,-5) independent (1, 0) given () union {(1, -1)}\n", "(1,-4) independent (1, 0) given () union {(1, -5), (1, -1)}\n", "(1,-3) independent (1, 0) given () union {(1, -4), (1, -1)}\n", "(1,-2) independent (1, 0) given () union {(1, -3), (1, -1)}\n", "(2,-5) independent (2, 0) given () union {(2, -1), (1, -2)}\n", "(2,-4) independent (2, 0) given () union {(2, -1), (1, -2), (2, -5)}\n", "(2,-3) independent (2, 0) given () union {(1, -5), (2, -1), (1, -2), (2, -4)}\n", "(2,-2) independent (2, 0) given () union {(2, -3), (2, -1), (1, -4), (1, -2)}\n", "Writing:   (0,-5) oL> (0, 0) ==> (0,-5)     (0, 0) \n", "Writing:   (0,-4) oL> (0, 0) ==> (0,-4)     (0, 0) \n", "Writing:   (0,-3) oL> (0, 0) ==> (0,-3)     (0, 0) \n", "Writing:   (0,-2) oL> (0, 0) ==> (0,-2)     (0, 0) \n", "Writing:   (1,-5) oL> (1, 0) ==> (1,-5)     (1, 0) \n", "Writing:   (1,-4) oL> (1, 0) ==> (1,-4)     (1, 0) \n", "Writing:   (1,-3) oL> (1, 0) ==> (1,-3)     (1, 0) \n", "Writing:   (1,-2) oL> (1, 0) ==> (1,-2)     (1, 0) \n", "Writing:   (2,-5) oL> (2, 0) ==> (2,-5)     (2, 0) \n", "Writing:   (2,-4) oL> (2, 0) ==> (2,-4)     (2, 0) \n", "Writing:   (2,-3) oL> (2, 0) ==> (2,-3)     (2, 0) \n", "Writing:   (2,-2) oL> (2, 0) ==> (2,-2)     (2, 0) \n", "(0, 0) independent (1, 0) given () union {(0, -1), (1, -1)}\n", "(0, 0) independent (1, 0) given () union {(0, -1), (1, -1)}\n", "(0, 0) independent (2, 0) given () union {(2, -1), (0, -1), (1, -2)}\n", "(0, 0) independent (2, 0) given () union {(2, -1), (0, -1), (1, -2)}\n", "(1, 0) independent (2, 0) given () union {(2, -1), (1, -2), (1, -1)}\n", "(1, 0) independent (2, 0) given () union {(2, -1), (1, -1), (1, -2)}\n", "Writing:   (0, 0) o?o (1, 0) ==> (0, 0)     (1, 0) \n", "Writing:   (1, 0) o?o (0, 0) ==> (1, 0)     (0, 0) \n", "Writing:   (0, 0) o?o (2, 0) ==> (0, 0)     (2, 0) \n", "Writing:   (2, 0) o?o (0, 0) ==> (2, 0)     (0, 0) \n", "Writing:   (1, 0) o?o (2, 0) ==> (1, 0)     (2, 0) \n", "Writing:   (2, 0) o?o (1, 0) ==> (2, 0)     (1, 0) \n", "(0,-1) independent (2, 0) given () union {(2, -1), (0, -2), (1, -2)}\n", "(1,-1) independent (0, 0) given () union {(0, -1), (1, -2)}\n", "(1,-1) independent (2, 0) given () union {(2, -1), (1, -2)}\n", "(2,-1) independent (0, 0) given () union {(2, -2), (1, -3), (0, -1)}\n", "(2,-1) independent (1, 0) given () union {(2, -2), (1, -3), (1, -1)}\n", "Writing:   (1,-1) oL> (0, 0) ==> (1,-1)     (0, 0) \n", "Writing:   (2,-1) oL> (0, 0) ==> (2,-1)     (0, 0) \n", "Writing:   (2,-1) oL> (1, 0) ==> (2,-1)     (1, 0) \n", "Writing:   (0,-1) oL> (2, 0) ==> (0,-1)     (2, 0) \n", "Writing:   (1,-1) oL> (2, 0) ==> (1,-1)     (2, 0) \n", "(0,-2) independent (1, 0) given () union {(0, -3), (1, -1)}\n", "(0,-2) independent (2, 0) given () union {(2, -1), (0, -3), (1, -2)}\n", "(1,-2) independent (0, 0) given () union {(1, -3), (0, -1)}\n", "(2,-2) independent (0, 0) given () union {(2, -3), (1, -4), (0, -1)}\n", "(2,-2) independent (1, 0) given () union {(2, -3), (1, -4), (1, -1)}\n", "Writing:   (1,-2) oL> (0, 0) ==> (1,-2)     (0, 0) \n", "Writing:   (2,-2) oL> (0, 0) ==> (2,-2)     (0, 0) \n", "Writing:   (0,-2) oL> (1, 0) ==> (0,-2)     (1, 0) \n", "Writing:   (2,-2) oL> (1, 0) ==> (2,-2)     (1, 0) \n", "Writing:   (0,-2) oL> (2, 0) ==> (0,-2)     (2, 0) \n", "(0,-3) independent (1, 0) given () union {(0, -4), (1, -1)}\n", "(0,-3) independent (2, 0) given () union {(2, -1), (1, -2), (0, -4)}\n", "(1,-3) independent (0, 0) given () union {(1, -4), (0, -1)}\n", "(1,-3) independent (2, 0) given () union {(2, -1), (1, -4), (1, -2)}\n", "(2,-3) independent (0, 0) given () union {(1, -5), (0, -1), (2, -4)}\n", "(2,-3) independent (1, 0) given () union {(1, -5), (1, -1), (2, -4)}\n", "Writing:   (1,-3) oL> (0, 0) ==> (1,-3)     (0, 0) \n", "Writing:   (2,-3) oL> (0, 0) ==> (2,-3)     (0, 0) \n", "Writing:   (0,-3) oL> (1, 0) ==> (0,-3)     (1, 0) \n", "Writing:   (2,-3) oL> (1, 0) ==> (2,-3)     (1, 0) \n", "Writing:   (0,-3) oL> (2, 0) ==> (0,-3)     (2, 0) \n", "Writing:   (1,-3) oL> (2, 0) ==> (1,-3)     (2, 0) \n", "(0,-4) independent (1, 0) given () union {(0, -5), (1, -1)}\n", "(0,-4) independent (2, 0) given () union {(0, -5), (2, -1), (1, -2)}\n", "(1,-4) independent (0, 0) given () union {(1, -5), (0, -1)}\n", "(1,-4) independent (2, 0) given () union {(1, -5), (2, -1), (1, -2)}\n", "(2,-4) independent (0, 0) given () union {(0, -1), (2, -5)}\n", "(2,-4) independent (1, 0) given () union {(1, -1), (2, -5)}\n", "Writing:   (1,-4) oL> (0, 0) ==> (1,-4)     (0, 0) \n", "Writing:   (2,-4) oL> (0, 0) ==> (2,-4)     (0, 0) \n", "Writing:   (0,-4) oL> (1, 0) ==> (0,-4)     (1, 0) \n", "Writing:   (2,-4) oL> (1, 0) ==> (2,-4)     (1, 0) \n", "Writing:   (0,-4) oL> (2, 0) ==> (0,-4)     (2, 0) \n", "Writing:   (1,-4) oL> (2, 0) ==> (1,-4)     (2, 0) \n", "(0,-5) independent (1, 0) given () union {(1, -1)}\n", "(0,-5) independent (2, 0) given () union {(2, -1), (1, -2)}\n", "(1,-5) independent (0, 0) given () union {(0, -1)}\n", "(1,-5) independent (2, 0) given () union {(2, -1), (1, -2)}\n", "(2,-5) independent (0, 0) given () union {(0, -1)}\n", "(2,-5) independent (1, 0) given () union {(1, -1)}\n", "Writing:   (1,-5) oL> (0, 0) ==> (1,-5)     (0, 0) \n", "Writing:   (2,-5) oL> (0, 0) ==> (2,-5)     (0, 0) \n", "Writing:   (0,-5) oL> (1, 0) ==> (0,-5)     (1, 0) \n", "Writing:   (2,-5) oL> (1, 0) ==> (2,-5)     (1, 0) \n", "Writing:   (0,-5) oL> (2, 0) ==> (0,-5)     (2, 0) \n", "Writing:   (1,-5) oL> (2, 0) ==> (1,-5)     (2, 0) \n", "\n", "Test phase complete\n", "\n", "Starting orientation phase\n", "with rule list:  [['APR'], ['ER-08'], ['ER-02'], ['ER-01'], ['ER-09'], ['ER-10']]\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "Orientation phase complete\n", "p = 1\n", "Writing:   (0,-1) -L> (0, 0) ==> (0,-1) -!> (0, 0) \n", "Writing:   (2,-1) -L> (2, 0) ==> (2,-1) -!> (2, 0) \n", "Writing:   (0,-1) oL> (1, 0) ==> (0,-1) o!> (1, 0) \n", "Writing:   (1,-2) -L> (2, 0) ==> (1,-2) -!> (2, 0) \n", "\n", "Test phase complete\n", "p = 2\n", "Writing:   (1,-1) -L> (1, 0) ==> (1,-1) -!> (1, 0) \n", "\n", "Test phase complete\n", "\n", "Starting orientation phase\n", "with rule list:  [['APR'], ['ER-08'], ['ER-02'], ['ER-01'], ['ER-00-d'], ['ER-00-c'], ['ER-03'], ['R-04'], ['ER-09'], ['ER-10'], ['ER-00-b'], ['ER-00-a']]\n", "\n", "APR:\n", "Marked:    (1,-1) -!> (1, 0) ==> (1,-1) --> (1, 0) \n", "Marked:    (1,-2) -!> (2, 0) ==> (1,-2) --> (2, 0) \n", "Marked:    (2,-1) -!> (2, 0) ==> (2,-1) --> (2, 0) \n", "Marked:    (0,-1) -!> (0, 0) ==> (0,-1) --> (0, 0) \n", "Writing:   (0,-1) -!> (0, 0) ==> (0,-1) --> (0, 0) \n", "Writing:   (1,-1) -!> (1, 0) ==> (1,-1) --> (1, 0) \n", "Writing:   (1,-2) -!> (2, 0) ==> (1,-2) --> (2, 0) \n", "Writing:   (2,-1) -!> (2, 0) ==> (2,-1) --> (2, 0) \n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-00-d:\n", "Found nothing\n", "\n", "ER-00-c:\n", "Marked:    (0,-1) o!> (1, 0) ==> (0,-1) <!> (1, 0) \n", "Writing:   (0,-1) o!> (1, 0) ==> (0,-1) <!> (1, 0) \n", "Update:    Marking (0, -1) as non-anc of (1, 0)\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-00-d:\n", "Found nothing\n", "\n", "ER-00-c:\n", "Found nothing\n", "\n", "ER-03:\n", "Found nothing\n", "\n", "R-04:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "ER-00-b:\n", "Found nothing\n", "\n", "ER-00-a:\n", "Found nothing\n", "\n", "Orientation phase complete\n", "\n", "Middle mark updates\n", "\n", "\n", "Final ancestral phase complete\n", "\n", "Graph:\n", "--------------------------------\n", "(0,-1) --> (0, 0)\n", "(0,-1) <!> (1, 0)\n", "(1,-1) --> (1, 0)\n", "(1,-2) --> (2, 0)\n", "(2,-1) --> (2, 0)\n", "--------------------------------\n", "\n", "=======================================================\n", "=======================================================\n", "Starting non-ancestral phase\n", "\n", "Middle mark updates\n", "\n", "\n", "Starting test phase\n", "\n", "p = 0\n", "\n", "Test phase complete\n", "p = 1\n", "Writing:   (0,-1) <!> (1, 0) ==> (0,-1) <-> (1, 0) \n", "\n", "Test phase complete\n", "\n", "Starting orientation phase\n", "with rule list:  [['APR'], ['ER-08'], ['ER-02'], ['ER-01'], ['ER-00-d'], ['ER-00-c'], ['ER-03'], ['R-04'], ['ER-09'], ['ER-10'], ['ER-00-b'], ['ER-00-a']]\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-00-d:\n", "Found nothing\n", "\n", "ER-00-c:\n", "Found nothing\n", "\n", "ER-03:\n", "Found nothing\n", "\n", "R-04:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "ER-00-b:\n", "Found nothing\n", "\n", "ER-00-a:\n", "Found nothing\n", "\n", "Orientation phase complete\n", "\n", "Non-ancestral phase complete\n", "\n", "Graph:\n", "--------------------------------\n", "(0,-1) --> (0, 0)\n", "(0,-1) <-> (1, 0)\n", "(1,-1) --> (1, 0)\n", "(1,-2) --> (2, 0)\n", "(2,-1) --> (2, 0)\n", "--------------------------------\n", "\n", "=======================================================\n", "=======================================================\n", "Final rule application phase\n", "\n", "Setting all middle marks to '-'\n", "\n", "Starting orientation phase\n", "with rule list:  [['APR'], ['ER-08'], ['ER-02'], ['ER-01'], ['ER-00-d'], ['ER-00-c'], ['ER-03'], ['R-04'], ['ER-09'], ['ER-10'], ['ER-00-b'], ['ER-00-a']]\n", "\n", "APR:\n", "Found nothing\n", "\n", "ER-08:\n", "Found nothing\n", "\n", "ER-02:\n", "Found nothing\n", "\n", "ER-01:\n", "Found nothing\n", "\n", "ER-00-d:\n", "Found nothing\n", "\n", "ER-00-c:\n", "Found nothing\n", "\n", "ER-03:\n", "Found nothing\n", "\n", "R-04:\n", "Found nothing\n", "\n", "ER-09:\n", "Found nothing\n", "\n", "ER-10:\n", "Found nothing\n", "\n", "ER-00-b:\n", "Found nothing\n", "\n", "ER-00-a:\n", "Found nothing\n", "\n", "Orientation phase complete\n", "\n", "=======================================================\n", "=======================================================\n", "\n", "LPCMCI has converged\n", "\n", "Final graph:\n", "--------------------------------\n", "--------------------------------\n", "(0,-1) --> (0, 0)\n", "(0,-1) <-> (1, 0)\n", "(1,-1) --> (1, 0)\n", "(1,-2) --> (2, 0)\n", "(2,-1) --> (2, 0)\n", "--------------------------------\n", "--------------------------------\n", "\n", "Max search set: 0\n", "Max na-pds set: 1\n", "\n"]}], "source": ["# 创建 a LPCMCI object, passing the dataframe and (conditional)\n", "# independence 测试 objects.\n", "# parcorr = ParCorr(significance='analytic')\n", "lpcmci = LPCMCI(dataframe=dataframe, \n", "                cond_ind_test=parcorr,\n", "                verbosity=1)\n", "\n", "# Define the 分析 参数.\n", "tau_max = 5\n", "pc_alpha = 0.01\n", "\n", "# 运行 LPCMCI\n", "results = lpcmci.run_lpcmci(tau_max=tau_max,\n", "                            pc_alpha=pc_alpha)"]}, {"cell_type": "markdown", "id": "ef531e09", "metadata": {}, "source": ["Next, we use the 函数 `tp.plot_time_series_graph` to 绘制 the learned 时间序列 DPAG (in these plots only the time window $[t-\\tau_{\\max}, t]$ is shown). In the present case the learned graph exactly agrees with the true 时间序列 DPAG $\\mathcal{P}^5(\\mathcal{G})$. 注意, in particular, that LPCMCI has correctly identified $X^1_{t-1}$ and $X^2_t$ to be correlated due to an unobserved 变量. The edges are colored according to the 测试 statistic values returned by `LPCMCI.run_lpcmci`, i.e., the value of `val_matrix[i, j, tau]` determines the color of the edge between $X^i_{t-\\tau}$ and $X^j_t$ (provided this edge exists, i.e., provided `graph[i, j, tau]` is not the empty string) according to the color scale at the bottom."]}, {"cell_type": "code", "execution_count": 7, "id": "8b148020", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制 the learned 时间序列 DPAG\n", "tp.plot_time_series_graph(graph=results['graph'],\n", "                          val_matrix=results['val_matrix'])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "cb8eb5ac", "metadata": {}, "source": ["Another visualization is offered by the function `tp.plot_graph`, which as compared to `tp.plot_time_series_graph` intuitively speaking collapses the graph along the time dimension and thus contains a single node $i$ per observed component time series $X^i$. Among all edges between $X^i_{t-\\tau}$ and $X^j_t$ with $\\tau > 0$ that are in `graph`, the strongest one (where *strongest* is measured according to the absolute values of the test statistic values in `val_matrix`) is drawn between nodes $i$ and $j$ in a curved way and the small numbers above this edge list the lags of all these edges in order of their strength. Equivalently for all edges between $X^j_{t-\\tau}$ and $X^i_t$ with $\\tau > 0$. Contemporaneous edges are drawn in a straight (uncurved) way. The color of node $i$ denotes the maximum of the absolute value of `val_matrix[i, i, tau]` across all $1 \\leq \\tau \\leq \\tau_{\\max}$."]}, {"cell_type": "code", "execution_count": 8, "id": "084d5af7", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAHBCAYAAAD0E7h1AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABFH0lEQVR4nO3dd3wc1b0+/uec2aZdadWLZTV3G8s2JaEY0wIOzUAI7YYQapLLN4QkEEIaN6RcSCA/EpJckptGuylcEnIJJLTEhIAJ1WDAIHDBkrus3lbaMnN+f4wkW7Yk70izO7O7z/v10gt5y8zHi9b76Mw5nyOUUgpERESUs6TTBRAREZGzGAaIiIhyHMMAERFRjmMYICIiynEMA0RERDmOYYCIiCjHMQwQERHlOIYBIiKiHMcwQERElOMYBoiIiHIcwwAREVGOYxggIiLKcQwDREREOY5hgIiIKMcxDBAREeU4hgEiIqIcxzBARESU4xgGiIiIchzDABERUY5jGCAiIspxDANEREQ5jmGAiIgoxzEMEBER5TiGASIiohzncbqAVDPicUR3bsfQthYkenuh4jEYiQSEkBBeL6TPC1/lDOTVNcBTXAIhhNMlExERpVVWhQGlFCIb30Xv62sx1LIFg1s2I9q6GzAM8wFC7P1SAJQClDH6fJkXRKCuHsFZc5A3aw4Kj1oOb1GxM38ZIiKiNBFKKeV0EdM1uK0FXc8+jc5//B3x9jZADl/9MIzJnzgRTQN0HRACBcsOR8kJJ6Pw6OXQ8oL2FU1EROQSGRsGlK6j42+Po+2vf8bQthYzAEz1w38yw8cVHg8Kj1yOyvM/huDsOfafh4iIyCEZGQZ61r6MHb/6GaK7dqT3xMPBoOSU01B98WXwlpSm9/xEREQpkFFhYLBlC3bc/XP0vfFa6kYCkiElhMeDqgsuRsXZ50H6/c7UQUREZIOMCANK17H7f3+D3X/4HSAkYOhOl2QSAt6SUsz6ys0IzVvgdDVERERT4vowkOjvQ/Mdt6Lv9bVOlzI+KSGERO1nPo/Sk091uhoiIiLLXB0GYm17sOkbX0a0dZdzlwQsqDz/Y5jx8cvZq4CIiDKKa8PA0Lat2PgfX0KitycjgsCIkpM/jLrPXAehaU6XQkRElBRXtiNO9PVi07e+mnFBAAA6Vz+F3f/7G6fLICIiSprrwoAyDDT/4LuId3ZkXBAYsfvB36Ln5RecLoOIiCgprgsDu//3N+ZkwQwNAgAAIdD8g++lvw8CERHRFLgqDPS88mJ2DLErBSMWxeZbvgF9aNDpaoiIiCblmjBgxGPYetcPzU2EsoFhILpjO9oe+ZPTlRAREU3KNWGg8+mnkOjuMncSzBZKofXhP0AfjDhdCRER0YRcEQZUIoHdD/4ue0YF9mEMDqL98UedLoOIiGhCrggDnc/8HfGO9uwaFRihFFr/9CDnDhARkWu5Igzs/uPvs3JUYITe34fOp//mdBlERETjcjwMxNr2ILZ7V3aOCoyQEr1u3VuBiIhynuNhoP+dt5wuIfUMA/1vvwmVyb0TiIgoa3mcLmCg6W1A0wA99dsSr23vwv2bmtHU3Yv2aAx3HLkMJ82oSPl5AcCIDCC6YzsCtXVpOR8REVGyHB8Z6HtrXVqCAAAM6TrmFxbgy0sXpuV8++tvWu/IeYmIiCbj6MiAER1CdMf2tJ3v2MoyHFtZlrbzjaFpiGx8D/jwGc6cn4iIaAKOjgzokRxqxmMY0AcGnK6CiIjoAI6GASMadfL06aUUjOiQ01UQEREdwPE5Azklm5dPEhFRxnI0DEi/38nTp5cQkIE8p6sgIiI6gLNhIC+HPhylhBYMOl0FERHRARxdTaAF8uCfUY3orp1pOV8kkcC2gb17BOyIDOK9nj6EvR7MCKY4mOg6gnPnp/YcREREU+B406H8JYciuqc1Lb0G3unuxaef39sW+AfrNwAAzqqdgW8d3pjy84cOSf05iIiIrHI+DCxqRMdTj6XlXB8oK8Fr56xMy7n2J/OCCNSw+yAREbmP46sJQocsdrqE1BMC+Yc0QkjHX24iIqIDOP7p5Kuogq+8Iqu3MAaAgsOOcLoEIiKicTkeBoQQqDz/Y1m9Bl/LC6L05NOcLoOIiGhcjocBACj50Ep4ioqdLiM1hEDFuRdAy6VllERElFFcEQak14eqCy52uoyUkIEAys84x+kyiIiIJuSKMAAApaecBi1c6HQZ9hISFWefBy0UcroSIiKiCbkmDEi/H3VXf87pMuwjNfgqq1BxznlOV0JERDQp14QBAChafhwqzr0QQIavLBACwuPBnK9/G1qQowJERORurgoDAFB9yRXIb1wCZPKafKVQ/4UvIVDLJkNEROR+rvvEFZqGhi/dBE9hUYYGAoGKcy9E8fLjnS6EiIgoKa78tPUWFmHuzbdCC+VnXCAoWnECqi+5wukyiIiIkiaUcm+3n+juXdj0H19CrKMdMAynyzmo8rM/ipmXf5pth4mIKKO4OgwAQLynG1tu/w4G3n7L6VLGJyQggJqrrkbJkjkQ/iBEIB/whwB/EEIwGBARkbu5PgwAgNJ17Lz/19jz5z8CUgOM1G93nBQh4AkXYtaXv4H8QxphtLcAPa1j7jdDQT5EwPwvPD6zfqlBZPl+DERElBkyIgyMiGzagO2/+ikG3n3H/KB1qnQpIaRExbkXoPKjF0HLCwIAVDwKtfWNgzzXA0ABmg8omQmRVwCheVNfMxER0QQyKgwAgFIKPS+swfZ7fo54WxuANJYvJWAYKD7uRFRf+klzt8X9GK2bgf4Oa8f1+ABf0Lys4A+Z33t8HDkgIqK0yLgwMMKIx9D+2CNoe+wRxFp3p+7ywfAIRDS/CL0nrMIxx30QBQsXTfhwFR2A2v729M8rPYA/CPiCEP4g4A9B+LjZERER2S9jw8AIpRQGN29E57P/QNc/VyPR0w1omrn6YKp/NakBykA0rwBdK05H64IPYLc0LwVcfFgN6oom/1A2dr4LDPZO7dwTECW1EMUzbD0mERERkAVhYF9K19H/znr0vv4qhlq2YLD5fcQ72vc+QEqzVTAE1MjlhX1Cg/B6YcxZiI7G5dheNQet2oGthD9QU4RT5pVPXkekB2rXe7b9vRAsgqiax8sGRESUElkVBsajDw5iaPtWDG1tRqKvFyoeh4rHzUmAXi+k14dERTW2FszApqjE9p6hSY9XGPDg6qMbJv1gVkqZlwpiken/BTw+iJpGCM0z/WMRERGNI+vDQDJebOnEM+8nP+nvig/WoTLfP+ljVH8HVOvm6ZYGMfMQs28BERFRirAjDoD55dY+bDe29R/8QaGS4WWE0yAklNSmdwwiIqKDYBgAUBL0oSzoS/rxG9qTCAN2LHlUBrDtLRj7NjIiIiKyGcPAsHnlB04WnMie/hi6B+OTPkYICdQsBmDDpL/2Fhg734WRAfszEBFR5mEYGDa/zOKlgiRGB6TXDxRXT7WksQZ7gZbXYURtmJRIRES0D4aBYVUFfhT4k7/Gv6FtIKnHiaIqs2+BHQwd2L4eRtdOe45HREQEhoFRQgjMK0v+UsH2nkFEYomDH1dqEIVV0yntQJ3bYWx/B4ZbNmwiIqKMxjCwDyuXChSATR3JjQ6gsNLc6thO0X6gZR2MoWQmMxIREU2MYWAftUV58HuSf0mSvlSgecxAYDdDB3a8A6Njm/3HJiKinMEwsA9NCswtTf5SwZauCGKJ5Gb4i8Iq+0cHRnTvgrH1TRiJyVc4EBERjYdhYD9W5g3ohkJzV3Kz+4XHC4TH2dMgvxSwo9VwfMi8bNDfOf1jERFRTmEY2M/skhA0mXxvgJYkwwAAiKIZGNN3wB+CqJgF1B0KhIqTL3JCCmjdBGP3RvYkICKipDEM7MfnkWgoDib9+GRHBgBAeHx7RwekBlE5F0JISCkhq+YBFbOslju+gS5zlMCOjZKIiCjrMQyMw8q8gY5IHL1DyV+rHxkdEBVzILxjNzuSBeVA3TJAS7418oSMBLBtPYzO7dM/FhERZTWGgXE0FOdZenxL12DSjxVeP0T1QohQ0bj3S68fon6ZOZfADl07zcmF8Zg9xyMioqzDMDCOojwvwha6EVq5VAAAIq9g8vuFgKycA1TOhS17G8SHgK3rYPTsmf6xiIgo6zAMjEMIgToLowPNXREoZcMuhfuR+SVA/TLA4z/4g5PR3jzcufDgnROJiCh3MAxMoL4o+UmEAzEd7ZHUDMNLjw+ibikQrrDngNF+oPl1LkEkIqJRDAMTsDIyAADNnambuS+EgCxvAGYstKdxkRpegrhrA5cgEhERw8BECgNeFOd5k3681XkDUyGDYaDhMCBgbbvlCUW6zW2RB/vsOR4REWUkhoFJ1FsYHdjaPQjdsH/ewP6k1CBnHgKU1dlzQEMHdjbBaN3MUQIiohzFMDCJOgvzBuK6ws7eoRRWM5YsrALqlgJa8qMXk+rvGN4FkaMERES5hmFgElZGBoD0XCrYl/QGIOoPBfLL7DmgkQB2cJSAiCjXMAxMIuTzoCyUfDdAK/sU2MXsSTAbqLZpciGwd5SAcwmIiHICw8BB1BclPzqwo3cI0YSewmomJvPCQMPhQF6hPQc0EpxLQESUIxgGDqLOwqZFSgHbupNvTWw3KSVk9QKgYg4gbOhcCAyPErwOI9Jjz/GIiMh1GAYOos7CyAAANFvYpyBVZEEpUH8Y4E9+w6VJGTqw6z0YO9+DYTgz8kFERKnDMHAQeV4NlfnJtwNOZfMhK6TmgaxZDJTatAQRAAZ7gObXYPS22XdMIiJyHMNAEqysKmiPxNAXdU/vf1lUBdQdat/+BkoBbVtgbFsPI8GdEImIsgHDQBLqLcwbAJxZVTAZ6R3e36Bohn0HjUXMFQddO+07JhEROYJhIAk1hXmW5uNtccmlgn0JISBLa4HapfaNEgBA53YYLetgRN33dyYiouQwDCTB75GoLggk/fgdPenrRGiV9AUg65fZO0qQiAHb18No25KSrZyJiCi1GAaSZGXeQPdQHAMx98wbGE9KRgl626CaX4Mx0GXfMYmIKOUYBpJkZZ8CAGndp2Cq9o4SVNt3UEMHdm+Esf0dGIm4fcclIqKUYRhI0szCADQLEwcyIQyMkKU19o8SRPvNZkUd23jpgIjI5RgGkuTVJGYWJj9vYKeL5w2MZ3SUoKTG3gN374JqeR3GYK+9xyUiItswDFhgZd7Arr4hGBn4G7EsrgYabOxeCAB6Atj5Loyd78LQ3T2XgogoFzEMWGBl3kBMV2gfyMymPFLzmt0L7dzjAAAGe4FmXjogInIbhgELqsMBeGV2zhsYjywoBRqOAIJFNh5VmZcOml+HMdBt43GJiGiqGAYs0KRAjYWNizI9DADDOyHOmA/MWABIzb4DGwlg9wazrXE8at9xiYjIMoYBi6rD2TuJcDIyWGjuhFhQbu+BYxFg6xsw9rwPwzDsPTYRESWFYcCiCgs7GLZHYhiKZ8+Wv1JKyIpZQG2jvcsQAaCv3dwRsWePvcclIqKDYhiwqCLks/T4XX3ZMzowQvqC5jLE0joANk4wVAbQ3gyj5Q0YQ/32HZeIiCbFMGBRUZ4XXi13JhFORhZVAbMOB4KF9h44EQV2vANjRxO3SSYiSgOGAYuEECgPJT9Enk3zBsYjpQY5YwEwcxGgee09+FCfuU0y5xMQEaUUw8AUVOQnf6lgT4b2GrBKBgog6g8Fim3c52BEXzvQvBZG1y72JyAiSgGGgSmwMomwL5rAYBZNIpyMEAKypMbsYBgI23twpYDObWZ/gv5Oe49NRJTjGAamwMplAgDY059b6+il5oWcuRCoXgRo1iZcHpSRAFo3wdj6BoyhPnuPTUSUoxgGpqDcwmUCIPfCwAiZVwDZcKi56sDOtsYAEI8CO5pgbH8bRmzQ3mMTEeUYhoEpCHg0FAY8ST9+T39uzBuYiCyqMtsah0rsP3h0ANj2lrkJElceEBFNCcPAFJVbmDfQNpCbIwP7klJCVs0FapcA3uRbOidtsNdcedC6CYaRG3M0iIjswjAwRRUW5g20DcRgGJwFDwDSlwdZt8TcEVGm4MevvxPY8hqMthYuRyQiShLDwBRZWV6oGwqdg/EUVpN5ZEEpRMMRQGElbO1iCABQQG+r2d64czuXIxIRHQTDwBRZWV4IAG05OolwMkIIyLJ6oOFwm7dJHqYMoGsn1Ja1MLp2MhQQEU2AYWCKivK88Mrkf6PN1RUFyZCaZm6TXLsE8KVgPoEygM7tUFteg9G9m6GAiGg/DANTJIVAmYVNi/ZwEuFBSV8eZO0SYMYC+1sbA4DSgY6tUM2vwehpZSggIhrGMDANVi4V5PryQitksNBsbVxaB4gU/IgaOtDewlBARDSMYWAa2JY4dYQQw/0JDgcKq2D/JEOMDQXc94CIchjDwDRY6TUAmEsMyRopJWRZnblVcn5pak5i6Oa+B1vWwujcziWJRJRzGAamocLCnAEA6BpkGJgqKTXIyjlA/WFAXmFqTjK8+gDNa2G0b2UoIKKcwTAwDQGvhrA/+bbEXRH2Gpgu6fFCVi8A6pYC/lBqTqIU0LMb2LIWRlszOxoSUdZjGJgmK/MGuth4yDbSG4CsWQzMXAx4Ayk6iwJ695ihoPV9GHoiRechInIWw8A0WelEyMsE9pOBEGTdUqBqPuCxebvkffW3mx0Nd74HI85lokSUXRgGpsnKJMKuwThnrKeIDBVB1h8KVM4FtBSGgsEeYOsbMLathzHUn7rzEBGlUfIXvGlcVi4TxHWF/piOAgvzDMgamV8C5JfA6OsAOrYCeoouzcQiwI53YHj8QGmteV4iogzFT6VpKs7zwiMFEknuStg1GGcYSANZUAoUlMLobTdDgZGi6/2JKNC6CUabByiqAgqrIFOxGyMRUQrxX61pstqWuCvCeQPpJMNlkLMOB8obAJnCEGYkgM7t5rLE1s0wUjUiQUSUAvwV1QZFAS929yU3qYwrCpwhwxVAuAJGXzvQsS11lw+UAvo7gP4OGP58oKwOMpCfmnMREdmEYcAGBQELvQYYBhwlC8qAgjIY/Z3m5YNECkdqov3D8wp8QPFMiIIyCJGCtspERNPEMGADK42HOnmZwBVGJxoOdAMdLUAqlwsmYkDbFqj2FqiCcqCkBlLTUnc+IiKLGAZsEA4kv91u9/DyQv6G6A4yVASEimAM9gFtW4D4UOpOpgygtxXobYURKDAvIaSqiyIRkQUMAzawMjIQN7i80I1kXgFQt9TsHdDWbC4dTKWhPmD72zA0r7kKIVxpaRWCUgotLS144403EIlEsGDBAhx++OEpLJiIshk/kWwQtjBnAAB6uLzQtWQgH6hthBEbBNpbgMHe1J5Qj5sTGju2wwgVAaV1kN7Je1f88Ic/xK233or29vYxtz/66KNYtWpVCoslomzFpYU2CHo1aBaG/fti7HHvdtKXB1m9EGg4bHjr5FRf1lHAQJfZ3XDrmzB628btVhmNRvGVr3wFK1euxKOPPoqdO3eiv78fDQ0N+Ne//pXiGokoWzEM2EAIYWlFQV+UYSBTSM1rbp086wigsAoQaXjLxIfMCYdbXoWxeyOMfeYxJBIJxGIx1NXVoampCb/85S8RCoU4B4WIpoVj1TYJ+z3oTnLZYD/DQMaRUgJldVCltVA9rUD3TiDVuxiq4dGCgS5zeWJhJfLClfjQhz6E2267DQBw9NFH4xvf+EZq6yCirMeRAZtYmTfAMJC5hBCQRVWQDYcDMxYAvmB6TpyImXMLtryKx+/5EVo2vovLL788PecmoqzHkQGbWFlR0BfVU1gJpYsMFgLBQnNL4/YWINKdlvN64hHUCEBGutJyPiLKfgwDNrHSa4ATCLOL9PqBGfNhGAbQtQPo3QMYaQh8+00wVNEIDMPgRklEZBnDgE2sLBXsjybYeCgLSSmB0lqgtNbcQrlzu7mrYbpEuoEta2HkhYGSashAQfrOTUQZjb9C2MTKZYKEoTCUMFJYDTlNFpRC1i8DahqBvLCtxx6KRvF/Tz2Djc1bx9z+5nub8Nd/PIdEXwewownGlrUw9mwxL2MQEU2CYcAmVhsPcXlhbpD+oNmvYNYRQNEMW7ZR/vTXbsX5n/kynntl3ZjbH/vH8zj7U1/EV79/l3mDoQN9bWbvgubXYbS3cGtlIhoXw4BN/B4Nfi35l5MrCnKLlBpkaS3krOmvQnjlzXdwww03IB6PY82aNQCAjRs3Ih6P4xOf+ARefuPtA5+kx4GeVqD5dRgt62B0bINh8GeQiEycM2CjcMCDtoHkdiXkyEDuGl2FoMeBju1Af4e5iVGS5tTX4J577sFjjz12wH07duzAuSuPm/wAiRjQvQvo3gXDGwAKyoHCCkjJnRSJcpVQ4/U8pSl58I0deL8zuQ1uVjSUYMWs0hRXRJlAKQU10AV07Uxqg6RtO1tx9x8eQU9f/wH3lZcU46qLzkZFaYn1Qnx5ZjAIlzMYEOUYhgEbPfFeK9btTG5jm0OrwzhtQWWKK6JMY+hxoHMn0N+enuWJE/EGgPwyoKgC0oZ5DkTkbnyX26jAn3yvgUicjYfoQFLzAuX1QHk9jMFec3ni0IEjACkXHwK6tgNd22F4/EB+CVA0A1LjPxlE2YjvbBsFvckPrQ7FubSQJifzwsDMQ2AYunmNv7fNnAiYbono3jkGmhcIFgFFVZC+vPTXQkQpwTBgo4A3+dUE7DNAyZJSA0pqgJIaGNGIOVow2HNAB8K00OPmcsW+NhhCAoECoLACyCtk50OiDMYwYKOAx0IY4GUCmgLpDwIz5u8z6XAXEBtwphhlmKFksAcAYPiCQH4pEK6A1DgBkSiTMAzYKM/KZQKODNA0CCEg8kuA/BLzMkLPbvMyQiK5pa0pEYsAnRGgc5t5OSFUBBTycgJRJmAYsFHAk3wYiOkGdENBk9yfgKZHSg0ongkUzzRbD3ftAAa6ASebCulxM5z0Dl9OGB01KOOyRXKdeG8Port3Q8ViMGIxQBkQXh+k1wtPYSH8lVUQWT7axTBgIyuXCQAgmtAR9PF/AdlHev1AxWwAMOcXdO8EIj3OLlNUBhDtN786WsxRg7wCIFwJEcjnhl2UNkopDGzcgMjmjRhsbkZky/sYbH4fid7Jl4QLjweBmloEZ89BXn0D8hpmIdy4FFoolKbKU4+fRDbyWwwDQwkDQV+KiqGcJ/1BoHIuAMAY6jebGg32Wup2mBJ6HOjvBPo7oSCgvH4gWGg2O5pGm2aiiUSat6DjmafRsfopxNraAABC06D05EKySiQw2LwFg1tbzBsMA8LjQdFRx6D0Q6eg6INHQfoy+x9zNh2y2Z3PbU56PsClh9eiujCQ4oqIxjIiveaIwWAfALe9/YXZCTFYZIYDr9/pgihDJfr70fb4X9D2tycxtLUFkBIwbA7CmgboOmQgDyXHn4iKM1chf8Eie8+RJgwDNvvvF5rRPZTcWvALllZjTmn2DDNRZjFXJHSbkw+jA86PGIxHSDMchIqBgjJIT2b/9kWpp3Qdex77C7bf92voAwPpW4I7HAxKjj8RtVd9Gv7KqvSc1yYMAza799Wt2N2X3P7xZx1SicWV9u51TzRVRqTXDAZuuJQwkZFwECwE8ssgfRxZo726X30ZW3/2Xxjasd25IqQGIQWqzr8I1Rd+DFowMy59MQzY7IF1O9DcldxmRSvnleOImqLUFkQ0BcbQgNl1cNDhyYcHIwTgCZgTEvNLOSExR8W7OvH+Hbeh59VXUnM5YCqEgKegALO+cAOKl69wupqDYhiw2cNv78K7e5LrJX/crBIc28CdC8ndjNgg0NMKRLqd7WOQLM0H+IPDowcl5n4PlLX6323Cxm/dhHhvL5DkhMC0EQJQCjP+7eOo+cTlrl6eyNUENrPUhZCNhygDSF8eUN4AAGaDo949QF8HEBuE+yYgAtBjQCRmhpf2FrPPgccPBPKBUAmQV8DWyVmi49ln8P5tt0Ap5Y7RgP0N/66964HfYnDLZsz56jegBdx5aYsjAzb7x6Z2vLStK6nHHlZdiFMXVKS4IqLUUEpBRbrNcDDYDyiX/VY2Gekx5x7kFQDBYgh/kJcXMkzrIw+j5ac/drqM5EmJ0PyFWPCd78JTUOB0NQfgyIDNDtZRMBrpR9fuHfAHQ4hVcCUBZS4hBESo2Jzpj+HLCd2twGC3+y8nGAlgqM/86toJBUBJD+ALmJsvhYog/Jx/4FbtT/89s4IAABgGBja8iw03fw0Lb/8hpMddH78cGbDZv5o78eyWjjG3KaXw6uN/xMuPPoAdG9Zj5CUPBEP4/LWfxU033YT8/HwnyiVKCcMwgIFOoK8dGBrIrFGDfUkN8AbMdsp5YSBYCKm56x/xXBN5fzPe/vxnoOIObOdtByFQ+ZGPov7fr3G6kjEYBmz20tYu/GNz+5jbNryyBvd8+Up89KMfxapVq7Bo0SL09PTg2WefxY9+9CMsXboUzz77LDwuS4pEdjHiUaC31dwzIT7kdDnTJADNa44ijExU9Odzz4U0SPT1Yf01n0Ksvd2dcwQsmPPlr6P0pJOdLmMUw4DN1m7vxt82to257ZW/Pog/3XETurq68MADD+Dll19GWVkZrrvuOjQ3N2P58uV44IEHcNFFFzlUNVH6KKWgBvuGRw163X9JIVlCAh7v8EhCyJyPEOBkRbsow8CGb3wVPa+tzfggAADC68XiH/0UwdlznC4FAOcM2G68OQMFpeUAgPr6evT29qJmwRK0Nm/Eq6++iqeffhpHHnkk/vKXvzAMUE4QQkAEw0DQbLhlGIbZz6Cvw7yGr2fo8K8ygHjU/Ir0AN3mzQYEoHnMFQ3+PMCfD+QVQHj8WTMnQekJc6e/FHaIbHvycbOPQJZQuo7Nt92Cxv/+tSt+DhgGbOYZJwzM++BxOOPqLyMRj+EDp5+PgpJyPPi9G9Gy6U0AQDAYRCLh4HazRA6SUpqTEEcmIhoGMNAF9HcAQ/3ObsVsC2UGHD1u7tyItpFboYQ0VzZ4fOaIgj8IBEKAL5RZIwqJKNSOJqBoBlBUBWHzJRMjkcDO391v6zEdZxgYbGlG94v/QvExxzpdDcOA3cYLA5rmwXEXXjX657efewqvP/Uw7rjjDuzcuRMvvvgibrnllnSWSeRaUkqgoNT8wnBvg4Eu82uoP3NHDsajDLMvgh4zg8I+/coMCHMCo8e7Nyz4guYlCF/AXWHB0AFlQHXtMJealtaaHSFt+o234x+rR3cbzCpSYsdv7kPR0csdHx1gGLDZwZYWbnjlOfz+O9fhoosuwrXXXotVq1YhHA7jqquumvR5RLlKSg0oKDO/MDxyMNRvjhwMjsw5yMapT8ocFYklhhs89Yy51wAAoZkb5Hh8ZudFjx/w+QFvHuDLS9/Kh31bVutxqD3vm10rS+sg8qa3pl7pOnb+9v7Rbn5ZxTAQ2bwJPWtfQdEHjnS0FIYBm403MjBi09rn8T83/T+ccfppuPfee3HFFVdg9erVePLJJ1FYWJjGKokyl5TSnG8wPOdAKQUVHxoOB33mB2fGX1pIktKBhD7hJEwzMAhzcqP0DM9d8AKaH/D6AK/fDBDewPRWQ4y3f0V0AGpnE1SoGKK0FsI7tc57nWueRXT3rqnX5nZSYudv72cYyDbaBEM97697Cfd9/WqsPOVkPPjgg7jmmmvw29/+Fvfeey+OP/74NFdJlD2EEBC+PKCkZvS20UmJA11mn4PEUPb9VpkspczQYOhAIgpMsKmqOT9fmOFBSnPUQWp7Q8TI1+gohBfw+M35AZPN7h/oghrohiqshCiuhrA4WtH1wr/cs/lQKhgG+pveQby3B96wc78UcmmhzXb2DOH+17aNua1r93b88MozcdLxx+HPf/4zvvCFL+DnP//56P3hcBh33303zjvvvHSXS5QzjETcbIQU6Qaig8NzD/jPX1oJCQSLgGARZLgsqae8/rHzEe/qTG1dLjDv5u84OpGQIwM2G2/OwNtr/g6/R8PDDz+M3t5enHDCCTjhhBNG77/rrrvwi1/8gmGAKIWkxwsUVppfw4x4DIh0ApFe8/JC1s4/cAk13JlyoBNKCCC/ZNKJc9E9rWkPAn/cugO/ad6KjmgMs/KDuG7hPBxWXJTak2oa+t5ezzCQTbRxJvh6vD4kEgncdttt4z6nt7cXJSUlKa6MiPYnvT6gsMr8GmYk4kCky5ycGBsE4rHMbafsYmrPZqB7F1BaA+QVjhsK+t5en9aa/rarFT98dyNuPGQ+lhYV4v+27cR1a9/EA8ceiaq8FO42qOvoe3Nd6o6fBIYBm433O8WSE0/Duy/+A3f+9OcH3Ffg96CiogI33nhj6osjooOSHi8QrjC/himloKIRcx7CYD8QHzSXA/Iq6/TEIlC7NpibQ5XWQgTG7tHS/856CE2D0tMTxn7fsg1n18zAOTXVAIDrF83DSx2deGjbDlwzP7WdAgc2b4IRi0H6Ute4aTIMAzYb79+GUGEJLv/uLw+43adJXH+8O1pREtHEhBAQgZDZEKh47+2GrptdEwd7gGjEnKCnxxkSrBrqg9rxjrnyoKTGnBAKINHTA5WmiYNxw8C7vf24dFb9mNuPLC3BW909EzzLRroOPRJhGMgWVv4JcEEHSiKaBqlpQKjI/NqHYRhAbACI9AHRAXNzJj02/hI82mugC2qgC6qgDKJ4JvSh9K0C6Y7FoSuFkv0+jEv9XrzYnp79M4yoc5t4MQzYzMriDGYBouwkpTSHvgMHNtwx9Li53DHaPzwnYWi4XbEOTl4c1tcONdQP5cAmVvv/kqYU0tYdMF2XQ8bDMOAgjgwQ5R6peccdTQBGRhQiZofF2IC56VEibjZRypVRBV8eRNEMIL8EWl4obact8nmhCYGO6NgA0hmLo8TnTUsN0p/CSYoHwTBgMysjWoJjA0S0D3NEId/8GocZFgbNwBCLDIeFmDmyYOjD/wBl6OhCoMAMAcG9KwtkIJC2hkNeKbEwnI+XOzpxYmX56O0vd3Ti+IrkeiJMlwz403Ke8TAM2MxSGGAWICILzLAwPJFxAkopKD1uTmiMD5lfiZj5NTLCYBhwTWgIFUMUzThgJQEABBtmoSONpXysvhbffKsJC8NhLCkK4+HtO9E6FMVHa2em/NzeklJ4QuOHwHRgGLCZsvAGYxYgIrsJISA8PrNt8EEopczr8qOBYWSkIbF3tMEwzGZByrB35KGgHKKoanTlwLgPaVya1jbEK2dUoieewN2bm9EejWJ2QQg/PHwpZqSyxwAAaBrCyw5L7TkOgmHAZtZGBhgHiMg5QggIr9/csMiC0dGHeNQMEHveT/7JHh+QXwZZWnPQhwbnzoPweKAS6dt46vy6mTi/LvUjAWMYBgoaG9N7zv0wDNjM0tLClFVBRJQ6+44+qIT/4P/uefwQhZVAuNzc2ChJ0utFaMEi9L/91rTqdT2lkL94iaMlMAzYzNK+T0wDRJTpJlvlECgwQ0CoeMojoeFDD0N/09vZu2shAC0/H3l19Qd/YAqN00mfpoMjA0SUU9T+H9ICyC+FqFkMOXMRxEE2IzqYijNWQcgs/qgSAlXnnu/43zGLX2FncM4AEeWUkZEBqQFFMyDql0FWzoHw29MjwFdahvLTzjSXGGYh6fej8pyPOl0Gw4DdEhaGsjSGASLKdEJClDVA1B8KWVprziWw2YwL/w1ZOZYqJao+ch48+c4tKRwtxekCsk1MT35owKdl4Q83EeUUEciHKKywNDHQKn9FJco+fKo5+pBFhMeDynPPd7oMAAwDtovryY8MeDW+/EREyZj58UuhBQJZ1a1t5iWXwVtY6HQZABgGbBezEAZ8DANEREnxl1dgzte+kR3bQ0uJoqOXY8YF/+Z0JaP4aWSzOC8TEBGlRNEHPoiay650uozpkRL+ikrM+dJXXTWJnGHAZlZGBniZgIjImhkXXYyio47O2NUFwuPB/G/dAi2Uvh0Zk5GZr6aLMQwQEaWOkBKzb/wa8uobMisQCAGhaZj71f8wa3eZDHolM4OVCYScM0BEZJ0nlI9D7vixuZGRi4baJyQlpM+HBbfchuJjjnW6mnHx08hmnDNARJR6WjCIBf/5PZSd8mGnS5mclPAWl2DRD36C8KGHO13NhBgGbMbLBERE6SF9Psy6/kbUf/bzZg8CzX19CAqWLEXjz36J0Jy5TpcyKW5UZDOGASKi9BFCoHLVOQgvWYaWX/wMvWtfMecSOLmxkRDwFIRRe9WnUXbKhyFcGFL2xzBgM14mICJKv7z6Biy85TZ0v/oKtv7sJxjasT39RUgNQgrMOP8izLjwY9CCwfTXMEUMAzZj0yEiIucUfeCDKPzFPWh74q/Y8Zv7Ee/qNC8f6JNstTwdQpo7N0oNJcefiNorPwl/RWVqzpVCDAM2iyZ4mYCIyElC01Bx5tkoP30V+ta/hY5/rEbHP5+GEYmYcwsMG4KBEIBSyF+8GGUnr0TJiuPhKQhP/7gOEUplQ29Hd1BK4fZnNiHZF/STR9ahLORPaU1ERAQY8Th61r6Czuf+iYH33sXQrp17Rws0zWxzPPI1QkoIIaEMffR2T7gQebNmo+jIo1B6/EnwlZc78LexH0cGbDQYN5IOAgAQ9PLlJyJKB+n1ovjo5Sg+ejkAwEgkEN2xHYMtzYi0NCO6exdULAYjFoMydEifH8LrhSccRrC+AXn1s5BX3wBPQYHDf5PU4KeRjSLxRNKPFQDyvLxMQETkBOnxIK++AXn1DShxuhgX4KeRjSKx5K9D5Xk1V21SQUREuYthwEaRePJhIOhz/7pTIiLKDQwDNrIUBrwMA0RE5A4MAzYasHCZgCMDRETkFgwDNhrkyAAREWUghgEbWRoZYBggIiKXYBiwEScQEhFRJmIYsJGVpYUcGSAiIrdgGLCRtZEB9nsiIiJ3YBiwiW4oSxMIQxwZICIil2AYsElvNG7p8ZwzQEREbsEwYJPuweTDgFcTCHj40hMRkTvwE8kmPUPJb1JU6PdyXwIiInINhgGbWBkZCAc4eZCIiNyDYcAm3UPJh4HCgDeFlRAREVnDMGATjgwQEVGmYhiwSQ9HBoiIKEMxDNhgKKFjMG4k/fhCjgwQEZGLMAzYoGcw+ZUEABDmyAAREbkIw4ANrEwe1IRAPhsOERGRizAM2MDK5MGCgIc9BoiIyFUYBmxgbfIg5wsQEZG7MAzYwMrIAFcSEBGR2zAM2MDKyEDYz5EBIiJyF4aBaVJKodvCvgRFeRwZICIid2EYmKb+mA7dUEk/vjToS2E1RERE1jEMTJOV+QIAwwAREbkPw8A0WekxUOD3wOfhS05ERO7CT6Zp6rEwMsBRASIiciOGgWnqshIGQgwDRETkPgwD09TaH036sWVBriQgIiL3YRiYhrhuoGMglvTjeZmAiIjciGFgGvb0R5H8okJeJiAiIndiGJiG3X3JXyIIeCSCXu5WSERE7sMwMA1WwkBp0MfdComIyJUYBqahtX8o6cfyEgEREbkVw8AUJXQD7Zw8SEREWYBhYIraBmKwsCUBwwAREbkWw8AU7e5L/hIBAJTnMwwQEZE7MQxMkZXJg3leDWG/J4XVEBERTR3DwBRZCQNVBX6uJCAiItdiGJgC3VBoG7AQBvL9KayGiIhoehgGpqBtIGpp8mBlAcMAERG5F8PAFLRauEQAAFUFgRRVQkRENH0MA1NgtQ1xYYCTB4mIyL0YBqbAyrLCSk4eJCIil2MYsEg3FPZY6DzISwREROR2DAMWdURi0C3MHqzi5EEiInI5hgGLrHYe5LJCIiJyO4YBi6xMHvR7JIryvCmshoiIaPoYBiyysqywMp+TB4mIyP0YBiwwlEJrv4UwwPkCRESUARgGLOiIxJCwMHlwZpgrCYiIyP0YBiyw2nmwpjAvRZUQERHZh2HAAiuTB4vzvMjntsVERJQBGAYssLKskKMCRESUKRgGkqQbytJlgpoizhcgIqLMwDCQpF29Q4hbmDxYy5EBIiLKEAwDSWrpHkz6sUGvhmI2GyIiogzBMJCkrV2RpB9bUxRgsyEiIsoYDANJSBgGdvRy8iAREWUnhoEk7OwZstRsiPMFiIgokzAMJMHKfAGvFKjgToVERJRBGAaSsLUr+TBQHQ5Ak5wvQEREmYNh4CDiuoGdVuYLFPESARERZRaGgYPY0TMEXXG+ABERZS+GgYPY2p38kkJNCswsZOdBIiLKLAwDB9FiYb7AzHAAXo0vKRERZRZuqzfshZZOtPVH0VASwqySIAr8HsQSBnZZ2JyooTiYwgqJiIhSg2FgWNjvwT/f78A7e/oBAGUhH0qCXlhoL8AwQEREGYlhYFhVwdhr/e0DMbQPxJJ+vt8jUVXA/gJERJR5eIF7WHHQC6829f4AdUV5kOwvQEREGYhhYJgUApXT6BwYTRh4v2MAcd2wsSoiIqLU42WCfVTm+7G9J/kJg/va2j2Ird2D0IRATVEAs4qDmFMWQnmIlw6IiMjdODKwj8qC6fcI0JVCS9cgnt3Sgf5owoaqiIiIUothYB92TgBctagKs0pCth2PiIgoVRgG9lEa9NmyydDJc8twSGWBDRURERGlHsPAPjQpUBHyTesYx9QV44O1xTZVRERElHoMA/vZv9+AFUtnhHH87FIbqyEiIko9hoH9VE5x3sDcshBOm18BIdhrgIiIMgvDwH7Kp3CZoKYwgHMOqWLTISIiykgMA/spDVoLA+UhH85fUs3dComIKGPxE2w/Aa+GkE9L6rFhvwcXLpuJgDe5xxMREbkRw8A4khkdCPk0XHToTBT42cSRiIgyG8PAOA4WBvK8Ev+2bKblSwpERERuxDAwjtJJJhH6PRIXLZuJ8mlsakREROQmDAPjKA16x73dpwlcuLR6Wr0IiIiI3IZhYBzjDf97pMD5S2diZmGeAxURERGlDsPAOAr8Hni1vT0DNClw3pIZqCtiECAiouzDMDAOIcTo6IAUwLmLuQMhERFlr5xZF6eUgkokoOJxGPE4hJQQXi+k1wuhHdgnoDToQ2tfFGcfUoW5ZfkOVExERJQeQimlnC7CbrGOdkTe34JI8xZEtmzBwKZNGGxugREdGvfx3tIyhObOQXD2bAQbZiE4exbe1IpRGApgcVU4zdUTERGlV9aEgaFdu9C+ejXannoSgy0t5o1CAFICun7wAwgBoWlQiQQAQPkDKD3ySJR/eCWKjzoa0s+lhERElJ0yOgzEe3rQ/vRqtD35JPqbmswPfqXMLztoGqDrkIEASk86CeWnrEThEUdwZ0IiIsoqGRkGjFgMux56CNvuvQdGNGremOK/htA0KF1HaOFCzP7c51GweHFKz0dERJQuGRUGlFLo+Oc/0XzXfyHW1pbyADAuKQHDQOmHPoT6f78agaqq9NdARERko4wJA5HmZmz+/u3oW7/enAvgdNlSQkiJmRdfjJrLLof05MzCDCIiyjIZEQban3kGm275Txi6ntxkwHQSAgWLF2PBt78DX2mp09UQERFZ5uowoJTCtnvuwfb77nXHaMBENA3ecBiH/H93IDR3rtPVEBERWeLaMKASCWz+wR3Y89e/Ol1KcqSE9Pmw6Hu3ofCww5yuhoiIKGmuDANKKWz63vfQ9uQT7h0NGI+UEEKg8cc/QUFjo9PVEBERJcWVexO0/vnPaHvi8cwKAgBgGFBK4d2bvo5YZ6fT1RARESXFdWGgb/16vP/jHzldxtQZBuK9vXjvG/8x2s2QiIjIzVwVBmKdnXj3pq9n3ojA/nQdfevXo/nn/+10JURERAflqjCw6XvfRby3FzAMp0uZPqWw68EH0fXyy05XQkRENCnXhIG+pnfQ/dJL7usjMB1SYuuvfgkXztEkIiIa5ZowsO3ee82NgbKJYWDgvffQ8+qrTldCREQ0IVeEgf4NG9D94ovZNSowQkpsu/cep6sgIiKakCvCwPb778u+UYERhoG+9evRs26d05UQERGNy/EwoEci6Hz++ewcFRgmNA3tf/+b02UQERGNy/Ew0NfUlB2rByahdB09r7/udBlERETjcnzf3b633jQvEaR4ZOD+rdvwz/Z2tAwOwi8lloTD+H+zGlAfDKb0vCOGtm9HvKcH3sLCtJyPiIgoWY6PDPSsW5eWkYF1PT34aHU1fnHoMty5pBG6UrjurfUYTOPlib7169N2LiIiomQ5GgZUIoH+t99OS8fBHyxpxJlVlZgdCmFefj6+Nn8eWqNRvNfXn/JzA+a8gd4330zLuYiIiKxwNAwk+vthxGKOnHtgeEQg7E3PlRKlFGJtbWk5FxERkRWOhgEjGnXkvEop/Hjz+1gaDmN2KJSekxoGjOhQes5FRERkgaMTCI143JHz/mDTZmweGMDPDl2W1vPqUWdGQYiIiCbj6MiA9PnSfs4fbNqENR0d+MnSpajw+9N6bi2Q3vMRERElw9GRARkIpO1cSin8YPNmPNvegf9athTVeek7NwBASsg0hw8iIqJkODoy4CkogJama/Z3bNqMp1r34JsLFyCoaeiIxdARiyGarqWFQiCvtjY95yIiIrLA0ZEBIQTCy5ah68UXU95r4P927QIAfPbNt8bc/rX583FmVWVKzw0A0HUULFma+vMQERFZ5HgHwvDS4TCQYs8ff1zKzzEpKVGwaJGzNRAREY3D8Q6E4SVLsn5vAgAIzp4NLU2tj4mIiKxwPAyEFiyA8HqdLiOlhKah8LDDnS6DiIhoXI6HAen1ouK00wHpeCkpo3QdFWec4XQZRERE43LFJ/DMj3/c6RJSR9NQctxxCM2e7XQlRERE43JFGAjMmIHyU081tzLONrqOmksvc7oKIiKiCbkiDABAzSWfyL6JhJqGoqOPRv78+U5XQkRENCHXhIG8mhpUnHEmIITTpdhHKdRddZXTVRAREU3KNWEAAGZ97nPIa2jImssFsz73OeTPX+B0GURERJNyVRjQAgEsuvW70Pz+zB4hEBJlK1ei6iPnOl0JERHRQbkqDABAoLoa82/+JqCU06VMjaYhr6Eec274EkQmBxoiIsoZrgsDAFB89NGo+/d/d7oM6zQNnlDIHN1I446MRERE0+HKMAAANRd/HA3XXmv+IRN+w9Y0+MrKsPS/f45AdbXT1RARESVNKOXu8fj2p1dj4623Qum6e5ceCoHQvHlYdNvt8JWUOF0NERGRJa4PAwAwsHEjmr76FcQ6OtwVCIQAlELFmasw+wtfgPT5nK6IiIjIsowIAwAQ7+3F1l/9Eq2PPGLuY6DrzhYkBHylpWi45hqUfehkZ2shIiKahowJAyMiW7Zgy3/9BD2vvmqGgnSPFEgJ6fWi5tLLMOOCC8xlkERERBks48LAiK6XXsKWn/wYQ9u2mU2KUj1SMHpJ4EzUXfVJ+EpLU3s+IiKiNMnYMAAAyjDQ+9abaP/739G+ejX0gQF7g8HwyENw7lxUnHoaSk86Cf7ycnuOTURE5BIZHQb2ZcTj6H71VbT/7W/oevEFMxgA5m/0B5tjIASElOaKheE/B2pqUH7KKSg7+RTk1dam/i9ARETkkKwJA/tSSiHe2YlIczMiW97H4JZmDGzaiHh3N4x4HCoeh5ASwuuF9PkRqK1BaPZsBGfNRl5DA/Lq6zkXgIiIckZWhgEiIiJKnms7EBIREVF6MAwQERHlOIYBIiKiHMcwQERElOMYBoiIiHIcwwAREVGOYxggIiLKcQwDREREOY5hgIiIKMcxDBAREeU4hgEiIqIcxzBARESU4xgGiIiIchzDABERUY5jGCAiIspxHqcL2JfvsCshPT4IqUFIDZp37/dCyr33aRqkxwc5ep92wH1CapBSQEgBTZMQ+30vpYDUxOhjJr1PCGgeCU0KaFLAN/y9Z/TP2t77tL2P8+zzWG2874WAFAKaALyaHP3eo0loAuafpYBXinG+N+/3Sjn6vSYEhACkAITA8PEBAUCTAhIw/y4So99LAWhi3+/NYwilAGVAGAlgzPeG+WVMfJ9QBqDre783EoChQxkGkIhB6TpgGOZtiTiUoZvfx+PAyPcjjx15XDy29zmGDiOegNINKMOAEUvA0M3nKN2AEU/A0Pd+r4a/1+MJqH0ep8cS+3yvQxkKhq6G/zz8fEOZ9+kKSlcwdAN63Bg+poIe14efs/d5hlLQlULMUNAV9vt+/z+b3xswv9cVhu/b+/1/q2ZH35d24fub72++v937/ubIABERUY5jGCAiIspxDANEREQ5jmGAiIgoxzEMEBER5TiGASIiohzHMEBERJTjGAaIiIhyHMMAERFRjmMYICIiynEMA0RERDmOYYCIiCjHMQwQERHlOIYBIiKiHMcwQERElOMYBoiIiHIcwwAREVGOYxggIiLKcQwDREREOY5hgIiIKMcxDBAREeU4hgEiIqIcxzBARESU4xgGiIiIchzDABERUa5TWWpoaEjdfPPNamhoyOlSDuDm2pRifdPh5tqyiZtfZzfXphTrmw431zZdQimlnA4kqdDb24vCwkL09PQgHA47Xc4Ybq4NYH3T4ebasombX2c31wawvulwc23TxcsEREREOY5hgIiIKMcxDBAREeW4rA0Dfr8fN998M/x+v9OlHMDNtQGsbzrcXFs2cfPr7ObaANY3HW6ubbqydgIhERERJSdrRwaIiIgoOQwDREREOY5hgIiIKMdlXRi44YYbcNxxx+HjH/84YrHYmPsGBwexatUqnHDCCVi5ciU6OztdVd+I7373u/jABz7geE2JRAKXX345jjvuOHz+859PWz3J1jci3a/XviaqzQ0/a9mI72/7auL7++By6f2dVWHg9ddfx+7du/Hcc8/hkEMOwR//+Mcx9z/++ONobGzEP//5T1x44YX4n//5H1fVBwB9fX1Yv369K2p69NFHUVNTg+eeew6RSAT/+te/0lZXMvUB6X+9kq3N6Z+1bMT3t7018f099dqc/llLhawKAy+88AI+/OEPAwBOO+20A364582bh0gkAgDo7u5GeXm5q+oDgB/96Ee45pprXFFTMvU6WR+Q/tdrX5PV5vTPWjbi+9vemvj+nlyuvb89Thdgp+7ublRXVwMACgsLDxi6mTNnDtavX4/GxkYIIfDSSy+5qr6enh689dZbuOmmm1xRU3d392j/7fHqdbo+J16vZGtz+mctG/H9bW9NfH9PvTanf9ZSISNHBnbv3o0VK1Yc8KWUQm9vLwDzf2RJScmY591333048cQTsX79enzrW9/Ct7/9bVfVd+edd+Kzn/1sSmqaSHFx8YQ1TXafG+pz4vXa12S1petnLRvx/W0fvr+nLtfe3xkZBqqqqrBmzZoDvs444ww89dRTAIAnn3wSxx577AHPHfkfWlRUhO7ublfVt2nTJtxyyy047bTTsHHjRnzve99LSX37OvrooyesabL70mWyGpx4vZKtDUjPz1o24vvbPnx/p6Y2IAvf387tnpwaX/ziF9WKFSvUxRdfrKLRqFJKqU9/+tNKKaV6enrUGWecoU444QR17LHHqvfee89V9e3riCOOcKymkXri8bi69NJL1YoVK9S1116btnqSrW9f6Xy99jVRbW74WctGfH9Pvya+v5OXS+9vtiMmIiLKcRl5mYCIiIjswzBARESU4xgGiIiIchzDABERUY5jGMgB9957L4qKimw5VnNzM4QQ8Hg82LFjx5j7du3aBY/HAyEEmpubx9z30EMP4cQTT0RhYSHy8/OxdOlSfPvb3x5t5GFnjUS55vLLL4cQAldfffUB933mM5+BEAKXX3756G27d+/Gtddei9mzZ8Pv96O2thZnnXUWVq9ePfqYhoYG3HnnnWmontyAYYCmpLq6Gvfff/+Y2+677z7MnDnzgMd+/etfx0UXXYQPfvCDePzxx7F+/XrccccdeOONN7KipzeRG9TW1uKBBx7A4ODg6G1DQ0P4/e9/j7q6utHbmpubccQRR+Dpp5/G7bffjrfeegtPPPEETjrpJMda/5LzGAYywBNPPIEVK1agqKgIpaWlWLVqFTZv3gwAeOaZZyCEGNP0Yt26daO/nT/zzDO44oor0NPTAyEEhBD45je/CQDo6urCpZdeiuLiYgSDQZx++unYuHFjUjVddtlluOeee8bcdu+99+Kyyy4bc9vLL7+MW2+9FXfccQe+//3vY/ny5WhoaMDKlSvx0EMPHfB4Ipqaww8/HHV1dfjTn/40etuf/vQn1NbW4rDDDhu9bWSk4OWXX8b555+P+fPnY/Hixbj++uvx4osvOlE6uQDDQAYYGBjA9ddfj1deeQWrV6+GlBLnnnsuDMM46HOXL1+OO++8E+FwGLt27cKuXbtwww03ADCHFl999VU88sgjeOGFF6CUwhlnnIF4PH7Q45599tno6urCmjVrAABr1qxBZ2cnzjrrrDGP++1vf4v8/Hx85jOfGfc4vDRAZJ8rrrhiTEi/++67ceWVV47+ubOzE0888QSuueYahEKhA57P92PuyqqNirLVeeedN+bPv/71r1FRUYF33nnnoM/1+XwoLCyEEAJVVVWjt2/cuBGPPPIInn/+eSxfvhyA+cFdW1uLhx9+GBdccMGkx/V6vbjkkktw9913Y8WKFbj77rtxySWXwOv1jnncxo0bMXv27ANuJyL7feITn8BXv/rV0bk9zz//PB544AE888wzAMwWv0opLFy40NlCyXU4MpABNm/ejIsvvhizZ89GOBzGrFmzAABbt26d8jGbmprg8Xhw1FFHjd5WWlqKBQsWoKmpCQBw+umnIz8/H/n5+Vi8ePEBx7jqqqvwhz/8Abt378Yf/vCHMb+BjFBKQQgx5TqJKHllZWU488wzcd999+Gee+7BmWeeibKystH7RxrO8j1J++PIQAY466yzUFtbi1/+8peorq6GYRhobGxELBZDfn4+gL1vcgBJDfNP1IV63w/vX/3qV6OTkcb7zb6xsRELFy7Exz72MSxatAiNjY1Yt27dmMfMnz8fa9asQTwe5+gAURpceeWVo7v93XXXXWPumzdvHoQQaGpqwkc+8hEHqiO34siAy3V0dKCpqQk33XQTTj75ZCxatAhdXV2j95eXlwMwl/WN2P8D2efzQdf1MbcdcsghSCQSY/bh7ujowIYNG7Bo0SIAwMyZMzF37lzMnTsX9fX149Z35ZVX4plnnhl3VAAALr74YvT39+OnP/3puPdnxW5fRC5y2mmnIRaLIRaL4dRTTx1zX0lJCU499VTcddddGBgYOOC5fD/mLoYBlysuLkZpaSl+8YtfYNOmTXj66adx/fXXj94/d+5c1NbW4pvf/CY2bNiAv/71r7jjjjvGHKOhoQH9/f1YvXo12tvbEYlEMG/ePJxzzjn41Kc+hTVr1uCNN97AJZdcgpkzZ+Kcc85Jur5PfepTaGtrwyc/+clx7z/qqKNw44034otf/CJuvPFGvPDCC2hpacHq1atxwQUX4L777pvaC0NE49I0DU1NTWhqaoKmaQfc/9Of/hS6ruPII4/EQw89hI0bN6KpqQk//vGPccwxxzhQMbkBw4DLSSnxwAMPYO3atWhsbMR1112H73//+6P3e71e/P73v8e7776LZcuW4bbbbsN//ud/jjnG8uXLcfXVV+Oiiy5CeXk5br/9dgDAPffcgyOOOAKrVq3CMcccA6UUHnvsMUvD+R6PB2VlZfB4Jr7idNttt+F3v/sdXnrpJZx66qmjy5iWLl3KpYVEKRAOhxEOh8e9b9asWXjttddw0kkn4Ytf/CIaGxuxcuVKrF69Gj/72c/SXCm5BbcwJiIiynEcGSAiIspxDANEREQ5jmGAiIgoxzEMEBER5TiGASIiohzHMEBERJTjGAaIiIhyHMMAERFRjmMYICIiynEMA0RERDmOYYCIiCjH/f99Z06q8+cA3AAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制 the learned 时间序列 DPAG\n", "tp.plot_graph(graph=results['graph'],\n", "              val_matrix=results['val_matrix'])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "3a6c1818", "metadata": {}, "source": ["For reference we here print out the values returned by the above application of `LPCMCI.run_lpcmci`."]}, {"cell_type": "code", "execution_count": 9, "id": "4bdb5269", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Graph:\n", " [[['' '-->' '' '' '' '']\n", "  ['' '<->' '' '' '' '']\n", "  ['' '' '' '' '' '']]\n", "\n", " [['' '' '' '' '' '']\n", "  ['' '-->' '' '' '' '']\n", "  ['' '' '-->' '' '' '']]\n", "\n", " [['' '' '' '' '' '']\n", "  ['' '' '' '' '' '']\n", "  ['' '-->' '' '' '' '']]]\n", "\n", " Maximum p-values:\n", " [[[0.00000000e+00 3.27584784e-64 6.51861006e-01 1.66931712e-01\n", "   3.04058216e-01 2.69057390e-01]\n", "  [4.98048404e-01 1.89520368e-05 8.15648017e-01 3.92853993e-01\n", "   1.53679032e-01 1.45390490e-01]\n", "  [9.95865710e-01 8.18453900e-01 2.04903492e-01 9.50877747e-01\n", "   8.78253711e-01 6.62506989e-01]]\n", "\n", " [[4.98048404e-01 3.32516902e-01 9.33958515e-01 4.00491113e-01\n", "   3.51910421e-01 6.25033871e-01]\n", "  [0.00000000e+00 4.98133560e-59 7.65327504e-02 9.28068682e-01\n", "   6.83165853e-02 1.95806389e-01]\n", "  [6.17173962e-01 6.30053604e-01 3.96190611e-23 6.53400604e-01\n", "   8.99607321e-01 2.03196768e-01]]\n", "\n", " [[9.95865710e-01 5.72580879e-01 4.83793353e-01 7.79201995e-02\n", "   4.34900855e-01 5.91366138e-01]\n", "  [6.17173962e-01 4.41473417e-01 1.86116367e-02 3.31452071e-01\n", "   5.04650001e-01 1.93950903e-02]\n", "  [0.00000000e+00 1.06276799e-69 9.53624556e-01 7.81343854e-01\n", "   5.31473165e-02 7.35232120e-01]]]\n", "\n", " Associated test statistic values:\n", " [[[ 0.00000000e+00  6.66897503e-01  2.04742498e-02 -6.26652974e-02\n", "   -4.66191999e-02 -5.00772920e-02]\n", "  [ 3.07434559e-02  1.92483911e-01 -1.05808638e-02  3.87641451e-02\n", "    6.46799225e-02  6.59400254e-02]\n", "  [-2.35405048e-04 -1.04276922e-02  5.75454423e-02 -2.79875571e-03\n", "    6.95919833e-03  1.98062227e-02]]\n", "\n", " [[ 3.07434559e-02 -4.39586601e-02 -3.76077458e-03 -3.81426791e-02\n", "    4.22289108e-02  2.21550575e-02]\n", "  [ 0.00000000e+00  6.46212249e-01 -8.02534602e-02 -4.09704718e-03\n", "    8.25901675e-02  5.85979432e-02]\n", "  [-2.27062286e-02 -2.18567815e-02 -4.27743218e-01 -2.03983847e-02\n", "    5.73141961e-03  5.77031850e-02]]\n", "\n", " [[-2.35405048e-04 -2.56304800e-02 -3.18034486e-02  7.99613916e-02\n", "   -3.54263936e-02 -2.43362162e-02]\n", "  [-2.27062286e-02  3.49579487e-02 -1.06604485e-01 -4.41008808e-02\n", "   -3.02730397e-02 -1.05695395e-01]\n", "  [ 0.00000000e+00  6.88594445e-01 -2.64480498e-03 -1.26227516e-02\n", "   -8.76818044e-02  1.53468700e-02]]]\n"]}], "source": ["print(\"Graph:\\n\", results['graph'])\n", "print(\"\\n Maximum p-values:\\n\", results['p_matrix'])\n", "print(\"\\n Associated test statistic values:\\n\", results['val_matrix'])"]}, {"cell_type": "markdown", "id": "806e01c5", "metadata": {}, "source": ["### 3.5 A different choice of $\\tau_{\\max}$"]}, {"cell_type": "markdown", "id": "c71cce08", "metadata": {}, "source": ["The below code runs LPCMCI with $\\tau_{max} = 2$ on the same data. The learned graph perfectly agrees with the true time series DPAG $\\mathcal{P}^2(\\mathcal{G})$ shown above in section 1.3. We note that $\\mathcal{P}^2(\\mathcal{G})$ differs from $\\mathcal{P}^5(\\mathcal{G})$ in that in $\\mathcal{P}^2(\\mathcal{G})$ there is the edge $X^2_{t-2} {\\circ\\!{\\rightarrow}} X^3_t$ while in $\\mathcal{P}^5(\\mathcal{G})$ there is the edge $X^2_{t-2} {\\rightarrow} X^3_t$. This is NOT a finite-sample effect due to erroneous test decisions but rather another example of the fact that $\\mathcal{P}^{\\tau_{\\max}}(\\mathcal{G})$ can depend on $\\tau_{\\max}$."]}, {"cell_type": "code", "execution_count": 10, "id": "a601047b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 创建 a LPCMCI object, passing the dataframe and (conditional)\n", "# independence 测试 objects.\n", "# parcorr = ParCorr(significance='analytic')\n", "lpcmci = LPCMCI(dataframe=dataframe, \n", "                cond_ind_test=parcorr,\n", "                verbosity=0)\n", "\n", "# Define the 分析 参数.\n", "tau_max = 2\n", "pc_alpha = 0.01\n", "\n", "# 运行 LPCMCI\n", "results = lpcmci.run_lpcmci(tau_max=tau_max,\n", "                            pc_alpha=pc_alpha)\n", "\n", "# 绘制 the learned 时间序列 DPAG\n", "tp.plot_time_series_graph(graph=results['graph'],\n", "                          val_matrix=results['val_matrix'])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "e2302aa5", "metadata": {}, "source": ["### 3.6 Comparison with PCMCIplus"]}, {"cell_type": "markdown", "id": "651f368f", "metadata": {}, "source": ["The PCMCIplus 算法 assumes the absence of unobserved variables. While this assumption is violated in the present 示例, let us see what happens when we apply it to the 数据."]}, {"cell_type": "code", "execution_count": 11, "id": "791174d8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 创建 a PCMCI object, passing the dataframe and (conditional)\n", "# independence 测试 objects.\n", "# parcorr = ParCorr(significance='analytic')\n", "pcmci = PCMCI(dataframe=dataframe, \n", "              cond_ind_test=parcorr,\n", "              verbosity=0)\n", "\n", "# Define the 分析 参数.\n", "tau_max = 5\n", "pc_alpha = 0.01\n", "\n", "# 运行 LPCMCI\n", "results = pcmci.run_pcmciplus(tau_max=tau_max,\n", "                              pc_alpha=pc_alpha)\n", "\n", "# 绘制 the learned 时间序列 DPAG\n", "tp.plot_time_series_graph(graph=results['graph'],\n", "                          val_matrix=results['val_matrix'])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "ede278b0", "metadata": {}, "source": ["We see that PCMCIplus infers the correct adjacencies and for most edges also the correct edge type. However, instead of $X^0_{t-1} {\\leftrightarrow} X^1_t$ it wrongly infers $X^0_{t-1} {\\rightarrow} X^1_t$, which wrongly claims a causal influence of $X^0_{t-1}$ on $X^1_t$. This is an immediate implication of the assumption of no unobserved variables: Without unobserved variables the only possible edge orientations are $X^0_{t-1} {\\rightarrow} X^1_t$ and $X^0_{t-1} {\\leftarrow} X^1_t$, the latter of which is excluded because there is no causal influence backwards in time."]}, {"cell_type": "markdown", "id": "fd10e83b", "metadata": {}, "source": ["### 3.7 On the importance of preliminary 阶段"]}, {"cell_type": "markdown", "id": "5160646f", "metadata": {}, "source": ["In subsection 2.4 we have introduced and discussed the importance of LPCMCI's preliminary phases, i.e., of step 2. of the algorithm. By default LPCMCI uses `n_preliminary_iterations = 1` ($k=1$ in step 2. of the algorithm), i.e., by default runs one preliminary phase. As we have seen above, this led to good results for the present example. We here show the results of running LPCMCI WITHOUT preliminary phases, i.e., with `n_preliminary_iterations = 0` for both `tau_max = 5` and `tau_max = 2`."]}, {"cell_type": "code", "execution_count": 12, "id": "b007d6d8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 创建 a LPCMCI object, passing the dataframe and (conditional)\n", "# independence 测试 objects.\n", "# parcorr = ParCorr(significance='analytic')\n", "lpcmci = LPCMCI(dataframe=dataframe, \n", "                cond_ind_test=parcorr,\n", "                verbosity=0)\n", "\n", "# Define the 分析 参数.\n", "tau_max = 5\n", "pc_alpha = 0.01\n", "n_preliminary_iterations = 0\n", "\n", "# 运行 LPCMCI\n", "results = lpcmci.run_lpcmci(tau_max=tau_max,\n", "                            pc_alpha=pc_alpha,\n", "                            n_preliminary_iterations=n_preliminary_iterations)\n", "\n", "# 绘制 the learned 时间序列 DPAG\n", "tp.plot_time_series_graph(graph=results['graph'],\n", "                          val_matrix=results['val_matrix'])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "id": "c1275c6e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 创建 a LPCMCI object, passing the dataframe and (conditional)\n", "# independence 测试 objects.\n", "# parcorr = ParCorr(significance='analytic')\n", "lpcmci = LPCMCI(dataframe=dataframe, \n", "                cond_ind_test=parcorr,\n", "                verbosity=0)\n", "\n", "# Define the 分析 参数.\n", "tau_max = 2\n", "pc_alpha = 0.01\n", "n_preliminary_iterations = 0\n", "\n", "# 运行 LPCMCI\n", "results = lpcmci.run_lpcmci(tau_max=tau_max,\n", "                            pc_alpha=pc_alpha,\n", "                            n_preliminary_iterations=n_preliminary_iterations)\n", "\n", "# 绘制 the learned 时间序列 DPAG\n", "tp.plot_time_series_graph(graph=results['graph'],\n", "                          val_matrix=results['val_matrix'])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "2abdf006", "metadata": {}, "source": ["We see that, unlike with the default setting, the algorithm fails to detect the edge $X^0_{t-1} {\\leftrightarrow} X^1_t$. This is a manifestion of what has been discussed in subsection 2.3: A low effect size due to autocorrelation of the time series. With the default settings, where `n_preliminary_iterations = 1`, this problem is addressed by restoring all removed edges after the preliminary phase and then working with larger default conditioning sets $\\mathcal{S}_{def}(X^i_{t-\\tau}, X^j_t)$ in the final ancestral phase (step 3.) of the algorithm.\n", "\n", "For those interested in validating this point in more detail:\\\n", "The edge between $X^0_{t-1}$ and $X^1_t$ is removed because the (conditional) independence test wrongly judges $X^0_{t-1}$ and $X^1_t$ to be independent conditional on $X^0_{t-2}$. To see this set `verbosity = 1` when creating the LPCMCI object in the previous two cells and search for the line `(0,-1) independent (1, 0) given ((0, -2),) union set()` in the verbose output, or set ``verbosity = 2`` and search for ``ANC(Y):    (0, -1) _|_ (1, 0)  |  S_def = , S_pc = (0, -2): val = 0.10 / pval =  0.0287`` (here, ``S_pc`` refers to the standard conditioning set $\\mathcal{S}$ and ``S_def`` to the default conditions $\\mathcal{S}_{def}$). Then set `verbosity = 2` in the default applications of LPCMCI in subsections 3.4 and 3.5 above and see that the same happens in the preliminary phases of these runs. However, in these cases the algorithm restores this edge before moving to the final ancestral phase while it remembers that $X^0_{t-2}$ is a causal ancestor of $X^0_{t-1}$ and that $X^1_{t-1}$ is a causal ancestor of $X^1_{t}$. The final ancestral phase therefore uses $\\mathcal{S}_{def}(X^0_{t-1}, X^0_t) = \\{X^0_{t-2}, X^1_{t-1}\\}$ and never tests whether $X^0_{t-1}$ and $X^1_t$ are conditionally independent given $X^0_{t-2}$. Indeed, search for ``ANC(Y):    (0, -1) _|_ (1, 0)  |  S_def = (0, -2) (1, -1), S_pc = : val = 0.19 / pval =  0.0000`` in the verbose output.\n", "\n", "For `tau_max = 2` it is further wrongly inferred that $X^2_{t-2}$ and $X^1_t$ are adjacent."]}, {"cell_type": "markdown", "id": "321a6e1f", "metadata": {}, "source": ["## 4. Incorporating background knowledge"]}, {"cell_type": "markdown", "id": "27bee0c4", "metadata": {}, "source": ["In some cases, you might have a priori knowledge about the existence vs absence of certain links and/or about the orientation of certain links. You can inform LPCMCI about this background knowledge by means of the optional keyword argument `link_assumptions` that is passed to `LPCMCI.run_lpcmci`. The default value of `link_assumptions` is `None`.\n", "\n", "The value passed to `link_assumptions` needs to be a two-level nested dictionary of the form `{j: {(i, lag_i): link, ...}, ...}`, where `link` is a string that specifies your a priori knowledge about the link between the variables $X^i_{t+lag_i}$ and $X^j_t$ (with $lag_i < 0$) according to the 以下 rules:\n", "\n", "- `link_assumptions[j][(i, lag_i)] = '-?>'`: $X^i_{t+lag_i}$ is an ancestor of $X^j_t$.\n", "- `link_assumptions[j][(i, lag_i)] = '-->'`: $X^i_{t+lag_i}$ is an ancestor of $X^j_t$, and there is a link between $X^i_{t+lag_i}$ and $X^j_t$.\n", "- `link_assumptions[j][(i, lag_i)] = '<?-'`: Only allowed for $lag_i = 0$. $X^j_t$ is an ancestor of $X^i_t$.\n", "- `link_assumptions[j][(i, lag_i)] = '<--'`: Only allowed for $lag_i = 0$. $X^j_t$ is an ancestor of $X^i_t$, and there is a link between $X^i_t$ and $X^j_t$.\n", "- `link_assumptions[j][(i, lag_i)] = '<?>'`: Neither $X^i_{t+lag_i}$ is an ancestor of $X^j_t$ nor the other way around.\n", "- `link_assumptions[j][(i, lag_i)] = '<->'`: Neither $X^i_{t+lag_i}$ is an ancestor of $X^j_t$ nor the other way around, and there is a link between $X^i_{t+lag_i}$ and $X^j_t$.\n", "- `link_assumptions[j][(i, lag_i)] = 'o?>'`: $X^j_t$ is not an ancestor of $X^i_{t+lag_i}$ (for $lag_i < 0$ this background knowledge is for the default settings of self.run_lpcmci() imposed automatically).\n", "- `link_assumptions[j][(i, lag_i)] = 'o->'`: $X^j_t$ is not an ancestor of $X^i_{t+lag_i}$, and there is a link between $X^i_{t+lag_i}$ and $X^j_t$.\n", "- `link_assumptions[j][(i, lag_i)] = '<?o'`: Only allowed for $lag_i = 0$. $X^i_t$ is not an ancestor of $X^j_t$.\n", "- `link_assumptions[j][(i, lag_i)] = '<-o'`: Only allowed for $lag_i = 0$. $X^i_t$ is not an ancestor of $X^j_t$, and there is a link between $X^i_t$ and $X^j_t$.\n", "- `link_assumptions[j][(i, lag_i)] = 'o-o'`: Only allowed for $lag_i = 0$. There is a link between $X^i_t$ and $X^j_t$.\n", "- `link_assumptions[j][(i, lag_i)] = 'o?o'`: Only allowed for $lag_i = 0$. No claim is made.\n", "- `link_assumptions[j][(i, lag_i)] = ''`: There is no link between $X^i_{t+lag_i}$ and $X^j_t$.\n", "- If `link_assumptions` is not `None` and `link_assumptions[j]` or `link_assumptions[j][(i, lag_i)]` does not exist, then it is assumed that there is no link between $X^i_{t+lag_i}$ and $X^j_t$ (so equivalent to `link_assumptions[j][(i, lag_i)] = ''`).\n", "\n", "The information contained in `link_assumptions` must be consistent in all of the 以下 three regards:\n", "- The $lag_i = 0$ zero links must be symmetric. For example, if `link_assumptions[j][(i, 0)] = '-?>'`, then it is necessary that `link_assumptions[i][(j, 0)] = '<?-'`.\n", "- The ancestorships must not specify a cyclic causal relationships. That is, if the ancestorships specify that $X^i_{t+lag_i}$ is an ancestor of $X^j_t$, then `link_assumptions[j][(i, lag_i)]` must be neither of `'<?-`', `'<--`'.\n", "- If the ancestorships specify that $X^i_{t+lag_i}$ is an ancestor of $X^j_t$, then `link_assumptions[j](i, lag_i)]` must be neither of `'<?>`', `'<->`', `'<?o`', `'<-o`'.\n", "\n", "These requirements are checked automatically. If the passed value violates these requirements, then 错误 messages are raised that explain the violation in detail and help you to specify a valid value.\n", "\n", "Moreover, both $i$ and $j$ in `link_assumptions[j][(i, lag_i)]` must be within $[0, \\ldots, N-1]$, where $N$ is the number of component 时间序列, and $lag_i$ must be within $[-\\tau_{max}, \\tau_{\\min}]$. Lastly, `link_assumptions[j]` must not contain the key `(j, 0)` because this key would refer to a link between the 变量 $X^j_t$ and itself."]}, {"cell_type": "markdown", "id": "3ebaf01e", "metadata": {}, "source": ["Consider, for example, a case with $N=2$ component time series and $\\tau_{max} = 1$ and $\\tau_{min} = 0$. Say you would like to specfiy that $X^1_{t-1} \\rightarrow X^0_t$ exists for sure and that $X^0_t$ does not cause $X^1_t$. Then, `link_assumptions` needs to look as follows:\n", "\n", "`link_assumptions = {0: {(0, -1): 'o?>', (1, 0): 'o?>', (1, -1): '-->'}, 1: {(1, -1): 'o?>', (0, 0): '<?o', (0, -1): 'o?>'}}`"]}, {"cell_type": "markdown", "id": "a1201ef6", "metadata": {}, "source": ["**Importantly**, keep in mind the last bullet point in the above explanation of what entries in `link_assumptions` mean: If `link_assumptions` does not take its default value `None` and `link_assumptions[j][(i, lag_i)]` does not exist, then this combination of facts is equivalent to specifying `link_assumptions[j][(i, lag_i)] = ''`.\n", "\n", "There might be cases in which you have background knowledge on only a small number of the possible links and it is cumbersome to build the full nested dictionary, where absence of background knowledge needs to be specified by `link_assumptions[j][(i, lag_i)] = 'o?>` for $lag_i < 0$ and by `link_assumptions[j][(i, 0)] = 'o?o'` for $lag_i = 0$. In such cases, you can use the following convenience functions in your code or notebook."]}, {"cell_type": "code", "execution_count": 14, "id": "e3e670d1", "metadata": {}, "outputs": [], "source": ["def build_link_assumptions(link_assumptions_absent_link_means_no_knowledge,\n", "                           n_component_time_series,\n", "                           tau_max,\n", "                           tau_min=0):\n", "\n", "    out = {j: {(i, -tau_i): (\"o?>\" if tau_i > 0 else \"o?o\")\n", "         for i in range(n_component_time_series) for tau_i in range(tau_min, tau_max+1)\n", "         if (tau_i > 0 or i != j)} for j in range(n_component_time_series)}\n", "\n", "    for j, links_j in link_assumptions_absent_link_means_no_knowledge.items():\n", "        for (i, lag_i), link_ij in links_j.items():\n", "            if link_ij == \"\": \n", "                del out[j][(i, lag_i)]\n", "            else:\n", "                out[j][(i, lag_i)] = link_ij\n", "    return out"]}, {"cell_type": "markdown", "id": "4e183d20", "metadata": {}, "source": ["Returning to the previous 示例, you can specify\n", "\n", "`link_assumptions_absent_link_means_no_knowledge = {0: {(1, 0): 'o?>', (1, -1): '-->'}, 1: {(0, 0): '<?o'}}`\n", "\n", "and the build `link_assumptions` as\n", "\n", "`link_assumptions = build_link_assumptions(link_assumptions_absent_link_means_no_knowledge, 2, 1)`."]}, {"cell_type": "markdown", "id": "6eab467d", "metadata": {}, "source": ["## References"]}, {"cell_type": "markdown", "id": "13789b36", "metadata": {}, "source": ["[1] <PERSON>, <PERSON><PERSON> (2009). *Causality: Models, Reasoning, and Inference*. Cambridge University Press, Cambridge, UK, 2nd edition.\\\n", "[2] <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> (2017). *Elements of 因果推断: Foundations and Learning Algorithms*. MIT Press, Cambridge, MA, USA.\\\n", "[3] <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> (2002). Ancestral graph markov models. *The Annals of Statistics*, 30:962–1030.\\\n", "[4] <PERSON>, <PERSON><PERSON> (2008a). Causal reasoning with ancestral graphs. *Journal of Machine Learning Research*, 9:1437–1474.\\\n", "[5] <PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> (2009). Markov equivalence for ancestral graphs. *The Annals of Statistics*, 37(5B):2808–2837.\\\n", "[6] <PERSON>, <PERSON><PERSON> (2008b). On the completeness of orientation rules for causal discovery in the presence of latent confounders and selection bias. *Artificial Intelligence*, 172:1873–1896.\\\n", "[7] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>, T<PERSON> (1995). 因果推断 in the Presence of 潜在 Variables and Selection Bias. In <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON>, editors, *Proceedings of the Eleventh Conference on Uncertainty in Artificial Intelligence*, UAI’95, page 499–506, San Francisco, CA, USA. Morgan Kaufmann Publishers Inc.\\\n", "[8] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>, <PERSON><PERSON> (2000). *因果关系, 预测, and Search*. MIT Press, Cambridge, MA, USA.\\\n", "[9] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2019). Detecting and quantifying causal associations in large nonlinear 时间序列 datasets. *Science Advances*, 5:eaau4996.\\\n", "[10] <PERSON><PERSON>, <PERSON><PERSON> (2020). Discovering contemporaneous and lagged causal relations in autocorrelated nonlinear 时间序列 datasets. In Sontag, <PERSON><PERSON> and <PERSON>, J<PERSON>, editors, *Proceedings of the 36th Conference on Uncertainty in Artificial Intelligence*, UAI 2020, Toronto, Canada, 2019. AUAI Press.\\\n", "[11] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> (1991). An Algorithm for Fast Recovery of Sparse Causal Graphs. *Social Science Computer Review*, 9:62–72."]}, {"cell_type": "code", "execution_count": null, "id": "e42c0952", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tigenv", "language": "python", "name": "tigenv"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}