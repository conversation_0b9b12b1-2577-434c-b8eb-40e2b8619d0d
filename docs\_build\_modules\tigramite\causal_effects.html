<!DOCTYPE html>

<html lang="en" data-content_root="../../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.causal_effects &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=db26dd79" />
    <link rel="stylesheet" type="text/css" href="../../_static/alabaster.css?v=19da42e6" />
    <script src="../../_static/documentation_options.js?v=625b3a9a"></script>
    <script src="../../_static/doctools.js?v=aa79a7b1"></script>
    <script src="../../_static/sphinx_highlight.js?v=4825356b"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
   
  <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.causal_effects</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite causal inference for time series.&quot;&quot;&quot;</span>

<span class="c1"># Author: Jakob Runge &lt;<EMAIL>&gt;</span>
<span class="c1">#</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">numpy</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">np</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">math</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">itertools</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">copy</span><span class="w"> </span><span class="kn">import</span> <span class="n">deepcopy</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">collections</span><span class="w"> </span><span class="kn">import</span> <span class="n">defaultdict</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.models</span><span class="w"> </span><span class="kn">import</span> <span class="n">Models</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.graphs</span><span class="w"> </span><span class="kn">import</span> <span class="n">Graphs</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">struct</span>

<div class="viewcode-block" id="CausalEffects">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">CausalEffects</span><span class="p">(</span><span class="n">Graphs</span><span class="p">):</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Causal effect estimation.</span>

<span class="sd">    Methods for the estimation of linear or non-parametric causal effects </span>
<span class="sd">    between (potentially multivariate) X and Y (potentially conditional </span>
<span class="sd">    on S) by (generalized) backdoor adjustment. Various graph types are </span>
<span class="sd">    supported, also including hidden variables.</span>
<span class="sd">    </span>
<span class="sd">    Linear and non-parametric estimators are based on sklearn. For the </span>
<span class="sd">    linear case without hidden variables also an efficient estimation </span>
<span class="sd">    based on Wright&#39;s path coefficients is available. This estimator </span>
<span class="sd">    also allows to estimate mediation effects.</span>

<span class="sd">    See the corresponding paper [6]_ and tigramite tutorial for an </span>
<span class="sd">    in-depth introduction. </span>

<span class="sd">    References</span>
<span class="sd">    ----------</span>

<span class="sd">    .. [6] J. Runge, Necessary and sufficient graphical conditions for</span>
<span class="sd">           optimal adjustment sets in causal graphical models with </span>
<span class="sd">           hidden variables, Advances in Neural Information Processing</span>
<span class="sd">           Systems, 2021, 34 </span>
<span class="sd">           https://proceedings.neurips.cc/paper/2021/hash/8485ae387a981d783f8764e508151cd9-Abstract.html</span>


<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    graph : array of either shape [N, N], [N, N, tau_max+1], or [N, N, tau_max+1, tau_max+1]</span>
<span class="sd">        Different graph types are supported, see tutorial.</span>
<span class="sd">    X : list of tuples</span>
<span class="sd">        List of tuples [(i, -tau), ...] containing cause variables.</span>
<span class="sd">    Y : list of tuples</span>
<span class="sd">        List of tuples [(j, 0), ...] containing effect variables.</span>
<span class="sd">    S : list of tuples</span>
<span class="sd">        List of tuples [(i, -tau), ...] containing conditioned variables.  </span>
<span class="sd">    graph_type : str</span>
<span class="sd">        Type of graph.</span>
<span class="sd">    hidden_variables : list of tuples</span>
<span class="sd">        Hidden variables in format [(i, -tau), ...]. The internal graph is </span>
<span class="sd">        constructed by a latent projection.</span>
<span class="sd">    check_SM_overlap : bool</span>
<span class="sd">        Whether to check whether S overlaps with M.</span>
<span class="sd">    verbosity : int, optional (default: 0)</span>
<span class="sd">        Level of verbosity.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">graph</span><span class="p">,</span>
                 <span class="n">graph_type</span><span class="p">,</span>
                 <span class="n">X</span><span class="p">,</span>
                 <span class="n">Y</span><span class="p">,</span>
                 <span class="n">S</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">hidden_variables</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">check_SM_overlap</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                 <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
        
        <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">=</span> <span class="n">verbosity</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">N</span> <span class="o">=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="k">if</span> <span class="n">S</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">S</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">listX</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">X</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">listY</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">listS</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">S</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">X</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">X</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">Y</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">S</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">S</span><span class="p">)</span>    

        <span class="c1"># TODO?: check that masking aligns with hidden samples in variables</span>
        <span class="k">if</span> <span class="n">hidden_variables</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">hidden_variables</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="c1"># </span>
        <span class="c1"># Checks regarding graph type</span>
        <span class="c1">#</span>
        <span class="n">supported_graphs</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;dag&#39;</span><span class="p">,</span> 
                            <span class="s1">&#39;admg&#39;</span><span class="p">,</span>
                            <span class="s1">&#39;tsg_dag&#39;</span><span class="p">,</span>
                            <span class="s1">&#39;tsg_admg&#39;</span><span class="p">,</span>
                            <span class="s1">&#39;stationary_dag&#39;</span><span class="p">,</span>
                            <span class="s1">&#39;stationary_admg&#39;</span><span class="p">,</span>

                            <span class="c1"># &#39;mag&#39;,</span>
                            <span class="c1"># &#39;tsg_mag&#39;,</span>
                            <span class="c1"># &#39;stationary_mag&#39;,</span>
                            <span class="c1"># &#39;pag&#39;,</span>
                            <span class="c1"># &#39;tsg_pag&#39;,</span>
                            <span class="c1"># &#39;stationary_pag&#39;,</span>
                            <span class="p">]</span>
        <span class="k">if</span> <span class="n">graph_type</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">supported_graphs</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Only graph types </span><span class="si">%s</span><span class="s2"> supported!&quot;</span> <span class="o">%</span><span class="n">supported_graphs</span><span class="p">)</span>

        <span class="c1"># Determine tau_max</span>
        <span class="k">if</span> <span class="n">graph_type</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;dag&#39;</span><span class="p">,</span> <span class="s1">&#39;admg&#39;</span><span class="p">]:</span> 
            <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="k">elif</span> <span class="n">graph_type</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;tsg_dag&#39;</span><span class="p">,</span> <span class="s1">&#39;tsg_admg&#39;</span><span class="p">]:</span>
            <span class="c1"># tau_max is implicitely derived from</span>
            <span class="c1"># the dimensions </span>
            <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">-</span> <span class="mi">1</span>

        <span class="k">elif</span> <span class="n">graph_type</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;stationary_dag&#39;</span><span class="p">,</span> <span class="s1">&#39;stationary_admg&#39;</span><span class="p">]:</span>
            <span class="c1"># For a stationary DAG without hidden variables it&#39;s sufficient to consider</span>
            <span class="c1"># a tau_max that includes the parents of X, Y, M, and S. A conservative</span>
            <span class="c1"># estimate thereof is simply the lag-dimension of the stationary DAG plus</span>
            <span class="c1"># the maximum lag of XYS.</span>
            <span class="n">statgraph_tau_max</span> <span class="o">=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">-</span> <span class="mi">1</span>
            <span class="n">maxlag_XYS</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">):</span>
                <span class="n">maxlag_XYS</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">maxlag_XYS</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">varlag</span><span class="p">[</span><span class="mi">1</span><span class="p">]))</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="n">maxlag_XYS</span> <span class="o">+</span> <span class="n">statgraph_tau_max</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;graph_type invalid.&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">hidden_variables</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">hidden_variables</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">hidden_variables</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">)))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;XYS overlaps with hidden_variables!&quot;</span><span class="p">)</span>

        <span class="c1"># self.tau_max is needed in the Graphs class</span>
        <span class="n">Graphs</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
                        <span class="n">graph</span><span class="o">=</span><span class="n">graph</span><span class="p">,</span>
                        <span class="n">graph_type</span><span class="o">=</span><span class="n">graph_type</span><span class="p">,</span>
                        <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                        <span class="n">hidden_variables</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">hidden_variables</span><span class="p">,</span>
                        <span class="n">verbosity</span><span class="o">=</span><span class="n">verbosity</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_check_XYS</span><span class="p">()</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">ancX</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_ancestors</span><span class="p">(</span><span class="n">X</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">ancY</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_ancestors</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">ancS</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_ancestors</span><span class="p">(</span><span class="n">S</span><span class="p">)</span>

        <span class="c1"># If X is not in anc(Y), then no causal link exists</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">ancY</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">X</span><span class="p">))</span> <span class="o">==</span> <span class="nb">set</span><span class="p">():</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">no_causal_path</span> <span class="o">=</span> <span class="kc">True</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No causal path from X to Y exists.&quot;</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">no_causal_path</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="c1"># Get mediators</span>
        <span class="n">mediators</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_mediators</span><span class="p">(</span><span class="n">start</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)</span> 

        <span class="n">M</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">mediators</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">M</span> <span class="o">=</span> <span class="n">M</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">listM</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">):</span>
            <span class="k">if</span> <span class="nb">abs</span><span class="p">(</span><span class="n">varlag</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;X, Y, S must have time lags inside graph.&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Overlap between X and Y.&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">)))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Conditions S overlap with X or Y.&quot;</span><span class="p">)</span>

        <span class="c1"># # TODO: need to prove that this is sufficient for non-identifiability!</span>
        <span class="c1"># if len(self.X.intersection(self._get_descendants(self.M))) &gt; 0:</span>
        <span class="c1">#     raise ValueError(&quot;Not identifiable: Overlap between X and des(M)&quot;)</span>

        <span class="k">if</span> <span class="n">check_SM_overlap</span> <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Conditions S overlap with mediators M.&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">desX</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_descendants</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">desY</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_descendants</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">desM</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_descendants</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">descendants</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">desY</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">desM</span><span class="p">)</span>

        <span class="c1"># Define forb as X and descendants of YM</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">forbidden_nodes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">descendants</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">)</span>  <span class="c1">#.union(S)</span>

        <span class="c1"># Define valid ancestors</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">vancs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">ancX</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">ancY</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">ancS</span><span class="p">)</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">forbidden_nodes</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">desX</span><span class="p">))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Warning: Potentially outside assumptions: Conditions S overlap with des(X)&quot;</span><span class="p">)</span>

        <span class="c1"># Here only check if S overlaps with des(Y), leave the option that S</span>
        <span class="c1"># contains variables in des(M) to the user</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">desY</span><span class="p">))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Not identifiable: Conditions S overlap with des(Y).&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">## Initializing CausalEffects class</span><span class="se">\n</span><span class="s2">##&quot;</span>
                  <span class="s2">&quot;</span><span class="se">\n\n</span><span class="s2">Input:&quot;</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">graph_type = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">graph_type</span>
                  <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">X = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">listX</span>
                  <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">Y = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">listY</span>
                  <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">S = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">listS</span>
                  <span class="o">+</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">M = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">listM</span>
                  <span class="p">)</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">hidden_variables</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">hidden_variables = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">hidden_variables</span>
                      <span class="p">)</span> 
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n\n</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">no_causal_path</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No causal path from X to Y exists!&quot;</span><span class="p">)</span>


    <span class="k">def</span><span class="w"> </span><span class="nf">_check_XYS</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check whether XYS are sober.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">XYS</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">xys</span> <span class="ow">in</span> <span class="n">XYS</span><span class="p">:</span>
            <span class="n">var</span><span class="p">,</span> <span class="n">lag</span> <span class="o">=</span> <span class="n">xys</span> 
            <span class="k">if</span> <span class="n">var</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="ow">or</span> <span class="n">var</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;XYS vars must be in [0...N]&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">lag</span> <span class="o">&lt;</span> <span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="ow">or</span> <span class="n">lag</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;XYS lags must be in [-taumax...0]&quot;</span><span class="p">)</span>


<div class="viewcode-block" id="CausalEffects.check_XYS_paths">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.check_XYS_paths">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">check_XYS_paths</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check whether one can remove nodes from X and Y with no proper causal paths.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        X, Y : cleaned lists of X and Y with irrelevant nodes removed.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="c1"># TODO: Also check S...</span>
        <span class="n">oldX</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
        <span class="n">oldY</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>

        <span class="c1"># anc_Y = self._get_ancestors(self.Y)</span>
        <span class="c1"># anc_S = self._get_ancestors(self.S)</span>

        <span class="c1"># Remove first from X those nodes with no causal path to Y or S</span>
        <span class="n">X</span> <span class="o">=</span> <span class="nb">set</span><span class="p">([</span><span class="n">x</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span> <span class="k">if</span> <span class="n">x</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">ancY</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">ancS</span><span class="p">)])</span>
        
        <span class="c1"># Remove from Y those nodes with no causal path from X</span>
        <span class="c1"># des_X = self._get_descendants(X)</span>

        <span class="n">Y</span> <span class="o">=</span> <span class="nb">set</span><span class="p">([</span><span class="n">y</span> <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span> <span class="k">if</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">desX</span><span class="p">])</span>

        <span class="c1"># Also require that all x in X have proper path to Y or S,</span>
        <span class="c1"># that is, the first link goes out of x </span>
        <span class="c1"># and into path nodes</span>
        <span class="n">mediators_S</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_mediators</span><span class="p">(</span><span class="n">start</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">)</span>
        <span class="n">path_nodes</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">mediators_S</span><span class="p">))</span> 
        <span class="n">X</span> <span class="o">=</span> <span class="n">X</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_get_all_parents</span><span class="p">(</span><span class="n">path_nodes</span><span class="p">))</span>

        <span class="k">if</span> <span class="nb">set</span><span class="p">(</span><span class="n">oldX</span><span class="p">)</span> <span class="o">!=</span> <span class="nb">set</span><span class="p">(</span><span class="n">X</span><span class="p">)</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Consider pruning X = </span><span class="si">%s</span><span class="s2"> to X = </span><span class="si">%s</span><span class="s2"> &quot;</span> <span class="o">%</span><span class="p">(</span><span class="n">oldX</span><span class="p">,</span> <span class="n">X</span><span class="p">)</span> <span class="o">+</span>
                  <span class="s2">&quot;since only these have causal path to Y&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="nb">set</span><span class="p">(</span><span class="n">oldY</span><span class="p">)</span> <span class="o">!=</span> <span class="nb">set</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Consider pruning Y = </span><span class="si">%s</span><span class="s2"> to Y = </span><span class="si">%s</span><span class="s2"> &quot;</span> <span class="o">%</span><span class="p">(</span><span class="n">oldY</span><span class="p">,</span> <span class="n">Y</span><span class="p">)</span> <span class="o">+</span>
                  <span class="s2">&quot;since only these have causal path from X&quot;</span><span class="p">)</span>

        <span class="k">return</span> <span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">X</span><span class="p">),</span> <span class="nb">list</span><span class="p">(</span><span class="n">Y</span><span class="p">))</span></div>



<div class="viewcode-block" id="CausalEffects.get_optimal_set">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.get_optimal_set">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_optimal_set</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
        <span class="n">alternative_conditions</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">minimize</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="n">return_separate_sets</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns optimal adjustment set.</span>
<span class="sd">        </span>
<span class="sd">        See Runge NeurIPS 2021.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        alternative_conditions : set of tuples</span>
<span class="sd">            Used only internally in optimality theorem. If None, self.S is used.</span>
<span class="sd">        minimize : {False, True, &#39;colliders_only&#39;} </span>
<span class="sd">            Minimize optimal set. If True, minimize such that no subset </span>
<span class="sd">            can be removed without making it invalid. If &#39;colliders_only&#39;,</span>
<span class="sd">            only colliders are minimized.</span>
<span class="sd">        return_separate_sets : bool</span>
<span class="sd">            Whether to return tuple of parents, colliders, collider_parents, and S.</span>
<span class="sd">        </span>
<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Oset_S : False or list or tuple of lists</span>
<span class="sd">            Returns optimal adjustment set if a valid set exists, otherwise False.</span>
<span class="sd">        &quot;&quot;&quot;</span>


        <span class="c1"># Needed for optimality theorem where Osets for alternative S are tested</span>
        <span class="k">if</span> <span class="n">alternative_conditions</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">S</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
            <span class="n">vancs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">vancs</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">S</span> <span class="o">=</span> <span class="n">alternative_conditions</span>
            <span class="n">newancS</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_ancestors</span><span class="p">(</span><span class="n">S</span><span class="p">)</span>
            <span class="n">vancs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">ancX</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">ancY</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">newancS</span><span class="p">)</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">forbidden_nodes</span>

            <span class="c1"># vancs = self._get_ancestors(list(self.X.union(self.Y).union(S))) - self.forbidden_nodes</span>

        <span class="c1"># descendants = self._get_descendants(self.Y.union(self.M))</span>

        <span class="c1"># Sufficient condition for non-identifiability</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">descendants</span><span class="p">))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>  <span class="c1"># raise ValueError(&quot;Not identifiable: Overlap between X and des(M)&quot;)</span>

        <span class="c1">##</span>
        <span class="c1">## Construct O-set</span>
        <span class="c1">##</span>

        <span class="c1"># Start with parents </span>
        <span class="n">parents</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_parents</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">))</span> <span class="c1"># set([])</span>

        <span class="c1"># Remove forbidden nodes</span>
        <span class="n">parents</span> <span class="o">=</span> <span class="n">parents</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">forbidden_nodes</span>

        <span class="c1"># Construct valid collider path nodes</span>
        <span class="n">colliders</span> <span class="o">=</span> <span class="nb">set</span><span class="p">([])</span>
        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">):</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">w</span> 
            <span class="n">this_level</span> <span class="o">=</span> <span class="p">[</span><span class="n">w</span><span class="p">]</span>
            <span class="n">non_suitable_nodes</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">while</span> <span class="nb">len</span><span class="p">(</span><span class="n">this_level</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">next_level</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="n">this_level</span><span class="p">:</span>
                    <span class="n">suitable_spouses</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_get_spouses</span><span class="p">(</span><span class="n">varlag</span><span class="p">))</span> <span class="o">-</span> <span class="nb">set</span><span class="p">(</span><span class="n">non_suitable_nodes</span><span class="p">)</span>
                    <span class="k">for</span> <span class="n">spouse</span> <span class="ow">in</span> <span class="n">suitable_spouses</span><span class="p">:</span>
                        <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">spouse</span>
                        <span class="k">if</span> <span class="n">spouse</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span>
                            <span class="k">return</span> <span class="kc">False</span>

                        <span class="k">if</span> <span class="p">(</span><span class="c1"># Node not already in set</span>
                            <span class="n">spouse</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">colliders</span>  <span class="c1">#.union(parents)</span>
                            <span class="c1"># not forbidden</span>
                            <span class="ow">and</span> <span class="n">spouse</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">forbidden_nodes</span> 
                            <span class="c1"># in time bounds</span>
                            <span class="ow">and</span> <span class="p">(</span><span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">)</span> <span class="c1"># or self.ignore_time_bounds)</span>
                            <span class="ow">and</span> <span class="p">(</span><span class="n">spouse</span> <span class="ow">in</span> <span class="n">vancs</span>
                                <span class="ow">or</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span><span class="c1">#graph=self.graph, </span>
                                    <span class="n">start</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="p">[</span><span class="n">spouse</span><span class="p">],</span> 
                                                    <span class="n">conditions</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">parents</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">vancs</span><span class="p">))</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">S</span><span class="p">),</span>
                                                    <span class="p">))</span>
                                <span class="p">):</span>
                                <span class="n">colliders</span> <span class="o">=</span> <span class="n">colliders</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">spouse</span><span class="p">]))</span>
                                <span class="n">next_level</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">spouse</span><span class="p">)</span>
                        <span class="k">else</span><span class="p">:</span>
                            <span class="k">if</span> <span class="n">spouse</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">colliders</span><span class="p">:</span>
                                <span class="n">non_suitable_nodes</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">spouse</span><span class="p">)</span>


                <span class="n">this_level</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">next_level</span><span class="p">)</span> <span class="o">-</span> <span class="nb">set</span><span class="p">(</span><span class="n">non_suitable_nodes</span><span class="p">)</span>  

        <span class="c1"># Add parents and raise Error if not identifiable</span>
        <span class="n">collider_parents</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_parents</span><span class="p">(</span><span class="n">colliders</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="n">collider_parents</span><span class="p">))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>

        <span class="n">colliders_and_their_parents</span> <span class="o">=</span> <span class="n">colliders</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">collider_parents</span><span class="p">)</span>

        <span class="c1"># Add valid collider path nodes and their parents</span>
        <span class="n">Oset</span> <span class="o">=</span> <span class="n">parents</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">colliders_and_their_parents</span><span class="p">)</span>


        <span class="k">if</span> <span class="n">minimize</span><span class="p">:</span> 
            <span class="n">removable</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="c1"># First remove all those that have no path from X</span>
            <span class="n">sorted_Oset</span> <span class="o">=</span>  <span class="n">Oset</span>
            <span class="k">if</span> <span class="n">minimize</span> <span class="o">==</span> <span class="s1">&#39;colliders_only&#39;</span><span class="p">:</span>
                <span class="n">sorted_Oset</span> <span class="o">=</span> <span class="p">[</span><span class="n">node</span> <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">sorted_Oset</span> <span class="k">if</span> <span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">parents</span><span class="p">]</span>

            <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">sorted_Oset</span><span class="p">:</span>
                <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span><span class="c1">#graph=self.graph, </span>
                    <span class="n">start</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="p">[</span><span class="n">node</span><span class="p">],</span> 
                                <span class="n">conditions</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">Oset</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">node</span><span class="p">]))</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">S</span><span class="p">))):</span>
                    <span class="n">removable</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">node</span><span class="p">)</span> 

            <span class="n">Oset</span> <span class="o">=</span> <span class="n">Oset</span> <span class="o">-</span> <span class="nb">set</span><span class="p">(</span><span class="n">removable</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">minimize</span> <span class="o">==</span> <span class="s1">&#39;colliders_only&#39;</span><span class="p">:</span>
                <span class="n">sorted_Oset</span> <span class="o">=</span> <span class="p">[</span><span class="n">node</span> <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">Oset</span> <span class="k">if</span> <span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">parents</span><span class="p">]</span>

            <span class="n">removable</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="c1"># Next remove all those with no direct connection to Y</span>
            <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">sorted_Oset</span><span class="p">:</span>
                <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span><span class="c1">#graph=self.graph, </span>
                    <span class="n">start</span><span class="o">=</span><span class="p">[</span><span class="n">node</span><span class="p">],</span> <span class="n">end</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">,</span> 
                            <span class="n">conditions</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">Oset</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">node</span><span class="p">]))</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">S</span><span class="p">)</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">),</span>
                            <span class="n">ends_with</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;**&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;**+&#39;</span><span class="p">])):</span> 
                    <span class="n">removable</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">node</span><span class="p">)</span> 

            <span class="n">Oset</span> <span class="o">=</span> <span class="n">Oset</span> <span class="o">-</span> <span class="nb">set</span><span class="p">(</span><span class="n">removable</span><span class="p">)</span>

        <span class="n">Oset_S</span> <span class="o">=</span> <span class="n">Oset</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">S</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">return_separate_sets</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">parents</span><span class="p">,</span> <span class="n">colliders</span><span class="p">,</span> <span class="n">collider_parents</span><span class="p">,</span> <span class="n">S</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">list</span><span class="p">(</span><span class="n">Oset_S</span><span class="p">)</span></div>



    <span class="k">def</span><span class="w"> </span><span class="nf">_get_collider_paths_optimality</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source_nodes</span><span class="p">,</span> <span class="n">target_nodes</span><span class="p">,</span>
        <span class="n">condition</span><span class="p">,</span> 
        <span class="n">inside_set</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> 
        <span class="n">start_with_tail_or_head</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> 
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns relevant collider paths to check optimality.</span>

<span class="sd">        Iterates over collider paths within O-set via depth-first search</span>

<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">source_nodes</span><span class="p">:</span>
            <span class="c1"># Only used to return *all* collider paths </span>
            <span class="c1"># (needed in optimality theorem)</span>
            
            <span class="n">coll_path</span> <span class="o">=</span> <span class="p">[]</span>

            <span class="n">queue</span> <span class="o">=</span> <span class="p">[(</span><span class="n">w</span><span class="p">,</span> <span class="n">coll_path</span><span class="p">)]</span>

            <span class="n">non_valid_subsets</span> <span class="o">=</span> <span class="p">[]</span>

            <span class="k">while</span> <span class="n">queue</span><span class="p">:</span>

                <span class="n">varlag</span><span class="p">,</span> <span class="n">coll_path</span> <span class="o">=</span> <span class="n">queue</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>

                <span class="n">coll_path</span> <span class="o">=</span> <span class="n">coll_path</span> <span class="o">+</span> <span class="p">[</span><span class="n">varlag</span><span class="p">]</span>

                <span class="n">suitable_nodes</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_get_spouses</span><span class="p">(</span><span class="n">varlag</span><span class="p">))</span>

                <span class="k">if</span> <span class="n">start_with_tail_or_head</span> <span class="ow">and</span> <span class="n">coll_path</span> <span class="o">==</span> <span class="p">[</span><span class="n">w</span><span class="p">]:</span>
                    <span class="n">children</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_get_children</span><span class="p">(</span><span class="n">varlag</span><span class="p">))</span>
                    <span class="n">suitable_nodes</span> <span class="o">=</span> <span class="n">suitable_nodes</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">children</span><span class="p">)</span>
 
                <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">suitable_nodes</span><span class="p">:</span>
                    <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">node</span>
                    <span class="k">if</span> <span class="p">((</span><span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">)</span> <span class="c1"># or self.ignore_time_bounds)</span>
                        <span class="ow">and</span> <span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">coll_path</span><span class="p">):</span>

                        <span class="k">if</span> <span class="n">condition</span> <span class="o">==</span> <span class="s1">&#39;II&#39;</span> <span class="ow">and</span> <span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">target_nodes</span> <span class="ow">and</span> <span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">vancs</span><span class="p">:</span>
                            <span class="k">continue</span>

                        <span class="k">if</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">inside_set</span><span class="p">:</span>
                            <span class="k">if</span> <span class="n">condition</span> <span class="o">==</span> <span class="s1">&#39;I&#39;</span><span class="p">:</span>
                                <span class="n">non_valid</span> <span class="o">=</span> <span class="kc">False</span>
                                <span class="k">for</span> <span class="n">pathset</span> <span class="ow">in</span> <span class="n">non_valid_subsets</span><span class="p">[::</span><span class="o">-</span><span class="mi">1</span><span class="p">]:</span>
                                    <span class="k">if</span> <span class="nb">set</span><span class="p">(</span><span class="n">pathset</span><span class="p">)</span><span class="o">.</span><span class="n">issubset</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">coll_path</span> <span class="o">+</span> <span class="p">[</span><span class="n">node</span><span class="p">])):</span>
                                        <span class="n">non_valid</span> <span class="o">=</span> <span class="kc">True</span>
                                        <span class="k">break</span>
                                <span class="k">if</span> <span class="n">non_valid</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
                                    <span class="n">queue</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">node</span><span class="p">,</span> <span class="n">coll_path</span><span class="p">))</span> 
                                <span class="k">else</span><span class="p">:</span>
                                    <span class="k">continue</span>
                            <span class="k">elif</span> <span class="n">condition</span> <span class="o">==</span> <span class="s1">&#39;II&#39;</span><span class="p">:</span>
                                <span class="n">queue</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">node</span><span class="p">,</span> <span class="n">coll_path</span><span class="p">))</span>

                        <span class="k">if</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">target_nodes</span><span class="p">:</span>  
                            <span class="c1"># yield coll_path</span>
                            <span class="c1"># collider_paths[node].append(coll_path) </span>
                            <span class="k">if</span> <span class="n">condition</span> <span class="o">==</span> <span class="s1">&#39;I&#39;</span><span class="p">:</span>         
                                <span class="c1"># Construct OπiN</span>
                                <span class="n">Sprime</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">coll_path</span><span class="p">)</span>
                                <span class="n">OpiN</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_optimal_set</span><span class="p">(</span><span class="n">alternative_conditions</span><span class="o">=</span><span class="n">Sprime</span><span class="p">)</span>
                                <span class="k">if</span> <span class="n">OpiN</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
                                    <span class="n">queue</span> <span class="o">=</span> <span class="p">[(</span><span class="n">q_node</span><span class="p">,</span> <span class="n">q_path</span><span class="p">)</span> <span class="k">for</span> <span class="p">(</span><span class="n">q_node</span><span class="p">,</span> <span class="n">q_path</span><span class="p">)</span> <span class="ow">in</span> <span class="n">queue</span> <span class="k">if</span> <span class="nb">set</span><span class="p">(</span><span class="n">coll_path</span><span class="p">)</span><span class="o">.</span><span class="n">issubset</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">q_path</span> <span class="o">+</span> <span class="p">[</span><span class="n">q_node</span><span class="p">]))</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">]</span>
                                    <span class="n">non_valid_subsets</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">coll_path</span><span class="p">)</span>
                                <span class="k">else</span><span class="p">:</span>
                                    <span class="k">return</span> <span class="kc">False</span>

                            <span class="k">elif</span> <span class="n">condition</span> <span class="o">==</span> <span class="s1">&#39;II&#39;</span><span class="p">:</span>
                                <span class="k">return</span> <span class="kc">True</span>
                                <span class="c1"># yield coll_path</span>
 
        <span class="k">if</span> <span class="n">condition</span> <span class="o">==</span> <span class="s1">&#39;I&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">True</span>
        <span class="k">elif</span> <span class="n">condition</span> <span class="o">==</span> <span class="s1">&#39;II&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>
        <span class="c1"># return collider_paths</span>


<div class="viewcode-block" id="CausalEffects.check_optimality">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.check_optimality">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">check_optimality</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check whether optimal adjustment set exists according to Thm. 3 in Runge NeurIPS 2021.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        optimality : bool</span>
<span class="sd">            Returns True if an optimal adjustment set exists, otherwise False.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="c1"># Cond. 0: Exactly one valid adjustment set exists</span>
        <span class="n">cond_0</span> <span class="o">=</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_get_all_valid_adjustment_sets</span><span class="p">(</span><span class="n">check_one_set_exists</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>

        <span class="c1">#</span>
        <span class="c1"># Cond. I</span>
        <span class="c1">#</span>
        <span class="n">parents</span><span class="p">,</span> <span class="n">colliders</span><span class="p">,</span> <span class="n">collider_parents</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_optimal_set</span><span class="p">(</span><span class="n">return_separate_sets</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="n">Oset</span> <span class="o">=</span> <span class="n">parents</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">colliders</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">collider_parents</span><span class="p">)</span>
        <span class="n">n_nodes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_spouses</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">colliders</span><span class="p">))</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">forbidden_nodes</span> <span class="o">-</span> <span class="n">Oset</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">S</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span> <span class="o">-</span> <span class="n">colliders</span>

        <span class="k">if</span> <span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">n_nodes</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">):</span>
            <span class="c1"># # (1) There are no spouses N ∈ sp(YMC) \ (forbOS)</span>
            <span class="n">cond_I</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="k">else</span><span class="p">:</span>
            
            <span class="c1"># (2) For all N ∈ N and all its collider paths i it holds that </span>
            <span class="c1"># OπiN does not block all non-causal paths from X to Y</span>
            <span class="c1"># cond_I = True</span>
            <span class="n">cond_I</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_collider_paths_optimality</span><span class="p">(</span>
                <span class="n">source_nodes</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">n_nodes</span><span class="p">),</span> <span class="n">target_nodes</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">)),</span>
                <span class="n">condition</span><span class="o">=</span><span class="s1">&#39;I&#39;</span><span class="p">,</span> 
                <span class="n">inside_set</span><span class="o">=</span><span class="n">Oset</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">),</span> <span class="n">start_with_tail_or_head</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                <span class="p">)</span>

        <span class="c1">#</span>
        <span class="c1"># Cond. II</span>
        <span class="c1">#</span>
        <span class="n">e_nodes</span> <span class="o">=</span> <span class="n">Oset</span><span class="o">.</span><span class="n">difference</span><span class="p">(</span><span class="n">parents</span><span class="p">)</span>
        <span class="n">cond_II</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="k">for</span> <span class="n">E</span> <span class="ow">in</span> <span class="n">e_nodes</span><span class="p">:</span>
            <span class="n">Oset_minusE</span> <span class="o">=</span> <span class="n">Oset</span><span class="o">.</span><span class="n">difference</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">E</span><span class="p">]))</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span><span class="c1">#graph=self.graph, </span>
                <span class="n">start</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">),</span> <span class="n">end</span><span class="o">=</span><span class="p">[</span><span class="n">E</span><span class="p">],</span> 
                                <span class="n">conditions</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">)</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">Oset_minusE</span><span class="p">)):</span>
                   
                <span class="n">cond_II</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_collider_paths_optimality</span><span class="p">(</span>
                    <span class="n">target_nodes</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">),</span> 
                    <span class="n">source_nodes</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">E</span><span class="p">])),</span>
                    <span class="n">condition</span><span class="o">=</span><span class="s1">&#39;II&#39;</span><span class="p">,</span> 
                    <span class="n">inside_set</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">Oset</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">)),</span>
                    <span class="n">start_with_tail_or_head</span> <span class="o">=</span> <span class="kc">True</span><span class="p">)</span>
               
                <span class="k">if</span> <span class="n">cond_II</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
                    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
                        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Non-optimal due to E = &quot;</span><span class="p">,</span> <span class="n">E</span><span class="p">)</span>
                    <span class="k">break</span>

        <span class="n">optimality</span> <span class="o">=</span> <span class="p">(</span><span class="n">cond_0</span> <span class="ow">or</span> <span class="p">(</span><span class="n">cond_I</span> <span class="ow">and</span> <span class="n">cond_II</span><span class="p">))</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Optimality = </span><span class="si">%s</span><span class="s2"> with cond_0 = </span><span class="si">%s</span><span class="s2">, cond_I = </span><span class="si">%s</span><span class="s2">, cond_II = </span><span class="si">%s</span><span class="s2">&quot;</span>
                    <span class="o">%</span>  <span class="p">(</span><span class="n">optimality</span><span class="p">,</span> <span class="n">cond_0</span><span class="p">,</span> <span class="n">cond_I</span><span class="p">,</span> <span class="n">cond_II</span><span class="p">))</span>
        <span class="k">return</span> <span class="n">optimality</span></div>


    <span class="k">def</span><span class="w"> </span><span class="nf">_check_validity</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">Z</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Checks whether Z is a valid adjustment set.&quot;&quot;&quot;</span>

        <span class="c1"># causal_children = list(self.M.union(self.Y))</span>
        <span class="n">backdoor_path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span><span class="c1">#graph=self.graph, </span>
            <span class="n">start</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">),</span> <span class="n">end</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">),</span> 
                            <span class="n">conditions</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">Z</span><span class="p">),</span> 
                            <span class="c1"># causal_children=causal_children,</span>
                            <span class="n">path_type</span> <span class="o">=</span> <span class="s1">&#39;non_causal&#39;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">backdoor_path</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">True</span>
    
    <span class="k">def</span><span class="w"> </span><span class="nf">_get_adjust_set</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
        <span class="n">minimize</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns Adjust-set.</span>
<span class="sd">        </span>
<span class="sd">        See van der Zander, B.; Liśkiewicz, M. &amp; Textor, J.</span>
<span class="sd">        Separators and adjustment sets in causal graphs: Complete </span>
<span class="sd">        criteria and an algorithmic framework </span>
<span class="sd">        Artificial Intelligence, Elsevier, 2019, 270, 1-40</span>

<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">vancs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">vancs</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">minimize</span><span class="p">:</span>
            <span class="c1"># Get removable nodes by computing minimal valid set from Z</span>
            <span class="k">if</span> <span class="n">minimize</span> <span class="o">==</span> <span class="s1">&#39;keep_parentsYM&#39;</span><span class="p">:</span>
                <span class="n">minimize_nodes</span> <span class="o">=</span> <span class="n">vancs</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_parents</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">)))</span>

            <span class="k">else</span><span class="p">:</span>
                <span class="n">minimize_nodes</span> <span class="o">=</span> <span class="n">vancs</span>

            <span class="c1"># Zprime2 = Zprime</span>
            <span class="c1"># First remove all nodes that have no unique path to X given Oset</span>
            <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">minimize_nodes</span><span class="p">:</span>
                <span class="c1"># path = self.oracle.check_shortest_path(X=X, Y=[node], </span>
                <span class="c1">#     Z=list(vancs - set([node])), </span>
                <span class="c1">#     max_lag=None, </span>
                <span class="c1">#     starts_with=None, #&#39;arrowhead&#39;, </span>
                <span class="c1">#     forbidden_nodes=None, #list(Zprime - set([node])), </span>
                <span class="c1">#     return_path=False)</span>
                <span class="n">path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span><span class="c1">#graph=self.graph, </span>
                    <span class="n">start</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="p">[</span><span class="n">node</span><span class="p">],</span> 
                    <span class="n">conditions</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">vancs</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">node</span><span class="p">])),</span> 
                     <span class="p">)</span>
  
                <span class="k">if</span> <span class="n">path</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
                    <span class="n">vancs</span> <span class="o">=</span> <span class="n">vancs</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">node</span><span class="p">])</span>

            <span class="k">if</span> <span class="n">minimize</span> <span class="o">==</span> <span class="s1">&#39;keep_parentsYM&#39;</span><span class="p">:</span>
                <span class="n">minimize_nodes</span> <span class="o">=</span> <span class="n">vancs</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_parents</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">)))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">minimize_nodes</span> <span class="o">=</span> <span class="n">vancs</span>

            <span class="c1"># print(Zprime2) </span>
            <span class="c1"># Next remove all nodes that have no unique path to Y given Oset_min</span>
            <span class="c1"># Z = Zprime2</span>
            <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">minimize_nodes</span><span class="p">:</span>

                <span class="n">path</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span><span class="c1">#graph=self.graph, </span>
                    <span class="n">start</span><span class="o">=</span><span class="p">[</span><span class="n">node</span><span class="p">],</span> <span class="n">end</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">,</span> 
                    <span class="n">conditions</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">vancs</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">node</span><span class="p">]))</span> <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">),</span>
                    <span class="p">)</span>

                <span class="k">if</span> <span class="n">path</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
                   <span class="n">vancs</span> <span class="o">=</span> <span class="n">vancs</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">node</span><span class="p">])</span>  

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_validity</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">vancs</span><span class="p">))</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">False</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">list</span><span class="p">(</span><span class="n">vancs</span><span class="p">)</span>


    <span class="k">def</span><span class="w"> </span><span class="nf">_get_all_valid_adjustment_sets</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
        <span class="n">check_one_set_exists</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">yield_index</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Constructs all valid adjustment sets or just checks whether one exists.</span>
<span class="sd">        </span>
<span class="sd">        See van der Zander, B.; Liśkiewicz, M. &amp; Textor, J.</span>
<span class="sd">        Separators and adjustment sets in causal graphs: Complete </span>
<span class="sd">        criteria and an algorithmic framework </span>
<span class="sd">        Artificial Intelligence, Elsevier, 2019, 270, 1-40</span>

<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">cond_set</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">S</span><span class="p">)</span>
        <span class="n">all_vars</span> <span class="o">=</span> <span class="p">[(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
                    <span class="k">for</span> <span class="n">tau</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)]</span>

        <span class="n">all_vars_set</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">all_vars</span><span class="p">)</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">forbidden_nodes</span>


        <span class="k">def</span><span class="w"> </span><span class="nf">find_sep</span><span class="p">(</span><span class="n">I</span><span class="p">,</span> <span class="n">R</span><span class="p">):</span>
            <span class="n">Rprime</span> <span class="o">=</span> <span class="n">R</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span>
            <span class="c1"># TODO: anteriors and NOT ancestors where</span>
            <span class="c1"># anteriors include --- links in causal paths</span>
            <span class="c1"># print(I)</span>
            <span class="n">XYI</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">I</span><span class="p">))</span>
            <span class="c1"># print(XYI)</span>
            <span class="n">ancs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_ancestors</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">XYI</span><span class="p">))</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="n">ancs</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="n">Rprime</span><span class="p">)</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_validity</span><span class="p">(</span><span class="n">Z</span><span class="p">)</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
                <span class="k">return</span> <span class="kc">False</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">Z</span>


        <span class="k">def</span><span class="w"> </span><span class="nf">list_sep</span><span class="p">(</span><span class="n">I</span><span class="p">,</span> <span class="n">R</span><span class="p">):</span>
            <span class="c1"># print(find_sep(X, Y, I, R))</span>
            <span class="k">if</span> <span class="n">find_sep</span><span class="p">(</span><span class="n">I</span><span class="p">,</span> <span class="n">R</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">False</span><span class="p">:</span>
                <span class="c1"># print(I,R)</span>
                <span class="k">if</span> <span class="n">I</span> <span class="o">==</span> <span class="n">R</span><span class="p">:</span> 
                    <span class="c1"># print(&#39;---&gt;&#39;, I)</span>
                    <span class="k">yield</span> <span class="n">I</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="c1"># Pick arbitrary node from R-I</span>
                    <span class="n">RminusI</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">R</span> <span class="o">-</span> <span class="n">I</span><span class="p">)</span>
                    <span class="c1"># print(R, I, RminusI)</span>
                    <span class="n">v</span> <span class="o">=</span> <span class="n">RminusI</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
                    <span class="c1"># print(&quot;here &quot;, X, Y, I.union(set([v])), R)</span>
                    <span class="k">yield from</span> <span class="n">list_sep</span><span class="p">(</span><span class="n">I</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">v</span><span class="p">])),</span> <span class="n">R</span><span class="p">)</span>
                    <span class="k">yield from</span> <span class="n">list_sep</span><span class="p">(</span><span class="n">I</span><span class="p">,</span> <span class="n">R</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">v</span><span class="p">]))</span>

        <span class="c1"># print(&quot;all &quot;, X, Y, cond_set, all_vars_set)</span>
        <span class="n">all_sets</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">I</span> <span class="o">=</span> <span class="n">cond_set</span>
        <span class="n">R</span> <span class="o">=</span> <span class="n">all_vars_set</span>
        <span class="k">for</span> <span class="n">index</span><span class="p">,</span> <span class="n">valid_set</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">list_sep</span><span class="p">(</span><span class="n">I</span><span class="p">,</span> <span class="n">R</span><span class="p">)):</span>
            <span class="c1"># print(valid_set)</span>
            <span class="n">all_sets</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">valid_set</span><span class="p">))</span>
            <span class="k">if</span> <span class="n">check_one_set_exists</span> <span class="ow">and</span> <span class="n">index</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">break</span>

            <span class="k">if</span> <span class="n">yield_index</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">index</span> <span class="o">==</span> <span class="n">yield_index</span><span class="p">:</span>
                <span class="k">return</span> <span class="n">valid_set</span>

        <span class="k">if</span> <span class="n">yield_index</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span>

        <span class="k">if</span> <span class="n">check_one_set_exists</span><span class="p">:</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">all_sets</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">return</span> <span class="kc">True</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">return</span> <span class="kc">False</span>

        <span class="k">return</span> <span class="n">all_sets</span>


<div class="viewcode-block" id="CausalEffects.fit_total_effect">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.fit_total_effect">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">fit_total_effect</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
        <span class="n">dataframe</span><span class="p">,</span> 
        <span class="n">estimator</span><span class="p">,</span>
        <span class="n">adjustment_set</span><span class="o">=</span><span class="s1">&#39;optimal&#39;</span><span class="p">,</span>
        <span class="n">conditional_estimator</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>  
        <span class="n">data_transform</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">mask_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">ignore_identifiability</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns a fitted model for the total causal effect of X on Y </span>
<span class="sd">           conditional on S.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        dataframe : data object</span>
<span class="sd">            Tigramite dataframe object. It must have the attributes dataframe.values</span>
<span class="sd">            yielding a numpy array of shape (observations T, variables N) and</span>
<span class="sd">            optionally a mask of the same shape and a missing values flag.</span>
<span class="sd">        estimator : sklearn model object</span>
<span class="sd">            For example, sklearn.linear_model.LinearRegression() for a linear</span>
<span class="sd">            regression model.</span>
<span class="sd">        adjustment_set : str or list of tuples</span>
<span class="sd">            If &#39;optimal&#39; the Oset is used, if &#39;minimized_optimal&#39; the minimized Oset,</span>
<span class="sd">            and if &#39;colliders_minimized_optimal&#39;, the colliders-minimized Oset.</span>
<span class="sd">            If a list of tuples is passed, this set is used.</span>
<span class="sd">        conditional_estimator : sklearn model object, optional (default: None)</span>
<span class="sd">            Used to fit conditional causal effects in nested regression. </span>
<span class="sd">            If None, the same model as for estimator is used.</span>
<span class="sd">        data_transform : sklearn preprocessing object, optional (default: None)</span>
<span class="sd">            Used to transform data prior to fitting. For example,</span>
<span class="sd">            sklearn.preprocessing.StandardScaler for simple standardization. The</span>
<span class="sd">            fitted parameters are stored.</span>
<span class="sd">        mask_type : {None, &#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">            Masking mode: Indicators for which variables in the dependence</span>
<span class="sd">            measure I(X; Y | Z) the samples should be masked. If None, the mask</span>
<span class="sd">            is not used. Explained in tutorial on masking and missing values.</span>
<span class="sd">        ignore_identifiability : bool</span>
<span class="sd">            Only applies to adjustment sets supplied by user. Ignores if that </span>
<span class="sd">            set leads to a non-identifiable effect.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">no_causal_path</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No causal path from X to Y exists.&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="bp">self</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span> <span class="o">=</span> <span class="n">dataframe</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">conditional_estimator</span> <span class="o">=</span> <span class="n">conditional_estimator</span>

        <span class="c1"># if self.dataframe.has_vector_data:</span>
        <span class="c1">#     raise ValueError(&quot;vector_vars in DataFrame cannot be used together with CausalEffects!&quot;</span>
        <span class="c1">#                      &quot; You can estimate vector-valued effects by using multivariate X, Y, S.&quot;</span>
        <span class="c1">#                      &quot; Note, however, that this requires assuming a graph at the level &quot;</span>
        <span class="c1">#                      &quot;of the components of X, Y, S, ...&quot;)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span> <span class="o">!=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">N</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Dataset dimensions inconsistent with number of variables in graph.&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">adjustment_set</span> <span class="o">==</span> <span class="s1">&#39;optimal&#39;</span><span class="p">:</span>
            <span class="c1"># Check optimality and use either optimal or colliders_only set</span>
            <span class="n">adjustment_set</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_optimal_set</span><span class="p">()</span>
        <span class="k">elif</span> <span class="n">adjustment_set</span> <span class="o">==</span> <span class="s1">&#39;colliders_minimized_optimal&#39;</span><span class="p">:</span>
            <span class="n">adjustment_set</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_optimal_set</span><span class="p">(</span><span class="n">minimize</span><span class="o">=</span><span class="s1">&#39;colliders_only&#39;</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">adjustment_set</span> <span class="o">==</span> <span class="s1">&#39;minimized_optimal&#39;</span><span class="p">:</span>
            <span class="n">adjustment_set</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_optimal_set</span><span class="p">(</span><span class="n">minimize</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">ignore_identifiability</span> <span class="ow">is</span> <span class="kc">False</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_validity</span><span class="p">(</span><span class="n">adjustment_set</span><span class="p">)</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Chosen adjustment_set is not valid.&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">adjustment_set</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Causal effect not identifiable via adjustment.&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">adjustment_set</span> <span class="o">=</span> <span class="n">adjustment_set</span>

        <span class="c1"># Fit model of Y on X and Z (and conditions)</span>
        <span class="c1"># Build the model</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span> <span class="o">=</span> <span class="n">Models</span><span class="p">(</span>
                        <span class="n">dataframe</span><span class="o">=</span><span class="n">dataframe</span><span class="p">,</span>
                        <span class="n">model</span><span class="o">=</span><span class="n">estimator</span><span class="p">,</span>
                        <span class="n">conditional_model</span><span class="o">=</span><span class="n">conditional_estimator</span><span class="p">,</span>
                        <span class="n">data_transform</span><span class="o">=</span><span class="n">data_transform</span><span class="p">,</span>
                        <span class="n">mask_type</span><span class="o">=</span><span class="n">mask_type</span><span class="p">,</span>
                        <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>      

        <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">get_general_fitted_model</span><span class="p">(</span>
                <span class="n">Y</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">,</span> <span class="n">X</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">listX</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">adjustment_set</span><span class="p">),</span>
                <span class="n">conditions</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">listS</span><span class="p">,</span>
                <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;tau_max&#39;</span><span class="p">,</span>
                <span class="n">return_data</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>

        <span class="k">return</span> <span class="bp">self</span></div>


<div class="viewcode-block" id="CausalEffects.predict_total_effect">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.predict_total_effect">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">predict_total_effect</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
        <span class="n">intervention_data</span><span class="p">,</span> 
        <span class="n">conditions_data</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">pred_params</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">return_further_pred_results</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="n">aggregation_func</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">,</span>
        <span class="n">transform_interventions_and_prediction</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="n">intervention_type</span><span class="o">=</span><span class="s1">&#39;hard&#39;</span><span class="p">,</span>
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Predict effect of intervention with fitted model.</span>

<span class="sd">        Uses the model.predict() function of the sklearn model.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        intervention_data : numpy array</span>
<span class="sd">            Numpy array of shape (n_interventions, len(X)) that contains the do(X) values.</span>
<span class="sd">        conditions_data : data object, optional</span>
<span class="sd">            Numpy array of shape (n_interventions, len(S)) that contains the S=s values.</span>
<span class="sd">        pred_params : dict, optional</span>
<span class="sd">            Optional parameters passed on to sklearn prediction function.</span>
<span class="sd">        return_further_pred_results : bool, optional (default: False)</span>
<span class="sd">            In case the predictor class returns more than just the expected value,</span>
<span class="sd">            the entire results can be returned.</span>
<span class="sd">        aggregation_func : callable</span>
<span class="sd">            Callable applied to output of &#39;predict&#39;. Default is &#39;np.mean&#39;.</span>
<span class="sd">        transform_interventions_and_prediction : bool (default: False)</span>
<span class="sd">            Whether to perform the inverse data_transform on prediction results.</span>
<span class="sd">        intervention_type : {&#39;hard&#39;, &#39;soft&#39;}</span>
<span class="sd">            Specify whether intervention is &#39;hard&#39; (set value) or &#39;soft&#39; </span>
<span class="sd">            (add value to observed data).</span>
<span class="sd">        </span>
<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Results from prediction: an array of shape  (n_interventions, len(Y)).</span>
<span class="sd">        If estimate_confidence = True, then a tuple is returned.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">get_vectorized_length</span><span class="p">(</span><span class="n">W</span><span class="p">):</span>
            <span class="k">return</span> <span class="nb">sum</span><span class="p">([</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">vector_vars</span><span class="p">[</span><span class="n">w</span><span class="p">[</span><span class="mi">0</span><span class="p">]])</span> <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">W</span><span class="p">])</span>

        <span class="c1"># lenX = len(self.listX)</span>
        <span class="c1"># lenS = len(self.listS)</span>

        <span class="n">lenX</span> <span class="o">=</span> <span class="n">get_vectorized_length</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listX</span><span class="p">)</span>
        <span class="n">lenS</span> <span class="o">=</span> <span class="n">get_vectorized_length</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listS</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">intervention_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">!=</span> <span class="n">lenX</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;intervention_data.shape[1] must be len(X).&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">intervention_type</span> <span class="ow">not</span> <span class="ow">in</span> <span class="p">{</span><span class="s1">&#39;hard&#39;</span><span class="p">,</span> <span class="s1">&#39;soft&#39;</span><span class="p">}:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;intervention_type must be &#39;hard&#39; or &#39;soft&#39;.&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">conditions_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">lenS</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">conditions_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">!=</span> <span class="n">lenS</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;conditions_data.shape[1] must be len(S).&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">conditions_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">!=</span> <span class="n">intervention_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;conditions_data.shape[0] must match intervention_data.shape[0].&quot;</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">conditions_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">lenS</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;conditions_data specified, but S=None or empty.&quot;</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">conditions_data</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">lenS</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;S specified, but conditions_data is None.&quot;</span><span class="p">)</span>


        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">no_causal_path</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No causal path from X to Y exists.&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="nb">len</span><span class="p">(</span><span class="n">intervention_data</span><span class="p">),</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">)))</span>

        <span class="n">effect</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">get_general_prediction</span><span class="p">(</span>
            <span class="n">intervention_data</span><span class="o">=</span><span class="n">intervention_data</span><span class="p">,</span>
            <span class="n">conditions_data</span><span class="o">=</span><span class="n">conditions_data</span><span class="p">,</span>
            <span class="n">pred_params</span><span class="o">=</span><span class="n">pred_params</span><span class="p">,</span>
            <span class="n">return_further_pred_results</span><span class="o">=</span><span class="n">return_further_pred_results</span><span class="p">,</span>
            <span class="n">transform_interventions_and_prediction</span><span class="o">=</span><span class="n">transform_interventions_and_prediction</span><span class="p">,</span>
            <span class="n">aggregation_func</span><span class="o">=</span><span class="n">aggregation_func</span><span class="p">,</span>
            <span class="n">intervention_type</span><span class="o">=</span><span class="n">intervention_type</span><span class="p">,)</span> 

        <span class="k">return</span> <span class="n">effect</span></div>


<div class="viewcode-block" id="CausalEffects.fit_wright_effect">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.fit_wright_effect">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">fit_wright_effect</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
        <span class="n">dataframe</span><span class="p">,</span> 
        <span class="n">mediation</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">method</span><span class="o">=</span><span class="s1">&#39;parents&#39;</span><span class="p">,</span>
        <span class="n">links_coeffs</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>  
        <span class="n">data_transform</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">mask_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns a fitted model for the total or mediated causal effect of X on Y </span>
<span class="sd">           potentially through mediator variables.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        dataframe : data object</span>
<span class="sd">            Tigramite dataframe object. It must have the attributes dataframe.values</span>
<span class="sd">            yielding a numpy array of shape (observations T, variables N) and</span>
<span class="sd">            optionally a mask of the same shape and a missing values flag.</span>
<span class="sd">        mediation : None, &#39;direct&#39;, or list of tuples</span>
<span class="sd">            If None, total effect is estimated, if &#39;direct&#39; then only the direct effect is estimated,</span>
<span class="sd">            else only those causal paths are considerd that pass at least through one of these mediator nodes.</span>
<span class="sd">        method : {&#39;parents&#39;, &#39;links_coeffs&#39;, &#39;optimal&#39;}</span>
<span class="sd">            Method to use for estimating Wright&#39;s path coefficients. If &#39;optimal&#39;, </span>
<span class="sd">            the Oset is used, if &#39;links_coeffs&#39;, the coefficients in links_coeffs are used,</span>
<span class="sd">            if &#39;parents&#39;, the parents are used (only valid for DAGs).</span>
<span class="sd">        links_coeffs : dict</span>
<span class="sd">            Only used if method = &#39;links_coeffs&#39;.</span>
<span class="sd">            Dictionary of format: {0:[((i, -tau), coeff),...], 1:[...],</span>
<span class="sd">            ...} for all variables where i must be in [0..N-1] and tau &gt;= 0 with</span>
<span class="sd">            number of variables N. coeff must be a float.</span>
<span class="sd">        data_transform : None</span>
<span class="sd">            Not implemented for Wright estimator. Complicated for missing samples.</span>
<span class="sd">        mask_type : {None, &#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">            Masking mode: Indicators for which variables in the dependence</span>
<span class="sd">            measure I(X; Y | Z) the samples should be masked. If None, the mask</span>
<span class="sd">            is not used. Explained in tutorial on masking and missing values.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">no_causal_path</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No causal path from X to Y exists.&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="bp">self</span>

        <span class="k">if</span> <span class="n">data_transform</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;data_transform not implemented for Wright estimator.&quot;</span>
                             <span class="s2">&quot; You can preprocess data yourself beforehand.&quot;</span><span class="p">)</span>

        <span class="kn">import</span><span class="w"> </span><span class="nn">sklearn.linear_model</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span> <span class="o">=</span> <span class="n">dataframe</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">has_vector_data</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;vector_vars in DataFrame cannot be used together with Wright method!&quot;</span>
                             <span class="s2">&quot; You can either 1) estimate vector-valued effects by using multivariate (X, Y, S)&quot;</span>
                             <span class="s2">&quot; together with assuming a graph at the level of the components of (X, Y, S), &quot;</span>
                             <span class="s2">&quot; or 2) use vector_vars together with fit_total_effect and an estimator&quot;</span>
                             <span class="s2">&quot; that supports multiple outputs.&quot;</span><span class="p">)</span>

        <span class="n">estimator</span> <span class="o">=</span> <span class="n">sklearn</span><span class="o">.</span><span class="n">linear_model</span><span class="o">.</span><span class="n">LinearRegression</span><span class="p">()</span>

        <span class="c1"># Fit model of Y on X and Z (and conditions)</span>
        <span class="c1"># Build the model</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span> <span class="o">=</span> <span class="n">Models</span><span class="p">(</span>
                        <span class="n">dataframe</span><span class="o">=</span><span class="n">dataframe</span><span class="p">,</span>
                        <span class="n">model</span><span class="o">=</span><span class="n">estimator</span><span class="p">,</span>
                        <span class="n">data_transform</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="c1">#data_transform,</span>
                        <span class="n">mask_type</span><span class="o">=</span><span class="n">mask_type</span><span class="p">,</span>
                        <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>

        <span class="n">mediators</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span>  <span class="c1"># self.get_mediators(start=self.X, end=self.Y)</span>

        <span class="k">if</span> <span class="n">mediation</span> <span class="o">==</span> <span class="s1">&#39;direct&#39;</span><span class="p">:</span>
            <span class="n">causal_paths</span> <span class="o">=</span> <span class="p">{}</span>         
            <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">:</span>
                <span class="n">causal_paths</span><span class="p">[</span><span class="n">w</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
                <span class="k">for</span> <span class="n">z</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">w</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_parents</span><span class="p">(</span><span class="n">z</span><span class="p">):</span>
                        <span class="n">causal_paths</span><span class="p">[</span><span class="n">w</span><span class="p">][</span><span class="n">z</span><span class="p">]</span> <span class="o">=</span> <span class="p">[[</span><span class="n">w</span><span class="p">,</span> <span class="n">z</span><span class="p">]]</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="n">causal_paths</span><span class="p">[</span><span class="n">w</span><span class="p">][</span><span class="n">z</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">causal_paths</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_causal_paths</span><span class="p">(</span><span class="n">source_nodes</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">,</span> 
                <span class="n">target_nodes</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">,</span> <span class="n">mediators</span><span class="o">=</span><span class="n">mediators</span><span class="p">,</span> 
                <span class="n">mediated_through</span><span class="o">=</span><span class="n">mediation</span><span class="p">,</span> <span class="n">proper_paths</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">method</span> <span class="o">==</span> <span class="s1">&#39;links_coeffs&#39;</span><span class="p">:</span>
            <span class="n">coeffs</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="k">for</span> <span class="n">medy</span> <span class="ow">in</span> <span class="p">[</span><span class="n">med</span> <span class="k">for</span> <span class="n">med</span> <span class="ow">in</span> <span class="n">mediators</span><span class="p">]</span> <span class="o">+</span> <span class="p">[</span><span class="n">y</span> <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">]:</span>
                <span class="n">coeffs</span><span class="p">[</span><span class="n">medy</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
                <span class="n">j</span><span class="p">,</span> <span class="n">tauj</span> <span class="o">=</span> <span class="n">medy</span>
                <span class="k">for</span> <span class="n">ipar</span><span class="p">,</span> <span class="n">par_coeff</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">links_coeffs</span><span class="p">[</span><span class="n">medy</span><span class="p">[</span><span class="mi">0</span><span class="p">]]):</span>
                    <span class="n">par</span><span class="p">,</span> <span class="n">coeff</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">par_coeff</span>
                    <span class="n">i</span><span class="p">,</span> <span class="n">taui</span> <span class="o">=</span> <span class="n">par</span>
                    <span class="n">taui_shifted</span> <span class="o">=</span> <span class="n">taui</span> <span class="o">+</span> <span class="n">tauj</span>
                    <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="nb">abs</span><span class="p">(</span><span class="n">par</span><span class="p">[</span><span class="mi">1</span><span class="p">]),</span> <span class="n">max_lag</span><span class="p">)</span>
                    <span class="n">coeffs</span><span class="p">[</span><span class="n">medy</span><span class="p">][(</span><span class="n">i</span><span class="p">,</span> <span class="n">taui_shifted</span><span class="p">)]</span> <span class="o">=</span> <span class="n">coeff</span> <span class="c1">#self.fit_results[j][(j, 0)][&#39;model&#39;].coef_[ipar]</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="n">max_lag</span>
            <span class="c1"># print(coeffs)</span>

        <span class="k">elif</span> <span class="n">method</span> <span class="o">==</span> <span class="s1">&#39;optimal&#39;</span><span class="p">:</span>
            <span class="c1"># all_parents = {}</span>
            <span class="n">coeffs</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="k">for</span> <span class="n">medy</span> <span class="ow">in</span> <span class="p">[</span><span class="n">med</span> <span class="k">for</span> <span class="n">med</span> <span class="ow">in</span> <span class="n">mediators</span><span class="p">]</span> <span class="o">+</span> <span class="p">[</span><span class="n">y</span> <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">]:</span>
                <span class="n">coeffs</span><span class="p">[</span><span class="n">medy</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
                <span class="n">mediator_parents</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_parents</span><span class="p">([</span><span class="n">medy</span><span class="p">])</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="n">mediators</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">))</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">medy</span><span class="p">])</span>
                <span class="n">all_parents</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_parents</span><span class="p">([</span><span class="n">medy</span><span class="p">])</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">medy</span><span class="p">])</span>
                <span class="k">for</span> <span class="n">par</span> <span class="ow">in</span> <span class="n">mediator_parents</span><span class="p">:</span>
                    <span class="n">Sprime</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">all_parents</span><span class="p">)</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">par</span><span class="p">,</span> <span class="n">medy</span><span class="p">])</span>
                    <span class="n">causal_effects</span> <span class="o">=</span> <span class="n">CausalEffects</span><span class="p">(</span><span class="n">graph</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="p">,</span> 
                                        <span class="n">X</span><span class="o">=</span><span class="p">[</span><span class="n">par</span><span class="p">],</span> <span class="n">Y</span><span class="o">=</span><span class="p">[</span><span class="n">medy</span><span class="p">],</span> <span class="n">S</span><span class="o">=</span><span class="n">Sprime</span><span class="p">,</span>
                                        <span class="n">graph_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span><span class="p">,</span>
                                        <span class="n">check_SM_overlap</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                        <span class="p">)</span>
                    <span class="n">oset</span> <span class="o">=</span> <span class="n">causal_effects</span><span class="o">.</span><span class="n">get_optimal_set</span><span class="p">()</span>
                    <span class="c1"># print(medy, par, list(set(all_parents)), oset)</span>
                    <span class="k">if</span> <span class="n">oset</span> <span class="ow">is</span> <span class="kc">False</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Not identifiable via Wright&#39;s method.&quot;</span><span class="p">)</span>
                    <span class="n">fit_res</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">get_general_fitted_model</span><span class="p">(</span>
                        <span class="n">Y</span><span class="o">=</span><span class="p">[</span><span class="n">medy</span><span class="p">],</span> <span class="n">X</span><span class="o">=</span><span class="p">[</span><span class="n">par</span><span class="p">],</span> <span class="n">Z</span><span class="o">=</span><span class="n">oset</span><span class="p">,</span>
                        <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                        <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;tau_max&#39;</span><span class="p">,</span>
                        <span class="n">return_data</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
                    <span class="n">coeffs</span><span class="p">[</span><span class="n">medy</span><span class="p">][</span><span class="n">par</span><span class="p">]</span> <span class="o">=</span> <span class="n">fit_res</span><span class="p">[</span><span class="s1">&#39;model&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">coef_</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="k">elif</span> <span class="n">method</span> <span class="o">==</span> <span class="s1">&#39;parents&#39;</span><span class="p">:</span>
            <span class="n">coeffs</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="k">for</span> <span class="n">medy</span> <span class="ow">in</span> <span class="p">[</span><span class="n">med</span> <span class="k">for</span> <span class="n">med</span> <span class="ow">in</span> <span class="n">mediators</span><span class="p">]</span> <span class="o">+</span> <span class="p">[</span><span class="n">y</span> <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">]:</span>
                <span class="n">coeffs</span><span class="p">[</span><span class="n">medy</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
                <span class="c1"># mediator_parents = self._get_all_parents([medy]).intersection(mediators.union(self.X)) - set([medy])</span>
                <span class="n">all_parents</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_parents</span><span class="p">([</span><span class="n">medy</span><span class="p">])</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">medy</span><span class="p">])</span>
                <span class="k">if</span> <span class="s1">&#39;dag&#39;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span><span class="p">:</span>
                    <span class="n">spouses</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_spouses</span><span class="p">([</span><span class="n">medy</span><span class="p">])</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="n">medy</span><span class="p">])</span>
                    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">spouses</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;method == &#39;parents&#39; only possible for &quot;</span>
                                         <span class="s2">&quot;causal paths without adjacent bi-directed links!&quot;</span><span class="p">)</span>

                <span class="c1"># print(j, all_parents[j])</span>
                <span class="c1"># if len(all_parents[j]) &gt; 0:</span>
                <span class="c1"># print(medy, list(all_parents))</span>
                <span class="n">fit_res</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">get_general_fitted_model</span><span class="p">(</span>
                    <span class="n">Y</span><span class="o">=</span><span class="p">[</span><span class="n">medy</span><span class="p">],</span> <span class="n">X</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">all_parents</span><span class="p">),</span> <span class="n">Z</span><span class="o">=</span><span class="p">[],</span>
                    <span class="n">conditions</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                    <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                    <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;tau_max&#39;</span><span class="p">,</span>
                    <span class="n">return_data</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>

                <span class="k">for</span> <span class="n">ipar</span><span class="p">,</span> <span class="n">par</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">all_parents</span><span class="p">)):</span>
                    <span class="c1"># print(par, fit_res[&#39;model&#39;].coef_)</span>
                    <span class="n">coeffs</span><span class="p">[</span><span class="n">medy</span><span class="p">][</span><span class="n">par</span><span class="p">]</span> <span class="o">=</span> <span class="n">fit_res</span><span class="p">[</span><span class="s1">&#39;model&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">coef_</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="n">ipar</span><span class="p">]</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;method must be &#39;optimal&#39;, &#39;links_coeffs&#39;, or &#39;parents&#39;.&quot;</span><span class="p">)</span>
        
        <span class="c1"># Effect is sum over products over all path coefficients</span>
        <span class="c1"># from x in X to y in Y</span>
        <span class="n">effect</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span> <span class="ow">in</span> <span class="n">itertools</span><span class="o">.</span><span class="n">product</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listX</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">):</span>
            <span class="n">effect</span><span class="p">[(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)]</span> <span class="o">=</span> <span class="mf">0.</span>
            <span class="k">for</span> <span class="n">causal_path</span> <span class="ow">in</span> <span class="n">causal_paths</span><span class="p">[</span><span class="n">x</span><span class="p">][</span><span class="n">y</span><span class="p">]:</span>
                <span class="n">effect_here</span> <span class="o">=</span> <span class="mf">1.</span>
                <span class="c1"># print(x, y, causal_path)</span>
                <span class="k">for</span> <span class="n">index</span><span class="p">,</span> <span class="n">node</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">causal_path</span><span class="p">[:</span><span class="o">-</span><span class="mi">1</span><span class="p">]):</span>
                    <span class="n">i</span><span class="p">,</span> <span class="n">taui</span> <span class="o">=</span> <span class="n">node</span>
                    <span class="n">j</span><span class="p">,</span> <span class="n">tauj</span> <span class="o">=</span> <span class="n">causal_path</span><span class="p">[</span><span class="n">index</span> <span class="o">+</span> <span class="mi">1</span><span class="p">]</span>
                    <span class="c1"># tau_ij = abs(tauj - taui)</span>
                    <span class="c1"># print((j, tauj), (i, taui))</span>
                    <span class="n">effect_here</span> <span class="o">*=</span> <span class="n">coeffs</span><span class="p">[(</span><span class="n">j</span><span class="p">,</span> <span class="n">tauj</span><span class="p">)][(</span><span class="n">i</span><span class="p">,</span> <span class="n">taui</span><span class="p">)]</span>

                <span class="n">effect</span><span class="p">[(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)]</span> <span class="o">+=</span> <span class="n">effect_here</span>
               
        <span class="c1"># Make fitted coefficients available as attribute</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">coeffs</span> <span class="o">=</span> <span class="n">coeffs</span>

        <span class="c1"># Modify and overwrite variables in self.model</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">Y</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">listY</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">listX</span>  
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">Z</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">conditions</span> <span class="o">=</span> <span class="p">[]</span> 
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">cut_off</span> <span class="o">=</span> <span class="s1">&#39;tau_max&#39;</span> <span class="c1"># &#39;max_lag_or_tau_max&#39;</span>

        <span class="k">class</span><span class="w"> </span><span class="nc">dummy_fit_class</span><span class="p">():</span>
            <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">y_here</span><span class="p">,</span> <span class="n">listX_here</span><span class="p">,</span> <span class="n">effect_here</span><span class="p">):</span>
                <span class="n">dim</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">listX_here</span><span class="p">)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">coeff_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="n">effect_here</span><span class="p">[(</span><span class="n">x</span><span class="p">,</span> <span class="n">y_here</span><span class="p">)]</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">listX_here</span><span class="p">])</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">dim</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
            <span class="k">def</span><span class="w"> </span><span class="nf">predict</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">):</span>
                <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">dot</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">coeff_array</span><span class="p">)</span><span class="o">.</span><span class="n">squeeze</span><span class="p">()</span>

        <span class="n">fit_results</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">:</span>
            <span class="n">fit_results</span><span class="p">[</span><span class="n">y</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="n">fit_results</span><span class="p">[</span><span class="n">y</span><span class="p">][</span><span class="s1">&#39;model&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">dummy_fit_class</span><span class="p">(</span><span class="n">y</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">listX</span><span class="p">,</span> <span class="n">effect</span><span class="p">)</span>
            <span class="n">fit_results</span><span class="p">[</span><span class="n">y</span><span class="p">][</span><span class="s1">&#39;data_transform&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">data_transform</span><span class="p">)</span>

        <span class="c1"># self.effect = effect</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">fit_results</span> <span class="o">=</span> <span class="n">fit_results</span>
        <span class="k">return</span> <span class="bp">self</span></div>

 
<div class="viewcode-block" id="CausalEffects.predict_wright_effect">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.predict_wright_effect">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">predict_wright_effect</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
        <span class="n">intervention_data</span><span class="p">,</span> 
        <span class="n">pred_params</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Predict linear effect of intervention with fitted Wright-model.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        intervention_data : numpy array</span>
<span class="sd">            Numpy array of shape (n_interventions, len(X)) that contains the do(X) values.</span>
<span class="sd">        pred_params : dict, optional</span>
<span class="sd">            Optional parameters passed on to sklearn prediction function.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Results from prediction: an array of shape  (n_interventions, len(Y)).</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">lenX</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listX</span><span class="p">)</span>
        <span class="n">lenY</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">intervention_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">!=</span> <span class="n">lenX</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;intervention_data.shape[1] must be len(X).&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">no_causal_path</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;No causal path from X to Y exists.&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="nb">len</span><span class="p">(</span><span class="n">intervention_data</span><span class="p">),</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">)))</span>

        <span class="n">n_interventions</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">intervention_data</span><span class="o">.</span><span class="n">shape</span>


        <span class="n">predicted_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">n_interventions</span><span class="p">,</span> <span class="n">lenY</span><span class="p">))</span>
        <span class="n">pred_dict</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">iy</span><span class="p">,</span> <span class="n">y</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">):</span>
            <span class="c1"># Print message</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">## Predicting target </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">y</span><span class="p">))</span>
                <span class="k">if</span> <span class="n">pred_params</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">key</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">pred_params</span><span class="p">):</span>
                        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">pred_params</span><span class="p">[</span><span class="n">key</span><span class="p">]))</span>
            <span class="c1"># Default value for pred_params</span>
            <span class="k">if</span> <span class="n">pred_params</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">pred_params</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="c1"># Check this is a valid target</span>
            <span class="k">if</span> <span class="n">y</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">fit_results</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;y = </span><span class="si">%s</span><span class="s2"> not yet fitted&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">y</span><span class="p">))</span>

            <span class="c1"># data_transform is too complicated for Wright estimator</span>
            <span class="c1"># Transform the data if needed</span>
            <span class="c1"># fitted_data_transform = self.model.fit_results[y][&#39;fitted_data_transform&#39;]</span>
            <span class="c1"># if fitted_data_transform is not None:</span>
            <span class="c1">#     intervention_data = fitted_data_transform[&#39;X&#39;].transform(X=intervention_data)</span>

            <span class="c1"># Now iterate through interventions (and potentially S)</span>
            <span class="k">for</span> <span class="n">index</span><span class="p">,</span> <span class="n">dox_vals</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">intervention_data</span><span class="p">):</span>
                <span class="c1"># Construct XZS-array</span>
                <span class="n">intervention_array</span> <span class="o">=</span> <span class="n">dox_vals</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">lenX</span><span class="p">)</span> 
                <span class="n">predictor_array</span> <span class="o">=</span> <span class="n">intervention_array</span>

                <span class="n">predicted_vals</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="n">y</span><span class="p">][</span><span class="s1">&#39;model&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">predict</span><span class="p">(</span>
                <span class="n">X</span><span class="o">=</span><span class="n">predictor_array</span><span class="p">,</span> <span class="o">**</span><span class="n">pred_params</span><span class="p">)</span>
                <span class="n">predicted_array</span><span class="p">[</span><span class="n">index</span><span class="p">,</span> <span class="n">iy</span><span class="p">]</span> <span class="o">=</span> <span class="n">predicted_vals</span><span class="o">.</span><span class="n">mean</span><span class="p">()</span>

                <span class="c1"># data_transform is too complicated for Wright estimator</span>
                <span class="c1"># if fitted_data_transform is not None:</span>
                <span class="c1">#     rescaled = fitted_data_transform[&#39;Y&#39;].inverse_transform(X=predicted_array[index, iy].reshape(-1, 1))</span>
                <span class="c1">#     predicted_array[index, iy] = rescaled.squeeze()</span>

        <span class="k">return</span> <span class="n">predicted_array</span></div>



<div class="viewcode-block" id="CausalEffects.fit_bootstrap_of">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.fit_bootstrap_of">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">fit_bootstrap_of</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">method</span><span class="p">,</span> <span class="n">method_args</span><span class="p">,</span> 
                        <span class="n">boot_samples</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
                        <span class="n">boot_blocklength</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                        <span class="n">seed</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Runs chosen method on bootstrap samples drawn from DataFrame.</span>
<span class="sd">        </span>
<span class="sd">        Bootstraps for tau=0 are drawn from [max_lag, ..., T] and all lagged</span>
<span class="sd">        variables constructed in DataFrame.construct_array are consistently</span>
<span class="sd">        shifted with respect to this bootsrap sample to ensure that lagged</span>
<span class="sd">        relations in the bootstrap sample are preserved.</span>

<span class="sd">        This function fits the models, predict_bootstrap_of can then be used</span>
<span class="sd">        to get confidence intervals for the effect of interventions.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        method : str</span>
<span class="sd">            Chosen method among valid functions in this class.</span>
<span class="sd">        method_args : dict</span>
<span class="sd">            Arguments passed to method.</span>
<span class="sd">        boot_samples : int</span>
<span class="sd">            Number of bootstrap samples to draw.</span>
<span class="sd">        boot_blocklength : int, optional (default: 1)</span>
<span class="sd">            Block length for block-bootstrap.</span>
<span class="sd">        seed : int, optional(default = None)</span>
<span class="sd">            Seed for RandomState (default_rng)</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="c1"># if dataframe.analysis_mode != &#39;single&#39;:</span>
        <span class="c1">#     raise ValueError(&quot;CausalEffects class currently only supports single &quot;</span>
        <span class="c1">#                      &quot;datasets.&quot;)</span>

        <span class="n">valid_methods</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;fit_total_effect&#39;</span><span class="p">,</span>
                         <span class="s1">&#39;fit_wright_effect&#39;</span><span class="p">,</span>
                          <span class="p">]</span>

        <span class="k">if</span> <span class="n">method</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">valid_methods</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;method must be one of </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">valid_methods</span><span class="p">))</span>

        <span class="c1"># First call the method on the original dataframe </span>
        <span class="c1"># to make available adjustment set etc</span>
        <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">method</span><span class="p">)(</span><span class="o">**</span><span class="n">method_args</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">original_model</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">## Running Bootstrap of </span><span class="si">%s</span><span class="s2"> &quot;</span> <span class="o">%</span> <span class="n">method</span> <span class="o">+</span>
                  <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">+</span>
                  <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">boot_samples = </span><span class="si">%s</span><span class="s2"> </span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">boot_samples</span> <span class="o">+</span>
                  <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">boot_blocklength = </span><span class="si">%s</span><span class="s2"> </span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">boot_blocklength</span>
                  <span class="p">)</span>

        <span class="n">method_args_bootstrap</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">method_args</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap_results</span> <span class="o">=</span> <span class="p">{}</span>

        <span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">boot_samples</span><span class="p">):</span>
            <span class="c1"># # Replace dataframe in method args by bootstrapped dataframe</span>
            <span class="c1"># method_args_bootstrap[&#39;dataframe&#39;].bootstrap = boot_draw</span>
            <span class="k">if</span> <span class="n">seed</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">random_state</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="kc">None</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">random_state</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="o">*</span><span class="n">boot_samples</span> <span class="o">+</span> <span class="n">b</span><span class="p">)</span>

            <span class="n">method_args_bootstrap</span><span class="p">[</span><span class="s1">&#39;dataframe&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">bootstrap</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;boot_blocklength&#39;</span><span class="p">:</span><span class="n">boot_blocklength</span><span class="p">,</span>
                                                            <span class="s1">&#39;random_state&#39;</span><span class="p">:</span><span class="n">random_state</span><span class="p">}</span>

            <span class="c1"># Call method and save fitted model</span>
            <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">method</span><span class="p">)(</span><span class="o">**</span><span class="n">method_args_bootstrap</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap_results</span><span class="p">[</span><span class="n">b</span><span class="p">]</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="p">)</span>

        <span class="c1"># Reset model</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">original_model</span>

        <span class="k">return</span> <span class="bp">self</span></div>



<div class="viewcode-block" id="CausalEffects.predict_bootstrap_of">
<a class="viewcode-back" href="../../index.html#tigramite.causal_effects.CausalEffects.predict_bootstrap_of">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">predict_bootstrap_of</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">method</span><span class="p">,</span> <span class="n">method_args</span><span class="p">,</span> 
                        <span class="n">conf_lev</span><span class="o">=</span><span class="mf">0.9</span><span class="p">,</span>
                        <span class="n">return_individual_bootstrap_results</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Predicts with fitted bootstraps.</span>

<span class="sd">        To be used after fitting with fit_bootstrap_of. Only uses the </span>
<span class="sd">        expected values of the predict function, not potential other output.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        method : str</span>
<span class="sd">            Chosen method among valid functions in this class.</span>
<span class="sd">        method_args : dict</span>
<span class="sd">            Arguments passed to method.</span>
<span class="sd">        conf_lev : float, optional (default: 0.9)</span>
<span class="sd">            Two-sided confidence interval.</span>
<span class="sd">        return_individual_bootstrap_results : bool</span>
<span class="sd">            Returns the individual bootstrap predictions.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        confidence_intervals : numpy array</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">valid_methods</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;predict_total_effect&#39;</span><span class="p">,</span>
                         <span class="s1">&#39;predict_wright_effect&#39;</span><span class="p">,</span>
                          <span class="p">]</span>

        <span class="k">if</span> <span class="n">method</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">valid_methods</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;method must be one of </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">valid_methods</span><span class="p">))</span>

        <span class="c1"># def get_vectorized_length(W):</span>
        <span class="c1">#     return sum([len(self.dataframe.vector_vars[w[0]]) for w in W])</span>

        <span class="n">lenX</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listX</span><span class="p">)</span>
        <span class="n">lenS</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listS</span><span class="p">)</span>
        <span class="n">lenY</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">listY</span><span class="p">)</span>

        <span class="n">n_interventions</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">method_args</span><span class="p">[</span><span class="s1">&#39;intervention_data&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">shape</span>

        <span class="n">boot_samples</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">bootstrap_results</span><span class="p">)</span>
        <span class="c1"># bootstrap_predicted_array = np.zeros((boot_samples, n_interventions, lenY))</span>
        
        <span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">boot_samples</span><span class="p">):</span> <span class="c1">#self.bootstrap_results.keys():</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">model</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap_results</span><span class="p">[</span><span class="n">b</span><span class="p">]</span>
            <span class="n">boot_effect</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">method</span><span class="p">)(</span><span class="o">**</span><span class="n">method_args</span><span class="p">)</span>

            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">boot_effect</span><span class="p">,</span> <span class="nb">tuple</span><span class="p">):</span>
                <span class="n">boot_effect</span> <span class="o">=</span> <span class="n">boot_effect</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
            
            <span class="k">if</span> <span class="n">b</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">bootstrap_predicted_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">boot_samples</span><span class="p">,</span> <span class="p">)</span> <span class="o">+</span> <span class="n">boot_effect</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span> 
                                            <span class="n">dtype</span><span class="o">=</span><span class="n">boot_effect</span><span class="o">.</span><span class="n">dtype</span><span class="p">)</span>
            <span class="n">bootstrap_predicted_array</span><span class="p">[</span><span class="n">b</span><span class="p">]</span> <span class="o">=</span> <span class="n">boot_effect</span>

        <span class="c1"># Reset model</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">original_model</span>

        <span class="c1"># Confidence intervals for val_matrix; interval is two-sided</span>
        <span class="n">c_int</span> <span class="o">=</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">conf_lev</span><span class="p">)</span><span class="o">/</span><span class="mf">2.</span><span class="p">)</span>
        <span class="n">confidence_interval</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">percentile</span><span class="p">(</span>
                <span class="n">bootstrap_predicted_array</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">q</span> <span class="o">=</span> <span class="p">[</span><span class="mi">100</span><span class="o">*</span><span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">c_int</span><span class="p">),</span> <span class="mi">100</span><span class="o">*</span><span class="n">c_int</span><span class="p">])</span>   <span class="c1">#[:,:,0]</span>

        <span class="k">if</span> <span class="n">return_individual_bootstrap_results</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">bootstrap_predicted_array</span><span class="p">,</span> <span class="n">confidence_interval</span>

        <span class="k">return</span> <span class="n">confidence_interval</span></div>
</div>



<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    
    <span class="c1"># Consider some toy data</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.toymodels.structural_causal_processes</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">toys</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.data_processing</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">pp</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.plotting</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">tp</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">matplotlib</span><span class="w"> </span><span class="kn">import</span> <span class="n">pyplot</span> <span class="k">as</span> <span class="n">plt</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">sys</span>

    <span class="kn">import</span><span class="w"> </span><span class="nn">sklearn</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">sklearn.linear_model</span><span class="w"> </span><span class="kn">import</span> <span class="n">LinearRegression</span><span class="p">,</span> <span class="n">LogisticRegression</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">sklearn.preprocessing</span><span class="w"> </span><span class="kn">import</span> <span class="n">StandardScaler</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">sklearn.neural_network</span><span class="w"> </span><span class="kn">import</span> <span class="n">MLPRegressor</span>


    <span class="c1"># def lin_f(x): return x</span>
    <span class="c1"># coeff = .5</span>
 
    <span class="c1"># links_coeffs = {0: [((0, -1), 0.5, lin_f)],</span>
    <span class="c1">#          1: [((1, -1), 0.5, lin_f), ((0, -1), 0.5, lin_f)],</span>
    <span class="c1">#          2: [((2, -1), 0.5, lin_f), ((1, 0), 0.5, lin_f)]</span>
    <span class="c1">#          }</span>
    <span class="c1"># T = 1000</span>
    <span class="c1"># data, nonstat = toys.structural_causal_process(</span>
    <span class="c1">#     links_coeffs, T=T, noises=None, seed=7)</span>
    <span class="c1"># dataframe = pp.DataFrame(data)</span>

    <span class="c1"># graph = CausalEffects.get_graph_from_dict(links_coeffs)</span>

    <span class="c1"># original_graph = np.array([[[&#39;&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;--&gt;&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;--&gt;&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;&#39;, &#39;&#39;]],</span>

    <span class="c1">#    [[&#39;&lt;--&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;&#39;, &#39;--&gt;&#39;],</span>
    <span class="c1">#     [&#39;--&gt;&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;--&gt;&#39;, &#39;&#39;]],</span>

    <span class="c1">#    [[&#39;&lt;--&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;&lt;--&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;&#39;, &#39;--&gt;&#39;],</span>
    <span class="c1">#     [&#39;--&gt;&#39;, &#39;&#39;]],</span>

    <span class="c1">#    [[&#39;&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;&lt;--&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;&lt;--&#39;, &#39;&#39;],</span>
    <span class="c1">#     [&#39;&#39;, &#39;--&gt;&#39;]]], dtype=&#39;&lt;U3&#39;)</span>
    <span class="c1"># graph = np.copy(original_graph)</span>

    <span class="c1"># # Add T &lt;-&gt; Reco and T </span>
    <span class="c1"># graph[2,3,0] = &#39;+-&gt;&#39; ; graph[3,2,0] = &#39;&lt;-+&#39;</span>
    <span class="c1"># graph[1,3,1] = &#39;&lt;-&gt;&#39; #; graph[2,1,0] = &#39;&lt;--&#39;</span>

    <span class="c1"># added = np.zeros((4, 4, 1), dtype=&#39;&lt;U3&#39;)</span>
    <span class="c1"># added[:] = &quot;&quot;</span>
    <span class="c1"># graph = np.append(graph, added , axis=2)</span>


    <span class="c1"># X = [(1, 0)]</span>
    <span class="c1"># Y = [(3, 0)]</span>

    <span class="c1"># # # Initialize class as `stationary_dag`</span>
    <span class="c1"># causal_effects = CausalEffects(graph, graph_type=&#39;stationary_admg&#39;, </span>
    <span class="c1">#                             X=X, Y=Y, S=None, </span>
    <span class="c1">#                             hidden_variables=None, </span>
    <span class="c1">#                             verbosity=0)</span>

    <span class="c1"># print(causal_effects.get_optimal_set())</span>

    <span class="c1"># tp.plot_time_series_graph(</span>
    <span class="c1">#     graph = graph,</span>
    <span class="c1">#     save_name=&#39;Example_graph_in.pdf&#39;,</span>
    <span class="c1">#     # special_nodes=special_nodes,</span>
    <span class="c1">#     # var_names=var_names,</span>
    <span class="c1">#     figsize=(6, 4),</span>
    <span class="c1">#     )</span>

    <span class="c1"># tp.plot_time_series_graph(</span>
    <span class="c1">#     graph = causal_effects.graph,</span>
    <span class="c1">#     save_name=&#39;Example_graph_out.pdf&#39;,</span>
    <span class="c1">#     # special_nodes=special_nodes,</span>
    <span class="c1">#     # var_names=var_names,</span>
    <span class="c1">#     figsize=(6, 4),</span>
    <span class="c1">#     )</span>

    <span class="c1"># causal_effects.fit_wright_effect(dataframe=dataframe, </span>
    <span class="c1">#                         # links_coeffs = links_coeffs,</span>
    <span class="c1">#                         # mediation = [(1, 0), (1, -1), (1, -2)]</span>
    <span class="c1">#                         )</span>

    <span class="c1"># intervention_data = 1.*np.ones((1, 1))</span>
    <span class="c1"># y1 = causal_effects.predict_wright_effect( </span>
    <span class="c1">#         intervention_data=intervention_data,</span>
    <span class="c1">#         )</span>

    <span class="c1"># intervention_data = 0.*np.ones((1, 1))</span>
    <span class="c1"># y2 = causal_effects.predict_wright_effect( </span>
    <span class="c1">#         intervention_data=intervention_data,</span>
    <span class="c1">#         )</span>

    <span class="c1"># beta = (y1 - y2)</span>
    <span class="c1"># print(&quot;Causal effect is %.5f&quot; %(beta))</span>

    <span class="c1"># tp.plot_time_series_graph(</span>
    <span class="c1">#     graph = causal_effects.graph,</span>
    <span class="c1">#     save_name=&#39;Example_graph.pdf&#39;,</span>
    <span class="c1">#     # special_nodes=special_nodes,</span>
    <span class="c1">#     var_names=var_names,</span>
    <span class="c1">#     figsize=(8, 4),</span>
    <span class="c1">#     )</span>

    <span class="n">T</span> <span class="o">=</span> <span class="mi">10000</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">lin_f</span><span class="p">(</span><span class="n">x</span><span class="p">):</span> <span class="k">return</span> <span class="n">x</span>

    <span class="n">auto_coeff</span> <span class="o">=</span> <span class="mf">0.</span>
    <span class="n">coeff</span> <span class="o">=</span> <span class="mf">2.</span>

    <span class="n">links</span> <span class="o">=</span> <span class="p">{</span>
            <span class="mi">0</span><span class="p">:</span> <span class="p">[((</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="n">auto_coeff</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">)],</span> 
            <span class="mi">1</span><span class="p">:</span> <span class="p">[((</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="n">auto_coeff</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">)],</span> 
            <span class="mi">2</span><span class="p">:</span> <span class="p">[((</span><span class="mi">2</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="n">auto_coeff</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">),</span> <span class="p">((</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="n">coeff</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">)],</span>
            <span class="mi">3</span><span class="p">:</span> <span class="p">[((</span><span class="mi">3</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="n">auto_coeff</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">)],</span> 
            <span class="p">}</span>
    <span class="n">data</span><span class="p">,</span> <span class="n">nonstat</span> <span class="o">=</span> <span class="n">toys</span><span class="o">.</span><span class="n">structural_causal_process</span><span class="p">(</span><span class="n">links</span><span class="p">,</span> <span class="n">T</span><span class="o">=</span><span class="n">T</span><span class="p">,</span> 
                                <span class="n">noises</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">seed</span><span class="o">=</span><span class="mi">7</span><span class="p">)</span>


    <span class="c1"># # Create some missing values</span>
    <span class="c1"># data[-10:,:] = 999.</span>
    <span class="c1"># var_names = range(2)</span>

    <span class="n">dataframe</span> <span class="o">=</span> <span class="n">pp</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">(</span><span class="n">data</span><span class="p">,</span>
                    <span class="n">vector_vars</span><span class="o">=</span><span class="p">{</span><span class="mi">0</span><span class="p">:[(</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">),</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mi">0</span><span class="p">)],</span> 
                                 <span class="mi">1</span><span class="p">:[(</span><span class="mi">2</span><span class="p">,</span><span class="mi">0</span><span class="p">),</span> <span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">0</span><span class="p">)]}</span>
                    <span class="p">)</span>

    <span class="c1"># # Construct expert knowledge graph from links here </span>
    <span class="n">aux_links</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="p">[(</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">)],</span>
                 <span class="mi">1</span><span class="p">:</span> <span class="p">[(</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)],</span>
              <span class="p">}</span>
    <span class="c1"># # Use staticmethod to get graph</span>
    <span class="n">graph</span> <span class="o">=</span> <span class="n">CausalEffects</span><span class="o">.</span><span class="n">get_graph_from_dict</span><span class="p">(</span><span class="n">aux_links</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
    <span class="c1"># graph = np.array([[&#39;&#39;, &#39;--&gt;&#39;],</span>
    <span class="c1">#                   [&#39;&lt;--&#39;, &#39;&#39;]], dtype=&#39;&lt;U3&#39;)</span>
    
    <span class="c1"># # We are interested in lagged total effect of X on Y</span>
    <span class="n">X</span> <span class="o">=</span> <span class="p">[(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">)]</span>
    <span class="n">Y</span> <span class="o">=</span> <span class="p">[(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">)]</span>

    <span class="c1"># # Initialize class as `stationary_dag`</span>
    <span class="n">causal_effects</span> <span class="o">=</span> <span class="n">CausalEffects</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="o">=</span><span class="s1">&#39;stationary_dag&#39;</span><span class="p">,</span> 
                                <span class="n">X</span><span class="o">=</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="o">=</span><span class="n">Y</span><span class="p">,</span> <span class="n">S</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> 
                                <span class="n">hidden_variables</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> 
                                <span class="n">verbosity</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

    <span class="c1"># print(data)</span>
    <span class="c1"># # Optimal adjustment set (is used by default)</span>
    <span class="c1"># # print(causal_effects.get_optimal_set())</span>

    <span class="c1"># # # Fit causal effect model from observational data</span>
    <span class="n">causal_effects</span><span class="o">.</span><span class="n">fit_total_effect</span><span class="p">(</span>
        <span class="n">dataframe</span><span class="o">=</span><span class="n">dataframe</span><span class="p">,</span> 
        <span class="c1"># mask_type=&#39;y&#39;,</span>
        <span class="n">estimator</span><span class="o">=</span><span class="n">LinearRegression</span><span class="p">(),</span>
        <span class="p">)</span>

    <span class="c1"># # Fit causal effect model from observational data</span>
    <span class="c1"># causal_effects.fit_bootstrap_of(</span>
    <span class="c1">#     method=&#39;fit_total_effect&#39;,</span>
    <span class="c1">#     method_args={&#39;dataframe&#39;:dataframe,  </span>
    <span class="c1">#     # mask_type=&#39;y&#39;,</span>
    <span class="c1">#     &#39;estimator&#39;:LinearRegression()</span>
    <span class="c1">#     },</span>
    <span class="c1">#     boot_samples=3,</span>
    <span class="c1">#     boot_blocklength=1,</span>
    <span class="c1">#     seed=5</span>
    <span class="c1">#     )</span>


    <span class="c1"># Predict effect of interventions do(X=0.), ..., do(X=1.) in one go</span>
    <span class="n">lenX</span> <span class="o">=</span> <span class="mi">4</span> <span class="c1"># len(dataframe.vector_vars[X[0][0]])</span>
    <span class="n">dox_vals</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">linspace</span><span class="p">(</span><span class="mf">0.</span><span class="p">,</span> <span class="mf">1.</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
    <span class="n">intervention_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">tile</span><span class="p">(</span><span class="n">dox_vals</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">dox_vals</span><span class="p">),</span> <span class="mi">1</span><span class="p">),</span> <span class="n">lenX</span><span class="p">)</span>

    <span class="n">intervention_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([[</span><span class="mf">1.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">,</span> <span class="mf">0.</span><span class="p">]])</span>

    <span class="nb">print</span><span class="p">(</span><span class="n">intervention_data</span><span class="p">)</span>

    <span class="n">pred_Y</span> <span class="o">=</span> <span class="n">causal_effects</span><span class="o">.</span><span class="n">predict_total_effect</span><span class="p">(</span> 
            <span class="n">intervention_data</span><span class="o">=</span><span class="n">intervention_data</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">pred_Y</span><span class="p">,</span> <span class="n">pred_Y</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>





    <span class="c1"># # Predict effect of interventions do(X=0.), ..., do(X=1.) in one go</span>
    <span class="c1"># # dox_vals = np.array([1.]) #np.linspace(0., 1., 1)</span>
    <span class="c1"># intervention_data = np.tile(dox_vals.reshape(len(dox_vals), 1), len(X))</span>
    <span class="c1"># conf = causal_effects.predict_bootstrap_of(</span>
    <span class="c1">#     method=&#39;predict_total_effect&#39;,</span>
    <span class="c1">#     method_args={&#39;intervention_data&#39;:intervention_data})</span>
    <span class="c1"># print(conf, conf.shape)</span>



    <span class="c1"># # # Predict effect of interventions do(X=0.), ..., do(X=1.) in one go</span>
    <span class="c1"># # dox_vals = np.array([1.]) #np.linspace(0., 1., 1)</span>
    <span class="c1"># # intervention_data = dox_vals.reshape(len(dox_vals), len(X))</span>
    <span class="c1"># # pred_Y = causal_effects.predict_total_effect( </span>
    <span class="c1"># #         intervention_data=intervention_data)</span>
    <span class="c1"># # print(pred_Y)</span>



    <span class="c1"># # Fit causal effect model from observational data</span>
    <span class="c1"># causal_effects.fit_wright_effect(</span>
    <span class="c1">#     dataframe=dataframe, </span>
    <span class="c1">#     # mask_type=&#39;y&#39;,</span>
    <span class="c1">#     # estimator=LinearRegression(),</span>
    <span class="c1">#     # data_transform=StandardScaler(),</span>
    <span class="c1">#     )</span>

    <span class="c1"># # # Predict effect of interventions do(X=0.), ..., do(X=1.) in one go</span>
    <span class="c1"># dox_vals = np.linspace(0., 1., 5)</span>
    <span class="c1"># intervention_data = dox_vals.reshape(len(dox_vals), len(X))</span>
    <span class="c1"># pred_Y = causal_effects.predict_wright_effect( </span>
    <span class="c1">#         intervention_data=intervention_data)</span>
    <span class="c1"># print(pred_Y)</span>
</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../index.html">Documentation overview</a><ul>
  <li><a href="../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2023, Jakob Runge.
      
      |
      Powered by <a href="https://www.sphinx-doc.org/">Sphinx 8.2.3</a>
      &amp; <a href="https://alabaster.readthedocs.io">Alabaster 0.7.16</a>
      
    </div>

    

    
  </body>
</html>