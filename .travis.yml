language: python
python:
  # We don't actually use the Travis Python since we are using Conda, but this
  # keeps it organized.
  - "3.7"
  - "3.9"
install:
  - sudo apt-get update
  # We do this conditionally because it saves us some downloading if the
  # version is the same.
  # - if [[ "$TRAVIS_PYTHON_VERSION" == "2.7" ]]; then
  # - wget https://repo.continuum.io/miniconda/Miniconda2-latest-Linux-x86_64.sh -O miniconda.sh;
    # else
  - wget https://repo.continuum.io/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh;
    # fi
  - bash miniconda.sh -b -p $HOME/miniconda
  - export PATH="$HOME/miniconda/bin:$PATH"
  - hash -r
  - conda config --set always_yes yes --set changeps1 no
  - conda update -q conda
  # Useful for debugging any issues with conda
  - conda info -a

  # Create and activate the environment
  - conda create -q -n test-environment python=$TRAVIS_PYTHON_VERSION
  - source activate test-environment
  # Ensure we will install all pip packages within conda
  - conda install pip
  # Install numpy first to avoid headaches
  - pip install numpy
  - pip install .["test"]
script: 
  # Run the tests
  - pytest -v
