Search.setIndex({"alltitles":{"Indices and tables":[[0,"indices-and-tables"],[0,"id25"]],"TIGRAMITE":[[0,"tigramite"]],"Welcome to Tigramite\u2019s documentation!":[[0,null]],"tigramite.causal_effects: Causal Effect analysis":[[0,"tigramite-causal-effects-causal-effect-analysis"]],"tigramite.causal_mediation: Causal mediation analysis":[[0,"tigramite-causal-mediation-causal-mediation-analysis"]],"tigramite.data_processing: Data processing functions":[[0,"module-tigramite.data_processing"]],"tigramite.independence_tests: Conditional independence tests":[[0,"tigramite-independence-tests-conditional-independence-tests"]],"tigramite.jpcmciplus: JPCMCIplus":[[0,"tigramite-jpcmciplus-jpcmciplus"]],"tigramite.lpcmci: LPCMCI":[[0,"tigramite-lpcmci-lpcmci"]],"tigramite.models: Time series modeling, mediation, and prediction":[[0,"tigramite-models-time-series-modeling-mediation-and-prediction"]],"tigramite.pcmci: PCMCI":[[0,"tigramite-pcmci-pcmci"]],"tigramite.plotting: Plotting functions":[[0,"module-tigramite.plotting"]],"tigramite.rpcmci: RPCMCI":[[0,"tigramite-rpcmci-rpcmci"]],"tigramite.toymodels: Toy model generators":[[0,"module-tigramite.toymodels.structural_causal_processes"]]},"docnames":["index"],"envversion":{"sphinx":65,"sphinx.domains.c":3,"sphinx.domains.changeset":1,"sphinx.domains.citation":1,"sphinx.domains.cpp":9,"sphinx.domains.index":1,"sphinx.domains.javascript":3,"sphinx.domains.math":2,"sphinx.domains.python":4,"sphinx.domains.rst":2,"sphinx.domains.std":2,"sphinx.ext.viewcode":1},"filenames":["index.rst"],"indexentries":{"_initialized_from (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self._initialized_from",false]],"add_densityplot() (tigramite.plotting.setup_density_matrix method)":[[0,"tigramite.plotting.setup_density_matrix.add_densityplot",false]],"add_found_context_link_assumptions() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.add_found_context_link_assumptions",false]],"add_lagfuncs() (tigramite.plotting.setup_matrix method)":[[0,"tigramite.plotting.setup_matrix.add_lagfuncs",false]],"add_scatterplot() (tigramite.plotting.setup_scatter_matrix method)":[[0,"tigramite.plotting.setup_scatter_matrix.add_scatterplot",false]],"adjustfig() (tigramite.plotting.setup_density_matrix method)":[[0,"tigramite.plotting.setup_density_matrix.adjustfig",false]],"adjustfig() (tigramite.plotting.setup_scatter_matrix method)":[[0,"tigramite.plotting.setup_scatter_matrix.adjustfig",false]],"all_parents (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.all_parents",false]],"all_parents (tigramite.pcmci.pcmci attribute)":[[0,"tigramite.pcmci.PCMCI.all_parents",false]],"analysis_mode (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.analysis_mode",false]],"assume_exogenous_context() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.assume_exogenous_context",false]],"bootstrap (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.bootstrap",false]],"causaleffects (class in tigramite.causal_effects)":[[0,"tigramite.causal_effects.CausalEffects",false]],"causalmediation (class in tigramite.causal_mediation)":[[0,"tigramite.causal_mediation.CausalMediation",false]],"check_optimality() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.check_optimality",false]],"check_shortest_path() (tigramite.independence_tests.oracle_conditional_independence.oracleci method)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.check_shortest_path",false]],"check_stationarity() (in module tigramite.toymodels.structural_causal_processes)":[[0,"tigramite.toymodels.structural_causal_processes.check_stationarity",false]],"check_xys_paths() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.check_XYS_paths",false]],"clean_link_assumptions() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.clean_link_assumptions",false]],"clean_system_link_assumptions() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.clean_system_link_assumptions",false]],"cmiknn (class in tigramite.independence_tests.cmiknn)":[[0,"tigramite.independence_tests.cmiknn.CMIknn",false]],"cmisymb (class in tigramite.independence_tests.cmisymb)":[[0,"tigramite.independence_tests.cmisymb.CMIsymb",false]],"compute_wilks_lambda_cca() (tigramite.independence_tests.parcorr_mult.parcorrmult method)":[[0,"tigramite.independence_tests.parcorr_mult.ParCorrMult.compute_wilks_lambda_cca",false]],"condindtest (class in tigramite.independence_tests.independence_tests_base)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest",false]],"construct_array() (tigramite.data_processing.dataframe method)":[[0,"tigramite.data_processing.DataFrame.construct_array",false]],"dag_to_links() (in module tigramite.toymodels.structural_causal_processes)":[[0,"tigramite.toymodels.structural_causal_processes.dag_to_links",false]],"data_type (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.data_type",false]],"dataframe (class in tigramite.data_processing)":[[0,"tigramite.data_processing.DataFrame",false]],"datasets (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.datasets",false]],"datatime (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.datatime",false]],"discover_dummy_system_links() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.discover_dummy_system_links",false]],"discover_lagged_context_system_links() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.discover_lagged_context_system_links",false]],"discover_system_system_links() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.discover_system_system_links",false]],"dummy_ci_test (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.dummy_ci_test",false]],"dummy_parents (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.dummy_parents",false]],"fit() (tigramite.models.prediction method)":[[0,"tigramite.models.Prediction.fit",false]],"fit_bootstrap_of() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.fit_bootstrap_of",false]],"fit_full_model() (tigramite.models.models method)":[[0,"tigramite.models.Models.fit_full_model",false]],"fit_model() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.fit_model",false]],"fit_model_bootstrap() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.fit_model_bootstrap",false]],"fit_natural_direct_effect() (tigramite.causal_mediation.causalmediation method)":[[0,"tigramite.causal_mediation.CausalMediation.fit_natural_direct_effect",false]],"fit_total_effect() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.fit_total_effect",false]],"fit_wright_effect() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.fit_wright_effect",false]],"generate_and_save_nulldists() (tigramite.independence_tests.gpdc.gpdc method)":[[0,"tigramite.independence_tests.gpdc.GPDC.generate_and_save_nulldists",false]],"generate_and_save_nulldists() (tigramite.independence_tests.gpdc_torch.gpdctorch method)":[[0,"tigramite.independence_tests.gpdc_torch.GPDCtorch.generate_and_save_nulldists",false]],"generate_nulldist() (tigramite.independence_tests.gpdc.gpdc method)":[[0,"tigramite.independence_tests.gpdc.GPDC.generate_nulldist",false]],"generate_nulldist() (tigramite.independence_tests.gpdc_torch.gpdctorch method)":[[0,"tigramite.independence_tests.gpdc_torch.GPDCtorch.generate_nulldist",false]],"generate_structural_causal_process() (in module tigramite.toymodels.structural_causal_processes)":[[0,"tigramite.toymodels.structural_causal_processes.generate_structural_causal_process",false]],"get_ace() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_ace",false]],"get_acf() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.get_acf",false]],"get_acs() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_acs",false]],"get_all_ace() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_all_ace",false]],"get_all_acs() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_all_acs",false]],"get_all_amce() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_all_amce",false]],"get_amce() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_amce",false]],"get_analytic_confidence() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_analytic_confidence",false]],"get_analytic_confidence() (tigramite.independence_tests.parcorr.parcorr method)":[[0,"tigramite.independence_tests.parcorr.ParCorr.get_analytic_confidence",false]],"get_analytic_confidence() (tigramite.independence_tests.robust_parcorr.robustparcorr method)":[[0,"tigramite.independence_tests.robust_parcorr.RobustParCorr.get_analytic_confidence",false]],"get_analytic_significance() (tigramite.independence_tests.gpdc.gpdc method)":[[0,"tigramite.independence_tests.gpdc.GPDC.get_analytic_significance",false]],"get_analytic_significance() (tigramite.independence_tests.gpdc_torch.gpdctorch method)":[[0,"tigramite.independence_tests.gpdc_torch.GPDCtorch.get_analytic_significance",false]],"get_analytic_significance() (tigramite.independence_tests.gsquared.gsquared method)":[[0,"tigramite.independence_tests.gsquared.Gsquared.get_analytic_significance",false]],"get_analytic_significance() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_analytic_significance",false]],"get_analytic_significance() (tigramite.independence_tests.parcorr.parcorr method)":[[0,"tigramite.independence_tests.parcorr.ParCorr.get_analytic_significance",false]],"get_analytic_significance() (tigramite.independence_tests.parcorr_mult.parcorrmult method)":[[0,"tigramite.independence_tests.parcorr_mult.ParCorrMult.get_analytic_significance",false]],"get_analytic_significance() (tigramite.independence_tests.regressionci.regressionci method)":[[0,"tigramite.independence_tests.regressionCI.RegressionCI.get_analytic_significance",false]],"get_analytic_significance() (tigramite.independence_tests.robust_parcorr.robustparcorr method)":[[0,"tigramite.independence_tests.robust_parcorr.RobustParCorr.get_analytic_significance",false]],"get_block_length() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.get_block_length",false]],"get_bootstrap_confidence() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_bootstrap_confidence",false]],"get_bootstrap_of() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_bootstrap_of",false]],"get_ce() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_ce",false]],"get_ce_max() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_ce_max",false]],"get_coeff() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_coeff",false]],"get_coefs() (tigramite.models.models method)":[[0,"tigramite.models.Models.get_coefs",false]],"get_conditional_entropy() (tigramite.independence_tests.cmiknn.cmiknn method)":[[0,"tigramite.independence_tests.cmiknn.CMIknn.get_conditional_entropy",false]],"get_conditional_mce() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_conditional_mce",false]],"get_confidence() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_confidence",false]],"get_confidence() (tigramite.independence_tests.oracle_conditional_independence.oracleci method)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_confidence",false]],"get_dependence_measure() (tigramite.independence_tests.cmiknn.cmiknn method)":[[0,"tigramite.independence_tests.cmiknn.CMIknn.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.cmisymb.cmisymb method)":[[0,"tigramite.independence_tests.cmisymb.CMIsymb.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.gpdc.gpdc method)":[[0,"tigramite.independence_tests.gpdc.GPDC.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.gpdc_torch.gpdctorch method)":[[0,"tigramite.independence_tests.gpdc_torch.GPDCtorch.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.gsquared.gsquared method)":[[0,"tigramite.independence_tests.gsquared.Gsquared.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.parcorr.parcorr method)":[[0,"tigramite.independence_tests.parcorr.ParCorr.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.parcorr_mult.parcorrmult method)":[[0,"tigramite.independence_tests.parcorr_mult.ParCorrMult.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.parcorr_wls.parcorrwls method)":[[0,"tigramite.independence_tests.parcorr_wls.ParCorrWLS.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.regressionci.regressionci method)":[[0,"tigramite.independence_tests.regressionCI.RegressionCI.get_dependence_measure",false]],"get_dependence_measure() (tigramite.independence_tests.robust_parcorr.robustparcorr method)":[[0,"tigramite.independence_tests.robust_parcorr.RobustParCorr.get_dependence_measure",false]],"get_dependence_measure_raw() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_dependence_measure_raw",false]],"get_dict_from_graph() (tigramite.graphs.graphs static method)":[[0,"tigramite.graphs.Graphs.get_dict_from_graph",false]],"get_fixed_thres_significance() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_fixed_thres_significance",false]],"get_general_fitted_model() (tigramite.models.models method)":[[0,"tigramite.models.Models.get_general_fitted_model",false]],"get_general_prediction() (tigramite.models.models method)":[[0,"tigramite.models.Models.get_general_prediction",false]],"get_graph_from_dict() (tigramite.graphs.graphs static method)":[[0,"tigramite.graphs.Graphs.get_graph_from_dict",false]],"get_graph_from_links() (tigramite.independence_tests.oracle_conditional_independence.oracleci method)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_graph_from_links",false]],"get_graph_from_pmatrix() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.get_graph_from_pmatrix",false]],"get_joint_ce() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_joint_ce",false]],"get_joint_ce_matrix() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_joint_ce_matrix",false]],"get_joint_mce() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_joint_mce",false]],"get_lagged_dependencies() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.get_lagged_dependencies",false]],"get_links_from_graph() (tigramite.independence_tests.oracle_conditional_independence.oracleci method)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_links_from_graph",false]],"get_mce() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_mce",false]],"get_measure() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_measure",false]],"get_measure() (tigramite.independence_tests.oracle_conditional_independence.oracleci method)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_measure",false]],"get_mediation_graph_data() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_mediation_graph_data",false]],"get_mediators() (tigramite.graphs.graphs method)":[[0,"tigramite.graphs.Graphs.get_mediators",false]],"get_model_selection_criterion() (tigramite.independence_tests.cmiknn.cmiknn method)":[[0,"tigramite.independence_tests.cmiknn.CMIknn.get_model_selection_criterion",false]],"get_model_selection_criterion() (tigramite.independence_tests.gpdc.gpdc method)":[[0,"tigramite.independence_tests.gpdc.GPDC.get_model_selection_criterion",false]],"get_model_selection_criterion() (tigramite.independence_tests.gpdc_torch.gpdctorch method)":[[0,"tigramite.independence_tests.gpdc_torch.GPDCtorch.get_model_selection_criterion",false]],"get_model_selection_criterion() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_model_selection_criterion",false]],"get_model_selection_criterion() (tigramite.independence_tests.oracle_conditional_independence.oracleci method)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_model_selection_criterion",false]],"get_model_selection_criterion() (tigramite.independence_tests.parcorr.parcorr method)":[[0,"tigramite.independence_tests.parcorr.ParCorr.get_model_selection_criterion",false]],"get_model_selection_criterion() (tigramite.independence_tests.parcorr_mult.parcorrmult method)":[[0,"tigramite.independence_tests.parcorr_mult.ParCorrMult.get_model_selection_criterion",false]],"get_model_selection_criterion() (tigramite.independence_tests.parcorr_wls.parcorrwls method)":[[0,"tigramite.independence_tests.parcorr_wls.ParCorrWLS.get_model_selection_criterion",false]],"get_model_selection_criterion() (tigramite.independence_tests.robust_parcorr.robustparcorr method)":[[0,"tigramite.independence_tests.robust_parcorr.RobustParCorr.get_model_selection_criterion",false]],"get_optimal_set() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.get_optimal_set",false]],"get_predictors() (tigramite.models.prediction method)":[[0,"tigramite.models.Prediction.get_predictors",false]],"get_residuals_cov_mean() (tigramite.models.models method)":[[0,"tigramite.models.Models.get_residuals_cov_mean",false]],"get_shuffle_significance() (tigramite.independence_tests.cmiknn.cmiknn method)":[[0,"tigramite.independence_tests.cmiknn.CMIknn.get_shuffle_significance",false]],"get_shuffle_significance() (tigramite.independence_tests.cmisymb.cmisymb method)":[[0,"tigramite.independence_tests.cmisymb.CMIsymb.get_shuffle_significance",false]],"get_shuffle_significance() (tigramite.independence_tests.gpdc.gpdc method)":[[0,"tigramite.independence_tests.gpdc.GPDC.get_shuffle_significance",false]],"get_shuffle_significance() (tigramite.independence_tests.gpdc_torch.gpdctorch method)":[[0,"tigramite.independence_tests.gpdc_torch.GPDCtorch.get_shuffle_significance",false]],"get_shuffle_significance() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.get_shuffle_significance",false]],"get_shuffle_significance() (tigramite.independence_tests.parcorr.parcorr method)":[[0,"tigramite.independence_tests.parcorr.ParCorr.get_shuffle_significance",false]],"get_shuffle_significance() (tigramite.independence_tests.parcorr_mult.parcorrmult method)":[[0,"tigramite.independence_tests.parcorr_mult.ParCorrMult.get_shuffle_significance",false]],"get_shuffle_significance() (tigramite.independence_tests.parcorr_wls.parcorrwls method)":[[0,"tigramite.independence_tests.parcorr_wls.ParCorrWLS.get_shuffle_significance",false]],"get_shuffle_significance() (tigramite.independence_tests.robust_parcorr.robustparcorr method)":[[0,"tigramite.independence_tests.robust_parcorr.RobustParCorr.get_shuffle_significance",false]],"get_test_array() (tigramite.models.prediction method)":[[0,"tigramite.models.Prediction.get_test_array",false]],"get_train_array() (tigramite.models.prediction method)":[[0,"tigramite.models.Prediction.get_train_array",false]],"get_tsg() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_tsg",false]],"get_val_matrix() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.get_val_matrix",false]],"get_val_matrix() (tigramite.models.models method)":[[0,"tigramite.models.Models.get_val_matrix",false]],"gpdc (class in tigramite.independence_tests.gpdc)":[[0,"tigramite.independence_tests.gpdc.GPDC",false]],"gpdctorch (class in tigramite.independence_tests.gpdc_torch)":[[0,"tigramite.independence_tests.gpdc_torch.GPDCtorch",false]],"graphs (class in tigramite.graphs)":[[0,"tigramite.graphs.Graphs",false]],"gsquared (class in tigramite.independence_tests.gsquared)":[[0,"tigramite.independence_tests.gsquared.Gsquared",false]],"iterations (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.iterations",false]],"iterations (tigramite.pcmci.pcmci attribute)":[[0,"tigramite.pcmci.PCMCI.iterations",false]],"jpcmciplus (class in tigramite.jpcmciplus)":[[0,"tigramite.jpcmciplus.JPCMCIplus",false]],"largest_time_step (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.largest_time_step",false]],"linearmediation (class in tigramite.models)":[[0,"tigramite.models.LinearMediation",false]],"links_to_graph() (in module tigramite.toymodels.structural_causal_processes)":[[0,"tigramite.toymodels.structural_causal_processes.links_to_graph",false]],"lowhighpass_filter() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.lowhighpass_filter",false]],"lpcmci (class in tigramite.lpcmci)":[[0,"tigramite.lpcmci.LPCMCI",false]],"m (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.M",false]],"mask (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.mask",false]],"measure (tigramite.independence_tests.cmiknn.cmiknn property)":[[0,"tigramite.independence_tests.cmiknn.CMIknn.measure",false]],"measure (tigramite.independence_tests.cmisymb.cmisymb property)":[[0,"tigramite.independence_tests.cmisymb.CMIsymb.measure",false]],"measure (tigramite.independence_tests.gpdc.gpdc property)":[[0,"tigramite.independence_tests.gpdc.GPDC.measure",false]],"measure (tigramite.independence_tests.gpdc_torch.gpdctorch property)":[[0,"tigramite.independence_tests.gpdc_torch.GPDCtorch.measure",false]],"measure (tigramite.independence_tests.gsquared.gsquared property)":[[0,"tigramite.independence_tests.gsquared.Gsquared.measure",false]],"measure (tigramite.independence_tests.independence_tests_base.condindtest property)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.measure",false]],"measure (tigramite.independence_tests.oracle_conditional_independence.oracleci property)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.measure",false]],"measure (tigramite.independence_tests.parcorr.parcorr property)":[[0,"tigramite.independence_tests.parcorr.ParCorr.measure",false]],"measure (tigramite.independence_tests.parcorr_mult.parcorrmult property)":[[0,"tigramite.independence_tests.parcorr_mult.ParCorrMult.measure",false]],"measure (tigramite.independence_tests.regressionci.regressionci property)":[[0,"tigramite.independence_tests.regressionCI.RegressionCI.measure",false]],"measure (tigramite.independence_tests.robust_parcorr.robustparcorr property)":[[0,"tigramite.independence_tests.robust_parcorr.RobustParCorr.measure",false]],"missing_flag (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.missing_flag",false]],"mode (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.mode",false]],"models (class in tigramite.models)":[[0,"tigramite.models.Models",false]],"module":[[0,"module-tigramite.data_processing",false],[0,"module-tigramite.plotting",false],[0,"module-tigramite.toymodels.structural_causal_processes",false]],"mult_corr() (tigramite.independence_tests.parcorr_mult.parcorrmult method)":[[0,"tigramite.independence_tests.parcorr_mult.ParCorrMult.mult_corr",false]],"n (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.N",false]],"n (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.N",false]],"n (tigramite.pcmci.pcmci attribute)":[[0,"tigramite.pcmci.PCMCI.N",false]],"net_to_tsg() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.net_to_tsg",false]],"observed_context_parents (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.observed_context_parents",false]],"oracleci (class in tigramite.independence_tests.oracle_conditional_independence)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI",false]],"ordinal_patt_array() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.ordinal_patt_array",false]],"parcorr (class in tigramite.independence_tests.parcorr)":[[0,"tigramite.independence_tests.parcorr.ParCorr",false]],"parcorrmult (class in tigramite.independence_tests.parcorr_mult)":[[0,"tigramite.independence_tests.parcorr_mult.ParCorrMult",false]],"parcorrwls (class in tigramite.independence_tests.parcorr_wls)":[[0,"tigramite.independence_tests.parcorr_wls.ParCorrWLS",false]],"pcmci (class in tigramite.pcmci)":[[0,"tigramite.pcmci.PCMCI",false]],"plot_densityplots() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_densityplots",false]],"plot_graph() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_graph",false]],"plot_lagfuncs() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_lagfuncs",false]],"plot_mediation_graph() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_mediation_graph",false]],"plot_mediation_time_series_graph() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_mediation_time_series_graph",false]],"plot_scatterplots() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_scatterplots",false]],"plot_time_series_graph() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_time_series_graph",false]],"plot_timeseries() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_timeseries",false]],"plot_tsg() (in module tigramite.plotting)":[[0,"tigramite.plotting.plot_tsg",false]],"predict() (tigramite.models.prediction method)":[[0,"tigramite.models.Prediction.predict",false]],"predict_bootstrap_of() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.predict_bootstrap_of",false]],"predict_full_model() (tigramite.models.models method)":[[0,"tigramite.models.Models.predict_full_model",false]],"predict_natural_direct_effect() (tigramite.causal_mediation.causalmediation method)":[[0,"tigramite.causal_mediation.CausalMediation.predict_natural_direct_effect",false]],"predict_natural_direct_effect_function() (tigramite.causal_mediation.causalmediation method)":[[0,"tigramite.causal_mediation.CausalMediation.predict_natural_direct_effect_function",false]],"predict_total_effect() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.predict_total_effect",false]],"predict_wright_effect() (tigramite.causal_effects.causaleffects method)":[[0,"tigramite.causal_effects.CausalEffects.predict_wright_effect",false]],"prediction (class in tigramite.models)":[[0,"tigramite.models.Prediction",false]],"print_array_info() (tigramite.data_processing.dataframe method)":[[0,"tigramite.data_processing.DataFrame.print_array_info",false]],"print_info() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.print_info",false]],"print_results() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.print_results",false]],"print_significant_links() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.print_significant_links",false]],"pval_max (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.pval_max",false]],"pval_max (tigramite.pcmci.pcmci attribute)":[[0,"tigramite.pcmci.PCMCI.pval_max",false]],"quantile_bin_array() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.quantile_bin_array",false]],"reference_points (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.reference_points",false]],"regressionci (class in tigramite.independence_tests.regressionci)":[[0,"tigramite.independence_tests.regressionCI.RegressionCI",false]],"remove_dummy_link_assumptions() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.remove_dummy_link_assumptions",false]],"return_parents_dict() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.return_parents_dict",false]],"return_significant_links() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.return_significant_links",false]],"robustparcorr (class in tigramite.independence_tests.robust_parcorr)":[[0,"tigramite.independence_tests.robust_parcorr.RobustParCorr",false]],"rpcmci (class in tigramite.rpcmci)":[[0,"tigramite.rpcmci.RPCMCI",false]],"run_bivci() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.run_bivci",false]],"run_fullci() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.run_fullci",false]],"run_jpcmciplus() (tigramite.jpcmciplus.jpcmciplus method)":[[0,"tigramite.jpcmciplus.JPCMCIplus.run_jpcmciplus",false]],"run_lpcmci() (tigramite.lpcmci.lpcmci method)":[[0,"tigramite.lpcmci.LPCMCI.run_lpcmci",false]],"run_mci() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.run_mci",false]],"run_pc_stable() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.run_pc_stable",false]],"run_pcalg() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.run_pcalg",false]],"run_pcalg_non_timeseries_data() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.run_pcalg_non_timeseries_data",false]],"run_pcmci() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.run_pcmci",false]],"run_pcmciplus() (tigramite.pcmci.pcmci method)":[[0,"tigramite.pcmci.PCMCI.run_pcmciplus",false]],"run_rpcmci() (tigramite.rpcmci.rpcmci method)":[[0,"tigramite.rpcmci.RPCMCI.run_rpcmci",false]],"run_test() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.run_test",false]],"run_test() (tigramite.independence_tests.oracle_conditional_independence.oracleci method)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.run_test",false]],"run_test_raw() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.run_test_raw",false]],"savefig() (tigramite.plotting.setup_matrix method)":[[0,"tigramite.plotting.setup_matrix.savefig",false]],"set_dataframe() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.set_dataframe",false]],"set_dataframe() (tigramite.independence_tests.oracle_conditional_independence.oracleci method)":[[0,"tigramite.independence_tests.oracle_conditional_independence.OracleCI.set_dataframe",false]],"set_dataframe() (tigramite.independence_tests.regressionci.regressionci method)":[[0,"tigramite.independence_tests.regressionCI.RegressionCI.set_dataframe",false]],"set_mask_type() (tigramite.independence_tests.independence_tests_base.condindtest method)":[[0,"tigramite.independence_tests.independence_tests_base.CondIndTest.set_mask_type",false]],"setup_density_matrix (class in tigramite.plotting)":[[0,"tigramite.plotting.setup_density_matrix",false]],"setup_matrix (class in tigramite.plotting)":[[0,"tigramite.plotting.setup_matrix",false]],"setup_scatter_matrix (class in tigramite.plotting)":[[0,"tigramite.plotting.setup_scatter_matrix",false]],"smooth() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.smooth",false]],"space_context_nodes (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.space_context_nodes",false]],"space_dummy (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.space_dummy",false]],"structural_causal_process() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.structural_causal_process",false]],"structural_causal_process() (in module tigramite.toymodels.structural_causal_processes)":[[0,"tigramite.toymodels.structural_causal_processes.structural_causal_process",false]],"structural_causal_process_ensemble() (in module tigramite.toymodels.structural_causal_processes)":[[0,"tigramite.toymodels.structural_causal_processes.structural_causal_process_ensemble",false]],"system_nodes (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.system_nodes",false]],"t (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.T",false]],"t (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.T",false]],"t (tigramite.pcmci.pcmci attribute)":[[0,"tigramite.pcmci.PCMCI.T",false]],"tigramite.data_processing":[[0,"module-tigramite.data_processing",false]],"tigramite.plotting":[[0,"module-tigramite.plotting",false]],"tigramite.toymodels.structural_causal_processes":[[0,"module-tigramite.toymodels.structural_causal_processes",false]],"time_bin_with_mask() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.time_bin_with_mask",false]],"time_context_nodes (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.time_context_nodes",false]],"time_dummy (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.time_dummy",false]],"time_offsets (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.time_offsets",false]],"trafo2normal() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.trafo2normal",false]],"trafo2normal() (tigramite.independence_tests.robust_parcorr.robustparcorr method)":[[0,"tigramite.independence_tests.robust_parcorr.RobustParCorr.trafo2normal",false]],"tsg_to_net() (tigramite.models.linearmediation method)":[[0,"tigramite.models.LinearMediation.tsg_to_net",false]],"val_min (tigramite.jpcmciplus.jpcmciplus attribute)":[[0,"tigramite.jpcmciplus.JPCMCIplus.val_min",false]],"val_min (tigramite.pcmci.pcmci attribute)":[[0,"tigramite.pcmci.PCMCI.val_min",false]],"values (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.values",false]],"var_names (tigramite.data_processing.dataframe.self attribute)":[[0,"tigramite.data_processing.DataFrame.self.var_names",false]],"var_process() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.var_process",false]],"var_process() (in module tigramite.toymodels.structural_causal_processes)":[[0,"tigramite.toymodels.structural_causal_processes.var_process",false]],"weighted_avg_and_std() (in module tigramite.data_processing)":[[0,"tigramite.data_processing.weighted_avg_and_std",false]],"write_csv() (in module tigramite.plotting)":[[0,"tigramite.plotting.write_csv",false]]},"objects":{"tigramite":[[0,2,0,"-","data_processing"],[0,2,0,"-","plotting"]],"tigramite.causal_effects":[[0,0,1,"","CausalEffects"]],"tigramite.causal_effects.CausalEffects":[[0,1,1,"","check_XYS_paths"],[0,1,1,"","check_optimality"],[0,1,1,"","fit_bootstrap_of"],[0,1,1,"","fit_total_effect"],[0,1,1,"","fit_wright_effect"],[0,1,1,"","get_optimal_set"],[0,1,1,"","predict_bootstrap_of"],[0,1,1,"","predict_total_effect"],[0,1,1,"","predict_wright_effect"]],"tigramite.causal_mediation":[[0,0,1,"","CausalMediation"]],"tigramite.causal_mediation.CausalMediation":[[0,1,1,"","fit_natural_direct_effect"],[0,1,1,"","predict_natural_direct_effect"],[0,1,1,"","predict_natural_direct_effect_function"]],"tigramite.data_processing":[[0,0,1,"","DataFrame"],[0,4,1,"","get_acf"],[0,4,1,"","get_block_length"],[0,4,1,"","lowhighpass_filter"],[0,4,1,"","ordinal_patt_array"],[0,4,1,"","quantile_bin_array"],[0,4,1,"","smooth"],[0,4,1,"","structural_causal_process"],[0,4,1,"","time_bin_with_mask"],[0,4,1,"","trafo2normal"],[0,4,1,"","var_process"],[0,4,1,"","weighted_avg_and_std"]],"tigramite.data_processing.DataFrame":[[0,1,1,"","construct_array"],[0,1,1,"","print_array_info"]],"tigramite.data_processing.DataFrame.self":[[0,3,1,"","M"],[0,3,1,"","N"],[0,3,1,"","T"],[0,3,1,"","_initialized_from"],[0,3,1,"","analysis_mode"],[0,3,1,"","bootstrap"],[0,3,1,"","data_type"],[0,3,1,"","datasets"],[0,3,1,"","datatime"],[0,3,1,"","largest_time_step"],[0,3,1,"","mask"],[0,3,1,"","missing_flag"],[0,3,1,"","reference_points"],[0,3,1,"","time_offsets"],[0,3,1,"","values"],[0,3,1,"","var_names"]],"tigramite.graphs":[[0,0,1,"","Graphs"]],"tigramite.graphs.Graphs":[[0,1,1,"","get_dict_from_graph"],[0,1,1,"","get_graph_from_dict"],[0,1,1,"","get_mediators"]],"tigramite.independence_tests.cmiknn":[[0,0,1,"","CMIknn"]],"tigramite.independence_tests.cmiknn.CMIknn":[[0,1,1,"","get_conditional_entropy"],[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_model_selection_criterion"],[0,1,1,"","get_shuffle_significance"],[0,5,1,"","measure"]],"tigramite.independence_tests.cmisymb":[[0,0,1,"","CMIsymb"]],"tigramite.independence_tests.cmisymb.CMIsymb":[[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_shuffle_significance"],[0,5,1,"","measure"]],"tigramite.independence_tests.gpdc":[[0,0,1,"","GPDC"]],"tigramite.independence_tests.gpdc.GPDC":[[0,1,1,"","generate_and_save_nulldists"],[0,1,1,"","generate_nulldist"],[0,1,1,"","get_analytic_significance"],[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_model_selection_criterion"],[0,1,1,"","get_shuffle_significance"],[0,5,1,"","measure"]],"tigramite.independence_tests.gpdc_torch":[[0,0,1,"","GPDCtorch"]],"tigramite.independence_tests.gpdc_torch.GPDCtorch":[[0,1,1,"","generate_and_save_nulldists"],[0,1,1,"","generate_nulldist"],[0,1,1,"","get_analytic_significance"],[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_model_selection_criterion"],[0,1,1,"","get_shuffle_significance"],[0,5,1,"","measure"]],"tigramite.independence_tests.gsquared":[[0,0,1,"","Gsquared"]],"tigramite.independence_tests.gsquared.Gsquared":[[0,1,1,"","get_analytic_significance"],[0,1,1,"","get_dependence_measure"],[0,5,1,"","measure"]],"tigramite.independence_tests.independence_tests_base":[[0,0,1,"","CondIndTest"]],"tigramite.independence_tests.independence_tests_base.CondIndTest":[[0,1,1,"","get_analytic_confidence"],[0,1,1,"","get_analytic_significance"],[0,1,1,"","get_bootstrap_confidence"],[0,1,1,"","get_confidence"],[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_dependence_measure_raw"],[0,1,1,"","get_fixed_thres_significance"],[0,1,1,"","get_measure"],[0,1,1,"","get_model_selection_criterion"],[0,1,1,"","get_shuffle_significance"],[0,5,1,"","measure"],[0,1,1,"","print_info"],[0,1,1,"","run_test"],[0,1,1,"","run_test_raw"],[0,1,1,"","set_dataframe"],[0,1,1,"","set_mask_type"]],"tigramite.independence_tests.oracle_conditional_independence":[[0,0,1,"","OracleCI"]],"tigramite.independence_tests.oracle_conditional_independence.OracleCI":[[0,1,1,"","check_shortest_path"],[0,1,1,"","get_confidence"],[0,1,1,"","get_graph_from_links"],[0,1,1,"","get_links_from_graph"],[0,1,1,"","get_measure"],[0,1,1,"","get_model_selection_criterion"],[0,5,1,"","measure"],[0,1,1,"","run_test"],[0,1,1,"","set_dataframe"]],"tigramite.independence_tests.parcorr":[[0,0,1,"","ParCorr"]],"tigramite.independence_tests.parcorr.ParCorr":[[0,1,1,"","get_analytic_confidence"],[0,1,1,"","get_analytic_significance"],[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_model_selection_criterion"],[0,1,1,"","get_shuffle_significance"],[0,5,1,"","measure"]],"tigramite.independence_tests.parcorr_mult":[[0,0,1,"","ParCorrMult"]],"tigramite.independence_tests.parcorr_mult.ParCorrMult":[[0,1,1,"","compute_wilks_lambda_cca"],[0,1,1,"","get_analytic_significance"],[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_model_selection_criterion"],[0,1,1,"","get_shuffle_significance"],[0,5,1,"","measure"],[0,1,1,"","mult_corr"]],"tigramite.independence_tests.parcorr_wls":[[0,0,1,"","ParCorrWLS"]],"tigramite.independence_tests.parcorr_wls.ParCorrWLS":[[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_model_selection_criterion"],[0,1,1,"","get_shuffle_significance"]],"tigramite.independence_tests.regressionCI":[[0,0,1,"","RegressionCI"]],"tigramite.independence_tests.regressionCI.RegressionCI":[[0,1,1,"","get_analytic_significance"],[0,1,1,"","get_dependence_measure"],[0,5,1,"","measure"],[0,1,1,"","set_dataframe"]],"tigramite.independence_tests.robust_parcorr":[[0,0,1,"","RobustParCorr"]],"tigramite.independence_tests.robust_parcorr.RobustParCorr":[[0,1,1,"","get_analytic_confidence"],[0,1,1,"","get_analytic_significance"],[0,1,1,"","get_dependence_measure"],[0,1,1,"","get_model_selection_criterion"],[0,1,1,"","get_shuffle_significance"],[0,5,1,"","measure"],[0,1,1,"","trafo2normal"]],"tigramite.jpcmciplus":[[0,0,1,"","JPCMCIplus"]],"tigramite.jpcmciplus.JPCMCIplus":[[0,3,1,"","N"],[0,3,1,"","T"],[0,1,1,"","add_found_context_link_assumptions"],[0,3,1,"","all_parents"],[0,1,1,"","assume_exogenous_context"],[0,1,1,"","clean_link_assumptions"],[0,1,1,"","clean_system_link_assumptions"],[0,1,1,"","discover_dummy_system_links"],[0,1,1,"","discover_lagged_context_system_links"],[0,1,1,"","discover_system_system_links"],[0,3,1,"","dummy_ci_test"],[0,3,1,"","dummy_parents"],[0,3,1,"","iterations"],[0,3,1,"","mode"],[0,3,1,"","observed_context_parents"],[0,3,1,"","pval_max"],[0,1,1,"","remove_dummy_link_assumptions"],[0,1,1,"","run_jpcmciplus"],[0,3,1,"","space_context_nodes"],[0,3,1,"","space_dummy"],[0,3,1,"","system_nodes"],[0,3,1,"","time_context_nodes"],[0,3,1,"","time_dummy"],[0,3,1,"","val_min"]],"tigramite.lpcmci":[[0,0,1,"","LPCMCI"]],"tigramite.lpcmci.LPCMCI":[[0,1,1,"","run_lpcmci"]],"tigramite.models":[[0,0,1,"","LinearMediation"],[0,0,1,"","Models"],[0,0,1,"","Prediction"]],"tigramite.models.LinearMediation":[[0,1,1,"","fit_model"],[0,1,1,"","fit_model_bootstrap"],[0,1,1,"","get_ace"],[0,1,1,"","get_acs"],[0,1,1,"","get_all_ace"],[0,1,1,"","get_all_acs"],[0,1,1,"","get_all_amce"],[0,1,1,"","get_amce"],[0,1,1,"","get_bootstrap_of"],[0,1,1,"","get_ce"],[0,1,1,"","get_ce_max"],[0,1,1,"","get_coeff"],[0,1,1,"","get_conditional_mce"],[0,1,1,"","get_joint_ce"],[0,1,1,"","get_joint_ce_matrix"],[0,1,1,"","get_joint_mce"],[0,1,1,"","get_mce"],[0,1,1,"","get_mediation_graph_data"],[0,1,1,"","get_tsg"],[0,1,1,"","get_val_matrix"],[0,1,1,"","net_to_tsg"],[0,1,1,"","tsg_to_net"]],"tigramite.models.Models":[[0,1,1,"","fit_full_model"],[0,1,1,"","get_coefs"],[0,1,1,"","get_general_fitted_model"],[0,1,1,"","get_general_prediction"],[0,1,1,"","get_residuals_cov_mean"],[0,1,1,"","get_val_matrix"],[0,1,1,"","predict_full_model"]],"tigramite.models.Prediction":[[0,1,1,"","fit"],[0,1,1,"","get_predictors"],[0,1,1,"","get_test_array"],[0,1,1,"","get_train_array"],[0,1,1,"","predict"]],"tigramite.pcmci":[[0,0,1,"","PCMCI"]],"tigramite.pcmci.PCMCI":[[0,3,1,"","N"],[0,3,1,"","T"],[0,3,1,"","all_parents"],[0,1,1,"","get_graph_from_pmatrix"],[0,1,1,"","get_lagged_dependencies"],[0,3,1,"","iterations"],[0,1,1,"","print_results"],[0,1,1,"","print_significant_links"],[0,3,1,"","pval_max"],[0,1,1,"","return_parents_dict"],[0,1,1,"","return_significant_links"],[0,1,1,"","run_bivci"],[0,1,1,"","run_fullci"],[0,1,1,"","run_mci"],[0,1,1,"","run_pc_stable"],[0,1,1,"","run_pcalg"],[0,1,1,"","run_pcalg_non_timeseries_data"],[0,1,1,"","run_pcmci"],[0,1,1,"","run_pcmciplus"],[0,3,1,"","val_min"]],"tigramite.plotting":[[0,4,1,"","plot_densityplots"],[0,4,1,"","plot_graph"],[0,4,1,"","plot_lagfuncs"],[0,4,1,"","plot_mediation_graph"],[0,4,1,"","plot_mediation_time_series_graph"],[0,4,1,"","plot_scatterplots"],[0,4,1,"","plot_time_series_graph"],[0,4,1,"","plot_timeseries"],[0,4,1,"","plot_tsg"],[0,0,1,"","setup_density_matrix"],[0,0,1,"","setup_matrix"],[0,0,1,"","setup_scatter_matrix"],[0,4,1,"","write_csv"]],"tigramite.plotting.setup_density_matrix":[[0,1,1,"","add_densityplot"],[0,1,1,"","adjustfig"]],"tigramite.plotting.setup_matrix":[[0,1,1,"","add_lagfuncs"],[0,1,1,"","savefig"]],"tigramite.plotting.setup_scatter_matrix":[[0,1,1,"","add_scatterplot"],[0,1,1,"","adjustfig"]],"tigramite.rpcmci":[[0,0,1,"","RPCMCI"]],"tigramite.rpcmci.RPCMCI":[[0,1,1,"","run_rpcmci"]],"tigramite.toymodels":[[0,2,0,"-","structural_causal_processes"]],"tigramite.toymodels.structural_causal_processes":[[0,4,1,"","check_stationarity"],[0,4,1,"","dag_to_links"],[0,4,1,"","generate_structural_causal_process"],[0,4,1,"","links_to_graph"],[0,4,1,"","structural_causal_process"],[0,4,1,"","structural_causal_process_ensemble"],[0,4,1,"","var_process"]]},"objnames":{"0":["py","class","Python class"],"1":["py","method","Python method"],"2":["py","module","Python module"],"3":["py","attribute","Python attribute"],"4":["py","function","Python function"],"5":["py","property","Python property"]},"objtypes":{"0":"py:class","1":"py:method","2":"py:module","3":"py:attribute","4":"py:function","5":"py:property"},"terms":{"0":0,"00000":0,"001":0,"0020538":0,"00431":0,"005":0,"01":0,"019":0,"023":0,"025":0,"05":0,"051122":0,"062829":0,"075310":0,"0803":0,"1":0,"10":0,"100":0,"1000":0,"10105":0,"1038":0,"1063":0,"11":0,"1103":0,"113115":0,"12":0,"12532404":0,"14":0,"15":0,"18":0,"19":0,"1975":0,"1d":0,"1e":0,"2":0,"20":0,"2000":0,"2001":0,"2002":0,"2005":0,"2007":0,"2009":0,"2011":0,"2012a":0,"2013":0,"2014":0,"2015":0,"2018":0,"2019":0,"2020":0,"2021":0,"2023":0,"204101":0,"219":0,"21st":0,"22":0,"2293":0,"2295":0,"2326":0,"2328":0,"24365041":0,"25":0,"250648072987":0,"25718002":0,"28":0,"285":0,"291":0,"2d":0,"2xtau_max":0,"2xtau_max_futur":0,"3":0,"30":0,"33":0,"3365":0,"3383":0,"34":0,"36897445":0,"36th":0,"37":0,"38250406":0,"3d":0,"4":0,"40":0,"4101":0,"42":0,"447":0,"459":0,"499":0,"5":0,"500":0,"5025050":0,"579_main_pap":0,"588":0,"6":0,"606":0,"618":0,"62829":0,"7":0,"8":0,"83":0,"8485ae387a981d783f8764e508151cd9":0,"8502":0,"9":0,"90":0,"92":0,"94e70705efae423efda1088614128d0b":0,"95":0,"99":0,"A":0,"As":0,"At":0,"But":0,"By":0,"For":0,"If":0,"In":0,"It":0,"Its":0,"No":0,"Not":0,"OR":0,"One":0,"That":0,"The":0,"Then":0,"There":0,"These":0,"To":0,"Will":0,"With":0,"_":0,"_get_single_residu":0,"_initialized_from":0,"_run_ancestral_removal_phas":0,"_run_dsep_removal_phas":0,"_run_non_ancestral_removal_phas":0,"_t":0,"ab":0,"about":0,"abov":0,"absent":0,"absmax":0,"absolut":0,"abstract":0,"abstractmethod":0,"ac":0,"accept":0,"accord":0,"account":0,"across":0,"act":0,"actual":0,"acycl":0,"ad":0,"adapt":0,"add":0,"add_densityplot":0,"add_densityplot_arg":0,"add_found_context_link_assumpt":0,"add_lagfunc":0,"add_lagfunc_arg":0,"add_scatterplot":0,"add_scatterplot_arg":0,"add_to_null_dist":0,"addit":0,"address":0,"adj":0,"adjac":0,"adjust":0,"adjust_plot":0,"adjustfig":0,"adjustment_set":0,"admg":0,"adv":0,"advanc":0,"affect":0,"after":0,"afterward":0,"again":0,"aggregation_func":0,"agre":0,"ahead":0,"aic":0,"aip":0,"akaik":0,"al":0,"algorithm":0,"align":0,"all":0,"all_lag":0,"all_par":0,"alloc":0,"allow":0,"along":0,"alpha":0,"alpha_level":0,"alpha_or_thr":0,"alreadi":0,"also":0,"altern":0,"alternative_condit":0,"although":0,"alwai":0,"ambigu":0,"ambiguous_tripl":0,"amc":0,"amend":0,"among":0,"an":0,"analysis_mod":0,"analyt":0,"analyz":0,"anc_all_i":0,"anc_all_x":0,"anc_all_z":0,"anc_i":0,"anc_x":0,"anc_xi":0,"ancestor":0,"ancestr":0,"ani":0,"ann":0,"anneal":0,"anoth":0,"apds_t":0,"appear":0,"appendix":0,"appli":0,"applic":0,"approach":0,"appropri":0,"approxim":0,"apr":0,"ar":0,"arang":0,"arbitrari":0,"argument":0,"around":0,"arrai":0,"array_lik":0,"array_mask":0,"arrohead":0,"arrow":0,"arrow_linewidth":0,"arrowhead_s":0,"articl":0,"artifici":0,"arxiv":0,"assess":0,"assign":0,"associ":0,"assum":0,"assume_exogenous_context":0,"assumpt":0,"ast":0,"asymptot":0,"attribut":0,"au_i":0,"au_j":0,"auai":0,"auto":0,"auto_coeff":0,"auto_first":0,"autocorr":0,"autocorrel":0,"autocovari":0,"autodepend":0,"automat":0,"autoregress":0,"auxadmg":0,"auxiliari":0,"avail":0,"averag":0,"avoid":0,"ax":0,"axi":0,"b":0,"back":0,"backdoor":0,"background":0,"backward":0,"bakirov":0,"bandwidth":0,"base":0,"basemap":0,"been":0,"befor":0,"beforehand":0,"begin":0,"being":0,"below":0,"benjamini":0,"best":0,"beta":0,"beta_i":0,"beta_x":0,"better":0,"between":0,"bia":0,"bias":0,"bic":0,"bigger":0,"bin":0,"binari":0,"bindata":0,"biostatist":0,"bishop":0,"bivci":0,"black":0,"block":0,"block_len":0,"blocked_medi":0,"blue":0,"bold":0,"bool":0,"boolean":0,"boostrap":0,"boot_blocklength":0,"boot_sampl":0,"bootsrap":0,"bootstrap":0,"both":0,"bottom":0,"bound":0,"box":0,"breadth":0,"break":0,"break_once_separ":0,"briefli":0,"butterworth":0,"c":0,"c_":0,"c_t":0,"call":0,"callabl":0,"cambridg":0,"can":0,"canada":0,"canon":0,"cardin":0,"care":0,"cartopi":0,"case":0,"categor":0,"categori":0,"caus":0,"causal_result":0,"causaleffect":0,"causalmedi":0,"cc":0,"ccr":0,"cd":0,"cdf":0,"cdot":0,"ce":0,"certain":0,"cf_delta":0,"cf_intervention_value_x":0,"chain":0,"chang":0,"change_from":0,"change_to":0,"chao":0,"charact":0,"characterist":0,"check":0,"check_optim":0,"check_optimality_cond":0,"check_shortest_path":0,"check_sm_overlap":0,"check_stationar":0,"check_xys_path":0,"chi":0,"chi2":0,"child":0,"choic":0,"chosen":0,"ci":0,"circ":0,"cite":0,"ckdtree":0,"claim":0,"clash":0,"class":0,"classif":0,"classifi":0,"clean":0,"clean_link_assumpt":0,"clean_system_link_assumpt":0,"close":0,"cmap":0,"cmap_edg":0,"cmap_nod":0,"cmi":0,"cmiknn":0,"cmiknnmix":0,"cmisymb":0,"code":0,"coef1":0,"coef2":0,"coeff":0,"coeffici":0,"collid":0,"collider_par":0,"colliders_minimized_optim":0,"colliders_onli":0,"color":0,"colorbar":0,"colormap":0,"column":0,"com":0,"combin":0,"come":0,"command":0,"common":0,"commun":0,"compar":0,"comparison":0,"compat":0,"complet":0,"complex":0,"complic":0,"compon":0,"comprehens":0,"comput":0,"compute_ancestor":0,"compute_wilks_lambda_cca":0,"concret":0,"cond_ind_test":0,"condindtest":0,"condition":0,"conditional_estim":0,"conditional_model":0,"conditions_data":0,"conduct":0,"conf_blocklength":0,"conf_lev":0,"conf_low":0,"conf_matrix":0,"conf_sampl":0,"conf_upp":0,"confer":0,"confid":0,"confidence_interv":0,"configur":0,"conflict":0,"conflict_resolut":0,"confound":0,"consecut":0,"conserv":0,"consid":0,"consider":0,"considerd":0,"consist":0,"constant":0,"constrain":0,"constraint":0,"construct":0,"construct_arrai":0,"constructor":0,"contain":0,"contemp_collider_rul":0,"contemp_cond":0,"contemp_fract":0,"contemporan":0,"content":0,"context":0,"context_search":0,"context_system_result":0,"contextu":0,"conting":0,"continu":0,"contrast":0,"contribut":0,"control":0,"conveni":0,"converg":0,"convert":0,"coordin":0,"copula":0,"correct":0,"corrected_a":0,"correl":0,"correlation_typ":0,"correspond":0,"cost":0,"could":0,"count":0,"counter":0,"coupl":0,"covari":0,"cpu":0,"creat":0,"criteria":0,"criterion":0,"cross":0,"crosstab":0,"csv":0,"cube":0,"cube_root":0,"current":0,"curv":0,"curvatur":0,"curved_radiu":0,"custom":0,"cut":0,"cut_off":0,"cutoff":0,"cutperiod":0,"cython":0,"d":0,"d_z":0,"dag":0,"dag_to_link":0,"dash":0,"data_linewidth":0,"data_transform":0,"data_typ":0,"datafram":0,"dataset":0,"datatim":0,"dcor":0,"de":0,"deal":0,"debug":0,"decai":0,"decis":0,"def":0,"default":0,"default_rng":0,"defin":0,"definin":0,"definit":0,"degre":0,"delai":0,"delta":0,"denot":0,"densiti":0,"depend":0,"dependence_measur":0,"dependency_coeff":0,"dependency_func":0,"depict":0,"deprec":0,"depth":0,"deriv":0,"describ":0,"descript":0,"detail":0,"detect":0,"determin":0,"devianc":0,"deviat":0,"df":0,"diagon":0,"dict":0,"dictionari":0,"diff_g_f":0,"differ":0,"digamma":0,"digest":0,"digit":0,"dim":0,"dimens":0,"dimension":0,"direct":0,"direction":0,"directli":0,"disabl":0,"discov":0,"discover_dummy_system_link":0,"discover_lagged_and_context_system_link":0,"discover_lagged_context_system_link":0,"discover_system_system_link":0,"discoveri":0,"discret":0,"discuss":0,"disk":0,"dismiss":0,"dist":0,"distanc":0,"distribut":0,"divid":0,"do":0,"do_check":0,"doc":0,"docstr":0,"doe":0,"dof":0,"doi":0,"dpag":0,"draw":0,"drawn":0,"driver":0,"drton":0,"due":0,"dummi":0,"dummy_ci_test":0,"dummy_par":0,"dummy_search":0,"duplic":0,"dure":0,"dx":0,"dy":0,"dz":0,"e":0,"eaau4996":0,"each":0,"earth":0,"easi":0,"easier":0,"edg":0,"edge_tick":0,"edgemark":0,"effici":0,"eg":0,"either":0,"element":0,"elena":0,"els":0,"eman":0,"embed":0,"empir":0,"emploi":0,"empti":0,"empty_predictors_funct":0,"enabl":0,"enable_dataframe_based_preprocess":0,"encod":0,"end":0,"ends_with":0,"enforc":0,"ensembl":0,"ensemble_se":0,"ensur":0,"entir":0,"entri":0,"entropi":0,"enumer":0,"environ":0,"epsilon_":0,"eq":0,"equal":0,"equival":0,"error":0,"error_free_ann":0,"estim":0,"estimate_confid":0,"et":0,"eta":0,"etc":0,"evalu":0,"even":0,"everi":0,"exampl":0,"except":0,"exclud":0,"exclude_i":0,"exclude_j":0,"exclude_k":0,"exclude_self_effect":0,"execut":0,"exist":0,"expect":0,"experi":0,"experiment":0,"expert":0,"expert_knowledg":0,"explain":0,"explicitli":0,"extend":0,"extern":0,"extract":0,"extraz":0,"f":0,"f_x":0,"f_y":0,"factor":0,"factual":0,"faculti":0,"fail":0,"faith":0,"fall":0,"fals":0,"fancyarrowpatch":0,"fang":0,"faster":0,"fdr":0,"fdr_bh":0,"fdr_method":0,"featur":0,"feedback":0,"fienberg":0,"fig":0,"fig_ax":0,"figsiz":0,"figur":0,"file":0,"fill":0,"filter":0,"final":0,"fine":0,"finit":0,"first":0,"firstli":0,"fit":0,"fit_bootstrap_of":0,"fit_full_model":0,"fit_model":0,"fit_model_bootstrap":0,"fit_natural_direct_effect":0,"fit_result":0,"fit_total_effect":0,"fit_wright_effect":0,"fitsetup":0,"fix":0,"fix_all_edges_before_final_orient":0,"fixed_thr":0,"flag":0,"flaxman":0,"flexibl":0,"flexibli":0,"float":0,"fold":0,"follow":0,"fontsiz":0,"forbidden_nod":0,"forc":0,"forecast":0,"form":0,"format":0,"formula":0,"forward":0,"found":0,"four":0,"frac":0,"fraction":0,"framework":0,"frawn":0,"free":0,"freedom":0,"frenzel":0,"friendli":0,"from":0,"from_autocorrel":0,"full":0,"fullci":0,"fulli":0,"func":0,"function_arg":0,"further":0,"furthermor":0,"futur":0,"g":0,"gabor":0,"gatewai":0,"gauss_pr":0,"gaussian":0,"gaussianprocessregressor":0,"gaussprocreg":0,"gaussprocregtorch":0,"gau\u00df":0,"generate_and_save_nulldist":0,"generate_nulldist":0,"generate_structural_causal_process":0,"gerhardu":0,"get":0,"get_ac":0,"get_acf":0,"get_all_ac":0,"get_all_amc":0,"get_amc":0,"get_analytic_confid":0,"get_analytic_signific":0,"get_block_length":0,"get_bootstrap_confid":0,"get_bootstrap_of":0,"get_c":0,"get_ce_max":0,"get_coef":0,"get_coeff":0,"get_conditional_entropi":0,"get_conditional_mc":0,"get_confid":0,"get_dependence_measur":0,"get_dependence_measure_raw":0,"get_dict_from_graph":0,"get_fixed_thres_signific":0,"get_general_fitted_model":0,"get_general_predict":0,"get_graph_from_dict":0,"get_graph_from_link":0,"get_graph_from_pmatrix":0,"get_joint_c":0,"get_joint_ce_matrix":0,"get_joint_mc":0,"get_lagged_depend":0,"get_links_from_graph":0,"get_mc":0,"get_measur":0,"get_medi":0,"get_mediation_graph_data":0,"get_model_selection_criterion":0,"get_optimal_set":0,"get_predictor":0,"get_residuals_cov_mean":0,"get_shuffle_signific":0,"get_test_arrai":0,"get_train_arrai":0,"get_tsg":0,"get_val_matrix":0,"getgroundtruthgraph":0,"github":0,"give":0,"given":0,"go":0,"gp":0,"gp_param":0,"gpdc":0,"gpdc_torch":0,"gpdctorch":0,"gpytorch":0,"graph":0,"graph_data":0,"graph_is_mag":0,"graph_typ":0,"graphic":0,"greater":0,"grei":0,"grey_masked_sampl":0,"grid":0,"ground":0,"gsquar":0,"gt_std_matrix":0,"guarante":0,"guidanc":0,"g\u00fcnther":0,"h":0,"ha":0,"han":0,"handl":0,"hard":0,"harri":0,"has_path":0,"hash":0,"hat":0,"have":0,"head":0,"heavisid":0,"heigth":0,"help":0,"helper":0,"henc":0,"here":0,"heteroskedast":0,"hidden":0,"hidden_vari":0,"high":0,"higher":0,"hochberg":0,"hold":0,"holland":0,"homoskedast":0,"horizont":0,"hot":0,"how":0,"howev":0,"html":0,"http":0,"hyper":0,"hyperparamet":0,"hypothes":0,"hypothesi":0,"hypothet":0,"i":0,"i_":0,"i_t":0,"id":0,"idea":0,"identif":0,"identifi":0,"identifii":0,"idx":0,"ie":0,"ignor":0,"ignore_identifi":0,"ii":0,"iint":0,"implement":0,"impli":0,"import":0,"importantli":0,"impos":0,"improv":0,"incl":0,"includ":0,"include_lagzero_link":0,"include_lagzero_par":0,"include_neighbor":0,"incom":0,"independence_tests_bas":0,"index":0,"indirect":0,"individu":0,"induc":0,"inf":0,"infer":0,"infin":0,"infinit":0,"info":0,"inform":0,"inherit":0,"initi":0,"initial_valu":0,"inner_edg":0,"inner_edge_styl":0,"inno_cov":0,"innov":0,"input":0,"instanc":0,"instanti":0,"instead":0,"instruct":0,"int":0,"integ":0,"intellig":0,"interdisciplinari":0,"interest":0,"intermedi":0,"intern":0,"interpret":0,"interv":0,"interven":0,"intervent":0,"intervention_data":0,"intervention_typ":0,"introduc":0,"introduct":0,"inv_inno_cov":0,"invalid":0,"invers":0,"inverse_transform":0,"invit":0,"involv":0,"irrelev":0,"issu":0,"iter":0,"its":0,"itself":0,"j":0,"j_":0,"j_t":0,"jakob":0,"jana":0,"joblib":0,"john":0,"joint":0,"joint_c":0,"joint_ce_matrix":0,"joint_mc":0,"jointli":0,"journal":0,"just":0,"k":0,"k_":0,"kdeplot":0,"keep":0,"kei":0,"kernel":0,"kind":0,"knn":0,"knowledg":0,"known":0,"kretschmer":0,"kwarg":0,"l":0,"label":0,"label_color":0,"label_fonts":0,"label_rotation_left":0,"label_space_left":0,"label_space_top":0,"lafferti":0,"lag":0,"lag1":0,"lag2":0,"lag_arrai":0,"lag_i":0,"lag_mod":0,"lag_unit":0,"lagfunct":0,"lagged_context_dummy_par":0,"lagged_par":0,"lambda":0,"larg":0,"larger":0,"largest":0,"largest_time_step":0,"larri":0,"last":0,"latent":0,"later":0,"latest":0,"latter":0,"lead":0,"learn":0,"least":0,"leav":0,"left":0,"legend":0,"legend_fonts":0,"legend_width":0,"len":0,"length":0,"less":0,"let":0,"lett":0,"level":0,"lightgrei":0,"like":0,"likelihood":0,"limit":0,"line":0,"linear":0,"linear_model":0,"linearmedi":0,"linearregress":0,"linewidth":0,"link":0,"link_assumpt":0,"link_attribut":0,"link_coeff":0,"link_colorbar_label":0,"link_label_fonts":0,"link_matrix":0,"link_typ":0,"link_width":0,"links_coeff":0,"links_to_graph":0,"list":0,"literatur":0,"liu":0,"load":0,"local":0,"log":0,"long":0,"look":0,"low":0,"lower":0,"lowhighpass_filt":0,"m":0,"mach":0,"machin":0,"made":0,"mader":0,"mag":0,"magnitud":0,"mai":0,"main":0,"major":0,"make":0,"mani":0,"manner":0,"map":0,"margin":0,"maria":0,"mark":0,"marker":0,"markers":0,"markov":0,"marlen":0,"mask":0,"mask_typ":0,"mass":0,"match":0,"mathbf":0,"mathcal":0,"mathia":0,"matplotlib":0,"matric":0,"matrix":0,"matrix_lag":0,"matter":0,"max":0,"max_":0,"max_ann":0,"max_combin":0,"max_cond_px":0,"max_conds_dim":0,"max_conds_pi":0,"max_conds_px":0,"max_conds_px_lag":0,"max_corr":0,"max_delai":0,"max_lag":0,"max_lag_or_tau_max":0,"max_p_glob":0,"max_p_non_ancestr":0,"max_pds_set":0,"max_q_glob":0,"max_transit":0,"max_x":0,"maxim":0,"maximum":0,"mce":0,"mci":0,"mean":0,"meaning":0,"measur":0,"med":0,"member":0,"memori":0,"ment":0,"method":0,"method_arg":0,"michail":0,"middl":0,"might":0,"min_x":0,"ming":0,"minim":0,"minimized_optim":0,"minimum":0,"minu":0,"miss":0,"missing_flag":0,"mit":0,"mix":0,"mixed_data_estim":0,"mixed_fit":0,"mlr":0,"mmr":0,"mode":0,"model_param":0,"model_selection_fold":0,"modul":0,"modulo":0,"momentari":0,"more":0,"moreov":0,"most":0,"mostli":0,"motif":0,"much":0,"mult_corr":0,"multi":0,"multidimension":0,"multinomi":0,"multioutput":0,"multioutputregressor":0,"multipl":0,"multivari":0,"must":0,"mutual":0,"n":0,"n_en":0,"n_featur":0,"n_intervent":0,"n_job":0,"n_preliminary_iter":0,"n_regim":0,"n_sampl":0,"n_symb":0,"naftali":0,"nail":0,"name":0,"nan":0,"napds_t":0,"nat":0,"natur":0,"naturaleffects_graphmedi":0,"nb_node":0,"ncomms9502":0,"nde":0,"nearest":0,"nearestneighbor":0,"necessari":0,"need":0,"neg":0,"neighbor":0,"neighbour":0,"neither":0,"nest":0,"net_to_tsg":0,"network":0,"neural":0,"neurip":0,"neurosci":0,"never":0,"nevertheless":0,"new":0,"new_data":0,"next":0,"niehgbor":0,"ninad":0,"no_apr":0,"no_nois":0,"no_non_ancestral_phas":0,"node":0,"node_aspect":0,"node_classif":0,"node_colorbar_label":0,"node_label_s":0,"node_po":0,"node_s":0,"node_tick":0,"nois":0,"noise_dist":0,"noise_mean":0,"noise_se":0,"noise_sigma":0,"non":0,"non_rep":0,"none":0,"nonlinear":0,"nonparanorm":0,"nonvalid":0,"nonzero":0,"nor":0,"normal":0,"normal_data":0,"normalize_by_delta":0,"note":0,"notion":0,"notk":0,"novemb":0,"now":0,"nowack":0,"np":0,"npz":0,"null":0,"null_dist":0,"null_dist_filenam":0,"nulldist":0,"num_compon":0,"num_iter":0,"num_regim":0,"number":0,"numer":0,"numpi":0,"o":0,"object":0,"observ":0,"observed_context_nod":0,"observed_context_par":0,"observed_var":0,"obtain":0,"occur":0,"octob":0,"off":0,"offset":0,"ol":0,"onc":0,"one":0,"onli":0,"only_non_causal_path":0,"opac":0,"oper":0,"opposit":0,"optim":0,"optimality_cond_des_ym":0,"optimality_cond_i":0,"optimz":0,"option":0,"oracl":0,"oracle_conditional_independ":0,"oracleci":0,"order":0,"ordin":0,"ordinal_patt_arrai":0,"ordinari":0,"org":0,"orient":0,"orient_comtemp":0,"orient_contemp":0,"origin":0,"orrd":0,"oset":0,"oset_":0,"other":0,"otherwis":0,"otion":0,"our":0,"out":0,"outer":0,"output":0,"over":0,"overlaid":0,"overlap":0,"overrid":0,"overview":0,"overwrit":0,"p":0,"p_matrix":0,"p_valu":0,"pa":0,"packag":0,"page":0,"pair":0,"pairwis":0,"pairwisemultci":0,"panel":0,"paper":0,"paral":0,"parallel":0,"paramet":0,"parametr":0,"paranorm":0,"parcorr":0,"parcorr_mult":0,"parcorr_wl":0,"parcorrmult":0,"parcorrwl":0,"parent":0,"parent_node_id":0,"parents_dict":0,"parents_neighbors_coeff":0,"parents_of_lag":0,"parents_onli":0,"pars":0,"part":0,"partial":0,"particular":0,"pass":0,"pass_period":0,"path":0,"path_node_arrai":0,"path_val_matrix":0,"pathwai":0,"patt":0,"patt_mask":0,"patt_tim":0,"pattern":0,"pc":0,"pc1":0,"pc_1":0,"pc_alpha":0,"pcmciplu":0,"pdf":0,"peak":0,"pearl":0,"pearson":0,"percentag":0,"percentil":0,"perform":0,"period":0,"perkov":0,"permut":0,"perp":0,"persist":0,"perspect":0,"pfeifer":0,"phase":0,"phi":0,"phy":0,"physrev":0,"pip":0,"plai":0,"platecarre":0,"pleas":0,"plot_densityplot":0,"plot_graph":0,"plot_gridlin":0,"plot_lagfunc":0,"plot_mediation_graph":0,"plot_mediation_time_series_graph":0,"plot_scatterplot":0,"plot_time_series_graph":0,"plot_timeseri":0,"plot_tsg":0,"plu":0,"po":0,"point":0,"pomp":0,"posit":0,"possibl":0,"possibli":0,"post":0,"potenti":0,"power":0,"pp":0,"pq_matrix":0,"practic":0,"pre":0,"precis":0,"precomput":0,"pred_param":0,"predict_bootstrap_of":0,"predict_full_model":0,"predict_natural_direct_effect":0,"predict_natural_direct_effect_funct":0,"predict_total_effect":0,"predict_wright_effect":0,"prediction_model":0,"predictor":0,"prelim_onli":0,"prelim_rul":0,"prelim_with_collider_rul":0,"preliminari":0,"preprocess":0,"present":0,"preserv":0,"press":0,"previou":0,"prime":0,"principl":0,"print":0,"print_array_info":0,"print_info":0,"print_result":0,"print_significant_link":0,"prior":0,"priorit":0,"prl":0,"probabilit":0,"probabl":0,"procedur":0,"proceed":0,"processor":0,"project":0,"proper":0,"properti":0,"provid":0,"pseudcod":0,"pseudoc":0,"pseudocod":0,"psi":0,"psycholog":0,"purpos":0,"pval":0,"pval_max":0,"pyplot":0,"python":0,"qualiti":0,"quantifi":0,"quantil":0,"quantile_bin_arrai":0,"quantiti":0,"r":0,"r10":0,"r9":0,"r_x":0,"r_y":0,"rais":0,"randn":0,"random":0,"random_st":0,"randomli":0,"randomst":0,"rang":0,"rank":0,"rate":0,"rather":0,"ratio":0,"rdbu_r":0,"re":0,"realiz":0,"recal":0,"recommend":0,"reconstruct":0,"recycle_residu":0,"red":0,"reduc":0,"ref":0,"refer":0,"reference_point":0,"reference_value_x":0,"regard":0,"regim":0,"regress":0,"regressionci":0,"regular":0,"rel":0,"relat":0,"relationship":0,"relev":0,"remain":0,"remember_only_par":0,"remov":0,"remove_dummy_link_assumpt":0,"remove_missing_upto_maxlag":0,"remove_overlap":0,"repeat":0,"replac":0,"repo":0,"repres":0,"represent":0,"request":0,"requir":0,"research":0,"reserv":0,"reset_lagged_link":0,"residu":0,"resolv":0,"respect":0,"restrict":0,"result":0,"return":0,"return_cleaned_xyz":0,"return_data":0,"return_dict":0,"return_further_pred_result":0,"return_individual_bootstrap_result":0,"return_null_dist":0,"return_parents_dict":0,"return_path":0,"return_separate_set":0,"return_significant_link":0,"rev":0,"rho":0,"richardson":0,"right":0,"rizzo":0,"robust":0,"robust_parcorr":0,"robustifi":0,"robustparcorr":0,"role":0,"root":0,"rotat":0,"row":0,"rtype":0,"rule":0,"run":0,"run_bivci":0,"run_fullci":0,"run_jpcmciplu":0,"run_lpcmci":0,"run_mci":0,"run_pc_stabl":0,"run_pcalg":0,"run_pcalg_non_timeseries_data":0,"run_pcmci":0,"run_pcmciplu":0,"run_rpcmci":0,"run_test":0,"run_test_raw":0,"rung":0,"runge18a":0,"runtim":0,"s2":0,"s3":0,"s41467":0,"s43017":0,"saggioro":0,"same":0,"sampl":0,"sample_s":0,"saniti":0,"save":0,"save_iter":0,"save_nam":0,"savefig":0,"scale":0,"scatter":0,"scatterplot":0,"sci":0,"sciadv":0,"scienc":0,"sciencemag":0,"scikit":0,"scipi":0,"scitat":0,"score":0,"script":0,"seaborn":0,"search":0,"second":0,"section":0,"see":0,"seed":0,"seen":0,"sejdinov":0,"select":0,"selected_dataset":0,"selected_link":0,"selected_target":0,"selected_vari":0,"selection_var":0,"self":0,"semiparametr":0,"sens":0,"separ":0,"sepset":0,"set":0,"set_datafram":0,"set_mask_typ":0,"setminu":0,"setter":0,"setup":0,"setup_arg":0,"setup_density_matrix":0,"setup_matrix":0,"setup_scatter_matrix":0,"seventeenth":0,"sever":0,"shape":0,"share":0,"shift":0,"shorter":0,"should":0,"show":0,"show_autodependency_lag":0,"show_colorbar":0,"show_label":0,"show_marginal_densities_on_diagon":0,"show_meanlin":0,"shown":0,"shpitser":0,"shuffl":0,"shuffle_neighbor":0,"shuffle_test":0,"side":0,"sig_blocklength":0,"sig_sampl":0,"sig_thr":0,"sigma":0,"signfic":0,"signficic":0,"signific":0,"significantli":0,"sim":0,"similarli":0,"simpl":0,"simpli":0,"sinc":0,"singl":0,"size":0,"skeleton":0,"skip":0,"skip_ticks_data_i":0,"skip_ticks_data_x":0,"sklearn":0,"slice":0,"slightli":0,"small":0,"smaller":0,"smooth":0,"smooth_width":0,"smoothing_gaussian_sigma_in_step":0,"sn":0,"snskdeplot_arg":0,"snskdeplot_diagonal_arg":0,"soft":0,"some":0,"sort":0,"sound":0,"sourc":0,"space":0,"space_context":0,"space_context_nod":0,"space_dummi":0,"spatial":0,"spatio":0,"special":0,"special_nod":0,"specif":0,"specifi":0,"spirt":0,"squar":0,"stack":0,"stand":0,"standard":0,"standard_color_link":0,"standard_color_nod":0,"standardscal":0,"star":0,"start":0,"starts_with":0,"stat":0,"static":0,"stationar":0,"stationari":0,"stationary_dag":0,"statist":0,"std":0,"stem":0,"step":0,"steps_ahead":0,"still":0,"stop":0,"store":0,"str":0,"straight":0,"strength":0,"string":0,"structur":0,"structural_causal_process":0,"structural_causal_process_ensembl":0,"student":0,"style":0,"sub":0,"subplot":0,"subset":0,"subspac":0,"suffici":0,"suggest":0,"suitabl":0,"sum":0,"sum_":0,"summar":0,"superset":0,"supplement":0,"suppli":0,"support":0,"sure":0,"surrog":0,"suscept":0,"switch":0,"switch_thr":0,"symb_arrai":0,"symbol":0,"symbolifi":0,"symmetr":0,"system":0,"system_nod":0,"system_search":0,"szeke":0,"t":0,"t_i":0,"t_max":0,"t_miss":0,"tail":0,"take":0,"taken":0,"target":0,"target_predictor":0,"tau":0,"tau_":0,"tau_max":0,"tau_min":0,"tau_mix":0,"taui":0,"tauj":0,"taumax":0,"technic":0,"techniqu":0,"tempor":0,"term":0,"termin":0,"test_indic":0,"than":0,"thei":0,"theirs":0,"them":0,"themselv":0,"theorem":0,"theoret":0,"theori":0,"thi":0,"thm":0,"those":0,"thre":0,"threshold":0,"through":0,"throw":0,"ti":0,"tick":0,"tick_label_s":0,"tickmark":0,"tild":0,"time_bin_length":0,"time_bin_with_mask":0,"time_context":0,"time_context_nod":0,"time_dummi":0,"time_label":0,"time_lag":0,"time_offset":0,"timelabel":0,"togeth":0,"too":0,"top":0,"toronto":0,"total":0,"toward":0,"toy_setup":0,"trafo2norm":0,"train":0,"train_indic":0,"transfer":0,"transform":0,"transform_interventions_and_predict":0,"transient":0,"transient_fract":0,"transit":0,"translat":0,"treat":0,"tripl":0,"true":0,"true_par":0,"true_parent_neighbor":0,"truncat":0,"truth":0,"try":0,"tsagri":0,"tsg":0,"tsg_path_val_matrix":0,"tsg_to_net":0,"tune":0,"tupl":0,"tutori":0,"twice":0,"two":0,"two_sided_thr":0,"type":0,"typic":0,"u":0,"uai":0,"uai2020":0,"uint":0,"un":0,"uncertainti":0,"unclear":0,"uncondit":0,"undecid":0,"under":0,"underli":0,"undirect":0,"uniform":0,"uniqu":0,"unit":0,"univari":0,"unlik":0,"unobserv":0,"unori":0,"unrestrict":0,"unshield":0,"unshuffl":0,"up":0,"update_middle_mark":0,"upper":0,"us":0,"use_a_pds_t_for_major":0,"use_mediation_impl_for_total_effect_fallback":0,"user":0,"util":0,"v":0,"v84":0,"val":0,"val_matrix":0,"val_min":0,"val_onli":0,"valid":0,"valu":0,"vanderweel":0,"var":0,"var1":0,"var2":0,"var_nam":0,"var_network":0,"var_process":0,"var_unit":0,"varando":0,"vari":0,"variabl":0,"varianc":0,"variant":0,"varibl":0,"variou":0,"varlag":0,"varx":0,"vector":0,"vector_var":0,"verbos":0,"veri":0,"version":0,"vertic":0,"via":0,"view":0,"visual":0,"vmax_edg":0,"vmax_nod":0,"vmin_edg":0,"vmin_nod":0,"volum":0,"w":0,"wa":0,"wai":0,"wasserman":0,"we":0,"weibul":0,"weight":0,"weighted_avg_and_std":0,"well":0,"when":0,"where":0,"whether":0,"which":0,"while":0,"whose":0,"wide":0,"widehat":0,"width":0,"wildcard":0,"wilj":0,"wilk":0,"wilks_lambda":0,"window":0,"window_s":0,"within":0,"without":0,"wl":0,"work":0,"worker":0,"world":0,"would":0,"wrapper":0,"wright":0,"write":0,"write_csv":0,"www":0,"x":0,"x_":0,"x_base":0,"x_i":0,"x_j":0,"x_t":0,"x_type":0,"xy":0,"xyz":0,"xz":0,"y":0,"y_":0,"y_base":0,"y_t":0,"y_type":0,"yield":0,"you":0,"yuan":0,"yz":0,"z":0,"z_i":0,"z_j":0,"z_t":0,"z_type":0,"zero":0},"titles":["Welcome to Tigramite\u2019s documentation!"],"titleterms":{"":0,"analysi":0,"causal":0,"causal_effect":0,"causal_medi":0,"condit":0,"data":0,"data_process":0,"document":0,"effect":0,"function":0,"gener":0,"independ":0,"independence_test":0,"indic":0,"jpcmciplu":0,"lpcmci":0,"mediat":0,"model":0,"pcmci":0,"plot":0,"predict":0,"process":0,"rpcmci":0,"seri":0,"tabl":0,"test":0,"tigramit":0,"time":0,"toi":0,"toymodel":0,"welcom":0}})