<!DOCTYPE html>

<html lang="en" data-content_root="./">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Index &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=db26dd79" />
    <link rel="stylesheet" type="text/css" href="_static/alabaster.css?v=19da42e6" />
    <script src="_static/documentation_options.js?v=625b3a9a"></script>
    <script src="_static/doctools.js?v=aa79a7b1"></script>
    <script src="_static/sphinx_highlight.js?v=4825356b"></script>
    <link rel="index" title="Index" href="#" />
    <link rel="search" title="Search" href="search.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#_"><strong>_</strong></a>
 | <a href="#A"><strong>A</strong></a>
 | <a href="#B"><strong>B</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#D"><strong>D</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#J"><strong>J</strong></a>
 | <a href="#L"><strong>L</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#N"><strong>N</strong></a>
 | <a href="#O"><strong>O</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#Q"><strong>Q</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#V"><strong>V</strong></a>
 | <a href="#W"><strong>W</strong></a>
 
</div>
<h2 id="_">_</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self._initialized_from">_initialized_from (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.plotting.setup_density_matrix.add_densityplot">add_densityplot() (tigramite.plotting.setup_density_matrix method)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.add_found_context_link_assumptions">add_found_context_link_assumptions() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
      <li><a href="index.html#tigramite.plotting.setup_matrix.add_lagfuncs">add_lagfuncs() (tigramite.plotting.setup_matrix method)</a>
</li>
      <li><a href="index.html#tigramite.plotting.setup_scatter_matrix.add_scatterplot">add_scatterplot() (tigramite.plotting.setup_scatter_matrix method)</a>
</li>
      <li><a href="index.html#tigramite.plotting.setup_density_matrix.adjustfig">adjustfig() (tigramite.plotting.setup_density_matrix method)</a>

      <ul>
        <li><a href="index.html#tigramite.plotting.setup_scatter_matrix.adjustfig">(tigramite.plotting.setup_scatter_matrix method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.all_parents">all_parents (tigramite.jpcmciplus.JPCMCIplus attribute)</a>

      <ul>
        <li><a href="index.html#tigramite.pcmci.PCMCI.all_parents">(tigramite.pcmci.PCMCI attribute)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.analysis_mode">analysis_mode (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.assume_exogenous_context">assume_exogenous_context() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.bootstrap">bootstrap (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects">CausalEffects (class in tigramite.causal_effects)</a>
</li>
      <li><a href="index.html#tigramite.causal_mediation.CausalMediation">CausalMediation (class in tigramite.causal_mediation)</a>
</li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.check_optimality">check_optimality() (tigramite.causal_effects.CausalEffects method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.check_shortest_path">check_shortest_path() (tigramite.independence_tests.oracle_conditional_independence.OracleCI method)</a>
</li>
      <li><a href="index.html#tigramite.toymodels.structural_causal_processes.check_stationarity">check_stationarity() (in module tigramite.toymodels.structural_causal_processes)</a>
</li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.check_XYS_paths">check_XYS_paths() (tigramite.causal_effects.CausalEffects method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.clean_link_assumptions">clean_link_assumptions() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.clean_system_link_assumptions">clean_system_link_assumptions() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.cmiknn.CMIknn">CMIknn (class in tigramite.independence_tests.cmiknn)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.cmisymb.CMIsymb">CMIsymb (class in tigramite.independence_tests.cmisymb)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.parcorr_mult.ParCorrMult.compute_wilks_lambda_cca">compute_wilks_lambda_cca() (tigramite.independence_tests.parcorr_mult.ParCorrMult method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest">CondIndTest (class in tigramite.independence_tests.independence_tests_base)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.construct_array">construct_array() (tigramite.data_processing.DataFrame method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="D">D</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.toymodels.structural_causal_processes.dag_to_links">dag_to_links() (in module tigramite.toymodels.structural_causal_processes)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.data_type">data_type (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.DataFrame">DataFrame (class in tigramite.data_processing)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.datasets">datasets (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.datatime">datatime (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.discover_dummy_system_links">discover_dummy_system_links() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.discover_lagged_context_system_links">discover_lagged_context_system_links() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.discover_system_system_links">discover_system_system_links() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.dummy_ci_test">dummy_ci_test (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.dummy_parents">dummy_parents (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.models.Prediction.fit">fit() (tigramite.models.Prediction method)</a>
</li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.fit_bootstrap_of">fit_bootstrap_of() (tigramite.causal_effects.CausalEffects method)</a>
</li>
      <li><a href="index.html#tigramite.models.Models.fit_full_model">fit_full_model() (tigramite.models.Models method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.fit_model">fit_model() (tigramite.models.LinearMediation method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.models.LinearMediation.fit_model_bootstrap">fit_model_bootstrap() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.causal_mediation.CausalMediation.fit_natural_direct_effect">fit_natural_direct_effect() (tigramite.causal_mediation.CausalMediation method)</a>
</li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.fit_total_effect">fit_total_effect() (tigramite.causal_effects.CausalEffects method)</a>
</li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.fit_wright_effect">fit_wright_effect() (tigramite.causal_effects.CausalEffects method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.independence_tests.gpdc.GPDC.generate_and_save_nulldists">generate_and_save_nulldists() (tigramite.independence_tests.gpdc.GPDC method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.gpdc_torch.GPDCtorch.generate_and_save_nulldists">(tigramite.independence_tests.gpdc_torch.GPDCtorch method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.independence_tests.gpdc.GPDC.generate_nulldist">generate_nulldist() (tigramite.independence_tests.gpdc.GPDC method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.gpdc_torch.GPDCtorch.generate_nulldist">(tigramite.independence_tests.gpdc_torch.GPDCtorch method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.toymodels.structural_causal_processes.generate_structural_causal_process">generate_structural_causal_process() (in module tigramite.toymodels.structural_causal_processes)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_ace">get_ace() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.get_acf">get_acf() (in module tigramite.data_processing)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_acs">get_acs() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_all_ace">get_all_ace() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_all_acs">get_all_acs() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_all_amce">get_all_amce() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_amce">get_amce() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_analytic_confidence">get_analytic_confidence() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.parcorr.ParCorr.get_analytic_confidence">(tigramite.independence_tests.parcorr.ParCorr method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.robust_parcorr.RobustParCorr.get_analytic_confidence">(tigramite.independence_tests.robust_parcorr.RobustParCorr method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.independence_tests.gpdc.GPDC.get_analytic_significance">get_analytic_significance() (tigramite.independence_tests.gpdc.GPDC method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.gpdc_torch.GPDCtorch.get_analytic_significance">(tigramite.independence_tests.gpdc_torch.GPDCtorch method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gsquared.Gsquared.get_analytic_significance">(tigramite.independence_tests.gsquared.Gsquared method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_analytic_significance">(tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr.ParCorr.get_analytic_significance">(tigramite.independence_tests.parcorr.ParCorr method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr_mult.ParCorrMult.get_analytic_significance">(tigramite.independence_tests.parcorr_mult.ParCorrMult method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.regressionCI.RegressionCI.get_analytic_significance">(tigramite.independence_tests.regressionCI.RegressionCI method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.robust_parcorr.RobustParCorr.get_analytic_significance">(tigramite.independence_tests.robust_parcorr.RobustParCorr method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.data_processing.get_block_length">get_block_length() (in module tigramite.data_processing)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_bootstrap_confidence">get_bootstrap_confidence() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_bootstrap_of">get_bootstrap_of() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_ce">get_ce() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_ce_max">get_ce_max() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_coeff">get_coeff() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.Models.get_coefs">get_coefs() (tigramite.models.Models method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.cmiknn.CMIknn.get_conditional_entropy">get_conditional_entropy() (tigramite.independence_tests.cmiknn.CMIknn method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_conditional_mce">get_conditional_mce() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_confidence">get_confidence() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_confidence">(tigramite.independence_tests.oracle_conditional_independence.OracleCI method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.independence_tests.cmiknn.CMIknn.get_dependence_measure">get_dependence_measure() (tigramite.independence_tests.cmiknn.CMIknn method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.cmisymb.CMIsymb.get_dependence_measure">(tigramite.independence_tests.cmisymb.CMIsymb method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gpdc.GPDC.get_dependence_measure">(tigramite.independence_tests.gpdc.GPDC method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gpdc_torch.GPDCtorch.get_dependence_measure">(tigramite.independence_tests.gpdc_torch.GPDCtorch method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gsquared.Gsquared.get_dependence_measure">(tigramite.independence_tests.gsquared.Gsquared method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_dependence_measure">(tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr.ParCorr.get_dependence_measure">(tigramite.independence_tests.parcorr.ParCorr method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr_mult.ParCorrMult.get_dependence_measure">(tigramite.independence_tests.parcorr_mult.ParCorrMult method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr_wls.ParCorrWLS.get_dependence_measure">(tigramite.independence_tests.parcorr_wls.ParCorrWLS method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.regressionCI.RegressionCI.get_dependence_measure">(tigramite.independence_tests.regressionCI.RegressionCI method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.robust_parcorr.RobustParCorr.get_dependence_measure">(tigramite.independence_tests.robust_parcorr.RobustParCorr method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_dependence_measure_raw">get_dependence_measure_raw() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.graphs.Graphs.get_dict_from_graph">get_dict_from_graph() (tigramite.graphs.Graphs static method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_fixed_thres_significance">get_fixed_thres_significance() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
      <li><a href="index.html#tigramite.models.Models.get_general_fitted_model">get_general_fitted_model() (tigramite.models.Models method)</a>
</li>
      <li><a href="index.html#tigramite.models.Models.get_general_prediction">get_general_prediction() (tigramite.models.Models method)</a>
</li>
      <li><a href="index.html#tigramite.graphs.Graphs.get_graph_from_dict">get_graph_from_dict() (tigramite.graphs.Graphs static method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_graph_from_links">get_graph_from_links() (tigramite.independence_tests.oracle_conditional_independence.OracleCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.get_graph_from_pmatrix">get_graph_from_pmatrix() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_joint_ce">get_joint_ce() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_joint_ce_matrix">get_joint_ce_matrix() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_joint_mce">get_joint_mce() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.get_lagged_dependencies">get_lagged_dependencies() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_links_from_graph">get_links_from_graph() (tigramite.independence_tests.oracle_conditional_independence.OracleCI method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_mce">get_mce() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_measure">get_measure() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_measure">(tigramite.independence_tests.oracle_conditional_independence.OracleCI method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_mediation_graph_data">get_mediation_graph_data() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.graphs.Graphs.get_mediators">get_mediators() (tigramite.graphs.Graphs method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.cmiknn.CMIknn.get_model_selection_criterion">get_model_selection_criterion() (tigramite.independence_tests.cmiknn.CMIknn method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.gpdc.GPDC.get_model_selection_criterion">(tigramite.independence_tests.gpdc.GPDC method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gpdc_torch.GPDCtorch.get_model_selection_criterion">(tigramite.independence_tests.gpdc_torch.GPDCtorch method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_model_selection_criterion">(tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.get_model_selection_criterion">(tigramite.independence_tests.oracle_conditional_independence.OracleCI method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr.ParCorr.get_model_selection_criterion">(tigramite.independence_tests.parcorr.ParCorr method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr_mult.ParCorrMult.get_model_selection_criterion">(tigramite.independence_tests.parcorr_mult.ParCorrMult method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr_wls.ParCorrWLS.get_model_selection_criterion">(tigramite.independence_tests.parcorr_wls.ParCorrWLS method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.robust_parcorr.RobustParCorr.get_model_selection_criterion">(tigramite.independence_tests.robust_parcorr.RobustParCorr method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.get_optimal_set">get_optimal_set() (tigramite.causal_effects.CausalEffects method)</a>
</li>
      <li><a href="index.html#tigramite.models.Prediction.get_predictors">get_predictors() (tigramite.models.Prediction method)</a>
</li>
      <li><a href="index.html#tigramite.models.Models.get_residuals_cov_mean">get_residuals_cov_mean() (tigramite.models.Models method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.cmiknn.CMIknn.get_shuffle_significance">get_shuffle_significance() (tigramite.independence_tests.cmiknn.CMIknn method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.cmisymb.CMIsymb.get_shuffle_significance">(tigramite.independence_tests.cmisymb.CMIsymb method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gpdc.GPDC.get_shuffle_significance">(tigramite.independence_tests.gpdc.GPDC method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gpdc_torch.GPDCtorch.get_shuffle_significance">(tigramite.independence_tests.gpdc_torch.GPDCtorch method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_shuffle_significance">(tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr.ParCorr.get_shuffle_significance">(tigramite.independence_tests.parcorr.ParCorr method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr_mult.ParCorrMult.get_shuffle_significance">(tigramite.independence_tests.parcorr_mult.ParCorrMult method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr_wls.ParCorrWLS.get_shuffle_significance">(tigramite.independence_tests.parcorr_wls.ParCorrWLS method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.robust_parcorr.RobustParCorr.get_shuffle_significance">(tigramite.independence_tests.robust_parcorr.RobustParCorr method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.models.Prediction.get_test_array">get_test_array() (tigramite.models.Prediction method)</a>
</li>
      <li><a href="index.html#tigramite.models.Prediction.get_train_array">get_train_array() (tigramite.models.Prediction method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_tsg">get_tsg() (tigramite.models.LinearMediation method)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation.get_val_matrix">get_val_matrix() (tigramite.models.LinearMediation method)</a>

      <ul>
        <li><a href="index.html#tigramite.models.Models.get_val_matrix">(tigramite.models.Models method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.independence_tests.gpdc.GPDC">GPDC (class in tigramite.independence_tests.gpdc)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.gpdc_torch.GPDCtorch">GPDCtorch (class in tigramite.independence_tests.gpdc_torch)</a>
</li>
      <li><a href="index.html#tigramite.graphs.Graphs">Graphs (class in tigramite.graphs)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.gsquared.Gsquared">Gsquared (class in tigramite.independence_tests.gsquared)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.iterations">iterations (tigramite.jpcmciplus.JPCMCIplus attribute)</a>

      <ul>
        <li><a href="index.html#tigramite.pcmci.PCMCI.iterations">(tigramite.pcmci.PCMCI attribute)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="J">J</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus">JPCMCIplus (class in tigramite.jpcmciplus)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="L">L</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.largest_time_step">largest_time_step (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.models.LinearMediation">LinearMediation (class in tigramite.models)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.toymodels.structural_causal_processes.links_to_graph">links_to_graph() (in module tigramite.toymodels.structural_causal_processes)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.lowhighpass_filter">lowhighpass_filter() (in module tigramite.data_processing)</a>
</li>
      <li><a href="index.html#tigramite.lpcmci.LPCMCI">LPCMCI (class in tigramite.lpcmci)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.M">M (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.mask">mask (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.cmiknn.CMIknn.measure">measure (tigramite.independence_tests.cmiknn.CMIknn property)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.cmisymb.CMIsymb.measure">(tigramite.independence_tests.cmisymb.CMIsymb property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gpdc.GPDC.measure">(tigramite.independence_tests.gpdc.GPDC property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gpdc_torch.GPDCtorch.measure">(tigramite.independence_tests.gpdc_torch.GPDCtorch property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.gsquared.Gsquared.measure">(tigramite.independence_tests.gsquared.Gsquared property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.measure">(tigramite.independence_tests.independence_tests_base.CondIndTest property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.measure">(tigramite.independence_tests.oracle_conditional_independence.OracleCI property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr.ParCorr.measure">(tigramite.independence_tests.parcorr.ParCorr property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.parcorr_mult.ParCorrMult.measure">(tigramite.independence_tests.parcorr_mult.ParCorrMult property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.regressionCI.RegressionCI.measure">(tigramite.independence_tests.regressionCI.RegressionCI property)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.robust_parcorr.RobustParCorr.measure">(tigramite.independence_tests.robust_parcorr.RobustParCorr property)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.missing_flag">missing_flag (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.mode">mode (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
      <li><a href="index.html#tigramite.models.Models">Models (class in tigramite.models)</a>
</li>
      <li>
    module

      <ul>
        <li><a href="index.html#module-tigramite.data_processing">tigramite.data_processing</a>
</li>
        <li><a href="index.html#module-tigramite.plotting">tigramite.plotting</a>
</li>
        <li><a href="index.html#module-tigramite.toymodels.structural_causal_processes">tigramite.toymodels.structural_causal_processes</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.independence_tests.parcorr_mult.ParCorrMult.mult_corr">mult_corr() (tigramite.independence_tests.parcorr_mult.ParCorrMult method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="N">N</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.N">N (tigramite.data_processing.DataFrame.self attribute)</a>

      <ul>
        <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.N">(tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
        <li><a href="index.html#tigramite.pcmci.PCMCI.N">(tigramite.pcmci.PCMCI attribute)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.models.LinearMediation.net_to_tsg">net_to_tsg() (tigramite.models.LinearMediation method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="O">O</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.observed_context_parents">observed_context_parents (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI">OracleCI (class in tigramite.independence_tests.oracle_conditional_independence)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.ordinal_patt_array">ordinal_patt_array() (in module tigramite.data_processing)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.independence_tests.parcorr.ParCorr">ParCorr (class in tigramite.independence_tests.parcorr)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.parcorr_mult.ParCorrMult">ParCorrMult (class in tigramite.independence_tests.parcorr_mult)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.parcorr_wls.ParCorrWLS">ParCorrWLS (class in tigramite.independence_tests.parcorr_wls)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI">PCMCI (class in tigramite.pcmci)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_densityplots">plot_densityplots() (in module tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_graph">plot_graph() (in module tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_lagfuncs">plot_lagfuncs() (in module tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_mediation_graph">plot_mediation_graph() (in module tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_mediation_time_series_graph">plot_mediation_time_series_graph() (in module tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_scatterplots">plot_scatterplots() (in module tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_time_series_graph">plot_time_series_graph() (in module tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_timeseries">plot_timeseries() (in module tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.plot_tsg">plot_tsg() (in module tigramite.plotting)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.models.Prediction.predict">predict() (tigramite.models.Prediction method)</a>
</li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.predict_bootstrap_of">predict_bootstrap_of() (tigramite.causal_effects.CausalEffects method)</a>
</li>
      <li><a href="index.html#tigramite.models.Models.predict_full_model">predict_full_model() (tigramite.models.Models method)</a>
</li>
      <li><a href="index.html#tigramite.causal_mediation.CausalMediation.predict_natural_direct_effect">predict_natural_direct_effect() (tigramite.causal_mediation.CausalMediation method)</a>
</li>
      <li><a href="index.html#tigramite.causal_mediation.CausalMediation.predict_natural_direct_effect_function">predict_natural_direct_effect_function() (tigramite.causal_mediation.CausalMediation method)</a>
</li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.predict_total_effect">predict_total_effect() (tigramite.causal_effects.CausalEffects method)</a>
</li>
      <li><a href="index.html#tigramite.causal_effects.CausalEffects.predict_wright_effect">predict_wright_effect() (tigramite.causal_effects.CausalEffects method)</a>
</li>
      <li><a href="index.html#tigramite.models.Prediction">Prediction (class in tigramite.models)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.print_array_info">print_array_info() (tigramite.data_processing.DataFrame method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.print_info">print_info() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.print_results">print_results() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.print_significant_links">print_significant_links() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.pval_max">pval_max (tigramite.jpcmciplus.JPCMCIplus attribute)</a>

      <ul>
        <li><a href="index.html#tigramite.pcmci.PCMCI.pval_max">(tigramite.pcmci.PCMCI attribute)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="Q">Q</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.quantile_bin_array">quantile_bin_array() (in module tigramite.data_processing)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.reference_points">reference_points (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.regressionCI.RegressionCI">RegressionCI (class in tigramite.independence_tests.regressionCI)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.remove_dummy_link_assumptions">remove_dummy_link_assumptions() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.return_parents_dict">return_parents_dict() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.return_significant_links">return_significant_links() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.robust_parcorr.RobustParCorr">RobustParCorr (class in tigramite.independence_tests.robust_parcorr)</a>
</li>
      <li><a href="index.html#tigramite.rpcmci.RPCMCI">RPCMCI (class in tigramite.rpcmci)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.run_bivci">run_bivci() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.run_fullci">run_fullci() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.run_jpcmciplus">run_jpcmciplus() (tigramite.jpcmciplus.JPCMCIplus method)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.lpcmci.LPCMCI.run_lpcmci">run_lpcmci() (tigramite.lpcmci.LPCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.run_mci">run_mci() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.run_pc_stable">run_pc_stable() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.run_pcalg">run_pcalg() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.run_pcalg_non_timeseries_data">run_pcalg_non_timeseries_data() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.run_pcmci">run_pcmci() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.pcmci.PCMCI.run_pcmciplus">run_pcmciplus() (tigramite.pcmci.PCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.rpcmci.RPCMCI.run_rpcmci">run_rpcmci() (tigramite.rpcmci.RPCMCI method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.run_test">run_test() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.run_test">(tigramite.independence_tests.oracle_conditional_independence.OracleCI method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.run_test_raw">run_test_raw() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.plotting.setup_matrix.savefig">savefig() (tigramite.plotting.setup_matrix method)</a>
</li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.set_dataframe">set_dataframe() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.oracle_conditional_independence.OracleCI.set_dataframe">(tigramite.independence_tests.oracle_conditional_independence.OracleCI method)</a>
</li>
        <li><a href="index.html#tigramite.independence_tests.regressionCI.RegressionCI.set_dataframe">(tigramite.independence_tests.regressionCI.RegressionCI method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.set_mask_type">set_mask_type() (tigramite.independence_tests.independence_tests_base.CondIndTest method)</a>
</li>
      <li><a href="index.html#tigramite.plotting.setup_density_matrix">setup_density_matrix (class in tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.plotting.setup_matrix">setup_matrix (class in tigramite.plotting)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.plotting.setup_scatter_matrix">setup_scatter_matrix (class in tigramite.plotting)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.smooth">smooth() (in module tigramite.data_processing)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.space_context_nodes">space_context_nodes (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.space_dummy">space_dummy (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.structural_causal_process">structural_causal_process() (in module tigramite.data_processing)</a>

      <ul>
        <li><a href="index.html#tigramite.toymodels.structural_causal_processes.structural_causal_process">(in module tigramite.toymodels.structural_causal_processes)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.toymodels.structural_causal_processes.structural_causal_process_ensemble">structural_causal_process_ensemble() (in module tigramite.toymodels.structural_causal_processes)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.system_nodes">system_nodes (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.T">T (tigramite.data_processing.DataFrame.self attribute)</a>

      <ul>
        <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.T">(tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
        <li><a href="index.html#tigramite.pcmci.PCMCI.T">(tigramite.pcmci.PCMCI attribute)</a>
</li>
      </ul></li>
      <li>
    tigramite.data_processing

      <ul>
        <li><a href="index.html#module-tigramite.data_processing">module</a>
</li>
      </ul></li>
      <li>
    tigramite.plotting

      <ul>
        <li><a href="index.html#module-tigramite.plotting">module</a>
</li>
      </ul></li>
      <li>
    tigramite.toymodels.structural_causal_processes

      <ul>
        <li><a href="index.html#module-tigramite.toymodels.structural_causal_processes">module</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.time_bin_with_mask">time_bin_with_mask() (in module tigramite.data_processing)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.time_context_nodes">time_context_nodes (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.time_dummy">time_dummy (tigramite.jpcmciplus.JPCMCIplus attribute)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.time_offsets">time_offsets (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.trafo2normal">trafo2normal() (in module tigramite.data_processing)</a>

      <ul>
        <li><a href="index.html#tigramite.independence_tests.robust_parcorr.RobustParCorr.trafo2normal">(tigramite.independence_tests.robust_parcorr.RobustParCorr method)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.models.LinearMediation.tsg_to_net">tsg_to_net() (tigramite.models.LinearMediation method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="V">V</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.jpcmciplus.JPCMCIplus.val_min">val_min (tigramite.jpcmciplus.JPCMCIplus attribute)</a>

      <ul>
        <li><a href="index.html#tigramite.pcmci.PCMCI.val_min">(tigramite.pcmci.PCMCI attribute)</a>
</li>
      </ul></li>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.values">values (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.DataFrame.self.var_names">var_names (tigramite.data_processing.DataFrame.self attribute)</a>
</li>
      <li><a href="index.html#tigramite.data_processing.var_process">var_process() (in module tigramite.data_processing)</a>

      <ul>
        <li><a href="index.html#tigramite.toymodels.structural_causal_processes.var_process">(in module tigramite.toymodels.structural_causal_processes)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="W">W</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.data_processing.weighted_avg_and_std">weighted_avg_and_std() (in module tigramite.data_processing)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="index.html#tigramite.plotting.write_csv">write_csv() (in module tigramite.plotting)</a>
</li>
  </ul></td>
</tr></table>



          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
  </ul></li>
</ul>
</div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2023, Jakob Runge.
      
      |
      Powered by <a href="https://www.sphinx-doc.org/">Sphinx 8.2.3</a>
      &amp; <a href="https://alabaster.readthedocs.io">Alabaster 0.7.16</a>
      
    </div>

    

    
  </body>
</html>