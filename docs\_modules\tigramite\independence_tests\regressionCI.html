
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.independence_tests.regressionCI &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/alabaster.css" />
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
   
  <link rel="stylesheet" href="../../../_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.independence_tests.regressionCI</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite causal discovery for time series.&quot;&quot;&quot;</span>

<span class="c1"># Author: Tom Hochsprung &lt;<EMAIL>&gt;, Jakob Runge &lt;<EMAIL>&gt;</span>
<span class="c1">#</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>

<span class="kn">from</span> <span class="nn">scipy.stats</span> <span class="kn">import</span> <span class="n">chi2</span><span class="p">,</span> <span class="n">normaltest</span>
<span class="kn">from</span> <span class="nn">sklearn.linear_model</span> <span class="kn">import</span> <span class="n">LinearRegression</span><span class="p">,</span> <span class="n">LogisticRegression</span>
<span class="kn">from</span> <span class="nn">sklearn</span> <span class="kn">import</span> <span class="n">metrics</span>

<span class="kn">from</span> <span class="nn">.independence_tests_base</span> <span class="kn">import</span> <span class="n">CondIndTest</span>
<span class="c1"># from numba import jit   # could make it even faster, also acticate @jit(forceobj=True)</span>


<div class="viewcode-block" id="RegressionCI"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.regressionCI.RegressionCI">[docs]</a><span class="k">class</span> <span class="nc">RegressionCI</span><span class="p">(</span><span class="n">CondIndTest</span><span class="p">):</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Flexible parametric conditional independence tests for continuous, categorical, or mixed data.</span>

<span class="sd">    Assumes one-dimensional X, Y.</span>

<span class="sd">    Notes</span>
<span class="sd">    -----</span>
<span class="sd">    To test :math:`X \perp Y | Z`, the regressions Y|XZ vs Y|Z, or, depending</span>
<span class="sd">    on certain criteria, X|YZ vs X|Z are compared. For that, the notion of</span>
<span class="sd">    the deviance is employed. If the fits of the respective regressions do</span>
<span class="sd">    not differ significantly (measured using the deviance), the null</span>
<span class="sd">    hypotheses of conditional independence is &quot;accepted&quot;. This approach</span>
<span class="sd">    assumes that X and Y are univariate, and Z can be either empty,</span>
<span class="sd">    univariate or multivariate. Moreover, this approach works for all</span>
<span class="sd">    combinations of &quot;discrete&quot; and &quot;continuous&quot; X, Y and respective columns</span>
<span class="sd">    of Z; depending on the case, linear regression or multinomial regression</span>
<span class="sd">    is employed.</span>

<span class="sd">    Assumes one-dimensional X, Y.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    **kwargs :</span>
<span class="sd">        Arguments passed on to parent class CondIndTest.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">measure</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Concrete property to return the measure of the independence test</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_measure</span>
    
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        
        <span class="c1"># Setup the member variables</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_measure</span> <span class="o">=</span> <span class="s1">&#39;regression_ci&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">two_sided</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">residual_based</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="n">CondIndTest</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>

<div class="viewcode-block" id="RegressionCI.set_dataframe"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.regressionCI.RegressionCI.set_dataframe">[docs]</a>    <span class="k">def</span> <span class="nf">set_dataframe</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">dataframe</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize and check the dataframe.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        dataframe : data object</span>
<span class="sd">            Set tigramite dataframe object. It must have the attributes</span>
<span class="sd">            dataframe.values yielding a numpy array of shape (observations T,</span>
<span class="sd">            variables N) and optionally a mask of the same shape and a missing</span>
<span class="sd">            values flag.</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span> <span class="o">=</span> <span class="n">dataframe</span>
        
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">dataframe</span><span class="o">.</span><span class="n">mask</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;mask_type is not None, but no mask in dataframe.&quot;</span><span class="p">)</span>
            <span class="n">dataframe</span><span class="o">.</span><span class="n">_check_mask</span><span class="p">(</span><span class="n">dataframe</span><span class="o">.</span><span class="n">mask</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">dataframe</span><span class="o">.</span><span class="n">data_type</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;data_type cannot be None for RegressionCI.&quot;</span><span class="p">)</span>
        <span class="n">dataframe</span><span class="o">.</span><span class="n">_check_mask</span><span class="p">(</span><span class="n">dataframe</span><span class="o">.</span><span class="n">data_type</span><span class="p">,</span> <span class="n">check_data_type</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span></div>

    <span class="c1"># @jit(forceobj=True)</span>
<div class="viewcode-block" id="RegressionCI.get_dependence_measure"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.regressionCI.RegressionCI.get_dependence_measure">[docs]</a>    <span class="k">def</span> <span class="nf">get_dependence_measure</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">data_type</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns test statistic.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        array : array-like</span>
<span class="sd">            data array with X, Y, Z in rows and observations in columns.</span>

<span class="sd">        xyz : array of ints</span>
<span class="sd">            XYZ identifier array of shape (dim,).</span>

<span class="sd">        data_type : array-like</span>
<span class="sd">            array of same shape as array which describes whether samples</span>
<span class="sd">            are continuous or discrete: 0s for continuous and</span>
<span class="sd">            1s for discrete</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        val : float</span>
<span class="sd">            test estimate.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">def</span> <span class="nf">convert_to_one_hot</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">nb_classes</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Convert an iterable of indices to one-hot encoded labels.&quot;&quot;&quot;</span>
            
            <span class="n">targets</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">data</span><span class="p">)</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span>
            <span class="c1"># categories need to be mapped to 0, 1, ... in this function</span>
            <span class="n">targets</span> <span class="o">=</span> <span class="n">targets</span> <span class="o">-</span> <span class="n">np</span><span class="o">.</span><span class="n">min</span><span class="p">(</span><span class="n">targets</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">eye</span><span class="p">(</span><span class="n">nb_classes</span><span class="p">)[</span><span class="n">targets</span><span class="p">]</span>

        <span class="k">def</span> <span class="nf">do_componentwise_one_hot_encoding</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">var_type</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;A function that one-hot encodes all categorical components of X&quot;&quot;&quot;</span>
           
            <span class="n">T</span><span class="p">,</span> <span class="n">dim</span> <span class="o">=</span> <span class="n">X</span><span class="o">.</span><span class="n">shape</span>
            <span class="n">X_new</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">([</span><span class="n">T</span><span class="p">,</span> <span class="mi">0</span><span class="p">])</span>
            <span class="c1"># componentwise dummy-encoding (if necessary, otherwise, keep component as usual):</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">var_type</span><span class="p">)):</span>
                <span class="k">if</span> <span class="n">var_type</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                    <span class="n">nb_classes</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">X</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]))</span>
                    <span class="n">X_new</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">X_new</span><span class="p">,</span> <span class="n">convert_to_one_hot</span><span class="p">(</span><span class="n">X</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="nb">int</span><span class="p">),</span> <span class="n">nb_classes</span><span class="o">=</span><span class="n">nb_classes</span><span class="p">)))</span>
                <span class="k">elif</span> <span class="n">var_type</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="n">X_new</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">X_new</span><span class="p">,</span> <span class="n">X</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">reshape</span><span class="p">((</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">))))</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;data_type only allows entries in {0, 1}&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">X_new</span>

        <span class="k">def</span> <span class="nf">calc_deviance_logistic</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">var_type</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Calculates the deviance (i.e., 2 * log-likelihood) for a multinomial logistic regression</span>
<span class="sd">            (with standard regression assumptions)</span>
<span class="sd">            &quot;&quot;&quot;</span>

            <span class="c1"># 1-hot-encode all categorical columns</span>
            <span class="n">X</span> <span class="o">=</span> <span class="n">do_componentwise_one_hot_encoding</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">var_type</span><span class="o">=</span><span class="n">var_type</span><span class="p">)</span>
            <span class="n">y</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ravel</span><span class="p">(</span><span class="n">y</span><span class="p">)</span>
            <span class="c1"># do logistic regression</span>
            <span class="n">model</span> <span class="o">=</span> <span class="n">LogisticRegression</span><span class="p">(</span><span class="n">multi_class</span><span class="o">=</span><span class="s1">&#39;multinomial&#39;</span><span class="p">,</span> <span class="n">solver</span><span class="o">=</span><span class="s1">&#39;lbfgs&#39;</span><span class="p">)</span>
            <span class="n">model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
            <span class="n">deviance</span> <span class="o">=</span> <span class="mi">2</span><span class="o">*</span><span class="n">metrics</span><span class="o">.</span><span class="n">log_loss</span><span class="p">(</span><span class="n">y</span><span class="p">,</span> <span class="n">model</span><span class="o">.</span><span class="n">predict_proba</span><span class="p">(</span><span class="n">X</span><span class="p">),</span> <span class="n">normalize</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
            <span class="c1"># dofs: +2 for intercept (+1) (not too important, cancels out later anyway)</span>
            <span class="n">dof</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">n_features_in_</span> <span class="o">+</span> <span class="mi">1</span>
            <span class="k">return</span> <span class="n">deviance</span><span class="p">,</span> <span class="n">dof</span>

        <span class="k">def</span> <span class="nf">calc_deviance_linear</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">var_type</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Calculates the deviance (i.e., 2 * log-likelihood) for a linear regression</span>
<span class="sd">            (with standard regression assumptions</span>
<span class="sd">            &quot;&quot;&quot;</span>

            <span class="n">n</span><span class="p">,</span> <span class="n">p</span> <span class="o">=</span> <span class="n">X</span><span class="o">.</span><span class="n">shape</span>  <span class="c1"># p is not important for later</span>
            <span class="c1"># 1-hot-encode all categorical columns</span>
            <span class="n">X</span> <span class="o">=</span> <span class="n">do_componentwise_one_hot_encoding</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">var_type</span> <span class="o">=</span> <span class="n">var_type</span><span class="p">)</span>
            <span class="n">y</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ravel</span><span class="p">(</span><span class="n">y</span><span class="p">)</span>
            <span class="c1"># do linear regression</span>
            <span class="n">model</span> <span class="o">=</span> <span class="n">LinearRegression</span><span class="p">()</span>
            <span class="n">model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
            <span class="c1"># predictions based on fitted model</span>
            <span class="n">preds</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">predict</span><span class="p">(</span><span class="n">X</span><span class="p">)</span>
            <span class="c1"># residual sum of squares</span>
            <span class="n">rss</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">power</span><span class="p">((</span><span class="n">preds</span> <span class="o">-</span> <span class="n">y</span><span class="p">),</span> <span class="mi">2</span><span class="p">))</span>
            <span class="c1"># deviance (only the term with the rss-term is important, the rest cancels out later anyway)</span>
            <span class="c1"># deviance is calculated as -2*log-likelihood</span>
            <span class="n">deviance</span> <span class="o">=</span> <span class="n">n</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">log</span><span class="p">(</span><span class="mi">2</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">pi</span><span class="p">)</span> <span class="o">+</span> <span class="n">n</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">log</span><span class="p">(</span><span class="n">rss</span> <span class="o">/</span> <span class="n">n</span><span class="p">)</span> <span class="o">+</span> <span class="n">n</span>
            <span class="c1"># dofs: +2 for intercept (+1)  (not too important, cancels out later anyway)</span>
            <span class="n">dof</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">n_features_in_</span>  <span class="o">+</span> <span class="mi">1</span>
            <span class="k">return</span> <span class="n">deviance</span><span class="p">,</span> <span class="n">dof</span>

        <span class="k">def</span> <span class="nf">entropy</span><span class="p">(</span><span class="n">series</span><span class="p">):</span>
            <span class="n">value</span><span class="p">,</span> <span class="n">counts</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">unique</span><span class="p">(</span><span class="n">series</span><span class="p">,</span> <span class="n">return_counts</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="n">norm_counts</span> <span class="o">=</span> <span class="n">counts</span> <span class="o">/</span> <span class="n">counts</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span>
            <span class="k">return</span> <span class="o">-</span><span class="p">(</span><span class="n">norm_counts</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">log</span><span class="p">(</span><span class="n">norm_counts</span><span class="p">))</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span>

        <span class="n">x_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
        <span class="n">y_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">1</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
        <span class="n">z_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">2</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

        <span class="n">x</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">x_indices</span><span class="p">]</span><span class="o">.</span><span class="n">T</span>
        <span class="n">y</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">y_indices</span><span class="p">]</span><span class="o">.</span><span class="n">T</span>

        <span class="n">x_type</span> <span class="o">=</span> <span class="n">data_type</span><span class="p">[</span><span class="n">x_indices</span><span class="p">]</span>
        <span class="n">y_type</span> <span class="o">=</span> <span class="n">data_type</span><span class="p">[</span><span class="n">y_indices</span><span class="p">]</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">z_indices</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">z</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">((</span><span class="n">array</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="mi">1</span><span class="p">))</span>
            <span class="n">z_type</span> <span class="o">=</span> <span class="p">[</span><span class="mi">0</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">z</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">z_indices</span><span class="p">]</span><span class="o">.</span><span class="n">T</span>
            <span class="n">z_type</span> <span class="o">=</span> <span class="n">data_type</span><span class="p">[</span><span class="n">z_indices</span><span class="p">]</span>
            <span class="n">z_type</span> <span class="o">=</span> <span class="n">z_type</span><span class="o">.</span><span class="n">max</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

        <span class="c1"># check, whether within X and within Y all datapoints have the same datatype</span>
        <span class="k">if</span> <span class="p">((</span><span class="n">x_type</span><span class="o">.</span><span class="n">max</span><span class="p">()</span> <span class="o">!=</span> <span class="n">x_type</span><span class="o">.</span><span class="n">min</span><span class="p">())</span> <span class="ow">or</span> <span class="p">(</span><span class="n">y_type</span><span class="o">.</span><span class="n">max</span><span class="p">()</span> <span class="o">!=</span> <span class="n">y_type</span><span class="o">.</span><span class="n">min</span><span class="p">())):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;All samples regarding X or respectively Y must have the same datatype&quot;</span><span class="p">)</span>
        
        <span class="n">x_type</span> <span class="o">=</span> <span class="n">x_type</span><span class="o">.</span><span class="n">max</span><span class="p">()</span>
        <span class="n">y_type</span> <span class="o">=</span> <span class="n">y_type</span><span class="o">.</span><span class="n">max</span><span class="p">()</span>

        <span class="c1"># if z was (originally) None, then just an intercept is fitted ...</span>
        <span class="c1"># Now, different cases for X discrete/continuous and Y discrete/continuous</span>
        
        <span class="c1"># Case 1: X continuous, Y continuous</span>
        <span class="k">if</span> <span class="p">(</span><span class="n">x_type</span> <span class="o">==</span> <span class="mi">0</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">y_type</span> <span class="o">==</span> <span class="mi">0</span><span class="p">):</span>
            <span class="c1"># Use the more normal variable as dependent variable TODO: makes sense?</span>
            <span class="k">if</span> <span class="n">normaltest</span><span class="p">(</span><span class="n">x</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span> <span class="o">&gt;=</span> <span class="n">normaltest</span><span class="p">(</span><span class="n">y</span><span class="p">)[</span><span class="mi">0</span><span class="p">]:</span>
                <span class="n">dep_var</span> <span class="o">=</span> <span class="n">y</span>
                <span class="n">rest</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">x</span><span class="p">,</span> <span class="n">z</span><span class="p">))</span>
                <span class="n">rest_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">x_type</span><span class="p">,</span> <span class="n">z_type</span><span class="p">))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">dep_var</span> <span class="o">=</span> <span class="n">x</span>
                <span class="n">rest</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">y</span><span class="p">,</span> <span class="n">z</span><span class="p">))</span>  
                <span class="n">rest_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">y_type</span><span class="p">,</span> <span class="n">z_type</span><span class="p">))</span>
              
            <span class="c1"># Fit Y | Z</span>
            <span class="n">dev1</span><span class="p">,</span> <span class="n">dof1</span> <span class="o">=</span> <span class="n">calc_deviance_linear</span><span class="p">(</span><span class="n">z</span><span class="p">,</span> <span class="n">dep_var</span><span class="p">,</span> <span class="n">var_type</span> <span class="o">=</span> <span class="n">z_type</span><span class="p">)</span>
            <span class="c1"># Fit Y | ZX</span>
            <span class="n">dev2</span><span class="p">,</span> <span class="n">dof2</span> <span class="o">=</span> <span class="n">calc_deviance_linear</span><span class="p">(</span><span class="n">rest</span><span class="p">,</span> <span class="n">dep_var</span><span class="p">,</span> <span class="n">var_type</span><span class="o">=</span><span class="n">rest_type</span><span class="p">)</span>
            <span class="c1"># print(dev1, dev2, np.abs(dev1 - dev2))</span>

        <span class="c1"># Case 2: X discrete, Y continuous</span>
        <span class="k">elif</span> <span class="p">(</span><span class="n">x_type</span> <span class="o">==</span> <span class="mi">1</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">y_type</span> <span class="o">==</span> <span class="mi">0</span><span class="p">):</span>
            <span class="n">xz</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">x</span><span class="p">,</span> <span class="n">z</span><span class="p">))</span>
            <span class="c1"># Fit Y | Z</span>
            <span class="n">dev1</span><span class="p">,</span> <span class="n">dof1</span> <span class="o">=</span> <span class="n">calc_deviance_linear</span><span class="p">(</span><span class="n">z</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">var_type</span> <span class="o">=</span> <span class="n">z_type</span><span class="p">)</span>
            <span class="c1"># Fit Y | XZ</span>
            <span class="n">dev2</span><span class="p">,</span> <span class="n">dof2</span> <span class="o">=</span> <span class="n">calc_deviance_linear</span><span class="p">(</span><span class="n">xz</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">var_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">x_type</span><span class="p">,</span> <span class="n">z_type</span><span class="p">)))</span>
        
        <span class="c1"># Case 3: X continuous, Y discrete</span>
        <span class="k">elif</span> <span class="p">(</span><span class="n">x_type</span> <span class="o">==</span> <span class="mi">0</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">y_type</span> <span class="o">==</span> <span class="mi">1</span><span class="p">):</span>
            <span class="n">yz</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">y</span><span class="p">,</span> <span class="n">z</span><span class="p">))</span>
            <span class="c1"># Fit X | Z</span>
            <span class="n">dev1</span><span class="p">,</span> <span class="n">dof1</span> <span class="o">=</span> <span class="n">calc_deviance_linear</span><span class="p">(</span><span class="n">z</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">var_type</span> <span class="o">=</span> <span class="n">z_type</span><span class="p">)</span>
            <span class="c1"># Fit X | YZ</span>
            <span class="n">dev2</span><span class="p">,</span> <span class="n">dof2</span> <span class="o">=</span> <span class="n">calc_deviance_linear</span><span class="p">(</span><span class="n">yz</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">var_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">y_type</span><span class="p">,</span> <span class="n">z_type</span><span class="p">)))</span>
        
        <span class="c1"># Case 4: X discrete, Y discrete</span>
        <span class="k">elif</span> <span class="p">(</span><span class="n">x_type</span> <span class="o">==</span> <span class="mi">1</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">y_type</span> <span class="o">==</span> <span class="mi">1</span><span class="p">):</span>
            <span class="c1"># Use the variable with smaller entropy as dependent variable TODO: makes sense?</span>
            <span class="k">if</span> <span class="n">entropy</span><span class="p">(</span><span class="n">x</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="n">entropy</span><span class="p">(</span><span class="n">y</span><span class="p">):</span>
                <span class="n">dep_var</span> <span class="o">=</span> <span class="n">y</span>
                <span class="n">rest</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">x</span><span class="p">,</span> <span class="n">z</span><span class="p">))</span>
                <span class="n">rest_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">x_type</span><span class="p">,</span> <span class="n">z_type</span><span class="p">))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">dep_var</span> <span class="o">=</span> <span class="n">x</span>
                <span class="n">rest</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">y</span><span class="p">,</span> <span class="n">z</span><span class="p">))</span>  
                <span class="n">rest_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">y_type</span><span class="p">,</span> <span class="n">z_type</span><span class="p">))</span>
            <span class="c1"># xz = np.hstack((x, z))</span>
            <span class="c1"># Fit Y | Z</span>
            <span class="n">dev1</span><span class="p">,</span> <span class="n">dof1</span> <span class="o">=</span> <span class="n">calc_deviance_logistic</span><span class="p">(</span><span class="n">z</span><span class="p">,</span> <span class="n">dep_var</span><span class="p">,</span> <span class="n">var_type</span> <span class="o">=</span> <span class="n">z_type</span><span class="p">)</span>
            <span class="c1"># Fit Y | XZ</span>
            <span class="n">dev2</span><span class="p">,</span> <span class="n">dof2</span> <span class="o">=</span> <span class="n">calc_deviance_logistic</span><span class="p">(</span><span class="n">rest</span><span class="p">,</span> <span class="n">dep_var</span><span class="p">,</span> <span class="n">var_type</span><span class="o">=</span><span class="n">rest_type</span><span class="p">)</span>

        <span class="c1"># calculate the difference between the deviance for the smaller and for the larger model</span>
        <span class="c1"># (i.e., the actual deviance)</span>
        <span class="n">stat</span> <span class="o">=</span> <span class="n">dev1</span> <span class="o">-</span> <span class="n">dev2</span>
        <span class="n">dof</span> <span class="o">=</span> <span class="n">dof2</span> <span class="o">-</span> <span class="n">dof1</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_temp_dof</span> <span class="o">=</span> <span class="n">dof</span>
        <span class="k">return</span> <span class="n">stat</span></div>

<div class="viewcode-block" id="RegressionCI.get_analytic_significance"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.regressionCI.RegressionCI.get_analytic_significance">[docs]</a>    <span class="k">def</span> <span class="nf">get_analytic_significance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">dim</span><span class="p">,</span> <span class="n">xyz</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Return the p_value of test statistic.</span>

<span class="sd">        According to a chi-square distribution with &#39;dof&#39; degrees of freedom.</span>

<span class="sd">        &quot;&quot;&quot;</span>
                      
        <span class="c1"># Calculate the p_value</span>
        <span class="n">p_value</span> <span class="o">=</span> <span class="n">chi2</span><span class="o">.</span><span class="n">sf</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">_temp_dof</span><span class="p">)</span>
        <span class="k">del</span> <span class="bp">self</span><span class="o">.</span><span class="n">_temp_dof</span>

        <span class="k">return</span> <span class="n">p_value</span></div></div>


<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    
    <span class="kn">import</span> <span class="nn">tigramite</span>
    <span class="kn">from</span> <span class="nn">tigramite.data_processing</span> <span class="kn">import</span> <span class="n">DataFrame</span>
    <span class="kn">import</span> <span class="nn">tigramite.data_processing</span> <span class="k">as</span> <span class="nn">pp</span>
    <span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>

    <span class="n">seed</span><span class="o">=</span><span class="mi">43</span>
    <span class="n">random_state</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="o">=</span><span class="n">seed</span><span class="p">)</span>
    <span class="n">ci</span> <span class="o">=</span> <span class="n">RegressionCI</span><span class="p">()</span>

    <span class="n">T</span> <span class="o">=</span> <span class="mi">100</span>

    <span class="n">reals</span> <span class="o">=</span> <span class="mi">100</span>
    <span class="n">rate</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">reals</span><span class="p">)</span>

    <span class="n">x_example</span> <span class="o">=</span> <span class="s2">&quot;continuous&quot;</span>
    <span class="n">y_example</span> <span class="o">=</span> <span class="s2">&quot;continuous&quot;</span>
    <span class="n">dimz</span> <span class="o">=</span> <span class="mi">1</span>
    <span class="c1"># z_example = [&quot;discrete&quot;, &quot;continuous&quot;]</span>
    <span class="n">z_example</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;continuous&quot;</span><span class="p">]</span> <span class="c1">#, &quot;discrete&quot;]</span>
    <span class="c1"># z_example = None</span>
    <span class="n">rate</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">reals</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">reals</span><span class="p">):</span>
        <span class="k">if</span> <span class="p">(</span><span class="n">dimz</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">):</span>
            <span class="n">z</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">T</span><span class="p">,</span> <span class="n">dimz</span><span class="p">))</span>
            <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">z_example</span><span class="p">)):</span>
                <span class="k">if</span> <span class="n">z_example</span><span class="p">[</span><span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;discrete&quot;</span><span class="p">:</span>
                    <span class="n">z</span><span class="p">[:,</span> <span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">binomial</span><span class="p">(</span><span class="n">n</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">p</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">size</span><span class="o">=</span><span class="n">T</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">z</span><span class="p">[:,</span> <span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">uniform</span><span class="p">(</span><span class="n">low</span> <span class="o">=</span> <span class="mi">0</span><span class="p">,</span> <span class="n">high</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="n">size</span><span class="o">=</span><span class="n">T</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">z</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="n">x</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">(</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
        <span class="n">y</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">(</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">T</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">dimz</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">z_example</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;discrete&quot;</span><span class="p">:</span>
                    <span class="n">val</span> <span class="o">=</span> <span class="n">z</span><span class="p">[</span><span class="n">t</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">squeeze</span><span class="p">()</span>
                    <span class="n">prob</span> <span class="o">=</span> <span class="mf">0.2</span> <span class="o">+</span> <span class="n">val</span> <span class="o">*</span> <span class="mf">0.6</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">prob</span> <span class="o">=</span> <span class="n">z</span><span class="p">[</span><span class="n">t</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">squeeze</span><span class="p">()</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">prob</span> <span class="o">=</span> <span class="mf">0.2</span>
            <span class="k">if</span> <span class="n">x_example</span> <span class="o">==</span> <span class="s2">&quot;discrete&quot;</span><span class="p">:</span>
                <span class="n">x</span><span class="p">[</span><span class="n">t</span><span class="p">]</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">choice</span><span class="p">([</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">],</span> <span class="n">p</span><span class="o">=</span><span class="p">[</span><span class="n">prob</span><span class="p">,</span> <span class="mf">1.</span> <span class="o">-</span> <span class="n">prob</span><span class="p">])</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">x</span><span class="p">[</span><span class="n">t</span><span class="p">]</span> <span class="o">=</span> <span class="mf">0.1</span><span class="o">*</span><span class="n">random_state</span><span class="o">.</span><span class="n">random</span><span class="p">()</span> <span class="c1"># np.random.uniform(prob, 1)  #np.random.normal(prob, 1)</span>
            <span class="k">if</span> <span class="n">y_example</span> <span class="o">==</span> <span class="s2">&quot;discrete&quot;</span><span class="p">:</span>
                <span class="n">y</span><span class="p">[</span><span class="n">t</span><span class="p">]</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">choice</span><span class="p">([</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">],</span> <span class="n">p</span><span class="o">=</span><span class="p">[</span><span class="n">prob</span><span class="p">,</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">prob</span><span class="p">)])</span> <span class="c1"># + x[t]</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">y</span><span class="p">[</span><span class="n">t</span><span class="p">]</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">normal</span><span class="p">(</span><span class="n">prob</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span> <span class="o">+</span> <span class="mf">0.5</span><span class="o">*</span><span class="n">x</span><span class="p">[</span><span class="n">t</span><span class="p">]</span>

        <span class="c1"># # Continuous data</span>
        <span class="c1"># z = np.random.randn(T, dimz)</span>
        <span class="c1"># x = (0.5*z[:,0] + np.random.randn(T)).reshape(T, 1)</span>
        <span class="c1"># y = (0.5*z[:,0] + np.random.randn(T)).reshape(T, 1) #+ 2*x</span>

        <span class="k">if</span> <span class="n">x_example</span> <span class="o">==</span> <span class="s2">&quot;discrete&quot;</span><span class="p">:</span>
            <span class="n">x_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">x_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">y_example</span> <span class="o">==</span> <span class="s2">&quot;discrete&quot;</span><span class="p">:</span>
            <span class="n">y_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">y_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">dimz</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">z_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">T</span><span class="p">,</span> <span class="n">dimz</span><span class="p">))</span>
            <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">z_example</span><span class="p">)):</span>
                <span class="k">if</span> <span class="n">z_example</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;discrete&quot;</span><span class="p">:</span>
                    <span class="n">z_type</span><span class="p">[:,</span> <span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">z_type</span><span class="p">[:,</span> <span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">z_type</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="n">val</span><span class="p">,</span> <span class="n">pval</span> <span class="o">=</span> <span class="n">ci</span><span class="o">.</span><span class="n">run_test_raw</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">z</span><span class="o">=</span><span class="n">z</span><span class="p">,</span> <span class="n">x_type</span><span class="o">=</span><span class="n">x_type</span><span class="p">,</span> <span class="n">y_type</span><span class="o">=</span><span class="n">y_type</span><span class="p">,</span> <span class="n">z_type</span><span class="o">=</span><span class="n">z_type</span><span class="p">)</span>
        <span class="n">rate</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">pval</span>

        <span class="c1"># data = np.hstack((x, y, z))</span>
        <span class="c1"># data_type = np.zeros(data.shape)</span>
        <span class="c1"># data_type[:, 0] = x_example == &quot;discrete&quot;</span>
        <span class="c1"># data_type[:, 1] = y_example == &quot;discrete&quot;</span>
        <span class="c1"># data_type[:, 2] = z_example == &quot;discrete&quot;</span>
        <span class="c1"># data_type = data_type.astype(&#39;int&#39;)</span>
        <span class="c1"># # print(data_type)</span>
        <span class="c1"># dataframe = pp.DataFrame(data=data, data_type=data_type)</span>
        <span class="c1"># ci.set_dataframe(dataframe)</span>
        
        <span class="c1"># val, pval = ci.run_test(X=[(0, 0)], Y=[(1, 0)], Z=[(2, 0)])</span>
        <span class="c1"># rate[i] = pval</span>

    <span class="nb">print</span><span class="p">((</span><span class="n">rate</span> <span class="o">&lt;=</span> <span class="mf">0.05</span><span class="p">)</span><span class="o">.</span><span class="n">mean</span><span class="p">())</span>


</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../../index.html">Documentation overview</a><ul>
  <li><a href="../../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;2023, Jakob Runge.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 5.0.2</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
    </div>

    

    
  </body>
</html>