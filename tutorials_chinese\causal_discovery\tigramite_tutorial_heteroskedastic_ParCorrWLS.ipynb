{"cells": [{"cell_type": "markdown", "id": "eeca0936", "metadata": {}, "source": ["## 因果发现 with `TIGRAMITE`\n", "\n", "TIGRAMITE is a time series analysis python module. It allows to reconstruct graphical models (conditional independence graphs) from discrete or continuously-valued time series based on the PCMCI framework and create high-quality plots of the results.\n", "\n", "PCMCI is described here: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Detecting and quantifying causal associations in large nonlinear 时间序列 datasets. Sci. Adv. 5, eaau4996 (2019) https://advances.sciencemag.org/content/5/11/eaau4996\n", "\n", "For further versions of PCMCI (e.g., PCMCI+, LPCMCI, etc.), see the corresponding tutorials.\n", "\n", "This tutorial explains how to apply the weighted least squares variant of the partial correrlation conditional independence test (ParCorrWLS). It is designed to deal with heteroskedasticity, i.e. non-constant variance of the error terms in a linear Gaussian setting. See the following paper for theoretical background: \n", "\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, 条件独立性 Testing with 异方差 数据 and Applications to 因果发现, Advances in neural information processing systems 35 (2022)\n", "\n", "Last, the following Nature Review Earth and Environment paper provides an overview of causal inference for time series in general: https://github.com/jakobrunge/tigramite/blob/master/tutorials/Runge_Causal_Inference_for_Time_Series_NREE.pdf"]}, {"cell_type": "code", "execution_count": 1, "id": "45fb8f5a", "metadata": {}, "outputs": [], "source": ["# 导入\n", "import numpy as np\n", "import matplotlib\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline     \n", "import sklearn\n", "\n", "import tigramite\n", "from tigramite import data_processing as pp\n", "from tigramite.toymodels import structural_causal_processes as toys\n", "from tigramite import plotting as tp\n", "from tigramite.pcmci import PCMCI\n", "from tigramite.independence_tests.parcorr import ParCorr\n", "from tigramite.independence_tests.parcorr_wls import ParCorrWLS\n", "\n", "from scipy import stats"]}, {"cell_type": "markdown", "id": "b6f59ef4", "metadata": {}, "source": ["## 1. Heteroskedasticity\n", "\n", "One assumption that the well-known partial 相关性 测试 (<PERSON>r<PERSON><PERSON><PERSON>) rests on is homoskedasticity, meaning that the variance of the 错误 term is constant. \n", "We are interested in the situation where this assumption is violated, i.e., we want to consider heteroskedastic data. In this case the variance can, for instance, depend on the sampling index or the value of one or multiple influencing variables. One may think of a linear dependence of the variance on one influencing variable. However the relationship can take many different functional forms.\n", "\n", "<PERSON><PERSON><PERSON><PERSON><PERSON> becomes unreliable on heteroskedastic data, since the covariance matrix of the ordinary least squares (OLS) estimator, which is used for calculating the test statistic, can be biased and inconsistent in this setting.\n", "\n", "The sources for heteroskedasticity in real data are manifold. For example, in environmental sciences\n", "precipitation in different areas might exhibit different variances that are unaccounted for by other\n", "variables in the system, i.e. location-scaled noise. Such a problem could be introduced by aggregating\n", "数据 of different catchments and not adding a 变量 that is well enough correlated with catchment\n", "location. 季节性 effects can 也 introduce heteroskedasticity.\n", "\n", "To 模型 heteroskedasticity within a structural causal 模型 (SCM), we represent it as a scaling 函数 of the noise 变量.\n", "Consider finitely many random variables $V = (X^1, \\ldots, X^d)$ with joint distribution $\\mathcal{P}_X$ over a domain $\\mathcal{X} = \\mathcal{X}_1 \\times \\ldots \\times \\mathcal{X}_d$. Then we are interested in $n$ samples from the following SCM with assignments\n", "\\begin{align} \\label{scm_hs}\n", "X_t^i &:= f_i(Pa(X_t^i)) + h_i(H(X_t^i)) \\cdot N_i, \\qquad i = 1, \\ldots, d\n", "\\end{align}\n", "where $f_i$ are linear functions, $t \\in \\mathcal{T}$ stands for the sample index, and we have the heteroskedasticity functions $s_i: \\mathcal{X} \\times \\mathcal{T} \\xrightarrow{} \\mathbf{R}_{\\geq 0}$. The noise variables $N_i$ are assumed independent standard Gaussian distributions. The parent set of the variable $X_t^i$ is denoted by $Pa(X_t^i)$, and the set of heteroskedasticity-inducing variables is denotes by $H(X_t^i) \\subset Pa(X_t^i) \\cup \\{t\\}$ which can also be the empty set. Furthermore, we make the restriction that the causal relationships are stable over time, i.e. the parent sets $Pa(X_t^i)$ as well as the functions $f_i$ are not time-dependent.\n", "\n", "\n", "## 2. ParCorrWLS: 条件独立性 testing with 异方差 数据\n", "### 2.1 一般 idea\n", "We have seen that the 标准 partial 相关性 测试 is sensitive to 异方差 noise since it\n", "is based on an OLS regression step. Therefore, we propose to replace the OLS regression by the\n", "weighted least squares (WLS) approach which 已知 be able to handle non constant 错误\n", "variance.\n", "\n", "The idea of WLS is to perform a re-weighting of each data point depending on how far it is from the\n", "true regression line. It is reasonable to assume 数据 points 其中 错误 has low variance to be\n", "more informative than those with high 错误 variance. Therefore, ideally the weights are chosen as\n", "the inverse variance of the associated 错误.\n", "\n", "### 2.2 Variance 函数 estimation\n", "Generally, we cannot hope to know the grund truth variance, therefore we have to approximate the conditional variance function of the heteroskedastic variables. We illustrate the approach for a simplified version of the SCM above.\n", "Namely, we consider\n", "\\begin{align}\n", "X_t &= aZ + s(Z,t) \\cdot N_X \\\\\n", "Z_t &= N_Z\n", "\\end{align}\n", "where $a$ is a constant, and $N_X$, and $N_Z$ are 标准 normal independent noise terms.\n", "\n", "For $X$ we now approximate $s^2(z, t) = Var(X_t | Z=z)$. \n", "\n", "For that, we use a residual-based non-parametric estimator for the conditional variance, similar to the approach of [1].\n", "Motivated by the identity $Var(X | Z) = \\mathbb{E}[(X - \\mathbb{E}[X | Z])^2 |Z]$, and noting that this is the regression of $(X-aZ)^2$ on $Z$, the first step is using OLS regression to obtain the squared residuals $(X - \\hat{a} Z)^2$. Afterwards, we use a non-parametric regression method to regress these residuals on $Z$ and thereby predict the conditional mean by using a linear combination of the $k$ residuals closest in $Z$ value. For sampling index-dependent heteroskedasticity this turns into a windowing approach, which essentially smoothes the squared residuals.\n", "\n", "注意 that we rely on the knowledge of the source of heteroskedasticity (in this case $Z$). This is expert knowledge that the user has to supply."]}, {"cell_type": "markdown", "id": "443563b3", "metadata": {}, "source": ["## 3. Application 示例\n", "### 3.1 Parent-dependent heteroskedasticity\n", "#### 3.1.1 Toy 数据 generation\n", "Consider 时间序列 数据 generated according to the 以下 process\n", "$$\n", "X_t = 0.7 Z_{t-1} + \\eta^X_t \\\\\n", "Y_t = 0.5 Z_{t-1} + \\eta^Y_t \\\\\n", "Z_t = \\eta^Z_t\n", "$$\n", "where $\\eta^Z_t \\sim \\mathcal{N}(0,1)$, but $\\eta^X_t, \\eta^Y_t \\sim \\mathcal{N}(0, \\sigma(Z_{t-1}))$.\n", "注意, that $X$ and $Y$ are both 异方差. In other words, their variances are changing with the value of $Z$ at 滞后 one. Since the variance depends on $Z$, we call this parent-dependent heteroskedasticity. 下面的, $\\sigma(\\cdot)$ is generated using the 函数 `generate_parent_dependent_stds`. \n", "\n", "The case of time-dependent heteroskedasticity will be covered 下面的."]}, {"cell_type": "code", "execution_count": 2, "id": "155d7755", "metadata": {}, "outputs": [], "source": ["random_state = np.random.RandomState(42)\n", "T = 500\n", "N = 3\n", "\n", "def generate_data(generate_stds, random_state):\n", "    data = np.zeros((T, N))\n", "    Z = random_state.standard_normal(T+1)\n", "    data[:, 2] = Z[1:]\n", "    stds_matrix = np.ones((T,N))\n", "    stds = generate_stds(Z, T)\n", "    stds_matrix[:, 0] = stds\n", "    stds_matrix[:, 1] = stds\n", "    noise_X = random_state.normal(0, stds, T)\n", "    noise_Y = random_state.normal(0, stds, T)\n", "    data[:, 0] = 0.8*Z[:T] + noise_X\n", "    data[:, 1] = 0.8*Z[:T] + noise_Y\n", "    return data, stds_matrix, noise_X, noise_Y\n", "\n", "def generate_parent_dependent_stds(Z, T):\n", "    stds = np.ones(T) + 5*(1 + Z[:T])*(1+Z[:T]>0)\n", "    return stds\n", "\n", "\n", "data, stds_matrix, noise_X, noise_Y = generate_data(generate_parent_dependent_stds, random_state)\n", "\n", "# Initialize dataframe object, specify time axis and 变量 names\n", "var_names = [r'$X$', r'$Y$', r'$Z$']\n", "dataframe = pp.DataFrame(data, \n", "                         datatime = {0:np.arange(len(data))}, \n", "                         var_names=var_names)"]}, {"cell_type": "markdown", "id": "31b1386b", "metadata": {}, "source": ["First, we 绘制 the 时间序列. This can be done with the 函数 `tp.plot_timeseries`."]}, {"cell_type": "markdown", "id": "3379c137", "metadata": {}, "source": ["#### 3.1.2 绘图 the 时间序列 and visualizing heteroskedasticity"]}, {"cell_type": "code", "execution_count": 3, "id": "5155bc6f", "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tp.plot_timeseries(dataframe); plt.show()"]}, {"cell_type": "markdown", "id": "9426c682", "metadata": {}, "source": ["We 也 绘制 $\\eta_X$ vs $Z$ and $\\eta_Y$ vs $Z$ to visualize the 异方差 relationships."]}, {"cell_type": "code", "execution_count": 4, "id": "26546c89", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(1, 2, figsize=(10, 4))\n", "axs = axs.ravel()\n", "\n", "axs[0].scatter(data[:-1, 2], noise_X[1:], color=\"black\")\n", "axs[0].set_xlabel(r'$Z_{t-1}$')\n", "axs[0].set_ylabel(r'$\\eta_t^X$')\n", "\n", "axs[1].scatter(data[:-1, 2], noise_Y[1:], color=\"black\")\n", "axs[1].set_xlabel(r'$Z_{t-1}$')\n", "axs[1].set_ylabel(r'$\\eta_t^Y$')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "id": "bfbab5d9", "metadata": {}, "source": ["#### 3.1.3 The ParCorrWLS 条件独立性 测试\n", "\n", "To test the independence of $X$ and $Y$ given $Z$, we choose the conditional independence test `ParCorrWLS`. This test is suitable for testing linear dependencies in heteroskedastic data.\n", "\n", "Since we don't know the variance 函数, we need to approximate it. In this case, `ParCorrWLS` requires us to provide expert knowledge on the 异方差 relationships in the form of a dictionary, specifying which node has 异方差 noise w.r.t which other node and at which 滞后.\n", "\n", "Furthermore, we can set the parameter `window_size` that handles how many neighbours are used for smoothing the squared residuals within the variance estimation step. See section 2.2 for details."]}, {"cell_type": "code", "execution_count": 5, "id": "8c51c08e", "metadata": {}, "outputs": [], "source": ["expert_knowledge = {0: [(2, -1)], 1: [(2, -1)]}\n", "\n", "parcorr_wls = ParCorrWLS(expert_knowledge=expert_knowledge, \n", "                         window_size=50,\n", "                         significance='analytic')\n", "\n", "pcmci = PCMCI(\n", "    dataframe=dataframe, \n", "    cond_ind_test=parcorr_wls,\n", "    verbosity=1)"]}, {"cell_type": "code", "execution_count": 6, "id": "feb478b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "##\n", "## Step 1: PC1 algorithm for selecting lagged conditions\n", "##\n", "\n", "Parameters:\n", "independence test = par_corr_wls\n", "tau_min = 1\n", "tau_max = 2\n", "pc_alpha = [0.01]\n", "max_conds_dim = None\n", "max_combinations = 1\n", "\n", "\n", "\n", "## Resulting lagged parent (super)sets:\n", "\n", "    Variable $X$ has 1 link(s):\n", "        ($Z$ -1): max_pval = 0.00459, |min_val| =  0.127\n", "\n", "    Variable $Y$ has 1 link(s):\n", "        ($Z$ -1): max_pval = 0.00099, |min_val| =  0.147\n", "\n", "    Variable $Z$ has 0 link(s):\n", "\n", "##\n", "## Step 2: PC algorithm with contemp. conditions and MCI tests\n", "##\n", "\n", "Parameters:\n", "\n", "independence test = par_corr_wls\n", "tau_min = 0\n", "tau_max = 2\n", "pc_alpha = 0.01\n", "contemp_collider_rule = majority\n", "conflict_resolution = True\n", "reset_lagged_links = False\n", "max_conds_dim = None\n", "max_conds_py = None\n", "max_conds_px = None\n", "max_conds_px_lagged = None\n", "fdr_method = none\n", "\n", "## Significant links at alpha = 0.01:\n", "\n", "    Variable $X$ has 1 link(s):\n", "        ($Z$ -1): pval = 0.00459 | val =  0.127\n", "\n", "    Variable $Y$ has 1 link(s):\n", "        ($Z$ -1): pval = 0.00099 | val =  0.147\n", "\n", "    Variable $Z$ has 0 link(s):\n"]}], "source": ["pcmci.verbosity = 1\n", "results = pcmci.run_pcmciplus(tau_min=0, tau_max=2, pc_alpha=0.01)\n", "#results = pcmci.run_pcmci(tau_max=8, pc_alpha=None, alpha_level=0.01)\n"]}, {"cell_type": "markdown", "id": "74eac956", "metadata": {}, "source": ["Now we use `tp.plot_graph` to 绘制 the learned DAG. 注意 that we indeed discovered the correct graph."]}, {"cell_type": "code", "execution_count": 7, "id": "755e99ce", "metadata": {}, "outputs": [{"data": {"image/png": "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********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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tp.plot_graph(\n", "    val_matrix=results['val_matrix'],\n", "    graph=results['graph'],\n", "    var_names=var_names,\n", "    link_colorbar_label='cross-MCI',\n", "    node_colorbar_label='auto-MCI',\n", "    ); plt.show()"]}, {"cell_type": "markdown", "id": "453c6d4d", "metadata": {}, "source": ["#### 3.1.4 Comparing to ParCorrWLS with ground truth weights\n", "\n", "It is also possible to supply the ground truth variance for each observation to `ParCorrWLS` using the parameter `gt_std_matrix` instead of approximating the variance based on expert knowledge. However, note, generally such knowledge is hard to obtain and probably is only known for toy-data."]}, {"cell_type": "code", "execution_count": 8, "id": "1f3e8755", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["parcorr_wls_gt = ParCorrWLS(gt_std_matrix=stds_matrix,\n", "                            significance='analytic')\n", "\n", "pcmci_gt = PCMCI(\n", "    dataframe=dataframe, \n", "    cond_ind_test=parcorr_wls_gt,\n", "    verbosity=0)\n", "pcmci_gt.verbosity = 0\n", "results_gt = pcmci_gt.run_pcmciplus(tau_min=0, tau_max=2, pc_alpha=0.01)\n", "\n", "tp.plot_graph(\n", "    val_matrix=results_gt['val_matrix'],\n", "    graph=results_gt['graph'],\n", "    var_names=var_names,\n", "    link_colorbar_label='cross-MCI',\n", "    node_colorbar_label='auto-MCI',\n", "    ); plt.show()"]}, {"cell_type": "markdown", "id": "89aee4e9", "metadata": {}, "source": ["#### 3.1.5 Comp<PERSON>on with <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "To get an intuition for the usefulness of ParCorrWLS, we compare its output to that of the standard ParCorr conditional independence test."]}, {"cell_type": "code", "execution_count": 9, "id": "3cfb4b66", "metadata": {}, "outputs": [], "source": ["parcorr = ParCorr(significance='analytic')\n", "pcmci_parcorr = PCMCI(\n", "    dataframe=dataframe, \n", "    cond_ind_test=parcorr,\n", "    verbosity=1)\n", "\n", "pcmci_parcorr.verbosity = 0\n", "results_parcorr = pcmci_parcorr.run_pcmciplus(tau_min=0, tau_max=2, pc_alpha=0.01)"]}, {"cell_type": "code", "execution_count": 10, "id": "378ab9c4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tp.plot_graph(\n", "    val_matrix=results_parcorr['val_matrix'],\n", "    graph=results_parcorr['graph'],\n", "    var_names=var_names,\n", "    link_colorbar_label='cross-MCI',\n", "    node_colorbar_label='auto-MCI',\n", "    ); plt.show()"]}, {"cell_type": "markdown", "id": "e425644c", "metadata": {}, "source": ["We see that the 标准 partial 相关性 测试 finds a wrong link between $X$ and $Y$."]}, {"cell_type": "markdown", "id": "dcbd8be5", "metadata": {}, "source": ["### 3.2 Time (or sampling index) -dependent heteroskedasticity\n", "\n", "Consider again the following process from which we are generating our data\n", "$$\n", "X_t = 0.7 Z_{t-1} + \\eta^X_t \\\\\n", "Y_t = 0.5 Z_{t-1} + \\eta^Y_t \\\\\n", "Z_t = \\eta^Z_t\n", "$$\n", "where $\\eta^Z_t \\sim \\mathcal{N}(0,1)$, but $\\eta^X_t, \\eta^Y_t \\sim \\mathcal{N}(0, \\sigma(t))$.\n", "注意 again, that $X$ and $Y$ are both 异方差 but this time with respect to $t$, i.e. the time or sampling index. In our code, $\\sigma(\\cdot)$ is generated using the 函数 `generate_time_dependent_stds`. "]}, {"cell_type": "code", "execution_count": 11, "id": "7f827281", "metadata": {}, "outputs": [], "source": ["random_state = np.random.RandomState(42)\n", "T = 2000\n", "def generate_time_dependent_stds(Z, T):\n", "    stds = np.array([1 + 0.018*t for t in range(T)])\n", "    return stds\n", "\n", "data, stds_matrix, noise_X, noise_Y = generate_data(generate_time_dependent_stds, random_state)\n", "\n", "# Initialize dataframe object, specify time axis and 变量 names\n", "var_names = [r'$X$', r'$Y$', r'$Z$']\n", "dataframe = pp.DataFrame(data, \n", "                         datatime = {0:np.arange(len(data))}, \n", "                         var_names=var_names)"]}, {"cell_type": "markdown", "id": "8d68cdc7", "metadata": {}, "source": ["Again, as above, we 绘制 the 时间序列 using the 函数 `tp.plot_timeseries`."]}, {"cell_type": "code", "execution_count": 12, "id": "0f209e3c", "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tp.plot_timeseries(dataframe); plt.show()"]}, {"cell_type": "markdown", "id": "ff362caf", "metadata": {}, "source": ["Here, we already can suspect heteroskedasticity from the timeseries plot. However, we also plot $\\eta_X$ and $\\eta_Y$ along the time axis to make sure that the heteroskedasticity isn't due to the parent."]}, {"cell_type": "code", "execution_count": 13, "id": "0f69e3a8", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(1, 2, figsize=(10, 4))\n", "axs = axs.ravel()\n", "\n", "axs[0].scatter(np.linspace(0, T, T), noise_X, color=\"black\")\n", "axs[0].set_xlabel('time')\n", "axs[0].set_ylabel(r'$\\eta_t^X$')\n", "\n", "axs[1].scatter(np.linspace(0, T, T), noise_Y, color=\"black\")\n", "axs[1].set_xlabel('time')\n", "axs[1].set_ylabel(r'$\\eta_t^Y$')\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "id": "6b6ef3d3", "metadata": {}, "source": ["If we deal with time-dependent heteroskedasticity in our 数据, we again choose the 条件独立性 测试 `ParCorrWLS` to 测试 the independence of $X$ and $Y$ given $Z$.\n", "The expert knowledge of time-dependent heteroskedasticity can be passed to `ParCorrWLS` in a similar way as done above for parent-dependent heteroskedasticity. If we want to specify that all variables are affected by time-dependent heteroskedasticity, we can pass the string `'time-dependent heteroskedasticity'` to the `expert_knowledge` parameter of `ParCorrWLS`. However, it is also possible to specify for each node individually where its heteroskedasticity comes from. In this case we use a dictionary with the key being the node index and its item being the list with one element: `['time-dependent heteroskedasticity']`.\n", "Again, we 也 set the `window_size` parameter."]}, {"cell_type": "code", "execution_count": 14, "id": "89b0bec7", "metadata": {}, "outputs": [], "source": ["expert_knowledge = {0: [\"time-dependent heteroskedasticity\"], 1: [\"time-dependent heteroskedasticity\"]}\n", "\n", "parcorr_wls = ParCorrWLS(expert_knowledge=expert_knowledge, \n", "                         window_size=50,\n", "                         significance='analytic')\n", "\n", "pcmci = PCMCI(\n", "    dataframe=dataframe, \n", "    cond_ind_test=parcorr_wls,\n", "    verbosity=1)"]}, {"cell_type": "code", "execution_count": 15, "id": "fe855878", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "##\n", "## Step 1: PC1 algorithm for selecting lagged conditions\n", "##\n", "\n", "Parameters:\n", "independence test = par_corr_wls\n", "tau_min = 1\n", "tau_max = 2\n", "pc_alpha = [0.01]\n", "max_conds_dim = None\n", "max_combinations = 1\n", "\n", "\n", "\n", "## Resulting lagged parent (super)sets:\n", "\n", "    Variable $X$ has 1 link(s):\n", "        ($Z$ -1): max_pval = 0.00125, |min_val| =  0.072\n", "\n", "    Variable $Y$ has 1 link(s):\n", "        ($Z$ -1): max_pval = 0.00001, |min_val| =  0.099\n", "\n", "    Variable $Z$ has 0 link(s):\n", "\n", "##\n", "## Step 2: PC algorithm with contemp. conditions and MCI tests\n", "##\n", "\n", "Parameters:\n", "\n", "independence test = par_corr_wls\n", "tau_min = 0\n", "tau_max = 2\n", "pc_alpha = 0.01\n", "contemp_collider_rule = majority\n", "conflict_resolution = True\n", "reset_lagged_links = False\n", "max_conds_dim = None\n", "max_conds_py = None\n", "max_conds_px = None\n", "max_conds_px_lagged = None\n", "fdr_method = none\n", "\n", "## Significant links at alpha = 0.01:\n", "\n", "    Variable $X$ has 1 link(s):\n", "        ($Z$ -1): pval = 0.00125 | val =  0.072\n", "\n", "    Variable $Y$ has 1 link(s):\n", "        ($Z$ -1): pval = 0.00001 | val =  0.099\n", "\n", "    Variable $Z$ has 0 link(s):\n"]}], "source": ["pcmci.verbosity = 1\n", "results = pcmci.run_pcmciplus(tau_min=0, tau_max=2, pc_alpha=0.01)"]}, {"cell_type": "code", "execution_count": 16, "id": "d75d0ee1", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tp.plot_graph(\n", "    val_matrix=results['val_matrix'],\n", "    graph=results['graph'],\n", "    var_names=var_names,\n", "    link_colorbar_label='cross-MCI',\n", "    node_colorbar_label='auto-MCI',\n", "    ); plt.show()"]}, {"cell_type": "markdown", "id": "aa83bcfc", "metadata": {}, "source": ["#### Comparison with ParCorrOLS\n", "\n", "In the time-dependent heteroskedasticity case, we 也 expect degraded performance of the 标准 ParCorr 测试."]}, {"cell_type": "code", "execution_count": 17, "id": "a7cbc1ca", "metadata": {}, "outputs": [], "source": ["parcorr = ParCorr(significance='analytic')\n", "pcmci_parcorr = PCMCI(\n", "    dataframe=dataframe, \n", "    cond_ind_test=parcorr,\n", "    verbosity=1)\n", "\n", "pcmci_parcorr.verbosity = 0\n", "results_parcorr = pcmci_parcorr.run_pcmciplus(tau_min=0, tau_max=2, pc_alpha=0.01)"]}, {"cell_type": "code", "execution_count": 18, "id": "5ca025b7", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAgMAAAHBCAYAAAD0E7h1AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAArpUlEQVR4nO3df5TcdX3v8dfn+2NmJwlJ+FUgFENNAg0/Ali5cBsQTjkVRAX1Fj0FbBHFiyAogkhP4UQoILZyQc5pjwjlRyleA1i4/kIqWKhUSu2RCLQpBA8eThWOBgghZnbm++N9//juzO5ms8kmuzvf78zn+ThnD5Odyc5nl/1mXvP5vr6fjzMzEwAA8FZQ9gAAAEC5CAMAAHiOMAAAgOcIAwAAeI4wAACA5wgDAAB4jjAAAIDnCAMAAHiOMAAAgOcIAwAAeI4wAACA5wgDAAB4jjAAAIDnCAMAAHiOMAAAgOcIAwAAeI4wAACA5wgDAAB4jjAAAIDnCAMAAHiOMAAAgOcIAwAAeI4wAACA5wgDAAB4Lip7ALPNzIqPPJcVnxi907mR/zgFQdC9DQCATwYqDHRe+PMsU57n3T/viE4wcEGgMAwJBwCAgedsR18tKyjPc2VZpixNZ/xruyBQFIYKCAYAgAHVt2HAzLoBoFffQhAEiuK4e0oBAIBB0JdhIMsypUnSsxCwpSAMFccxMwUAgIHQV2Egz3MlSSLL87KHIkmKokhhFBEKAAB9rS/CgJkpTdNZ6QTMhFq9zqkDAEDfqnwYMDMl7bbyiswGTCaKY0XRQF2cAQDwRKXDgOW52u12ad2AHRVGkSJOGwAA+kxlw0Ce52q3WmUPY4dRLgQA9JtKnug2s74MApKUZ5nSinYbAADYmsqFgU5HoJ9laaosy8oeBgAAU1K5MJCmaeXLglPRD6VHAACkioWB2VpSuCxJH5UfAQD+qkwYGITTA1sys4EKNwCAwVSZMDCo59jTHu6dAADAzqhEGDAzpUlS9jBmDbMDAIAqq0QYGNRZgQ5mBwAAVVaNMODBO+dBDzwAgP5VehiwPPfiXXNOGAAAVFTpYcCXa/FzT0IPAKD/eBEG0jTVQQcfrM9cfPGE+y644AIddPDB+tWvfjXr4yAMAACqyIswEEWRLrn4Yt1555169dVXu5//q7/6K93/wAP6fw88oN/6rd+a9XH4MgsCAOgvpYYBM+vZu+UzzzxTu+22m26++WZJ0v/9+tf1heuu07333qtly5b1ZAxGGAAAVFBU9gB6pVar6TMXXaQvXHed3va2t+m8887Tbbfdpv959NE9GwMnCQAAVeSsxBPZeZ73dKvi4eFhLV++XL/69a913XXX6YJPfrJnzy1JQRCoVq/39DkBANgeb2YGJGloaEjvOO44/fd//3fPgwAAAFVVamfAOdfz53z22Wd15JFH9vx5JUklfL8AAGxP6VcT9FKz2dRzzz2nI444opTnJwoAAKqo9JmBXs4O/PTpp5VlmY44/PCePedYLvAqewEA+kTpnYEgCHq2bv+ap57SvHnztHTp0tl/sjyXsmG5LFVxHYFTntWUz1mggFAAAKiQUq8mkIpNipJB2744aSrI2pIkF8ZyQSjLM1lWfJ8uqiuat7CUzgQAAFsqPQz0+vLCWTcSBFxUVzRnF7lwdPLFslTp5jdlafH9BrWGgnqjCAwEAwBASUoPA2am1vBwmUOYOXmuoP3mNt/5m5nSTRu6gUCSFIQKaw0FtaFx4QEAgF4oPQxIxUZC6SCcKkg2K8gSxfP32OaLumWpko3rt3qfC2MF9YaCeIjCIQCgJyrxNjQMw4EIAy5Liyn/7by7d2EkF8bdDsFYliXKNifKtFEuriusNeTiOqcRAACzphJhwDmnKI4HIBCYXBBO6ZEuCLcaBsZ9taSlNGlJzhX9gtoQ/QIAwIyrRBiQBmV2wMnyqV0mOdXHFQ825a3Nylub6RcAAGZcZV5NnHOKazUl7XbZQ9lpFkayLJFl6XY7A9ubFZhUnikb3qRseJNcFI/OGDj6BQCAnVOJAuFYSZIoS9Oyh7FzdvZqghkQxENFKKBfAADYQZULA2amdrsty/Oyh7JzdmCdgVnhAgW1IQW1RlFUJBgAALajcmFA6v+1B4KsLSVNSVtfgbB3AxnpF9QbUy42AgD8U8kwIPXvyoRBECiu1WRmyppvypKWNO5HXM6P20W1kRkD+gUAgPEqGwak/gsEYRgqird96Z9lqbJ2U3l7WNqRKwpmUPc0QlTjNAIAoNphQOqfDkEUxwrDcMovrmYmSxPl7abyZHiL2YMeoV8AAFAfhAFppIGfppW9yqBWqykId/6cvJnJkpaydrM4rVACF0TFMsi1IfoFAOCZvggDHXmeK2m3VZUhh1GkKJrZd9SW58VsQXu494XDEUW/oKGgVqdfAAAe6KswIBXvojuhoCxBECiO41nfSKjbL2g1JSvjNIlTUKvTLwCAAdd3YaDDzJSlqbIs69lMQRAEiuJYQY93Eyz6BW3l7eGieFjGFQkuKGYL6kMKwrj3zw8AmDV9GwY6zKwIBlk2K50C55zCKNqhcuBsMjPlybDyVlOWljM74sJodBlk+gUA0Pf6PgyM1TmFkOd5ce59J65AcM7JBYGCIFAYBLN+KmA6LM9GZguasqyccqWLakXxMB6qRFgCAOy4gQoDW9OZOcjzvHv5nknqvmw5VwSAMR/9KM8S5a0iGJTXLxhZ1Ih+AQD0lYEPA74Z7Rc0lbdbKq1fUG8oHFm/AABQbYSBAWaWK2+3itMI9AsAAJMgDHiiEv2CuF6cSqBfAACVQhjwUN5ZBrk9XE6/wDkFcWd/hG3v5QAAmH2EAY+N7xeUtGV0ECqoDdEvAIASEQYgqSr9gnh046QKX9IJAIOGMIAJin5BU3lrWJaX1y8Iaw25uM5pBACYZYQBTMrMZFlajX5BvSEX0i8AgNlAGMCUFP2CVrGwUVJevyDsXKZIvwAAZgxhADus6BeMXKaYlrTNchiPLoNMvwAApoUwgGkpTiMMK2s3pTwrZQz0CwBgeggDmBFFvyDpzhiojF8r50ZXO6RfAABTRhjAjDMzWdJS1m7KklY5g6BfAABTRhjArLI8V54MK281ZVlJ/YIoHp0xcPQLAGBLhAH0TBX6BcVlikNyEf0CAOggDKDnuv2CVrO4TLGUfkEwutphGBEMAHiNMIBSVaZfUG+MLIPMNssA/EMYQGVYPmb9gtL6BbWRGQP6BQD8QRhAJVmWKussg1xWv6BzGiGqcRoBwEAjDKDSimWQk2J/hAr0C4Io7v3zA8AsIwygb1ShX+DCaPQyRfoFAAYEYQB9qegXNEf6BSVtsxzVRoJBnX4BgL5GGEDf6/YLWs1ytlmWU1Cr0y8A0LcIAxgYRb+gPXJFwrCksvoFDQX1IQUh/QIA/YEwgIFkZqPLIKftUsZAvwBAvyAMYOBZno1Zv6DEfkG9oSAe4jQCgMohDMAreZYob3W2WS6rXzCyqBH9AgAVQRiAl0b7BU3l7ZZK6RcERb8gHNkfAQDKQhiA98xy5e1WcRqh9H5BQy7gMkUAvUUYAMaoRL8grhezBTHbLAPoDcIAMIm8swxye7icfoFzCuLO/ggxwQDArCEMANsxvl8wXM4gglBBbYh+AYBZQRgAdkDRLygWNSqvXxCPWb+AfgGA6SMMADup6Bc0lbWapW2zTL8AwEwgDADTZGayLK1Gv6DekAvpFwDYMYQBYAYV/YJWsbBRUl6/IOycRqBfAGAKCAPALBntFzRlaVLKGFwYjy6DTL8AwCQIA0APFKcRhpW16RcAqB7CANBDRb8g6c4YqIzDz7nRqxHoFwAQYQAojZnJkpaydlOWtMoZRLdf0JAL2WYZ8BVhAKgAy3PlybDyVlOWldQviMasX+DoFwA+IQwAFVOFfkFxmeKQXES/APABYQCoqG6/oNUsLlMspV8QKKiN7I8QRgQDYEARBoA+UIV+gQsiBfWRYBDQLwAGCWEA6DOWj1m/oLR+QW1kxoB+ATAICANAH7MsVdZZBrmsfkHnNEJU4zQC0KcIA8AAKJZBTor9ESrQLwiiuPfPD2CnEQaAAWNmxWWK7eHy+gVhNGabZfoFQNURBoABVmyz3OkXpKWMoegXNBTU6vQLgIoiDACe6PYLWs1ytlmWU1Cr0y8AKogwAHim6Be0R2YMhiWV1S9oFJcqhvQLgLIRBgCPdfsFraYsbZcyBvoFQPkIAwAkVahfUG8oiIc4jQD0EGEAwAR5lihvdbZZLqtfMLKoEf0CYNYRBgBMarRf0FTebqmUfkFQ9AvCkf0RAMw8wgCAKTHLlbdbxWmE0voF8eiKhwGXKQIzhTAAYId1+wWtpiwvqV8Q14vZgphtloHpIgwA2GnFNsvpyGmE4XL6Bc4piDv7I8QEA2AnEAYAzIjx/YLhcgYRhApqQ/QLgB1EGAAw44p+wcj+CGX2CzqXKdIvALaJMABgVlmeKW81lbWbpW2zTL8A2DbCAICeqEy/oHM1Qki/AOggDADoOTOTJa3iVEJSXr8g7CyDTL8AniMMACiV5XmxP0K7KUuTUsbgorjYH4F+ATxFGABQGcVphGH6BUCPEQYAVE7RL0i6GyepjH+mnBvZTbG4TJFggEFGGABQaZ1+QdZuypJWOYPo9gsaciHbLGPwEAYA9I1uv6DVlGUl9wtqQ3KOfgEGA2EAQF+yLFXWuUyxpH5BEA8pqDfYZhl9jzAAoK91+wWtZnGZYin9gmDM+gX0C9B/CAMABkYV+gUuiBTUO9ss0y9AfyAMABhIlufd1Q7L6xfURvoFdfoFqDTCAICBN9ovaEp5Ccsgyymo1Ue2WaZfgOohDADwRrHNcjJmm+Vy+wVBFPf++YGtIAwA8JKZjSyDPFxevyCMRi9TpF+AEhEGAHjP8qy72qFlaSljoF+AMhEGAGCMbr+g1Sxnm2X6BSgBYQAAtqLoF7RHZgzK7Bc0iksVQ/oFmD2EAQDYjm6/oNWUpe1SxkC/ALOJMAAAO6Aa/YJ6MVsQD3EaATOCMAAAOynPEuWtzjbLZfULRlY7jGKCAXYaYQAApmm0X9BU3m6plH5BUPQLwpH9EYAdQRgAgBlklitvt4rTCKX1C+LRGYOAyxSxfYQBAJglRb+gqbw1LMtL6hfE9WK2IK5zGgGTIgwAwCwrtllOR5dBLqNf4JyCeEhBvSEX0i/AeIQBAOihbr+g1VSeDJcziCBUUBuiX4AuwgAAlKToF4zsj1Bmv6DeKC5TpF/gLcIAAFSA5ZnyVlNZuynlWSljoF/gL8IAAFTI+H5BUyrjn2jnRlc7pF/gBcIAAFSUmcmSVnEqocR+QdgNBvQLBhVhAAD6gOV5sT9CuylLk1LG4KK4mDGgXzBwCAMA0GeK0wjDpfYLgniomC0YgH6BmcnMJLPu2pFOkka+L+dc33+P20MYAIA+VfQLku7GSeX0C4LR1Q7DqNIvmp0X/TzPi5kWM1k+tTUfnHNyQaCg898gqPT3uqMIAwAwADr9gqzdlCWtcgbR6RfUG5XaZjnPc2VZpiyd2VUggyBQGEUDEQwIAwAwYLr9glZTlpXVL6iNzBgMybne9wvMTFmaKssy9eJlLgzDbjDoR4QBABhglqXKOssgl9kvqDfkotqsv4M2M2VZpjQpJwQFQaA4jvuuYEkYAAAPdPsFnWWQB7Bf0AkBVXhZC6NIUVTtDsVYhAEA8EwV+gUuiBTUO9ssT69fYGZK2m3lUywD9lJcqykMq9OfmAxhAAA8Znne3U2x3H5BQ0GtvsP9gjzP1W6VVJicon6YJSAMAAAkje0XNKVS3mU7BbV6MVswhX5BlqZKSuoG7KggCBTXZr8zsbMIAwCAcYptlpPujIFUYr+g3lAQxhPuTtO0tJLgznLOqVav5iJNhAEAwKTMbOQyxWFZWvJ0vHNy8ZAUN5Rl5VwZMV1VDQSEAQDAlFiedVc7tGxmF/DZFhfGckEoy7NuryEPa1Lc6NkYZlIYhoprtbKHMQ5hAACww/IsUd7qLIM8O/0CF9UVzdll3G6JlqVKN78pS1t9HQjiOFYYVWcXSMIAAGCnFf2C9ki/oKWZ6he4qK5o3sKtTqebmdJNG4pAUNtF6rMFfjpq9XplViwkDAAAZoRZrrzdGtlmuT2trxXP32PcjMCE58pSJRvXKw9jKZ4zrecqS5X6A9WZowAA9DXnAoX1hsJ6Y1r9AhfG2wwCxWOi4nFZKpt4sUFf6OygWIVFiQgDAIAZ54JQ4dBchUNzlY+9THEK/YKprkjoglAqaaGkmZImSSV2PSQMAABmVRDFCqJY1thlSv0Cm+KGSsXjyp9in46qzA4QBgAAPeGck4vrCuK6bM7k/QLLElmWbrczYFki28qCRP0mTZLSwwAFQgBAqYp+QbNY2Cgv+gU+XE0wVn1oqNRTBYQBAEAlFNssp0p/84aUpwO9zsCWyt7dsP/jFABgIDjnFESxagv2UFCfI0tbSjauV7LxVaWbNijZ+KqSjetnPAjkea7DDj9cf3755eM+//3vf1/zFyzQN/7hH2bkebY3hjIRBgAAlRPNmS83dzflYaw8S5Ulw8qztPhzbZcZnREIgkCfveQS3XLLLXr99dclSU8//bTOOPNMXXnllfpfH/jAjD3XZPKS91rgNAEAoJKSdrtnGxKlaapDV6zQmWecoT/90z/Vcccfr/e+5z268cYbe/L8Urm9AcIAAKCS2u12T98x33rrrVr1+c9r0aJFWvyWt2j16tU9PY9PGAAAYAvtVqun59I3bdqk/d7yFi1ZskSPPfqo5s6d27PnlqR6vS5X0pURdAYAAJXU63eqF33mM5KkV9evL6XZX+Y7c8IAAKCSejlhfuWVV+p73/ueHnv0UaVZpjvuuKOHz14oc50BwgAAoJp69OJ4++2368s33aT77rtPK1as0CfPP1//54YblCT9ve/BjiAMAAAqKehBGHjooYf06Ysu0m1/+7c66n/8D0nSJz7xCb355pv62te+NuvPPxYzAwAAbCGY5fP2P/nJT3TGmWfqmmuu0fve977u5+fPn69PnHuuvnT99T27tHG2v9ft4WoCAEAlmZlaw8NlD6MnojhWFJW3dyAzAwCASnLOlTp13ktByZstEQYAAJVV9vR5r5QdeggDAIDKKnPqvFeiKCIMAAAwGedcqVv79kJYgcBDGAAAVNogzw6EFZgVkAgDAICKc0EwsN2BqgQdwgAAoPLiirxozqQqdAU6CAMAgMpzQaC4Vit7GDMmCIJKdAU6CAMAgL4QhmFlptWnwzmnuFarzKyARBgAAPSRMIpKX6BnuqoWBCTCAACgj1TxXfWOiGu1SoaZ6o0IAIBtcM6pVq/LVfBFdVviWq2yayawUREAoC+ZmZIkUd6jnQWno1avV3JGoIMwAADoW2amLMuUJknZQ9kqFwSq9cFpDcIAAKDv5XmuNEmU53nZQ+mK4lhhGFY+CEiEAQDAAOnMEpT50hZGUaUWFJoKwgAAYKCUdeogCALFcdx3xUaJMAAAGFBmJstzZVmmbJZKhi4IFIZh35wOmAxhAAAw8MxM+UgwsDyf1mkEFwQKR5YT7ucAMBZhAADgHTPrfuRjw8HYl0Tn5DSya6JzckEwMC/+WyIMAADguf5rOQAAgBlFGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzUdkD6BUzm/Q+51wPRwIAQLUMZBgwM+V5LjOT5Xn39rYEQSAXBAqckwsCOecICQAALzjb3qtkn8jzXHmWKcuy7b7wT1UQBAqjqAgKBAMAwIDq6zBgZsqyTFmazlgAmEwQhgrDkGAAABg4fRkGzExZmipN054/t3NOca2mIKB7CQAYDH0VBjpdgKTdLnsoCoJAcRzLEQoAAH2ub8JAJwRUbbhhFCmKIk4dAAD6Vl+EgSzLKjEbMBnnnGr1OoEAANCXKh0GzExpmioroRuwM2r1Ol0CAEDfqWwYMDMlSaI8y8oeyg6JazWFYVj2MAAAmLJKvo3t1yAgSUm73ZfjBgD4q5JhIMuyvn5BbVew6AgAwGQqFwbyLFOaJGUPY9rarRaBAADQFyoVBsxM7QpfNbAjOuVHAACqrlJhoMqXD+6MLE2V9fHpDgCAHyoTBvKR3QUHTZoknC4AAFRaZcLAIPQEtqazhDIAAFVViTAwqLMCHYMadAAAg6ESYWDQXyzNrK8vlQQADLbSw4Av0+gUCQEAVVV6GPAhCEj+fJ8AgP5TehiwHr1Ifu9731NjzpxJP84488xZfX4z46oCAEAllb5RUavV6kkgaDabeuONN8Z9Lssy/e9zz9VPf/pTPfjd7+qQQw6Z1TGwiREAoIqiMp/czHo2M9BoNNRoNLp/zrJMHzn77J4FAak4VUAYAABUTalhoCydIPCDH/ygZ0FAEqcJAACVVG5noIQXxyzLdPZHP6of/OAH+u53vqNDDz20d09OGAAAVFCpYaDXL42dIPDII4/ou9/5jlasWNHT5ycKAACqqNTTBM65nj1XJwg8/PDDpQQBSerddwsAwNSVfmlhL2RZpo9+7GN6+OGH9Z1vf1uHHXZYOQPpYfgBAGCqBr5AmOe5Pvqxj+lb3/qWvnb33dp77731yiuvjHvMnnvu2ZOWf0AYAABUUOnrDLRbrVldne/f/u3fdNzxx2/zMS//8pdauHDhrI2hg3UGAABVVHoYSJNEaZqWOYSeqQ8N9bQnAQDAVJTeGQiC0ofQE845ggAAoJJKfyV2noQBX0IPAKD/lP4K5ZxT4MF59DAa+K4mAKBPlR4GJCka8BfKIAiYGQAAVFYlXqGCIBjo2YEojsseAgAAk6pEGJAGd3aAWQEAQNVV5lVqUGcHmBUAAFRdZcKAJMVxPFCX30VxzKwAAKDyKvVK5ZxTXKuVPYwZEQQBqw0CAPpCpcKAVLyI9nsg6ISaQZrlAAAMrsqFAUkKw7CvC4UEAQBAP6lkGJCK8+39WL6r1ev0BAAAfaXSb7+jKJKTlCRJ2UPZLuecavU6MwIAgL5T+q6FU5HnudqtVtnDmFQQhgN3JQQAwB99EQYkycyUJInyLCt7KOPEccy+AwCAvtY3YaAjz3MlSSLL81LHEUWRwihiNgAA0Pf6Lgx0ZFmmNEnU6+FzSgAAMGj6NgxIxakDy3NlWaZsFk8fOOcURpHCMCQEAAAGTl+HgbHMTHmeK0tT5TNwCsE5pzAMFYQhlwoCAAbawISBsTrfUp7n3dmDPM9lxZ3jHuucKz6CQMHIfzufAwDABwMZBgAAwNQx/w0AgOcIAwAAeI4wAACA5wgDAAB4jjAAAIDnCAMAAHiOMAAAgOcIAwAAeI4wAACA5wgDAAB4jjAAAIDnCAMAAHiOMAAAgOcIAwAAeI4wAACA56KyBzBW7YizFUQ1uSCUC0KF8ehtFwSj94WhgqimoHtfOOE+F4QKAicXOIVhILfF7SBwCkLXfcw273NOYRQoDJzCwKk2cjvq/jkcvS8cfVw05rHh1m47p8A5hU6Kw6B7OwoDhU7FnwOnOHBbuV3cHwdB93bonJyTAic5p5GvLzlJYeAUSMX3Eqh7O3BS6MbeLr6GM5Msl8tTadztvPjIJ7/PWS5l2ejtPJXyTJbnUtqWZZmU58Xn0kSWZ8XtJJE6tzuP7TwuaY/+nTxTnqSyLJflufJ2qjwr/o5lufIkVZ6N3raR21mSysY8LmunY25nstyUZzby55G/n1txX2ayzJRnubIkH/mapizJRv7O6N/LzZSZqZ2bMtMWt7f8c3E7V3E7M43cN3r7K/bzUo/LmcLxzfHN8V3d45uZAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAPEcYAADAc4QBAAA8RxgAAMBzhAEAADxHGAAAwHOEAQAAfGcDanh42FatWmXDw8NlD2WCKo/NjPFNR5XHNkiq/HOu8tjMGN90VHls0+XMzMoOJLNh48aNWrBggd544w3Nnz+/7OGMU+WxSYxvOqo8tkFS5Z9zlccmMb7pqPLYpovTBAAAeI4wAACA5wgDAAB4bmDDQL1e16pVq1Sv18seygRVHpvE+KajymMbJFX+OVd5bBLjm44qj226BrZACAAApmZgZwYAAMDUEAYAAPAcYQAAAM8NXBi45JJLdOyxx+qMM85Qu90ed1+z2dR73vMeHXfccfrDP/xDvfbaa5UaX8cXvvAFvf3tby99TGma6qyzztKxxx6rT33qUz0bz1TH19Hrn9dYk42tCr9rg4jje+bGxPG9fT4d3wMVBp566im98sor+uEPf6iDDjpI991337j7H3zwQR1yyCF67LHH9MEPflB33XVXpcYnSW+++aaeffbZSozpW9/6ln77t39bP/zhD7V582b96Ec/6tm4pjI+qfc/r6mOrezftUHE8T2zY+L43vmxlf27NhsGKgw88cQTeuc73ylJOumkkyb8ci9btkybN2+WJG3YsEF77rlnpcYnSV/+8pd1/vnnV2JMUxlvmeOTev/zGmtbYyv7d20QcXzP7Jg4vrfNt+M7KnsAM2nDhg1atGiRJGnBggUTpm6WLFmiZ599Vocccoicc3ryyScrNb433nhDzzzzjC6//PJKjGnDhg3d9be3Nt6yx1fGz2uqYyv7d20QcXzP7Jg4vnd+bGX/rs2GvpwZeOWVV3TMMcdM+DAzbdy4UVLxP3K33XYb9/fuvPNOHX/88Xr22Wd15ZVX6qqrrqrU+G688UZ98pOfnJUxTWbXXXeddEzbuq8K4yvj5zXWtsbWq9+1QcTxPXM4vneeb8d3X4aBvffeW48//viEj5NPPln/+I//KEl66KGHtHLlygl/t/M/dOHChdqwYUOlxvfCCy/ommuu0UknnaR169bpuuuum5XxjXX00UdPOqZt3dcr2xpDGT+vqY5N6s3v2iDi+J45HN+zMzZpAI/v8nZPnh0XX3yxHXPMMXb66adbq9UyM7OPf/zjZmb2xhtv2Mknn2zHHXecrVy50p577rlKjW+s3/u93yttTJ3xJElif/Inf2LHHHOMXXDBBT0bz1THN1Yvf15jTTa2KvyuDSKO7+mPieN76nw6vlmOGAAAz/XlaQIAADBzCAMAAHiOMAAAgOcIAwAAeI4w4IE77rhDCxcunJGv9fOf/1zOOUVRpF/84hfj7nv55ZcVRZGcc/r5z38+7r5vfOMbOv7447VgwQLNmzdPK1as0FVXXdVdyGMmxwhg+vbff3855/T1r399wn0HH3ywnHO64447xn3+qaee0mmnnaa99tpLQ0NDOuCAA3TOOefo+eeflzT678eaNWt68B1gRxAGsFMWLVqkv/u7vxv3uTvvvFP77rvvhMf++Z//uT70oQ/pyCOP1IMPPqhnn31W119/vX76058OxJrewGxLkqSU591vv/10++23j/vcv/7rv+qVV17R3Llzx33+29/+to4++mi1Wi3dfffdWrt2re666y4tWLBAV1xxRS+HjZ1R9rWN2L4HH3zQVq5caQsWLLDddtvN3v3ud9sLL7xgZmb/9E//ZJLs9ddf7z7+qaeeMkn24osvdu8f+7Fq1SozM3vttdfswx/+sC1cuNAajYaddNJJ9vzzz29zLC+++KJJsssvv9yWLVs27r4DDzzQrrjiiu5zm5k9+eSTJsluvPHGrX69zrhvv/12W7BgwQ7/bIB+lWWZXXfddbZkyRKr1Wq233772dVXX909xlavXm3HHXec1et1u+222yzLMrvyyitt3333tVqtZocddpg9+OCD3a/XarXs/PPPt7333tvq9botXrzYrr322u79q1atsv32289qtZrts88+211bYPHixXbZZZdZvV63l156qfv5c845xy644AJbsGCB3X777WZm9pvf/Mb22GMPe9/73rfVr9U5zjvf21NPPbVzPzTMGmYG+sBvfvMbfeYzn9GPf/xjPfLIIwqCQO9///uV5/l2/+7v//7v68Ybb9T8+fP18ssv6+WXX9Yll1wiSTrrrLP07//+7/rmN7+pJ554Qmamk08+eUrvQk455RS9/vrrevzxxyVJjz/+uF577TW9973vHfe4u+++W/PmzdN555231a/DqQH46s/+7M/0xS9+UVdccYX+8z//U1/72te01157de//3Oc+pwsvvFBr167ViSeeqC9/+cu6/vrr9aUvfUlPP/20TjzxRJ1yyilat26dJOmmm27SN7/5Td1zzz167rnn9Pd///faf//9JUn33XefbrjhBt18881at26dHnjgAR166KHbHeNee+2lE088UXfeeackafPmzVq9erXOPvvscY976KGHtH79el166aVb/Toc532g7DSCHferX/3KJNkzzzyz3ZkBs62/637++edNkv3Lv/xL93Pr16+3RqNh99xzz6TPPTbZf/rTn7aPfOQjZmb2kY98xC666KIJz/2ud73LVqxYsd3viZkB+GTjxo1Wr9ftlltumXBf5xjbcjZt0aJFds0114z73JFHHmnnnXeemZldcMEF9gd/8AeW5/mEr3n99dfbAQccYO12e8pjXLx4sd1www32wAMP2JIlSyzPc7vzzjvtiCOOMDMbNzPwxS9+0STZa6+9ts2vycxAdTEz0Ad+9rOf6fTTT9db3/pWzZ8/X7/zO78jSXrppZd2+muuXbtWURTpqKOO6n5u991314EHHqi1a9dKkt71rndp3rx5mjdvng4++OAJX+OjH/2o7r33Xr3yyiu69957J7xbkCQzk3Nup8cJDKK1a9eq1WrphBNOmPQxb3/727u3N27cqF/+8pcT1sdfuXJl93g966yztGbNGh144IG68MILu+vqS9Jpp52mZrOpt771rTrnnHN0//33K01TSdK1117bPc7nzZs34d+Vd7/73dq0aZP++Z//Wbfddtukxzn6G2GgD7z3ve/Vq6++qltuuUVPPvlkd7vMdrutICj+F449GKcyzT/ZwTv2xfvWW2/VmjVrtGbNGn33u9+d8NhDDjlEv/u7v6s//uM/1vLly3XIIYdMeMwBBxygn/3sZ6UVoIAqajQa233MlgU9SROC9djj9W1ve5tefPFF/cVf/IWazaY++MEP6o/+6I8kFUXA5557Tn/913+tRqOh8847T+94xzuUJInOPffc7nG+Zs2a7ra9HVEU6cMf/rBWrVqlJ598UmecccaEcR1wwAGSpP/6r/+a2g8AlUMYqLhXX31Va9eu1eWXX64TTjhBy5cv1+uvv969f88995RUXNbXseVlO7VaTVmWjfvcQQcdpDRNx+3D/eqrr+r555/X8uXLJUn77ruvli5dqqVLl2rx4sVbHd/ZZ5+tRx99dKvvFiTp9NNP16ZNm/Q3f/M3W71/IHb7AnbQsmXL1Gg09Mgjj0zp8fPnz9eiRYu6HZ2OH/3oR93jtfO4D33oQ7rlllu0evVqfeMb3+hevttoNHTKKafopptu0qOPPqonnnhCzzzzjHbbbbfucb506VJFUTTh+c8++2w99thjOvXUU7XrrrtOuP+d73yn9thjD/3lX/7lVsfPcV59E/+vo1J23XVX7b777vrqV7+qffbZRy+99JIuu+yy7v1Lly7Vfvvtp89//vO6+uqrtW7dOl1//fXjvsb++++vTZs26ZFHHtFhhx2mOXPmaNmyZTr11FN1zjnn6Oabb9Yuu+yiyy67TPvuu69OPfXUKY/vnHPO0WmnnTZpQeioo47SpZdeqosvvli/+MUv9P73v1+LFi3SCy+8oK985Ss65phj9KlPfWqnfjZAvxoaGtLnPvc5XXrpparValq5cqV+/etf6z/+4z8mPXXw2c9+VqtWrdKSJUt0+OGH6/bbb9eaNWt09913S5JuuOEG7bPPPjr88MMVBIHuvfde7b333lq4cKHuuOMOZVmmo446SnPmzNFdd92lRqMxacjf0vLly7V+/XrNmTNnq/fPnTtXt956q0477TSdcsopuvDCC7V06VKtX79e99xzj1566aWtrleACimzsICp+f73v2/Lly+3er1uK1assEcffdQk2f33329mZo8//rgdeuihNjQ0ZMcee6zde++940p8Zmbnnnuu7b777lu9tHDBggXWaDTsxBNPnPKlhZMVgLYsEHasXr3a3vGOd9guu+xic+fOtRUrVthVV13FpYXwVpZldvXVV9vixYstjmN7y1veYtdee+2kx9jYSwvjOJ5waeFXv/pVO/zww23u3Lk2f/58O+GEE+wnP/mJmZndf//9dtRRR9n8+fNt7ty5dvTRR9vDDz+8zfF1CoSTGVsg7Pjxj39sH/jAB2zPPfe0er1uS5cutY9//OO2bt06M6NAWGVsYQwAgOfoDAAA4DnCAAAAniMMAADgOcIAAACeIwwAAOA5wgAAAJ4jDAAA4DnCAAAAniMMAADgOcIAAACeIwwAAOC5/w+lCf3e14IMSwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["tp.plot_graph(\n", "    val_matrix=results_parcorr['val_matrix'],\n", "    graph=results_parcorr['graph'],\n", "    var_names=var_names,\n", "    link_colorbar_label='cross-MCI',\n", "    node_colorbar_label='auto-MCI',\n", "    ); plt.show()"]}, {"cell_type": "markdown", "id": "d7484e87", "metadata": {}, "source": ["##### References\n", "\n", "[1] <PERSON><PERSON> <PERSON><PERSON>. Asymptotically efficient estimation in the presence of heteroskedasticity of unknown form.\n", "Econometrica: Journal of the Econometric Society, pages 875–891, 1987."]}, {"cell_type": "code", "execution_count": null, "id": "faec417f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "243f8968", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tigenv", "language": "python", "name": "tigenv"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}