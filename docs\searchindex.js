Search.setIndex({"docnames": ["index"], "filenames": ["index.rst"], "titles": ["Welcome to Tigramite\u2019s documentation!"], "terms": {"index": 0, "modul": 0, "search": 0, "page": 0, "github": 0, "repo": 0, "python": 0, "packag": 0, "It": 0, "allow": 0, "effici": 0, "estim": 0, "graph": 0, "from": 0, "high": 0, "dimension": 0, "dataset": 0, "discoveri": 0, "us": 0, "robust": 0, "forecast": 0, "direct": 0, "total": 0, "base": 0, "linear": 0, "well": 0, "non": 0, "parametr": 0, "applic": 0, "discret": 0, "continu": 0, "valu": 0, "also": 0, "includ": 0, "qualiti": 0, "result": 0, "pleas": 0, "cite": 0, "follow": 0, "paper": 0, "depend": 0, "which": 0, "method": 0, "you": 0, "overview": 0, "rung": 0, "j": 0, "gerhardu": 0, "A": 0, "varando": 0, "g": 0, "et": 0, "al": 0, "infer": 0, "nat": 0, "rev": 0, "earth": 0, "environ": 0, "2023": 0, "http": 0, "doi": 0, "org": 0, "10": 0, "1038": 0, "s43017": 0, "023": 0, "00431": 0, "y": 0, "p": 0, "nowack": 0, "m": 0, "kretschmer": 0, "flaxman": 0, "d": 0, "sejdinov": 0, "detect": 0, "quantifi": 0, "associ": 0, "larg": 0, "nonlinear": 0, "sci": 0, "adv": 0, "5": 0, "eaau4996": 0, "2019": 0, "advanc": 0, "sciencemag": 0, "content": 0, "11": 0, "2020": 0, "discov": 0, "contemporan": 0, "lag": 0, "relat": 0, "autocorrel": 0, "proceed": 0, "36th": 0, "confer": 0, "uncertainti": 0, "artifici": 0, "intellig": 0, "uai": 0, "toronto": 0, "canada": 0, "auai": 0, "press": 0, "uai2020": 0, "579_main_pap": 0, "pdf": 0, "recal": 0, "latent": 0, "confound": 0, "neural": 0, "inform": 0, "system": 0, "33": 0, "neurip": 0, "cc": 0, "hash": 0, "94e70705efae423efda1088614128d0b": 0, "abstract": 0, "html": 0, "elena": 0, "saggioro": 0, "jana": 0, "de": 0, "wilj": 0, "marlen": 0, "jakob": 0, "reconstruct": 0, "regim": 0, "relationship": 0, "observ": 0, "chao": 0, "1": 0, "novemb": 0, "30": 0, "113115": 0, "1063": 0, "0020538": 0, "2018": 0, "network": 0, "theoret": 0, "assumpt": 0, "practic": 0, "an": 0, "interdisciplinari": 0, "journal": 0, "scienc": 0, "28": 0, "7": 0, "075310": 0, "aip": 0, "scitat": 0, "5025050": 0, "natur": 0, "commun": 0, "perspect": 0, "www": 0, "com": 0, "articl": 0, "s41467": 0, "019": 0, "10105": 0, "3": 0, "necessari": 0, "suffici": 0, "graphic": 0, "optim": 0, "adjust": 0, "set": 0, "hidden": 0, "variabl": 0, "2021": 0, "34": 0, "class": 0, "2015": 0, "identifi": 0, "gatewai": 0, "complex": 0, "spatio": 0, "tempor": 0, "6": 0, "8502": 0, "ncomms9502": 0, "transfer": 0, "along": 0, "pathwai": 0, "phy": 0, "e": 0, "92": 0, "62829": 0, "1103": 0, "physrev": 0, "062829": 0, "cmiknn": 0, "nearest": 0, "neighbor": 0, "mutual": 0, "In": 0, "21st": 0, "intern": 0, "statist": 0, "mlr": 0, "v84": 0, "runge18a": 0, "datafram": 0, "cond_ind_test": 0, "verbos": 0, "0": 0, "sourc": 0, "framework": 0, "scale": 0, "thi": 0, "contain": 0, "sever": 0, "The": 0, "standard": 0, "address": 0, "describ": 0, "where": 0, "further": 0, "sub": 0, "variant": 0, "ar": 0, "discuss": 0, "pcmciplu": 0, "see": 0, "tutori": 0, "guidanc": 0, "appli": 0, "ha": 0, "differ": 0, "adapt": 0, "implement": 0, "mostli": 0, "hyperparamet": 0, "easi": 0, "parallel": 0, "separ": 0, "script": 0, "handl": 0, "mask": 0, "fals": 0, "control": 0, "confid": 0, "interv": 0, "note": 0, "structur": 0, "repres": 0, "shown": 0, "figur": 0, "node": 0, "defin": 0, "link": 0, "can": 0, "interpret": 0, "under": 0, "certain": 0, "assum": 0, "stationar": 0, "repeat": 0, "parent": 0, "mathcal": 0, "all": 0, "toward": 0, "blue": 0, "red": 0, "box": 0, "iter": 0, "flexibl": 0, "combin": 0, "ani": 0, "kind": 0, "its": 0, "type": 0, "These": 0, "avail": 0, "mci": 0, "particular": 0, "measur": 0, "strength": 0, "For": 0, "exampl": 0, "parcorr": 0, "normal": 0, "between": 0, "howev": 0, "interest": 0, "i": 0, "hypothet": 0, "intervent": 0, "mai": 0, "better": 0, "look": 0, "refer": 0, "w": 0, "paramet": 0, "object": 0, "among": 0, "other": 0, "attribut": 0, "yield": 0, "numpi": 0, "arrai": 0, "shape": 0, "t": 0, "n": 0, "option": 0, "same": 0, "extern": 0, "pass": 0, "callabl": 0, "condindtest": 0, "int": 0, "default": 0, "level": 0, "all_par": 0, "dictionari": 0, "form": 0, "2": 0, "pc": 0, "algorithm": 0, "val_min": 0, "tau": 0, "float": 0, "minimum": 0, "each": 0, "pval_max": 0, "maximum": 0, "step": 0, "number": 0, "sampl": 0, "length": 0, "dict": 0, "get_graph_from_pmatrix": 0, "p_matrix": 0, "alpha_level": 0, "tau_min": 0, "tau_max": 0, "link_assumpt": 0, "none": 0, "construct": 0, "threshold": 0, "alpha": 0, "take": 0, "account": 0, "matrix": 0, "fdr_method": 0, "05": 0, "signific": 0, "get": 0, "tau_mix": 0, "delai": 0, "link_typ": 0, "specifi": 0, "about": 0, "initi": 0, "entri": 0, "impli": 0, "must": 0, "exist": 0, "valid": 0, "o": 0, "addit": 0, "middl": 0, "mark": 0, "instead": 0, "Then": 0, "orient": 0, "need": 0, "consist": 0, "requir": 0, "acycl": 0, "hold": 0, "If": 0, "doe": 0, "appear": 0, "absent": 0, "That": 0, "have": 0, "return": 0, "descript": 0, "abov": 0, "get_lagged_depend": 0, "selected_link": 0, "val_onli": 0, "uncondit": 0, "_": 0, "matric": 0, "correct": 0, "new": 0, "4": 0, "fdr": 0, "deprec": 0, "replac": 0, "zero": 0, "undirect": 0, "larger": 0, "equal": 0, "bool": 0, "onli": 0, "comput": 0, "str": 0, "current": 0, "benjamini": 0, "hochberg": 0, "rate": 0, "fdr_bh": 0, "val_matrix": 0, "conf_matrix": 0, "percentil": 0, "print_result": 0, "return_dict": 0, "print": 0, "output": 0, "kei": 0, "print_significant_link": 0, "ambiguous_tripl": 0, "latter": 0, "ambigu": 0, "conflict": 0, "like": 0, "list": 0, "tripl": 0, "return_parents_dict": 0, "include_lagzero_par": 0, "sort": 0, "unclear": 0, "edgemark": 0, "x": 0, "whether": 0, "should": 0, "parents_dict": 0, "return_significant_link": 0, "pq_matrix": 0, "include_lagzero_link": 0, "boolean": 0, "Will": 0, "remov": 0, "futur": 0, "run_bivci": 0, "bivci": 0, "run_fullci": 0, "fullci": 0, "run_mci": 0, "max_conds_pi": 0, "max_conds_px": 0, "unrestrict": 0, "z": 0, "run_pc_stabl": 0, "save_iter": 0, "pc_alpha": 0, "max_conds_dim": 0, "max_combin": 0, "made": 0, "self": 0, "multi": 0, "ahead": 0, "greater": 0, "save": 0, "everi": 0, "across": 0, "given": 0, "score": 0, "get_model_selection_criterion": 0, "cardin": 0, "pc_1": 0, "origin": 0, "run_pcalg": 0, "01": 0, "lagged_par": 0, "max_conds_px_lag": 0, "mode": 0, "contemp_collider_rul": 0, "major": 0, "conflict_resolut": 0, "true": 0, "run": 0, "contemp_cond": 0, "ci": 0, "As": 0, "part": 0, "superset": 0, "pc1": 0, "conserv": 0, "rule": 0, "collid": 0, "phase": 0, "detail": 0, "lead": 0, "order": 0, "when": 0, "regard": 0, "adjac": 0, "sepset": 0, "relev": 0, "run_pcalg_non_timeseries_data": 0, "simpli": 0, "call": 0, "run_pcmci": 0, "wrapper": 0, "around": 0, "comprehens": 0, "analyt": 0, "numer": 0, "present": 0, "here": 0, "we": 0, "briefli": 0, "summar": 0, "two": 0, "procedur": 0, "select": 0, "tild": 0, "j_t": 0, "reduc": 0, "avoid": 0, "irrelev": 0, "momentari": 0, "i_": 0, "perp": 0, "j_": 0, "common": 0, "driver": 0, "indirect": 0, "main": 0, "free": 0, "tau_": 0, "max": 0, "chosen": 0, "accord": 0, "expect": 0, "recommend": 0, "rather": 0, "choic": 0, "peak": 0, "seen": 0, "sinc": 0, "hypothesi": 0, "do": 0, "precis": 0, "assess": 0, "role": 0, "regular": 0, "techniqu": 0, "criteria": 0, "respect": 0, "import": 0, "pp": 0, "structural_causal_process": 0, "random": 0, "seed": 0, "plai": 0, "incom": 0, "suppli": 0, "format": 0, "coeff": 0, "links_coeff": 0, "8": 0, "var_process": 0, "1000": 0, "pval": 0, "00000": 0, "val": 0, "588": 0, "606": 0, "447": 0, "618": 0, "499": 0, "run_pcmciplu": 0, "reset_lagged_link": 0, "contrast": 0, "full": 0, "up": 0, "markov": 0, "equival": 0, "faith": 0, "four": 0, "widehat": 0, "b": 0, "_t": 0, "skeleton": 0, "through": 0, "subset": 0, "conduct": 0, "motif": 0, "unshield": 0, "remain": 0, "Its": 0, "string": 0, "denot": 0, "unori": 0, "could": 0, "direction": 0, "undecid": 0, "due": 0, "importantli": 0, "alwai": 0, "dag": 0, "first": 0, "one": 0, "member": 0, "averag": 0, "over": 0, "fit": 0, "anoth": 0, "togeth": 0, "fulli": 0, "mean": 0, "matter": 0, "last": 0, "restrict": 0, "found": 0, "consid": 0, "again": 0, "improv": 0, "power": 0, "runtim": 0, "001": 0, "005": 0, "025": 0, "learn": 0, "specif": 0, "introduc": 0, "explain": 0, "still": 0, "experiment": 0, "being": 0, "fine": 0, "tune": 0, "actual": 0, "invit": 0, "feedback": 0, "work": 0, "best": 0, "experi": 0, "run_lpcmci": 0, "constructor": 0, "old": [], "some": 0, "might": 0, "nest": 0, "lag_i": 0, "compon": 0, "background": 0, "knowledg": 0, "possibl": 0, "correspond": 0, "claim": 0, "ancestor": 0, "i_t": 0, "neither": 0, "nor": 0, "wai": 0, "impos": 0, "automat": 0, "There": 0, "No": 0, "either": 0, "smaller": 0, "than": 0, "dpag": 0, "window": 0, "aumax": [], "au_max": [], "underli": 0, "n_preliminary_iter": 0, "determin": 0, "preliminari": 0, "k": 0, "max_cond_px": 0, "pair": 0, "au": [], "s2": 0, "_run_ancestral_removal_phas": 0, "apds_t": 0, "c": 0, "higher": 0, "s3": 0, "_run_non_ancestral_removal_phas": 0, "napds_t": 0, "max_p_glob": 0, "max_p_non_ancestr": 0, "second": 0, "_run_dsep_removal_phas": 0, "max_q_glob": 0, "most": 0, "mani": 0, "sum": 0, "more": 0, "max_pds_set": 0, "element": 0, "opposit": 0, "prelim_with_collider_rul": 0, "pseudocod": 0, "line": 0, "22": 0, "18": 0, "directli": 0, "befor": 0, "parents_of_lag": 0, "pa": 0, "prelim_onli": 0, "stop": 0, "after": 0, "perform": 0, "break_once_separ": 0, "break": 0, "command": 0, "no_non_ancestral_phas": 0, "execut": 0, "use_a_pds_t_for_major": 0, "instruct": 0, "adj": 0, "orient_contemp": 0, "orient_comtemp": 0, "update_middle_mark": 0, "pseudoc": 0, "mmr": 0, "prelim_rul": 0, "exclud": 0, "r9": 0, "prime": 0, "r10": 0, "fix_all_edges_before_final_orient": 0, "np": 0, "inf": 0, "termin": 0, "although": 0, "empti": 0, "nevertheless": 0, "sound": 0, "check": 0, "appropri": 0, "forc": 0, "auto_first": 0, "pseudcod": 0, "autodepend": 0, "priorit": 0, "even": 0, "remember_only_par": 0, "been": 0, "point": 0, "wa": 0, "later": 0, "tail": 0, "re": 0, "no_apr": 0, "apr": 0, "except": 0, "never": 0, "conveni": 0, "post": 0, "purpos": 0, "wildcard": 0, "st": [], "edg": 0, "star": 0, "prediction_model": 0, "extract": 0, "persist": 0, "finit": 0, "within": 0, "ignor": 0, "missing_flag": 0, "miss": 0, "sklearn": 0, "linear_model": 0, "linearregress": 0, "regress": 0, "ie": 0, "eg": 0, "gpdc": 0, "gaussianprocessregressor": 0, "nearestneighbor": 0, "anneal": 0, "run_rpcmci": 0, "num_regim": 0, "max_transit": 0, "switch_thr": 0, "num_iter": 0, "20": 0, "max_ann": 0, "n_job": 0, "transit": 0, "singl": 0, "switch": 0, "cpu": 0, "joblib": 0, "paral": 0, "n_regim": 0, "One": 0, "hot": 0, "encod": 0, "causal_result": 0, "converg": 0, "diff_g_f": 0, "tupl": 0, "consecut": 0, "error_free_ann": 0, "without": 0, "error": 0, "independence_tests_bas": 0, "42": 0, "mask_typ": 0, "fixed_thr": 0, "sig_sampl": 0, "500": 0, "sig_blocklength": 0, "conf_lev": 0, "9": 0, "conf_sampl": 0, "100": 0, "conf_blocklength": 0, "recycle_residu": 0, "provid": 0, "shuffl": 0, "bootstrap": 0, "inherit": 0, "randomst": 0, "default_rng": 0, "xy": 0, "xz": 0, "yz": 0, "xyz": 0, "shuffle_test": 0, "block": 0, "decai": 0, "autocovari": 0, "nan": 0, "side": 0, "residu": 0, "store": 0, "faster": 0, "cost": 0, "consider": 0, "memori": 0, "get_analytic_confid": 0, "df": 0, "concret": 0, "overrid": 0, "get_analytic_signific": 0, "dim": 0, "get_bootstrap_confid": 0, "dependence_measur": 0, "95": 0, "data_typ": 0, "With": 0, "row": 0, "column": 0, "get_dependence_measur": 0, "binari": 0, "individu": 0, "0s": 0, "1s": 0, "conf_low": 0, "conf_upp": 0, "upper": 0, "lower": 0, "bound": 0, "get_confid": 0, "child": 0, "var": 0, "make": 0, "sure": 0, "size": 0, "instanti": 0, "get_fixed_thres_signific": 0, "signfic": 0, "get_measur": 0, "get_shuffle_signific": 0, "return_null_dist": 0, "properti": 0, "print_info": 0, "run_test": 0, "cut_off": 0, "2xtau_max": 0, "alpha_or_thr": 0, "signficic": 0, "both": 0, "_get_single_residu": 0, "max_lag": 0, "max_lag_or_tau_max": 0, "how": 0, "cutoff": 0, "begin": 0, "guarante": 0, "compar": 0, "multipl": 0, "much": 0, "decis": 0, "run_test_raw": 0, "x_type": 0, "y_type": 0, "z_type": 0, "input": 0, "dimens": 0, "set_datafram": 0, "flag": 0, "set_mask_typ": 0, "setter": 0, "ensur": 0, "clash": 0, "kwarg": 0, "partial": 0, "correl": 0, "ordinari": 0, "least": 0, "squar": 0, "ol": 0, "pearson": 0, "To": 0, "out": 0, "beta_x": 0, "epsilon_": 0, "beta_i": 0, "rho": 0, "left": 0, "r_x": 0, "r_y": 0, "right": 0, "student": 0, "distribut": 0, "d_z": 0, "degre": 0, "freedom": 0, "argument": 0, "coeffici": 0, "less": 0, "featur": 0, "corrected_a": 0, "akaik": 0, "criterion": 0, "modulo": 0, "constant": 0, "leav": 0, "cross": 0, "asymptot": 0, "aic": 0, "target": 0, "unshuffl": 0, "robust_parcorr": 0, "robustparcorr": 0, "paranorm": 0, "transform": 0, "margin": 0, "firstli": 0, "phi": 0, "circ": 0, "hat": 0, "f": 0, "quantil": 0, "empir": 0, "idea": 0, "stem": 0, "literatur": 0, "nonparanorm": 0, "han": 0, "liu": 0, "john": 0, "lafferti": 0, "larri": 0, "wasserman": 0, "semiparametr": 0, "mach": 0, "2295": 0, "2328": 0, "2009": 0, "fang": 0, "ming": 0, "yuan": 0, "gaussian": 0, "copula": 0, "ann": 0, "40": 0, "2293": 0, "2326": 0, "2012a": 0, "naftali": 0, "harri": 0, "mathia": 0, "drton": 0, "machin": 0, "research": 0, "14": 0, "3365": 0, "3383": 0, "2013": 0, "afterward": 0, "now": 0, "uniform": 0, "plu": 0, "trafo2norm": 0, "thre": 0, "1e": 0, "code": 0, "small": 0, "too": 0, "close": 0, "similarli": 0, "null_dist_filenam": 0, "gp_param": 0, "distanc": 0, "gp": 0, "scikit": 0, "kernel": 0, "let": 0, "them": 0, "cython": 0, "null": 0, "precomput": 0, "generate_and_save_nulldist": 0, "npz": 0, "file": 0, "f_x": 0, "f_y": 0, "sim": 0, "sigma": 0, "bandwidth": 0, "optimz": 0, "r": 0, "pre": 0, "otherwis": 0, "dure": 0, "gabor": 0, "szeke": 0, "maria": 0, "l": 0, "rizzo": 0, "nail": 0, "bakirov": 0, "arxiv": 0, "ab": 0, "0803": 0, "4101": 0, "otion": 0, "path": 0, "gaussprocreg": 0, "sample_s": 0, "pairwis": 0, "generate_nulldist": 0, "dist": 0, "disk": 0, "add": 0, "gauss_pr": 0, "null_dist": 0, "name": 0, "add_to_null_dist": 0, "just": 0, "load": 0, "nulldist": 0, "wide": 0, "rang": 0, "beforehand": 0, "log": 0, "likelihood": 0, "neg": 0, "Is": 0, "gpdc_torch": 0, "gpdctorch": 0, "gpytorch": 0, "dcor": 0, "pip": 0, "gaussprocregtorch": 0, "knn": 0, "shuffle_neighbor": 0, "rank": 0, "worker": 0, "model_selection_fold": 0, "come": 0, "joint": 0, "densiti": 0, "frenzel": 0, "pomp": 0, "lett": 0, "99": 0, "204101": 0, "2007": 0, "suitabl": 0, "cmisymb": 0, "cmi": 0, "iint": 0, "frac": 0, "cdot": 0, "dx": 0, "dy": 0, "dz": 0, "psi": 0, "sum_": 0, "k_": 0, "digamma": 0, "hyper": 0, "cube": 0, "subspac": 0, "view": 0, "smooth": 0, "unlik": 0, "fix": 0, "bia": 0, "varianc": 0, "slightli": 0, "while": 0, "quantiti": 0, "scipi": 0, "spatial": 0, "ckdtree": 0, "fraction": 0, "henc": 0, "absolut": 0, "surrog": 0, "processor": 0, "fold": 0, "get_conditional_entropi": 0, "entropi": 0, "h": 0, "prl": 0, "overwrit": 0, "preserv": 0, "permut": 0, "those": 0, "x_i": 0, "x_j": 0, "z_j": 0, "niehgbor": 0, "z_i": 0, "n_symb": 0, "categor": 0, "symbol": 0, "local": 0, "mix": 0, "cmiknnmix": 0, "crosstab": 0, "conting": 0, "approxim": 0, "probabl": 0, "mass": 0, "drawn": 0, "oracle_conditional_independ": 0, "oracleci": 0, "observed_var": 0, "selection_var": 0, "graph_is_mag": 0, "oracl": 0, "link_coeff": 0, "ground": 0, "truth": 0, "unit": 0, "altern": 0, "digest": 0, "func": 0, "definin": 0, "check_shortest_path": 0, "starts_with": 0, "ends_with": 0, "forbidden_nod": 0, "only_non_causal_path": 0, "check_optimality_cond": 0, "optimality_cond_des_ym": 0, "optimality_cond_i": 0, "return_path": 0, "non_rep": 0, "au_i": 0, "au_j": 0, "alreadi": 0, "truncat": 0, "breadth": 0, "start": 0, "end": 0, "veri": 0, "long": 0, "constrain": 0, "has_path": 0, "ancestr": 0, "compute_ancestor": 0, "anc_all_x": 0, "anc_all_i": 0, "anc_all_z": 0, "arrohead": 0, "compat": 0, "get_graph_from_link": 0, "mag": 0, "admg": 0, "project": 0, "oper": 0, "pearl": 0, "get_links_from_graph": 0, "case": 0, "ad": 0, "canon": 0, "richardson": 0, "spirt": 0, "2002": 0, "support": 0, "evalu": 0, "els": 0, "Not": 0, "dummi": 0, "parcorr_mult": 0, "parcorrmult": 0, "correlation_typ": 0, "max_corr": 0, "multivari": 0, "mult_corr": 0, "gsquar": 0, "chi2": 0, "2000": 0, "stat": 0, "formula": 0, "bishop": 0, "fienberg": 0, "holland": 0, "1975": 0, "theori": 0, "mit": 0, "cambridg": 0, "p_valu": 0, "chi": 0, "dof": 0, "parcorr_wl": 0, "parcorrwl": 0, "gt_std_matrix": 0, "expert_knowledg": 0, "heteroskedast": 0, "window_s": 0, "robustifi": 0, "weight": 0, "wl": 0, "known": 0, "thei": 0, "neighbour": 0, "homoskedast": 0, "term": 0, "deviat": 0, "nois": 0, "nb_node": 0, "expert": 0, "regressionci": 0, "vs": 0, "notion": 0, "devianc": 0, "emploi": 0, "significantli": 0, "hypothes": 0, "accept": 0, "approach": 0, "univari": 0, "moreov": 0, "multinomi": 0, "causaleffect": 0, "graph_typ": 0, "hidden_vari": 0, "check_sm_overlap": 0, "potenti": 0, "backdoor": 0, "variou": 0, "wright": 0, "depth": 0, "introduct": 0, "8485ae387a981d783f8764e508151cd9": 0, "caus": 0, "overlap": 0, "check_xys_path": 0, "proper": 0, "clean": 0, "check_optim": 0, "thm": 0, "fit_bootstrap_of": 0, "method_arg": 0, "boot_sampl": 0, "boot_blocklength": 0, "construct_arrai": 0, "shift": 0, "bootsrap": 0, "predict_bootstrap_of": 0, "draw": 0, "fit_total_effect": 0, "adjustment_set": 0, "conditional_estim": 0, "data_transform": 0, "ignore_identifi": 0, "oset": 0, "minimized_optim": 0, "minim": 0, "colliders_minimized_optim": 0, "preprocess": 0, "prior": 0, "standardscal": 0, "simpl": 0, "user": 0, "fit_wright_effect": 0, "considerd": 0, "complic": 0, "static": 0, "get_dict_from_graph": 0, "parents_onli": 0, "helper": 0, "convert": 0, "get_graph_from_dict": 0, "get_medi": 0, "get_optimal_set": 0, "alternative_condit": 0, "return_separate_set": 0, "theorem": 0, "colliders_onli": 0, "invalid": 0, "collider_par": 0, "oset_": 0, "return_individual_bootstrap_result": 0, "confidence_interv": 0, "predict_total_effect": 0, "intervention_data": 0, "conditions_data": 0, "pred_param": 0, "return_further_pred_result": 0, "aggregation_func": 0, "transform_interventions_and_predict": 0, "len": 0, "predictor": 0, "entir": 0, "invers": 0, "estimate_confid": 0, "predict_wright_effect": 0, "conditional_model": 0, "care": 0, "inverse_transform": 0, "fit_full_model": 0, "selected_vari": 0, "empty_predictors_funct": 0, "return_data": 0, "integ": 0, "fit_result": 0, "get_coef": 0, "get_general_fitted_model": 0, "get_general_predict": 0, "get_val_matrix": 0, "fit_model": 0, "give": 0, "deriv": 0, "linearmedi": 0, "model_param": 0, "etc": 0, "ce": 0, "mce": 0, "ac": 0, "suscept": 0, "amc": 0, "chain": 0, "x_t": 0, "eta": 0, "y_t": 0, "x_": 0, "z_t": 0, "y_": 0, "25": 0, "37": 0, "true_par": 0, "med": 0, "get_coeff": 0, "get_c": 0, "get_mc": 0, "get_all_ac": 0, "get_all_amc": 0, "250648072987": 0, "36897445": 0, "25718002": 0, "24365041": 0, "38250406": 0, "12532404": 0, "fit_model_bootstrap": 0, "boostrap": 0, "version": 0, "cube_root": 0, "from_autocorrel": 0, "generate_noise_from": [], "root": 0, "get_ac": 0, "lag_mod": 0, "absmax": 0, "exclude_i": 0, "eman": 0, "all_lag": 0, "itself": 0, "exclude_j": 0, "affect": 0, "previou": 0, "exclude_k": 0, "exclude_self_effect": 0, "themselv": 0, "get_amc": 0, "get_bootstrap_of": 0, "function_arg": 0, "incl": 0, "get_ce_max": 0, "get_conditional_mc": 0, "notk": 0, "go": 0, "get_joint_c": 0, "count": 0, "joint_c": 0, "get_joint_ce_matrix": 0, "taui": 0, "tauj": 0, "stand": 0, "joint_ce_matrix": 0, "2d": 0, "get_joint_mc": 0, "joint_mc": 0, "minu": 0, "get_mediation_graph_data": 0, "include_neighbor": 0, "path_val_matrix": 0, "path_node_arrai": 0, "tsg_path_val_matrix": 0, "graph_data": 0, "color": 0, "get_tsg": 0, "link_matrix": 0, "analyz": 0, "sig_thr": 0, "array_lik": 0, "tsg": 0, "symmetr": 0, "net_to_tsg": 0, "translat": 0, "tsg_to_net": 0, "train_indic": 0, "test_indic": 0, "train": 0, "target_predictor": 0, "selected_target": 0, "instanc": 0, "get_predictor": 0, "steps_ahead": 0, "get_test_arrai": 0, "get_train_arrai": 0, "new_data": 0, "cut": 0, "off": 0, "below": 0, "vector_var": 0, "var_nam": 0, "datatim": 0, "analysis_mod": 0, "reference_point": 0, "time_offset": 0, "remove_missing_upto_maxlag": 0, "definit": 0, "OR": 0, "whose": 0, "t_i": 0, "vari": 0, "dismiss": 0, "slice": 0, "occur": 0, "bias": 0, "section": 0, "supplement": 0, "sciadv": 0, "vector": 0, "pars": 0, "creat": 0, "match": 0, "enumer": 0, "timelabel": 0, "1d": 0, "rel": 0, "share": 0, "axi": 0, "t_max": 0, "largest_time_step": 0, "bigger": 0, "At": 0, "align": 0, "agre": 0, "offset": 0, "_initialized_from": 0, "3d": 0, "map": 0, "represent": 0, "identifii": 0, "max_": 0, "largest": 0, "latest": 0, "random_st": 0, "extraz": 0, "return_cleaned_xyz": 0, "do_check": 0, "remove_overlap": 0, "n_en": 0, "var1": 0, "var2": 0, "varlag": 0, "assign": 0, "duplic": 0, "saniti": 0, "2xtau_max_futur": 0, "t_miss": 0, "principl": 0, "would": 0, "n_sampl": 0, "print_array_info": 0, "info": 0, "typic": 0, "varx": 0, "get_acf": 0, "autocorr": 0, "get_block_length": 0, "mader": 0, "eq": 0, "pfeifer": 0, "2005": 0, "multidimension": 0, "jointli": 0, "curv": 0, "fail": 0, "limit": 0, "neurosci": 0, "volum": 0, "219": 0, "issu": 0, "15": 0, "octob": 0, "285": 0, "291": 0, "block_len": 0, "lowhighpass_filt": 0, "cutperiod": 0, "pass_period": 0, "low": 0, "butterworth": 0, "filter": 0, "twice": 0, "onc": 0, "forward": 0, "backward": 0, "period": 0, "act": 0, "ordinal_patt_arrai": 0, "array_mask": 0, "symbolifi": 0, "ordin": 0, "pattern": 0, "uniqu": 0, "faculti": 0, "symb_arrai": 0, "shorter": 0, "2011": 0, "coupl": 0, "83": 0, "12": 0, "051122": 0, "label": 0, "embed": 0, "patt": 0, "patt_mask": 0, "patt_tim": 0, "quantile_bin_arrai": 0, "bin": 0, "smooth_width": 0, "width": 0, "heavisid": 0, "rtype": 0, "intervention_typ": 0, "hard": 0, "time_bin_with_mask": 0, "time_bin_length": 0, "bindata": 0, "outer": 0, "cdf": 0, "normal_data": 0, "parents_neighbors_coeff": 0, "inv_inno_cov": 0, "initial_valu": 0, "autoregress": 0, "innov": 0, "var_network": 0, "friendli": 0, "weighted_avg_and_std": 0, "std": 0, "check_stationar": 0, "stationari": 0, "dag_to_link": 0, "generate_structural_causal_process": 0, "dependency_func": 0, "dependency_coeff": 0, "auto_coeff": 0, "contemp_fract": 0, "noise_dist": 0, "noise_mean": 0, "noise_sigma": 0, "noise_se": 0, "randomli": 0, "characterist": 0, "frawn": 0, "arbitrari": 0, "factor": 0, "weibul": 0, "def": 0, "beta": 0, "links_to_graph": 0, "transient_fract": 0, "interven": 0, "randn": 0, "un": 0, "soft": 0, "percentag": 0, "transient": 0, "realiz": 0, "nonvalid": 0, "infin": 0, "lag1": 0, "coef1": 0, "lag2": 0, "coef2": 0, "nonzero": 0, "covari": 0, "inno_cov": 0, "debug": 0, "no_nois": 0, "disabl": 0, "max_delai": 0, "true_parent_neighbor": 0, "id": 0, "parent_node_id": 0, "time_lag": 0, "plot_densityplot": 0, "setup_arg": 0, "add_densityplot_arg": 0, "selected_dataset": 0, "show_marginal_densities_on_diagon": 0, "setup_density_matrix": 0, "add_densityplot": 0, "diagon": 0, "show": 0, "seaborn": 0, "doc": 0, "overlaid": 0, "plot_graph": 0, "fig_ax": 0, "figsiz": 0, "save_nam": 0, "link_colorbar_label": 0, "node_colorbar_label": 0, "auto": 0, "link_width": 0, "link_attribut": 0, "node_po": 0, "arrow_linewidth": 0, "vmin_edg": 0, "vmax_edg": 0, "edge_tick": 0, "cmap_edg": 0, "rdbu_r": 0, "vmin_nod": 0, "vmax_nod": 0, "node_tick": 0, "cmap_nod": 0, "node_s": 0, "node_aspect": 0, "arrowhead_s": 0, "curved_radiu": 0, "label_fonts": 0, "tick_label_s": 0, "node_label_s": 0, "link_label_fonts": 0, "lag_arrai": 0, "show_colorbar": 0, "inner_edge_styl": 0, "dash": 0, "special_nod": 0, "show_autodependency_lag": 0, "straight": 0, "arrow": 0, "maxim": 0, "magnitud": 0, "posit": 0, "coordin": 0, "via": 0, "ax": 0, "basemap": 0, "ccr": 0, "platecarre": 0, "cartopi": 0, "linewidth": 0, "colorbar": 0, "tick": 0, "colormap": 0, "orrd": 0, "ratio": 0, "heigth": 0, "varibl": 0, "head": 0, "fancyarrowpatch": 0, "curvatur": 0, "fontsiz": 0, "opac": 0, "arang": 0, "plot_lagfunc": 0, "add_lagfunc_arg": 0, "lagfunct": 0, "setup_matrix": 0, "add_lagfunc": 0, "plot_mediation_graph": 0, "standard_color_link": 0, "black": 0, "standard_color_nod": 0, "lightgrei": 0, "visual": 0, "plot_mediation_time_series_graph": 0, "top": 0, "bottom": 0, "plot_scatterplot": 0, "add_scatterplot_arg": 0, "scatter": 0, "setup_scatter_matrix": 0, "add_scatterplot": 0, "plot_time_series_graph": 0, "auxiliari": 0, "auxadmg": 0, "style": 0, "inner_edg": 0, "special": 0, "plot_timeseri": 0, "var_unit": 0, "time_label": 0, "grey_masked_sampl": 0, "show_meanlin": 0, "data_linewidth": 0, "skip_ticks_data_x": 0, "skip_ticks_data_i": 0, "adjust_plot": 0, "stack": 0, "panel": 0, "subplot": 0, "fig": 0, "pyplot": 0, "grei": 0, "fill": 0, "horizont": 0, "skip": 0, "tickmark": 0, "plot_tsg": 0, "anc_x": 0, "anc_i": 0, "anc_xi": 0, "help": 0, "label_space_left": 0, "label_space_top": 0, "legend_width": 0, "legend_fonts": 0, "plot_gridlin": 0, "setup": 0, "space": 0, "alloc": 0, "vertic": 0, "legend": 0, "grid": 0, "matrix_lag": 0, "label_color": 0, "snskdeplot_arg": 0, "cmap": 0, "snskdeplot_diagonal_arg": 0, "depict": 0, "sn": 0, "kdeplot": 0, "adjustfig": 0, "show_label": 0, "x_base": 0, "y_base": 0, "lag_unit": 0, "comparison": 0, "two_sided_thr": 0, "marker": 0, "markers": 0, "po": 0, "matplotlib": 0, "savefig": 0, "scatterplot": 0, "write_csv": 0, "digit": 0, "write": 0, "csv": 0, "g\u00fcnther": 0, "u": 0, "ninad": 0, "context": 0, "infinit": 0, "node_classif": 0, "enabl": 0, "unobserv": 0, "classif": 0, "time_context": 0, "space_context": 0, "time_dummi": 0, "space_dummi": 0, "dummy_par": 0, "observed_context_par": 0, "dummy_ci_test": 0, "system_search": 0, "context_search": 0, "dummy_search": 0, "time_context_nod": 0, "space_context_nod": 0, "system_nod": 0, "add_found_context_link_assumpt": 0, "assume_exogenous_context": 0, "observed_context_nod": 0, "amend": 0, "clean_link_assumpt": 0, "clean_system_link_assumpt": 0, "contextu": 0, "discover_dummy_system_link": 0, "context_system_result": 0, "run_jpcmciplu": 0, "discover_lagged_and_context_system_link": 0, "discover_lagged_context_system_link": 0, "discover_system_system_link": 0, "lagged_context_dummy_par": 0, "remove_dummy_link_assumpt": 0, "obtain": 0, "next": 0, "c_t": 0, "c_": 0, "mathbf": 0, "setminu": 0, "condition": 0, "By": 0, "final": 0, "cd": 0, "involv": 0, "multioutput": 0, "multioutputregressor": 0, "extend": 0, "get_residuals_cov_mean": 0, "predict_full_model": 0, "classifi": 0, "treat": 0, "resolv": 0, "manner": 0, "bic": 0, "ref": 0, "bold": 0, "taumax": 0, "ast": 0}, "objects": {"tigramite.causal_effects": [[0, 0, 1, "", "CausalEffects"]], "tigramite.causal_effects.CausalEffects": [[0, 1, 1, "", "check_XYS_paths"], [0, 1, 1, "", "check_optimality"], [0, 1, 1, "", "fit_bootstrap_of"], [0, 1, 1, "", "fit_total_effect"], [0, 1, 1, "", "fit_wright_effect"], [0, 1, 1, "", "get_dict_from_graph"], [0, 1, 1, "", "get_graph_from_dict"], [0, 1, 1, "", "get_mediators"], [0, 1, 1, "", "get_optimal_set"], [0, 1, 1, "", "predict_bootstrap_of"], [0, 1, 1, "", "predict_total_effect"], [0, 1, 1, "", "predict_wright_effect"]], "tigramite": [[0, 2, 0, "-", "data_processing"], [0, 2, 0, "-", "plotting"]], "tigramite.data_processing": [[0, 0, 1, "", "DataFrame"], [0, 4, 1, "", "get_acf"], [0, 4, 1, "", "get_block_length"], [0, 4, 1, "", "lowhighpass_filter"], [0, 4, 1, "", "ordinal_patt_array"], [0, 4, 1, "", "quantile_bin_array"], [0, 4, 1, "", "smooth"], [0, 4, 1, "", "structural_causal_process"], [0, 4, 1, "", "time_bin_with_mask"], [0, 4, 1, "", "trafo2normal"], [0, 4, 1, "", "var_process"], [0, 4, 1, "", "weighted_avg_and_std"]], "tigramite.data_processing.DataFrame": [[0, 1, 1, "", "construct_array"], [0, 1, 1, "", "print_array_info"]], "tigramite.data_processing.DataFrame.self": [[0, 3, 1, "", "M"], [0, 3, 1, "", "N"], [0, 3, 1, "", "T"], [0, 3, 1, "", "_initialized_from"], [0, 3, 1, "", "analysis_mode"], [0, 3, 1, "", "bootstrap"], [0, 3, 1, "", "data_type"], [0, 3, 1, "", "datasets"], [0, 3, 1, "", "datatime"], [0, 3, 1, "", "largest_time_step"], [0, 3, 1, "", "mask"], [0, 3, 1, "", "missing_flag"], [0, 3, 1, "", "reference_points"], [0, 3, 1, "", "time_offsets"], [0, 3, 1, "", "values"], [0, 3, 1, "", "var_names"]], "tigramite.independence_tests.cmiknn": [[0, 0, 1, "", "CMIknn"]], "tigramite.independence_tests.cmiknn.CMIknn": [[0, 1, 1, "", "get_conditional_entropy"], [0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 1, 1, "", "get_shuffle_significance"], [0, 5, 1, "", "measure"]], "tigramite.independence_tests.cmisymb": [[0, 0, 1, "", "CMIsymb"]], "tigramite.independence_tests.cmisymb.CMIsymb": [[0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_shuffle_significance"], [0, 5, 1, "", "measure"]], "tigramite.independence_tests.gpdc": [[0, 0, 1, "", "GPDC"]], "tigramite.independence_tests.gpdc.GPDC": [[0, 1, 1, "", "generate_and_save_nulldists"], [0, 1, 1, "", "generate_nulldist"], [0, 1, 1, "", "get_analytic_significance"], [0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 1, 1, "", "get_shuffle_significance"], [0, 5, 1, "", "measure"]], "tigramite.independence_tests.gpdc_torch": [[0, 0, 1, "", "GPDCtorch"]], "tigramite.independence_tests.gpdc_torch.GPDCtorch": [[0, 1, 1, "", "generate_and_save_nulldists"], [0, 1, 1, "", "generate_nulldist"], [0, 1, 1, "", "get_analytic_significance"], [0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 1, 1, "", "get_shuffle_significance"], [0, 5, 1, "", "measure"]], "tigramite.independence_tests.gsquared": [[0, 0, 1, "", "Gsquared"]], "tigramite.independence_tests.gsquared.Gsquared": [[0, 1, 1, "", "get_analytic_significance"], [0, 1, 1, "", "get_dependence_measure"], [0, 5, 1, "", "measure"]], "tigramite.independence_tests.independence_tests_base": [[0, 0, 1, "", "CondIndTest"]], "tigramite.independence_tests.independence_tests_base.CondIndTest": [[0, 1, 1, "", "get_analytic_confidence"], [0, 1, 1, "", "get_analytic_significance"], [0, 1, 1, "", "get_bootstrap_confidence"], [0, 1, 1, "", "get_confidence"], [0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_fixed_thres_significance"], [0, 1, 1, "", "get_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 1, 1, "", "get_shuffle_significance"], [0, 5, 1, "", "measure"], [0, 1, 1, "", "print_info"], [0, 1, 1, "", "run_test"], [0, 1, 1, "", "run_test_raw"], [0, 1, 1, "", "set_dataframe"], [0, 1, 1, "", "set_mask_type"]], "tigramite.independence_tests.oracle_conditional_independence": [[0, 0, 1, "", "OracleCI"]], "tigramite.independence_tests.oracle_conditional_independence.OracleCI": [[0, 1, 1, "", "check_shortest_path"], [0, 1, 1, "", "get_confidence"], [0, 1, 1, "", "get_graph_from_links"], [0, 1, 1, "", "get_links_from_graph"], [0, 1, 1, "", "get_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 5, 1, "", "measure"], [0, 1, 1, "", "run_test"], [0, 1, 1, "", "set_dataframe"]], "tigramite.independence_tests.parcorr": [[0, 0, 1, "", "ParCorr"]], "tigramite.independence_tests.parcorr.ParCorr": [[0, 1, 1, "", "get_analytic_confidence"], [0, 1, 1, "", "get_analytic_significance"], [0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 1, 1, "", "get_shuffle_significance"], [0, 5, 1, "", "measure"]], "tigramite.independence_tests.parcorr_mult": [[0, 0, 1, "", "ParCorrMult"]], "tigramite.independence_tests.parcorr_mult.ParCorrMult": [[0, 1, 1, "", "get_analytic_significance"], [0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 1, 1, "", "get_shuffle_significance"], [0, 5, 1, "", "measure"], [0, 1, 1, "", "mult_corr"]], "tigramite.independence_tests.parcorr_wls": [[0, 0, 1, "", "ParCorrWLS"]], "tigramite.independence_tests.parcorr_wls.ParCorrWLS": [[0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 1, 1, "", "get_shuffle_significance"]], "tigramite.independence_tests.regressionCI": [[0, 0, 1, "", "RegressionCI"]], "tigramite.independence_tests.regressionCI.RegressionCI": [[0, 1, 1, "", "get_analytic_significance"], [0, 1, 1, "", "get_dependence_measure"], [0, 5, 1, "", "measure"], [0, 1, 1, "", "set_dataframe"]], "tigramite.independence_tests.robust_parcorr": [[0, 0, 1, "", "RobustParCorr"]], "tigramite.independence_tests.robust_parcorr.RobustParCorr": [[0, 1, 1, "", "get_analytic_confidence"], [0, 1, 1, "", "get_analytic_significance"], [0, 1, 1, "", "get_dependence_measure"], [0, 1, 1, "", "get_model_selection_criterion"], [0, 1, 1, "", "get_shuffle_significance"], [0, 5, 1, "", "measure"], [0, 1, 1, "", "trafo2normal"]], "tigramite.jpcmciplus": [[0, 0, 1, "", "JPCMCIplus"]], "tigramite.jpcmciplus.JPCMCIplus": [[0, 3, 1, "", "N"], [0, 3, 1, "", "T"], [0, 1, 1, "", "add_found_context_link_assumptions"], [0, 3, 1, "", "all_parents"], [0, 1, 1, "", "assume_exogenous_context"], [0, 1, 1, "", "clean_link_assumptions"], [0, 1, 1, "", "clean_system_link_assumptions"], [0, 1, 1, "", "discover_dummy_system_links"], [0, 1, 1, "", "discover_lagged_context_system_links"], [0, 1, 1, "", "discover_system_system_links"], [0, 3, 1, "", "dummy_ci_test"], [0, 3, 1, "", "dummy_parents"], [0, 3, 1, "", "iterations"], [0, 3, 1, "", "mode"], [0, 3, 1, "", "observed_context_parents"], [0, 3, 1, "", "pval_max"], [0, 1, 1, "", "remove_dummy_link_assumptions"], [0, 1, 1, "", "run_jpcmciplus"], [0, 3, 1, "", "space_context_nodes"], [0, 3, 1, "", "space_dummy"], [0, 3, 1, "", "system_nodes"], [0, 3, 1, "", "time_context_nodes"], [0, 3, 1, "", "time_dummy"], [0, 3, 1, "", "val_min"]], "tigramite.lpcmci": [[0, 0, 1, "", "LPCMCI"]], "tigramite.lpcmci.LPCMCI": [[0, 1, 1, "", "run_lpcmci"]], "tigramite.models": [[0, 0, 1, "", "LinearMediation"], [0, 0, 1, "", "Models"], [0, 0, 1, "", "Prediction"]], "tigramite.models.LinearMediation": [[0, 1, 1, "", "fit_model"], [0, 1, 1, "", "fit_model_bootstrap"], [0, 1, 1, "", "get_ace"], [0, 1, 1, "", "get_acs"], [0, 1, 1, "", "get_all_ace"], [0, 1, 1, "", "get_all_acs"], [0, 1, 1, "", "get_all_amce"], [0, 1, 1, "", "get_amce"], [0, 1, 1, "", "get_bootstrap_of"], [0, 1, 1, "", "get_ce"], [0, 1, 1, "", "get_ce_max"], [0, 1, 1, "", "get_coeff"], [0, 1, 1, "", "get_conditional_mce"], [0, 1, 1, "", "get_joint_ce"], [0, 1, 1, "", "get_joint_ce_matrix"], [0, 1, 1, "", "get_joint_mce"], [0, 1, 1, "", "get_mce"], [0, 1, 1, "", "get_mediation_graph_data"], [0, 1, 1, "", "get_tsg"], [0, 1, 1, "", "get_val_matrix"], [0, 1, 1, "", "net_to_tsg"], [0, 1, 1, "", "tsg_to_net"]], "tigramite.models.Models": [[0, 1, 1, "", "fit_full_model"], [0, 1, 1, "", "get_coefs"], [0, 1, 1, "", "get_general_fitted_model"], [0, 1, 1, "", "get_general_prediction"], [0, 1, 1, "", "get_residuals_cov_mean"], [0, 1, 1, "", "get_val_matrix"], [0, 1, 1, "", "predict_full_model"]], "tigramite.models.Prediction": [[0, 1, 1, "", "fit"], [0, 1, 1, "", "get_predictors"], [0, 1, 1, "", "get_test_array"], [0, 1, 1, "", "get_train_array"], [0, 1, 1, "", "predict"]], "tigramite.pcmci": [[0, 0, 1, "", "PCMCI"]], "tigramite.pcmci.PCMCI": [[0, 3, 1, "", "N"], [0, 3, 1, "", "T"], [0, 3, 1, "", "all_parents"], [0, 1, 1, "", "get_graph_from_pmatrix"], [0, 1, 1, "", "get_lagged_dependencies"], [0, 3, 1, "", "iterations"], [0, 1, 1, "", "print_results"], [0, 1, 1, "", "print_significant_links"], [0, 3, 1, "", "pval_max"], [0, 1, 1, "", "return_parents_dict"], [0, 1, 1, "", "return_significant_links"], [0, 1, 1, "", "run_bivci"], [0, 1, 1, "", "run_fullci"], [0, 1, 1, "", "run_mci"], [0, 1, 1, "", "run_pc_stable"], [0, 1, 1, "", "run_pcalg"], [0, 1, 1, "", "run_pcalg_non_timeseries_data"], [0, 1, 1, "", "run_pcmci"], [0, 1, 1, "", "run_pcmciplus"], [0, 3, 1, "", "val_min"]], "tigramite.plotting": [[0, 4, 1, "", "plot_densityplots"], [0, 4, 1, "", "plot_graph"], [0, 4, 1, "", "plot_lagfuncs"], [0, 4, 1, "", "plot_mediation_graph"], [0, 4, 1, "", "plot_mediation_time_series_graph"], [0, 4, 1, "", "plot_scatterplots"], [0, 4, 1, "", "plot_time_series_graph"], [0, 4, 1, "", "plot_timeseries"], [0, 4, 1, "", "plot_tsg"], [0, 0, 1, "", "setup_density_matrix"], [0, 0, 1, "", "setup_matrix"], [0, 0, 1, "", "setup_scatter_matrix"], [0, 4, 1, "", "write_csv"]], "tigramite.plotting.setup_density_matrix": [[0, 1, 1, "", "add_densityplot"], [0, 1, 1, "", "adjustfig"]], "tigramite.plotting.setup_matrix": [[0, 1, 1, "", "add_lagfuncs"], [0, 1, 1, "", "savefig"]], "tigramite.plotting.setup_scatter_matrix": [[0, 1, 1, "", "add_scatterplot"], [0, 1, 1, "", "adjustfig"]], "tigramite.rpcmci": [[0, 0, 1, "", "RPCMCI"]], "tigramite.rpcmci.RPCMCI": [[0, 1, 1, "", "run_rpcmci"]], "tigramite.toymodels": [[0, 2, 0, "-", "structural_causal_processes"]], "tigramite.toymodels.structural_causal_processes": [[0, 4, 1, "", "check_stationarity"], [0, 4, 1, "", "dag_to_links"], [0, 4, 1, "", "generate_structural_causal_process"], [0, 4, 1, "", "links_to_graph"], [0, 4, 1, "", "structural_causal_process"], [0, 4, 1, "", "var_process"]]}, "objtypes": {"0": "py:class", "1": "py:method", "2": "py:module", "3": "py:attribute", "4": "py:function", "5": "py:property"}, "objnames": {"0": ["py", "class", "Python class"], "1": ["py", "method", "Python method"], "2": ["py", "module", "Python module"], "3": ["py", "attribute", "Python attribute"], "4": ["py", "function", "Python function"], "5": ["py", "property", "Python property"]}, "titleterms": {"welcom": 0, "tigramit": 0, "s": 0, "document": 0, "indic": 0, "tabl": 0, "pcmci": 0, "lpcmci": 0, "rpcmci": 0, "independence_test": 0, "condit": 0, "independ": 0, "test": 0, "causal_effect": 0, "causal": 0, "effect": 0, "analysi": 0, "model": 0, "time": 0, "seri": 0, "mediat": 0, "predict": 0, "data_process": 0, "data": 0, "process": 0, "function": 0, "toymodel": 0, "toi": 0, "gener": 0, "plot": 0, "jpcmciplu": 0}, "envversion": {"sphinx.domains.c": 2, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 6, "sphinx.domains.index": 1, "sphinx.domains.javascript": 2, "sphinx.domains.math": 2, "sphinx.domains.python": 3, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.viewcode": 1, "sphinx": 56}})