
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.independence_tests.cmisymb &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/alabaster.css" />
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
   
  <link rel="stylesheet" href="../../../_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.independence_tests.cmisymb</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite causal discovery for time series.&quot;&quot;&quot;</span>

<span class="c1"># Author: Sagar Nagaraj Simha, Jakob Runge &lt;<EMAIL>&gt;</span>
<span class="c1">#</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">print_function</span>
<span class="kn">import</span> <span class="nn">warnings</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>
<span class="kn">from</span> <span class="nn">scipy.stats.contingency</span> <span class="kn">import</span> <span class="n">crosstab</span>
<span class="c1"># from joblib import Parallel, delayed</span>
<span class="c1"># import dask</span>
<span class="kn">from</span> <span class="nn">numba</span> <span class="kn">import</span> <span class="n">jit</span>

<span class="kn">from</span> <span class="nn">.independence_tests_base</span> <span class="kn">import</span> <span class="n">CondIndTest</span>

<div class="viewcode-block" id="CMIsymb"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.cmisymb.CMIsymb">[docs]</a><span class="k">class</span> <span class="nc">CMIsymb</span><span class="p">(</span><span class="n">CondIndTest</span><span class="p">):</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Conditional mutual information test for discrete/categorical data.</span>

<span class="sd">    Conditional mutual information is the most general dependency measure</span>
<span class="sd">    coming from an information-theoretic framework. It makes no assumptions</span>
<span class="sd">    about the parametric form of the dependencies by directly estimating the</span>
<span class="sd">    underlying joint density. The test here is based on directly estimating</span>
<span class="sd">    the joint distribution assuming symbolic input, combined with a</span>
<span class="sd">    local shuffle test to generate  the distribution under the null hypothesis of</span>
<span class="sd">    independence. This estimator is suitable only for discrete variables.</span>
<span class="sd">    For continuous variables use the CMIknn class and for mixed-variable</span>
<span class="sd">    datasets the CMIknnMixed class (including mixed-type variables).</span>

<span class="sd">    Allows for multi-dimensional X, Y.</span>

<span class="sd">    Notes</span>
<span class="sd">    -----</span>
<span class="sd">    CMI and its estimator are given by</span>

<span class="sd">    .. math:: I(X;Y|Z) &amp;= \sum p(z)  \sum \sum  p(x,y|z) \log</span>
<span class="sd">                \frac{ p(x,y |z)}{p(x|z)\cdot p(y |z)} \,dx dy dz</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    n_symbs : int, optional (default: None)</span>
<span class="sd">        Number of symbols in input data. Should be at least as large as the</span>
<span class="sd">        maximum array entry + 1. If None, n_symbs is inferred by scipy&#39;s crosstab.</span>

<span class="sd">    significance : str, optional (default: &#39;shuffle_test&#39;)</span>
<span class="sd">        Type of significance test to use. For CMIsymb only &#39;fixed_thres&#39; and</span>
<span class="sd">        &#39;shuffle_test&#39; are available.</span>

<span class="sd">    sig_blocklength : int, optional (default: 1)</span>
<span class="sd">        Block length for block-shuffle significance test.</span>

<span class="sd">    conf_blocklength : int, optional (default: 1)</span>
<span class="sd">        Block length for block-bootstrap.</span>

<span class="sd">    **kwargs :</span>
<span class="sd">        Arguments passed on to parent class CondIndTest.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">measure</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Concrete property to return the measure of the independence test</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_measure</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">n_symbs</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">significance</span><span class="o">=</span><span class="s1">&#39;shuffle_test&#39;</span><span class="p">,</span>
                 <span class="n">sig_blocklength</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                 <span class="n">conf_blocklength</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                 <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="c1"># Setup the member variables</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_measure</span> <span class="o">=</span> <span class="s1">&#39;cmi_symb&#39;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">two_sided</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">residual_based</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">n_symbs</span> <span class="o">=</span> <span class="n">n_symbs</span>
        <span class="c1"># Call the parent constructor</span>
        <span class="n">CondIndTest</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                             <span class="n">significance</span><span class="o">=</span><span class="n">significance</span><span class="p">,</span>
                             <span class="n">sig_blocklength</span><span class="o">=</span><span class="n">sig_blocklength</span><span class="p">,</span>
                             <span class="n">conf_blocklength</span><span class="o">=</span><span class="n">conf_blocklength</span><span class="p">,</span>
                             <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;n_symbs = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">n_symbs</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">conf_blocklength</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="bp">self</span><span class="o">.</span><span class="n">sig_blocklength</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;Automatic block-length estimations from decay of &quot;</span>
                          <span class="s2">&quot;autocorrelation may not be correct for discrete &quot;</span>
                          <span class="s2">&quot;data&quot;</span><span class="p">)</span>

<div class="viewcode-block" id="CMIsymb.get_dependence_measure"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.cmisymb.CMIsymb.get_dependence_measure">[docs]</a>    <span class="k">def</span> <span class="nf">get_dependence_measure</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns CMI estimate based on contingency table from scipy&#39;s crosstab</span>
<span class="sd">        to approximate probability mass.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        array : array-like</span>
<span class="sd">            data array with X, Y, Z in rows and observations in columns</span>

<span class="sd">        xyz : array of ints</span>
<span class="sd">            XYZ identifier array of shape (dim,).</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        val : float</span>
<span class="sd">            Conditional mutual information estimate.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">_</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">n_symbs</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">levels</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Assuming same list of levels for (z, y, x).</span>
            <span class="n">levels</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">tile</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">n_symbs</span><span class="p">),</span> <span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">xyz</span><span class="p">),</span> <span class="mi">1</span><span class="p">))</span>

        <span class="c1"># High-dimensional contingency table</span>
        <span class="n">_</span><span class="p">,</span> <span class="n">hist</span> <span class="o">=</span> <span class="n">crosstab</span><span class="p">(</span><span class="o">*</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">split</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">xyz</span><span class="p">),</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">))</span><span class="o">.</span><span class="n">reshape</span><span class="p">((</span><span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="n">T</span><span class="p">))),</span> <span class="n">levels</span><span class="o">=</span><span class="n">levels</span><span class="p">,</span>
                           <span class="n">sparse</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>

        <span class="k">def</span> <span class="nf">_plogp_vector</span><span class="p">(</span><span class="n">T</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Precalculation of p*log(p) needed for entropies.&quot;&quot;&quot;</span>
            <span class="n">gfunc</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">T</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span>
            <span class="n">data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">T</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
            <span class="n">gfunc</span><span class="p">[</span><span class="mi">1</span><span class="p">:]</span> <span class="o">=</span> <span class="n">data</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">log</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
            <span class="k">def</span> <span class="nf">plogp_func</span><span class="p">(</span><span class="n">time</span><span class="p">):</span>
                <span class="k">return</span> <span class="n">gfunc</span><span class="p">[</span><span class="n">time</span><span class="p">]</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">vectorize</span><span class="p">(</span><span class="n">plogp_func</span><span class="p">)</span>

        <span class="c1"># Dimensions are hist are (X, Y, Z^1, .... Z^dz)</span>
        <span class="c1"># plogp = _plogp_vector(T)</span>
        <span class="c1"># hxyz = (-(plogp(hist)).sum() + plogp(T)) / float(T)</span>
        <span class="c1"># hxz = (-(plogp(hist.sum(axis=1))).sum() + plogp(T)) / float(T)</span>
        <span class="c1"># hyz = (-(plogp(hist.sum(axis=0))).sum() + plogp(T)) / float(T)</span>
        <span class="c1"># hz = (-(plogp(hist.sum(axis=0).sum(axis=0))).sum() + plogp(T)) / float(T)</span>

        <span class="c1"># Multivariate X, Y version</span>
        <span class="n">plogp</span> <span class="o">=</span> <span class="n">_plogp_vector</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="n">hxyz</span> <span class="o">=</span> <span class="p">(</span><span class="o">-</span><span class="p">(</span><span class="n">plogp</span><span class="p">(</span><span class="n">hist</span><span class="p">))</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">+</span> <span class="n">plogp</span><span class="p">(</span><span class="n">T</span><span class="p">))</span> <span class="o">/</span> <span class="nb">float</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="n">hxz</span> <span class="o">=</span> <span class="p">(</span><span class="o">-</span><span class="p">(</span><span class="n">plogp</span><span class="p">(</span><span class="n">hist</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="nb">tuple</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">1</span><span class="p">)[</span><span class="mi">0</span><span class="p">]))))</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">+</span> <span class="n">plogp</span><span class="p">(</span><span class="n">T</span><span class="p">))</span> <span class="o">/</span> <span class="nb">float</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="n">hyz</span> <span class="o">=</span> <span class="p">(</span><span class="o">-</span><span class="p">(</span><span class="n">plogp</span><span class="p">(</span><span class="n">hist</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="nb">tuple</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">]))))</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">+</span> <span class="n">plogp</span><span class="p">(</span><span class="n">T</span><span class="p">))</span> <span class="o">/</span> <span class="nb">float</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="n">hz</span> <span class="o">=</span> <span class="p">(</span><span class="o">-</span><span class="p">(</span><span class="n">plogp</span><span class="p">(</span><span class="n">hist</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="nb">tuple</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">((</span><span class="n">xyz</span><span class="o">==</span><span class="mi">0</span><span class="p">)</span> <span class="o">|</span> <span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">1</span><span class="p">))[</span><span class="mi">0</span><span class="p">]))))</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">+</span> <span class="n">plogp</span><span class="p">(</span><span class="n">T</span><span class="p">))</span> <span class="o">/</span> <span class="nb">float</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>
        <span class="n">val</span> <span class="o">=</span> <span class="n">hxz</span> <span class="o">+</span> <span class="n">hyz</span> <span class="o">-</span> <span class="n">hz</span> <span class="o">-</span> <span class="n">hxyz</span>

        <span class="k">return</span> <span class="n">val</span></div>

<div class="viewcode-block" id="CMIsymb.get_shuffle_significance"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.cmisymb.CMIsymb.get_shuffle_significance">[docs]</a>    <span class="k">def</span> <span class="nf">get_shuffle_significance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span>
                                 <span class="n">return_null_dist</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns p-value for shuffle significance test.</span>

<span class="sd">        Performes a local permutation test: x_i values are only permuted with</span>
<span class="sd">        those x_j for which z_i = z_j. Samples are drawn without replacement</span>
<span class="sd">        as much as possible.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        array : array-like</span>
<span class="sd">            data array with X, Y, Z in rows and observations in columns.</span>

<span class="sd">        xyz : array of ints</span>
<span class="sd">            XYZ identifier array of shape (dim,).</span>

<span class="sd">        value : number</span>
<span class="sd">            Value of test statistic for original (unshuffled) estimate.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        pval : float</span>
<span class="sd">            p-value.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>
        <span class="n">x_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
        <span class="n">z_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">2</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">z_indices</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="c1"># Get neighbors around each sample point in z</span>
            <span class="n">z_array</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">z_indices</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span>
            <span class="c1"># Unique combinations of z in the data (z1, z2, z3 ...)</span>
            <span class="n">z_comb</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">unique</span><span class="p">(</span><span class="n">z_array</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>

            <span class="c1"># Create neighbor indices of length z_comb with default as -1.</span>
            <span class="n">neighbors</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">full</span><span class="p">((</span><span class="nb">len</span><span class="p">(</span><span class="n">z_comb</span><span class="p">),</span> <span class="n">T</span><span class="p">),</span> <span class="o">-</span><span class="mi">1</span><span class="p">)</span>
            <span class="c1"># Neighborhood indices for each unique combination in z_comb.</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">z_comb</span><span class="p">)):</span>
                <span class="n">neighbor_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">((</span><span class="n">z_array</span> <span class="o">==</span> <span class="n">z_comb</span><span class="p">[</span><span class="n">i</span><span class="p">])</span><span class="o">.</span><span class="n">all</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">))[</span><span class="mi">0</span><span class="p">]</span>
                <span class="n">neighbors</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="p">:</span><span class="nb">len</span><span class="p">(</span><span class="n">neighbor_indices</span><span class="p">)]</span> <span class="o">=</span> <span class="n">neighbor_indices</span>

            <span class="n">random_seeds</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span><span class="o">.</span><span class="n">integers</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">iinfo</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">int32</span><span class="p">)</span><span class="o">.</span><span class="n">max</span><span class="p">,</span> <span class="n">size</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">sig_samples</span><span class="p">)</span>
            <span class="c1"># null_dist = Parallel(n_jobs=-1)(</span>
            <span class="c1">#     delayed(self.parallelize_shuffles)(array, xyz, z_indices, x_indices, T, z_comb, neighbors, seed=seed) for seed in random_seeds)</span>
            <span class="c1"># dask_jobs = [dask.delayed(self.parallelize_shuffles)(array, xyz, z_indices, x_indices, T, z_comb, neighbors, seed=seed) for seed in random_seeds]</span>
            <span class="c1"># null_dist = dask.compute(dask_jobs)</span>
            <span class="c1"># null_dist = np.asarray(null_dist)</span>

            <span class="n">null_dist</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">sig_samples</span><span class="p">)</span>
            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">seed</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">random_seeds</span><span class="p">):</span>
                <span class="n">null_dist</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">parallelize_shuffles</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">z_indices</span><span class="p">,</span> <span class="n">x_indices</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">z_comb</span><span class="p">,</span> <span class="n">neighbors</span><span class="p">,</span> <span class="n">seed</span><span class="o">=</span><span class="n">seed</span><span class="p">)</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="n">null_dist</span> <span class="o">=</span> \
                <span class="bp">self</span><span class="o">.</span><span class="n">_get_shuffle_dist</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span>
                                       <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span><span class="p">,</span>
                                       <span class="n">sig_samples</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">sig_samples</span><span class="p">,</span>
                                       <span class="n">sig_blocklength</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">sig_blocklength</span><span class="p">,</span>
                                       <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>

        <span class="n">pval</span> <span class="o">=</span> <span class="p">(</span><span class="n">null_dist</span> <span class="o">&gt;=</span> <span class="n">value</span><span class="p">)</span><span class="o">.</span><span class="n">mean</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">return_null_dist</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">pval</span><span class="p">,</span> <span class="n">null_dist</span>
        <span class="k">return</span> <span class="n">pval</span></div>

    <span class="nd">@jit</span><span class="p">(</span><span class="n">forceobj</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">parallelize_shuffles</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">z_indices</span><span class="p">,</span> <span class="n">x_indices</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">z_comb</span><span class="p">,</span> <span class="n">neighbors</span><span class="p">,</span> <span class="n">seed</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
        <span class="c1"># Generate random order in which to go through samples.</span>
        <span class="c1"># order = self.random_state.permutation(T).astype(&#39;int32&#39;)</span>
        <span class="n">rng</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="p">)</span>
        <span class="n">order</span> <span class="o">=</span> <span class="n">rng</span><span class="o">.</span><span class="n">permutation</span><span class="p">(</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>

        <span class="n">restricted_permutation</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>
        <span class="c1"># A global list of used indices across time samples and combinations.</span>
        <span class="c1"># Since there are no repetitive (z) indices across combinations, a global list can be used.</span>
        <span class="n">used</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([],</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">sample_index</span> <span class="ow">in</span> <span class="n">order</span><span class="p">:</span>
            <span class="c1"># Get the index of the z combination for sample_index in z_comb</span>
            <span class="n">z_choice_index</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">((</span><span class="n">z_comb</span> <span class="o">==</span> <span class="n">array</span><span class="p">[</span><span class="n">z_indices</span><span class="p">,</span> <span class="n">sample_index</span><span class="p">])</span><span class="o">.</span><span class="n">all</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">))[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">neighbors_choices</span> <span class="o">=</span> <span class="n">neighbors</span><span class="p">[</span><span class="n">z_choice_index</span><span class="p">][</span><span class="n">neighbors</span><span class="p">[</span><span class="n">z_choice_index</span><span class="p">]</span> <span class="o">&gt;</span> <span class="o">-</span><span class="mi">1</span><span class="p">]</span>
            <span class="c1"># Shuffle neighbors in-place to randomize the choice of indices</span>
            <span class="c1"># self.random_state.shuffle(neighbors_choices)</span>
            <span class="n">rng</span><span class="o">.</span><span class="n">shuffle</span><span class="p">(</span><span class="n">neighbors_choices</span><span class="p">)</span>

            <span class="c1"># Permuting indices</span>
            <span class="n">m</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="n">use</span> <span class="o">=</span> <span class="n">neighbors_choices</span><span class="p">[</span><span class="n">m</span><span class="p">]</span>
            <span class="k">while</span> <span class="p">((</span><span class="n">use</span> <span class="ow">in</span> <span class="n">used</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">m</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">neighbors_choices</span><span class="p">))):</span>
                <span class="n">m</span> <span class="o">+=</span> <span class="mi">1</span>
                <span class="n">use</span> <span class="o">=</span> <span class="n">neighbors_choices</span><span class="p">[</span><span class="n">m</span><span class="p">]</span>

            <span class="n">restricted_permutation</span><span class="p">[</span><span class="n">sample_index</span><span class="p">]</span> <span class="o">=</span> <span class="n">use</span>
            <span class="n">used</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">used</span><span class="p">,</span> <span class="n">use</span><span class="p">)</span>

        <span class="n">array_shuffled</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">copy</span><span class="p">(</span><span class="n">array</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">x_indices</span><span class="p">:</span>
            <span class="n">array_shuffled</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">restricted_permutation</span><span class="p">]</span>

        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span><span class="p">(</span><span class="n">array_shuffled</span><span class="p">,</span>
                                                     <span class="n">xyz</span><span class="p">)</span></div>


<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    
    <span class="kn">import</span> <span class="nn">tigramite</span>
    <span class="kn">from</span> <span class="nn">tigramite.data_processing</span> <span class="kn">import</span> <span class="n">DataFrame</span>
    <span class="kn">import</span> <span class="nn">tigramite.data_processing</span> <span class="k">as</span> <span class="nn">pp</span>
    <span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>
    <span class="c1"># from dask.distributed import Client</span>

    <span class="c1"># client = dask.distributed.Client(processes=True)</span>
    <span class="n">seed</span> <span class="o">=</span> <span class="mi">42</span>
    <span class="n">random_state</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="o">=</span><span class="n">seed</span><span class="p">)</span>
    <span class="n">cmi</span> <span class="o">=</span> <span class="n">CMIsymb</span><span class="p">(</span><span class="n">sig_samples</span><span class="o">=</span><span class="mi">200</span><span class="p">,</span> <span class="n">seed</span><span class="o">=</span><span class="n">seed</span><span class="p">)</span>

    <span class="n">T</span> <span class="o">=</span> <span class="mi">1000</span>
    <span class="n">dimz</span> <span class="o">=</span> <span class="mi">5</span>
    <span class="n">z</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">binomial</span><span class="p">(</span><span class="n">n</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">p</span><span class="o">=</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">size</span><span class="o">=</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="n">dimz</span><span class="p">))</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="n">dimz</span><span class="p">)</span>
    <span class="n">x</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">(</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="n">y</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">(</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">T</span><span class="p">):</span>
        <span class="n">val</span> <span class="o">=</span> <span class="n">z</span><span class="p">[</span><span class="n">t</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">squeeze</span><span class="p">()</span>
        <span class="n">prob</span> <span class="o">=</span> <span class="mf">0.2</span><span class="o">+</span><span class="n">val</span><span class="o">*</span><span class="mf">0.6</span>
        <span class="n">x</span><span class="p">[</span><span class="n">t</span><span class="p">]</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">choice</span><span class="p">([</span><span class="mi">0</span><span class="p">,</span><span class="mi">1</span><span class="p">],</span> <span class="n">p</span><span class="o">=</span><span class="p">[</span><span class="n">prob</span><span class="p">,</span> <span class="mf">1.</span><span class="o">-</span><span class="n">prob</span><span class="p">])</span>
        <span class="n">y</span><span class="p">[</span><span class="n">t</span><span class="p">]</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">choice</span><span class="p">([</span><span class="mi">0</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span><span class="p">],</span> <span class="n">p</span><span class="o">=</span><span class="p">[</span><span class="n">prob</span><span class="p">,</span> <span class="p">(</span><span class="mf">1.</span><span class="o">-</span><span class="n">prob</span><span class="p">)</span><span class="o">/</span><span class="mf">2.</span><span class="p">,</span> <span class="p">(</span><span class="mf">1.</span><span class="o">-</span><span class="n">prob</span><span class="p">)</span><span class="o">/</span><span class="mf">2.</span><span class="p">])</span>

    <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;start&#39;</span><span class="p">)</span>
    <span class="c1"># print(client.dashboard_link)</span>
    <span class="c1"># print(cmi.run_test_raw(x, y, z=None))</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">cmi</span><span class="o">.</span><span class="n">run_test_raw</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">z</span><span class="o">=</span><span class="n">z</span><span class="p">))</span>

    <span class="c1"># client.close()</span>


</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../../index.html">Documentation overview</a><ul>
  <li><a href="../../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;2023, Jakob Runge.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 5.0.2</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
    </div>

    

    
  </body>
</html>