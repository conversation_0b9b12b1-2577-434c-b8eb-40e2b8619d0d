<!DOCTYPE html>

<html lang="en" data-content_root="../../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.models &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=db26dd79" />
    <link rel="stylesheet" type="text/css" href="../../_static/alabaster.css?v=19da42e6" />
    <script src="../../_static/documentation_options.js?v=625b3a9a"></script>
    <script src="../../_static/doctools.js?v=aa79a7b1"></script>
    <script src="../../_static/sphinx_highlight.js?v=4825356b"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
   
  <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.models</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite causal inference for time series.&quot;&quot;&quot;</span>

<span class="c1"># Author: Jakob Runge &lt;<EMAIL>&gt;</span>
<span class="c1">#</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">__future__</span><span class="w"> </span><span class="kn">import</span> <span class="n">print_function</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">copy</span><span class="w"> </span><span class="kn">import</span> <span class="n">deepcopy</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">json</span><span class="o">,</span><span class="w"> </span><span class="nn">warnings</span><span class="o">,</span><span class="w"> </span><span class="nn">os</span><span class="o">,</span><span class="w"> </span><span class="nn">pathlib</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">numpy</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">np</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">sklearn</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">sklearn.linear_model</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">networkx</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.data_processing</span><span class="w"> </span><span class="kn">import</span> <span class="n">DataFrame</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.pcmci</span><span class="w"> </span><span class="kn">import</span> <span class="n">PCMCI</span>

<div class="viewcode-block" id="Models">
<a class="viewcode-back" href="../../index.html#tigramite.models.Models">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">Models</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Base class for time series models.</span>

<span class="sd">    Allows to fit any model from sklearn to the parents of a target variable.</span>
<span class="sd">    Also takes care of missing values, masking and preprocessing. If the</span>
<span class="sd">    target variable is multivariate, a model that supports multi-output</span>
<span class="sd">    regression must be used. Note that</span>
<span class="sd">    sklearn.multioutput.MultiOutputRegressor allows to extend single-output</span>
<span class="sd">    models.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    dataframe : data object</span>
<span class="sd">        Tigramite dataframe object. It must have the attributes dataframe.values</span>
<span class="sd">        yielding a numpy array of shape (observations T, variables N) and</span>
<span class="sd">        optionally a mask of the same shape and a missing values flag.</span>
<span class="sd">    model : sklearn model object</span>
<span class="sd">        For example, sklearn.linear_model.LinearRegression() for a linear</span>
<span class="sd">        regression model.</span>
<span class="sd">    conditional_model : sklearn model object, optional (default: None)</span>
<span class="sd">        Used to fit conditional causal effects in nested regression. </span>
<span class="sd">        If None, model is used.</span>
<span class="sd">    data_transform : sklearn preprocessing object, optional (default: None)</span>
<span class="sd">        Used to transform data prior to fitting. For example,</span>
<span class="sd">        sklearn.preprocessing.StandardScaler for simple standardization. The</span>
<span class="sd">        fitted parameters are stored. Note that the inverse_transform is then</span>
<span class="sd">        applied to the predicted data.</span>
<span class="sd">    mask_type : {None, &#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">        Masking mode: Indicators for which variables in the dependence</span>
<span class="sd">        measure I(X; Y | Z) the samples should be masked. If None, the mask</span>
<span class="sd">        is not used. Explained in tutorial on masking and missing values.</span>
<span class="sd">    verbosity : int, optional (default: 0)</span>
<span class="sd">        Level of verbosity.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">dataframe</span><span class="p">,</span>
                 <span class="n">model</span><span class="p">,</span>
                 <span class="n">conditional_model</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">data_transform</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">mask_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
        <span class="c1"># Set the mask type and dataframe object</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="o">=</span> <span class="n">mask_type</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span> <span class="o">=</span> <span class="n">dataframe</span>
        <span class="c1"># Get the number of nodes and length for this dataset</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">N</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">N</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">T</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">T</span>
        <span class="c1"># Set the model to be used</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">model</span> <span class="o">=</span> <span class="n">model</span>
        <span class="k">if</span> <span class="n">conditional_model</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">conditional_model</span> <span class="o">=</span> <span class="n">model</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">conditional_model</span> <span class="o">=</span> <span class="n">conditional_model</span>
        <span class="c1"># Set the data_transform object and verbosity</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span> <span class="o">=</span> <span class="n">data_transform</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">=</span> <span class="n">verbosity</span>
        <span class="c1"># Initialize the object that will be set later</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="c1"># @profile    </span>
<div class="viewcode-block" id="Models.get_general_fitted_model">
<a class="viewcode-back" href="../../index.html#tigramite.models.Models.get_general_fitted_model">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_general_fitted_model</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
                <span class="n">Y</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">conditions</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">tau_max</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;max_lag_or_tau_max&#39;</span><span class="p">,</span>
                <span class="n">empty_predictors_function</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">,</span>
                <span class="n">return_data</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Fit time series model.</span>

<span class="sd">        For each variable in selected_variables, the sklearn model is fitted</span>
<span class="sd">        with :math:`y` given by the target variable(s), and :math:`X` given by its</span>
<span class="sd">        parents. The fitted model class is returned for later use.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        X, Y, Z : lists of tuples</span>
<span class="sd">            List of variables for estimating model Y = f(X,Z)</span>
<span class="sd">        conditions : list of tuples.</span>
<span class="sd">            Conditions for estimating conditional causal effects.</span>
<span class="sd">        tau_max : int, optional (default: None)</span>
<span class="sd">            Maximum time lag. If None, the maximum lag in all_parents is used.</span>
<span class="sd">        cut_off : {&#39;max_lag_or_tau_max&#39;, &#39;2xtau_max&#39;, &#39;max_lag&#39;}</span>
<span class="sd">            How many samples to cutoff at the beginning. The default is</span>
<span class="sd">            &#39;max_lag_or_tau_max&#39;, which uses the maximum of tau_max and the</span>
<span class="sd">            conditions. This is useful to compare multiple models on the same</span>
<span class="sd">            sample. Other options are &#39;2xtau_max&#39;, which guarantees that MCI</span>
<span class="sd">            tests are all conducted on the same samples. Last, &#39;max_lag&#39; uses</span>
<span class="sd">            as much samples as possible.</span>
<span class="sd">        empty_predictors_function : function</span>
<span class="sd">            Function to apply to y if no predictors are given.</span>
<span class="sd">        return_data : bool, optional (default: False)</span>
<span class="sd">            Whether to save the data array.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        fit_results : dictionary of sklearn model objects</span>
<span class="sd">            Returns the sklearn model after fitting. Also returns the data</span>
<span class="sd">            transformation parameters.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">get_vectorized_length</span><span class="p">(</span><span class="n">W</span><span class="p">):</span>
            <span class="k">return</span> <span class="nb">sum</span><span class="p">([</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">vector_vars</span><span class="p">[</span><span class="n">w</span><span class="p">[</span><span class="mi">0</span><span class="p">]])</span> <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">W</span><span class="p">])</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">X</span> <span class="o">=</span> <span class="n">X</span> 
        <span class="bp">self</span><span class="o">.</span><span class="n">Y</span> <span class="o">=</span> <span class="n">Y</span>

        <span class="k">if</span> <span class="n">conditions</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">conditions</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">conditions</span> <span class="o">=</span> <span class="n">conditions</span>

        <span class="k">if</span> <span class="n">Z</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="p">[</span><span class="n">z</span> <span class="k">for</span> <span class="n">z</span> <span class="ow">in</span> <span class="n">Z</span> <span class="k">if</span> <span class="n">z</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">conditions</span><span class="p">]</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">Z</span> <span class="o">=</span> <span class="n">Z</span>

        <span class="c1"># lenX = len(self.X)</span>
        <span class="c1"># lenS = len(self.conditions)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">lenX</span> <span class="o">=</span> <span class="n">get_vectorized_length</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">lenS</span> <span class="o">=</span> <span class="n">get_vectorized_length</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">conditions</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">cut_off</span> <span class="o">=</span> <span class="n">cut_off</span>

        <span class="c1"># Find the maximal conditions lag</span>
        <span class="n">max_lag</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">:</span>
            <span class="n">this_lag</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">Z</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">conditions</span><span class="p">)[:,</span> <span class="mi">1</span><span class="p">])</span><span class="o">.</span><span class="n">max</span><span class="p">()</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">max_lag</span><span class="p">,</span> <span class="n">this_lag</span><span class="p">)</span>
        <span class="c1"># Set the default tau max and check if it should be overwritten</span>
        <span class="k">if</span> <span class="n">tau_max</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="n">max_lag</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="n">tau_max</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;</span> <span class="n">max_lag</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;tau_max = </span><span class="si">%d</span><span class="s2">, but must be at least &quot;</span>
                                 <span class="s2">&quot; max_lag = </span><span class="si">%d</span><span class="s2">&quot;</span>
                                 <span class="s2">&quot;&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">))</span>

        <span class="c1"># Construct array of shape (var, time)</span>
        <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> \
            <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">construct_array</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">,</span>  
                                           <span class="n">Z</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">conditions</span><span class="p">,</span>
                                           <span class="n">extraZ</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">Z</span><span class="p">,</span>
                                           <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                                           <span class="n">mask_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">,</span>
                                           <span class="n">cut_off</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">cut_off</span><span class="p">,</span>
                                           <span class="n">remove_overlaps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                           <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>

        <span class="c1"># Transform the data if needed</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fitted_data_transform</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># Fit only X, Y, and S for later use in transforming input</span>
            <span class="n">X_transform</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span><span class="p">)</span>
            <span class="n">x_indices</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
            <span class="n">X_transform</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">array</span><span class="p">[</span><span class="n">x_indices</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">fitted_data_transform</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;X&#39;</span><span class="p">:</span> <span class="n">X_transform</span><span class="p">}</span>
            <span class="n">Y_transform</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span><span class="p">)</span>
            <span class="n">y_indices</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">1</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
            <span class="n">Y_transform</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">array</span><span class="p">[</span><span class="n">y_indices</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">fitted_data_transform</span><span class="p">[</span><span class="s1">&#39;Y&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">Y_transform</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">conditions</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">S_transform</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span><span class="p">)</span>
                <span class="n">s_indices</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">2</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
                <span class="n">S_transform</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">array</span><span class="p">[</span><span class="n">s_indices</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span><span class="p">)</span> 
                <span class="bp">self</span><span class="o">.</span><span class="n">fitted_data_transform</span><span class="p">[</span><span class="s1">&#39;S&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">S_transform</span>

            <span class="c1"># Now transform whole array</span>
            <span class="c1"># TODO: Rather concatenate transformed arrays</span>
            <span class="n">all_transform</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span><span class="p">)</span>
            <span class="n">array</span> <span class="o">=</span> <span class="n">all_transform</span><span class="o">.</span><span class="n">fit_transform</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">array</span><span class="o">.</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">T</span>

        <span class="c1"># Fit the model </span>
        <span class="c1"># Copy and fit the model</span>
        <span class="n">a_model</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="p">)</span>

        <span class="n">predictor_indices</span> <span class="o">=</span>  <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span> \
                           <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">3</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span> \
                           <span class="o">+</span> <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">2</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
        <span class="n">predictor_array</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">predictor_indices</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span>
        <span class="n">target_array</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span><span class="o">==</span><span class="mi">1</span><span class="p">)[</span><span class="mi">0</span><span class="p">],</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span>

        <span class="k">if</span> <span class="n">predictor_array</span><span class="o">.</span><span class="n">size</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="c1"># Just fit default (eg, mean)</span>
            <span class="k">class</span><span class="w"> </span><span class="nc">EmptyPredictorModel</span><span class="p">:</span>
                <span class="k">def</span><span class="w"> </span><span class="nf">fit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
                    <span class="k">if</span> <span class="n">y</span><span class="o">.</span><span class="n">ndim</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">result</span> <span class="o">=</span> <span class="n">empty_predictors_function</span><span class="p">(</span><span class="n">y</span><span class="p">)</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">result</span> <span class="o">=</span> <span class="n">empty_predictors_function</span><span class="p">(</span><span class="n">y</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
                <span class="k">def</span><span class="w"> </span><span class="nf">predict</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">):</span>
                    <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">result</span>
            <span class="n">a_model</span> <span class="o">=</span> <span class="n">EmptyPredictorModel</span><span class="p">()</span>
        
        <span class="n">a_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">predictor_array</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="n">target_array</span><span class="p">)</span>
        
        <span class="c1"># Cache the results</span>
        <span class="n">fit_results</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;observation_array&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">array</span>
        <span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;xyz&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">xyz</span>
        <span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;model&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">a_model</span>
        <span class="c1"># Cache the data transform</span>
        <span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;fitted_data_transform&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fitted_data_transform</span>

        <span class="c1"># Cache and return the fit results</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span> <span class="o">=</span> <span class="n">fit_results</span>
        <span class="k">return</span> <span class="n">fit_results</span></div>


    <span class="c1"># @profile</span>
<div class="viewcode-block" id="Models.get_general_prediction">
<a class="viewcode-back" href="../../index.html#tigramite.models.Models.get_general_prediction">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_general_prediction</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                <span class="n">intervention_data</span><span class="p">,</span>
                <span class="n">conditions_data</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">pred_params</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">transform_interventions_and_prediction</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                <span class="n">return_further_pred_results</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                <span class="n">aggregation_func</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">,</span>
                <span class="n">intervention_type</span><span class="o">=</span><span class="s1">&#39;hard&#39;</span><span class="p">,</span>
                <span class="p">):</span>
<span class="w">        </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Predict effect of intervention with fitted model.</span>

<span class="sd">        Uses the model.predict() function of the sklearn model.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        intervention_data : numpy array</span>
<span class="sd">            Numpy array of shape (n_interventions, len(X)) that contains the do(X) values.</span>
<span class="sd">        conditions_data : data object, optional</span>
<span class="sd">            Numpy array of shape (n_interventions, len(S)) that contains the S=s values.</span>
<span class="sd">        pred_params : dict, optional</span>
<span class="sd">            Optional parameters passed on to sklearn prediction function (model and</span>
<span class="sd">            conditional_model).</span>
<span class="sd">        transform_interventions_and_prediction : bool (default: False)</span>
<span class="sd">            Whether to perform the inverse data_transform on prediction results.</span>
<span class="sd">        return_further_pred_results : bool, optional (default: False)</span>
<span class="sd">            In case the predictor class returns more than just the expected value,</span>
<span class="sd">            the entire results can be returned.</span>
<span class="sd">        aggregation_func : callable</span>
<span class="sd">            Callable applied to output of &#39;predict&#39;. Default is &#39;np.mean&#39;.</span>
<span class="sd">        intervention_type : {&#39;hard&#39;, &#39;soft&#39;}</span>
<span class="sd">            Specify whether intervention is &#39;hard&#39; (set value) or &#39;soft&#39; </span>
<span class="sd">            (add value to observed data).</span>
<span class="sd">  </span>
<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Results from prediction.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">n_interventions</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">intervention_data</span><span class="o">.</span><span class="n">shape</span>

        <span class="k">if</span> <span class="n">intervention_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">!=</span> <span class="bp">self</span><span class="o">.</span><span class="n">lenX</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;intervention_data.shape[1] must be len(X).&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">conditions_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">conditions_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">!=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">conditions</span><span class="p">):</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;conditions_data.shape[1] must be len(S).&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">conditions_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">!=</span> <span class="n">intervention_data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;conditions_data.shape[0] must match intervention_data.shape[0].&quot;</span><span class="p">)</span>

        <span class="c1"># Print message</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">## Predicting target </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Y</span><span class="p">))</span>
            <span class="k">if</span> <span class="n">pred_params</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">key</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">pred_params</span><span class="p">):</span>
                    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">pred_params</span><span class="p">[</span><span class="n">key</span><span class="p">]))</span>

        <span class="c1"># Default value for pred_params</span>
        <span class="k">if</span> <span class="n">pred_params</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">pred_params</span> <span class="o">=</span> <span class="p">{}</span>

        <span class="c1"># Check the model is fitted.</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Model not yet fitted.&quot;</span><span class="p">)</span>

        <span class="c1"># Transform the data if needed</span>
        <span class="n">fitted_data_transform</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;fitted_data_transform&#39;</span><span class="p">]</span>
        <span class="k">if</span> <span class="n">transform_interventions_and_prediction</span> <span class="ow">and</span> <span class="n">fitted_data_transform</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">intervention_data</span> <span class="o">=</span> <span class="n">fitted_data_transform</span><span class="p">[</span><span class="s1">&#39;X&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">transform</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">intervention_data</span><span class="p">)</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">conditions</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">conditions_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">conditions_data</span> <span class="o">=</span> <span class="n">fitted_data_transform</span><span class="p">[</span><span class="s1">&#39;S&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">transform</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">conditions_data</span><span class="p">)</span>

        <span class="c1"># Extract observational Z from stored array</span>
        <span class="n">z_indices</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;xyz&#39;</span><span class="p">]</span><span class="o">==</span><span class="mi">3</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
        <span class="n">z_array</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;observation_array&#39;</span><span class="p">][</span><span class="n">z_indices</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span>  
        <span class="n">Tobs</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;observation_array&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">T</span><span class="p">)</span> 

        <span class="k">if</span> <span class="n">intervention_type</span> <span class="o">==</span> <span class="s1">&#39;soft&#39;</span><span class="p">:</span>
            <span class="n">x_indices</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;xyz&#39;</span><span class="p">]</span><span class="o">==</span><span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
            <span class="n">x_array</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;observation_array&#39;</span><span class="p">][</span><span class="n">x_indices</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span>   

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">conditions</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">conditions_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">s_indices</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;xyz&#39;</span><span class="p">]</span><span class="o">==</span><span class="mi">2</span><span class="p">)[</span><span class="mi">0</span><span class="p">])</span>
            <span class="n">s_array</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;observation_array&#39;</span><span class="p">][</span><span class="n">s_indices</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span>  

        <span class="n">pred_dict</span> <span class="o">=</span> <span class="p">{}</span>

        <span class="c1"># Now iterate through interventions (and potentially S)</span>
        <span class="k">for</span> <span class="n">index</span><span class="p">,</span> <span class="n">dox_vals</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">intervention_data</span><span class="p">):</span>
            <span class="c1"># Construct XZS-array</span>
            <span class="n">intervention_array</span> <span class="o">=</span> <span class="n">dox_vals</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">lenX</span><span class="p">)</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">((</span><span class="n">Tobs</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">lenX</span><span class="p">))</span>
            <span class="k">if</span> <span class="n">intervention_type</span> <span class="o">==</span> <span class="s1">&#39;soft&#39;</span><span class="p">:</span>
                <span class="n">intervention_array</span> <span class="o">+=</span> <span class="n">x_array</span>

            <span class="n">predictor_array</span> <span class="o">=</span> <span class="n">intervention_array</span>

            <span class="k">if</span>  <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Z</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">predictor_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">predictor_array</span><span class="p">,</span> <span class="n">z_array</span><span class="p">))</span>

            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">conditions</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">conditions_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">conditions_array</span> <span class="o">=</span> <span class="n">conditions_data</span><span class="p">[</span><span class="n">index</span><span class="p">]</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">lenS</span><span class="p">)</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">((</span><span class="n">Tobs</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">lenS</span><span class="p">))</span>  
                <span class="n">predictor_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">predictor_array</span><span class="p">,</span> <span class="n">conditions_array</span><span class="p">))</span>

            <span class="n">predicted_vals</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;model&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">predict</span><span class="p">(</span>
                                                    <span class="n">X</span><span class="o">=</span><span class="n">predictor_array</span><span class="p">,</span> <span class="o">**</span><span class="n">pred_params</span><span class="p">)</span>

            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">conditions</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">conditions_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>

                <span class="n">a_conditional_model</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">conditional_model</span><span class="p">)</span>
                
                <span class="k">if</span> <span class="nb">type</span><span class="p">(</span><span class="n">predicted_vals</span><span class="p">)</span> <span class="ow">is</span> <span class="nb">tuple</span><span class="p">:</span>
                    <span class="n">predicted_vals_here</span> <span class="o">=</span> <span class="n">predicted_vals</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">predicted_vals_here</span> <span class="o">=</span> <span class="n">predicted_vals</span>
                
                <span class="n">a_conditional_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">s_array</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="n">predicted_vals_here</span><span class="p">)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="s1">&#39;conditional_model&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">a_conditional_model</span>

                <span class="n">predicted_vals</span> <span class="o">=</span> <span class="n">a_conditional_model</span><span class="o">.</span><span class="n">predict</span><span class="p">(</span>
                    <span class="n">X</span><span class="o">=</span><span class="n">conditions_data</span><span class="p">[</span><span class="n">index</span><span class="p">]</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">lenS</span><span class="p">),</span> <span class="o">**</span><span class="n">pred_params</span><span class="p">)</span>   <span class="c1"># was conditions_data before</span>

            <span class="k">if</span> <span class="n">transform_interventions_and_prediction</span> <span class="ow">and</span> <span class="n">fitted_data_transform</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">predicted_vals</span> <span class="o">=</span> <span class="n">fitted_data_transform</span><span class="p">[</span><span class="s1">&#39;Y&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">inverse_transform</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">predicted_vals</span><span class="p">)</span><span class="o">.</span><span class="n">squeeze</span><span class="p">()</span>

            <span class="n">pred_dict</span><span class="p">[</span><span class="n">index</span><span class="p">]</span> <span class="o">=</span> <span class="n">predicted_vals</span>

            <span class="c1"># Apply aggregation function</span>
            <span class="k">if</span> <span class="nb">type</span><span class="p">(</span><span class="n">predicted_vals</span><span class="p">)</span> <span class="ow">is</span> <span class="nb">tuple</span><span class="p">:</span>
                <span class="n">aggregated_pred</span> <span class="o">=</span> <span class="n">aggregation_func</span><span class="p">(</span><span class="n">predicted_vals</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">aggregated_pred</span> <span class="o">=</span> <span class="n">aggregation_func</span><span class="p">(</span><span class="n">predicted_vals</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>

            <span class="n">aggregated_pred</span> <span class="o">=</span> <span class="n">aggregated_pred</span><span class="o">.</span><span class="n">squeeze</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">index</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">predicted_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">n_interventions</span><span class="p">,</span> <span class="p">)</span> <span class="o">+</span> <span class="n">aggregated_pred</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span> 
                                        <span class="n">dtype</span><span class="o">=</span><span class="n">aggregated_pred</span><span class="o">.</span><span class="n">dtype</span><span class="p">)</span>

            <span class="n">predicted_array</span><span class="p">[</span><span class="n">index</span><span class="p">]</span> <span class="o">=</span> <span class="n">aggregated_pred</span>

            <span class="c1"># if fitted_data_transform is not None:</span>
            <span class="c1">#     rescaled = fitted_data_transform[&#39;Y&#39;].inverse_transform(X=predicted_array[index, iy].reshape(-1, 1))</span>
            <span class="c1">#     predicted_array[index, iy] = rescaled.squeeze()</span>

        <span class="k">if</span> <span class="n">return_further_pred_results</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">predicted_array</span><span class="p">,</span> <span class="n">pred_dict</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">predicted_array</span></div>



<div class="viewcode-block" id="Models.fit_full_model">
<a class="viewcode-back" href="../../index.html#tigramite.models.Models.fit_full_model">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">fit_full_model</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">all_parents</span><span class="p">,</span>
                <span class="n">selected_variables</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">tau_max</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;max_lag_or_tau_max&#39;</span><span class="p">,</span>
                <span class="n">empty_predictors_function</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">,</span>
                <span class="n">return_data</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Fit time series model.</span>

<span class="sd">        For each variable in selected_variables, the sklearn model is fitted</span>
<span class="sd">        with :math:`y` given by the target variable, and :math:`X` given by its</span>
<span class="sd">        parents. The fitted model class is returned for later use.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        all_parents : dictionary</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, 0), ...], 1:[], ...} containing</span>
<span class="sd">            the parents estimated with PCMCI.</span>
<span class="sd">        selected_variables : list of integers, optional (default: range(N))</span>
<span class="sd">            Specify to estimate parents only for selected variables. If None is</span>
<span class="sd">            passed, parents are estimated for all variables.</span>
<span class="sd">        tau_max : int, optional (default: None)</span>
<span class="sd">            Maximum time lag. If None, the maximum lag in all_parents is used.</span>
<span class="sd">        cut_off : {&#39;max_lag_or_tau_max&#39;, &#39;2xtau_max&#39;, &#39;max_lag&#39;}</span>
<span class="sd">            How many samples to cutoff at the beginning. The default is</span>
<span class="sd">            &#39;max_lag_or_tau_max&#39;, which uses the maximum of tau_max and the</span>
<span class="sd">            conditions. This is useful to compare multiple models on the same</span>
<span class="sd">            sample. Other options are &#39;2xtau_max&#39;, which guarantees that MCI</span>
<span class="sd">            tests are all conducted on the same samples. Last, &#39;max_lag&#39; uses</span>
<span class="sd">            as much samples as possible.</span>
<span class="sd">        empty_predictors_function : function</span>
<span class="sd">            Function to apply to y if no predictors are given.</span>
<span class="sd">        return_data : bool, optional (default: False)</span>
<span class="sd">            Whether to save the data array.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        fit_results : dictionary of sklearn model objects for each variable</span>
<span class="sd">            Returns the sklearn model after fitting. Also returns the data</span>
<span class="sd">            transformation parameters.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Initialize the fit by setting the instance&#39;s all_parents attribute</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span> <span class="o">=</span> <span class="n">all_parents</span>
        <span class="c1"># Set the default selected variables to all variables and check if this</span>
        <span class="c1"># should be overwritten</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span> <span class="o">=</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">selected_variables</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span> <span class="o">=</span> <span class="n">selected_variables</span>
        <span class="c1"># Find the maximal parents lag</span>
        <span class="n">max_parents_lag</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">all_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                <span class="n">this_parent_lag</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">all_parents</span><span class="p">[</span><span class="n">j</span><span class="p">])[:,</span> <span class="mi">1</span><span class="p">])</span><span class="o">.</span><span class="n">max</span><span class="p">()</span>
                <span class="n">max_parents_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">max_parents_lag</span><span class="p">,</span> <span class="n">this_parent_lag</span><span class="p">)</span>
        <span class="c1"># Set the default tau_max and check if it should be overwritten</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="n">max_parents_lag</span>
        <span class="k">if</span> <span class="n">tau_max</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="n">tau_max</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;</span> <span class="n">max_parents_lag</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;tau_max = </span><span class="si">%d</span><span class="s2">, but must be at least &quot;</span>
                                 <span class="s2">&quot; max_parents_lag = </span><span class="si">%d</span><span class="s2">&quot;</span>
                                 <span class="s2">&quot;&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span> <span class="n">max_parents_lag</span><span class="p">))</span>
        <span class="c1"># Initialize the fit results</span>
        <span class="n">fit_results</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span><span class="p">:</span>
            <span class="n">Y</span> <span class="o">=</span> <span class="p">[(</span><span class="n">j</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span>
            <span class="n">X</span> <span class="o">=</span> <span class="p">[(</span><span class="n">j</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span>  <span class="c1"># dummy</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]</span>
            <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> \
                <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">construct_array</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span>
                                               <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                                               <span class="n">mask_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">,</span>
                                               <span class="n">cut_off</span><span class="o">=</span><span class="n">cut_off</span><span class="p">,</span>
                                               <span class="n">remove_overlaps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                               <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>
            <span class="c1"># Get the dimensions out of the constructed array</span>
            <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>
            <span class="n">dim_z</span> <span class="o">=</span> <span class="n">dim</span> <span class="o">-</span> <span class="mi">2</span>
            <span class="c1"># Transform the data if needed</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">array</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span><span class="o">.</span><span class="n">fit_transform</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">array</span><span class="o">.</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">T</span>
            <span class="c1"># Cache the results</span>
            <span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="c1"># Cache the data transform</span>
            <span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="s1">&#39;data_transform&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">return_data</span><span class="p">:</span>
                <span class="c1"># Cache the data if needed</span>
                <span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="s1">&#39;data&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">array</span>
                <span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="s1">&#39;used_indices&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">use_indices_dataset_dict</span>
            <span class="c1"># Copy and fit the model if there are any parents for this variable to fit</span>
            <span class="n">a_model</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">dim_z</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">a_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">array</span><span class="p">[</span><span class="mi">2</span><span class="p">:]</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="n">array</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># Just fit default (eg, mean)</span>
                <span class="k">class</span><span class="w"> </span><span class="nc">EmptyPredictorModel</span><span class="p">:</span>
                    <span class="k">def</span><span class="w"> </span><span class="nf">fit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">result</span> <span class="o">=</span> <span class="n">empty_predictors_function</span><span class="p">(</span><span class="n">y</span><span class="p">)</span>
                    <span class="k">def</span><span class="w"> </span><span class="nf">predict</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">):</span>
                        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">result</span>
                <span class="n">a_model</span> <span class="o">=</span> <span class="n">EmptyPredictorModel</span><span class="p">()</span>
                <span class="c1"># a_model = empty_predictors_model(array[1])</span>
                <span class="n">a_model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">array</span><span class="p">[</span><span class="mi">2</span><span class="p">:]</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">y</span><span class="o">=</span><span class="n">array</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>

            <span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="s1">&#39;model&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">a_model</span>

        <span class="c1"># Cache and return the fit results</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span> <span class="o">=</span> <span class="n">fit_results</span>
        <span class="k">return</span> <span class="n">fit_results</span></div>


<div class="viewcode-block" id="Models.get_coefs">
<a class="viewcode-back" href="../../index.html#tigramite.models.Models.get_coefs">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_coefs</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns dictionary of coefficients for linear models.</span>

<span class="sd">        Only for models from sklearn.linear_model</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        coeffs : dictionary</span>
<span class="sd">            Dictionary of dictionaries for each variable with keys given by the</span>
<span class="sd">            parents and the regression coefficients as values.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">coeffs</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span><span class="p">:</span>
            <span class="n">coeffs</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="k">for</span> <span class="n">ipar</span><span class="p">,</span> <span class="n">par</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]):</span>
                <span class="n">coeffs</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="n">par</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="s1">&#39;model&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">coef_</span><span class="p">[</span><span class="n">ipar</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">coeffs</span></div>


<div class="viewcode-block" id="Models.get_val_matrix">
<a class="viewcode-back" href="../../index.html#tigramite.models.Models.get_val_matrix">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_val_matrix</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the coefficient array for different lags for linear model.</span>

<span class="sd">        Requires fit_model() before. An entry val_matrix[i,j,tau] gives the</span>
<span class="sd">        coefficient of the link from i to j at lag tau, including tau=0.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        val_matrix : array-like, shape (N, N, tau_max + 1)</span>
<span class="sd">            Array of coefficients for each time lag, including lag-zero.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">coeffs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_coefs</span><span class="p">()</span>
        <span class="n">val_matrix</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="p">))</span>

        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">coeffs</span><span class="p">):</span>
            <span class="k">for</span> <span class="n">par</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">coeffs</span><span class="p">[</span><span class="n">j</span><span class="p">]):</span>
                <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">par</span>
                <span class="n">val_matrix</span><span class="p">[</span><span class="n">i</span><span class="p">,</span><span class="n">j</span><span class="p">,</span><span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">)]</span> <span class="o">=</span> <span class="n">coeffs</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="n">par</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">val_matrix</span></div>


<div class="viewcode-block" id="Models.predict_full_model">
<a class="viewcode-back" href="../../index.html#tigramite.models.Models.predict_full_model">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">predict_full_model</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                <span class="n">new_data</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">pred_params</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;max_lag_or_tau_max&#39;</span><span class="p">):</span>
<span class="w">        </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Predict target variable with fitted model.</span>

<span class="sd">        Uses the model.predict() function of the sklearn model.</span>

<span class="sd">        A list of predicted time series for self.selected_variables is returned. </span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        new_data : data object, optional</span>
<span class="sd">            New Tigramite dataframe object with optional new mask. Note that</span>
<span class="sd">            the data will be cut off according to cut_off, see parameter</span>
<span class="sd">            `cut_off` below.</span>
<span class="sd">        pred_params : dict, optional</span>
<span class="sd">            Optional parameters passed on to sklearn prediction function.</span>
<span class="sd">        cut_off : {&#39;2xtau_max&#39;, &#39;max_lag&#39;, &#39;max_lag_or_tau_max&#39;}</span>
<span class="sd">            How many samples to cutoff at the beginning. The default is</span>
<span class="sd">            &#39;2xtau_max&#39;, which guarantees that MCI tests are all conducted on</span>
<span class="sd">            the same samples.  For modeling, &#39;max_lag_or_tau_max&#39; can be used,</span>
<span class="sd">            which uses the maximum of tau_max and the conditions, which is</span>
<span class="sd">            useful to compare multiple models on the same sample. Last,</span>
<span class="sd">            &#39;max_lag&#39; uses as much samples as possible.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Results from prediction.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="s1">&#39;selected_variables&#39;</span><span class="p">):</span>
            <span class="n">target_list</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Model not yet fitted.&quot;</span><span class="p">)</span>

        <span class="n">pred_list</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">stored_test_array</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">target</span> <span class="ow">in</span> <span class="n">target_list</span><span class="p">:</span>
            <span class="c1"># Default value for pred_params</span>
            <span class="k">if</span> <span class="n">pred_params</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">pred_params</span> <span class="o">=</span> <span class="p">{}</span>

            <span class="c1"># Construct the array form of the data</span>
            <span class="n">Y</span> <span class="o">=</span> <span class="p">[(</span><span class="n">target</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span>  <span class="c1"># dummy</span>
            <span class="n">X</span> <span class="o">=</span> <span class="p">[(</span><span class="n">target</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span>  <span class="c1"># dummy</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span><span class="p">[</span><span class="n">target</span><span class="p">]</span>

            <span class="c1"># Check if we&#39;ve passed a new dataframe object</span>
            <span class="k">if</span> <span class="n">new_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="c1"># if new_data.mask is None:</span>
                <span class="c1">#     # if no mask is supplied, use the same mask as for the fitted array</span>
                <span class="c1">#     new_data_mask = self.test_mask</span>
                <span class="c1"># else:</span>
                <span class="n">new_data_mask</span> <span class="o">=</span> <span class="n">new_data</span><span class="o">.</span><span class="n">mask</span>
                <span class="n">test_array</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">new_data</span><span class="o">.</span><span class="n">construct_array</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span>
                                                         <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                                                         <span class="n">mask</span><span class="o">=</span><span class="n">new_data_mask</span><span class="p">,</span>
                                                         <span class="n">mask_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">,</span>
                                                         <span class="n">cut_off</span><span class="o">=</span><span class="n">cut_off</span><span class="p">,</span>
                                                         <span class="n">remove_overlaps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                                         <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>
            <span class="c1"># Otherwise use the default values</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">test_array</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> \
                    <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">construct_array</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span>
                                                   <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                                                   <span class="n">mask_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">,</span>
                                                   <span class="n">cut_off</span><span class="o">=</span><span class="n">cut_off</span><span class="p">,</span>
                                                   <span class="n">remove_overlaps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                                   <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>
            <span class="c1"># Transform the data if needed</span>
            <span class="n">a_transform</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="n">target</span><span class="p">][</span><span class="s1">&#39;data_transform&#39;</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">a_transform</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">test_array</span> <span class="o">=</span> <span class="n">a_transform</span><span class="o">.</span><span class="n">transform</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">test_array</span><span class="o">.</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">T</span>
            <span class="c1"># Cache the test array</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">stored_test_array</span><span class="p">[</span><span class="n">target</span><span class="p">]</span> <span class="o">=</span> <span class="n">test_array</span>
            <span class="c1"># Run the predictor</span>
            <span class="n">predicted</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="n">target</span><span class="p">][</span><span class="s1">&#39;model&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">predict</span><span class="p">(</span>
                <span class="n">X</span><span class="o">=</span><span class="n">test_array</span><span class="p">[</span><span class="mi">2</span><span class="p">:]</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="o">**</span><span class="n">pred_params</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">test_array</span><span class="p">[</span><span class="mi">2</span><span class="p">:]</span><span class="o">.</span><span class="n">size</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="c1"># If there are no predictors, return the value of </span>
                <span class="c1"># empty_predictors_function, which is np.mean </span>
                <span class="c1"># and expand to the test array length</span>
                <span class="n">predicted</span> <span class="o">=</span> <span class="n">predicted</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">test_array</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>

            <span class="n">pred_list</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">predicted</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">pred_list</span></div>



<div class="viewcode-block" id="Models.get_residuals_cov_mean">
<a class="viewcode-back" href="../../index.html#tigramite.models.Models.get_residuals_cov_mean">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_residuals_cov_mean</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">new_data</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">pred_params</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Returns covariance and means of residuals from fitted model.</span>

<span class="sd">        Residuals are available as self.residuals.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        new_data : data object, optional</span>
<span class="sd">            New Tigramite dataframe object with optional new mask. Note that</span>
<span class="sd">            the data will be cut off according to cut_off, see parameter</span>
<span class="sd">            `cut_off` below.</span>
<span class="sd">        pred_params : dict, optional</span>
<span class="sd">            Optional parameters passed on to sklearn prediction function.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Results from prediction.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">assert</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">==</span> <span class="s1">&#39;single&#39;</span>

        <span class="n">N</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">N</span>
        <span class="n">T</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">T</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="c1"># Get overlapping samples</span>
        <span class="n">used_indices</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="n">overlapping</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">T</span><span class="p">)))</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">if</span> <span class="s1">&#39;used_indices&#39;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Run &quot;</span><span class="p">)</span>
                <span class="n">used_indices</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="s1">&#39;used_indices&#39;</span><span class="p">][</span><span class="mi">0</span><span class="p">])</span>
                <span class="n">overlapping</span> <span class="o">=</span> <span class="n">overlapping</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="n">used_indices</span><span class="p">[</span><span class="n">j</span><span class="p">])</span>

        <span class="n">overlapping</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">overlapping</span><span class="p">))</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">overlapping</span><span class="p">)</span> <span class="o">&lt;=</span> <span class="mi">10</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Less than 10 overlapping samples due to masking and/or missing values,&quot;</span>
                             <span class="s2">&quot; cannot compute residual covariance!&quot;</span><span class="p">)</span>

        <span class="n">predicted</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">predict_full_model</span><span class="p">(</span><span class="n">new_data</span><span class="o">=</span><span class="n">new_data</span><span class="p">,</span>
                                            <span class="n">pred_params</span><span class="o">=</span><span class="n">pred_params</span><span class="p">,</span>
                                            <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;max_lag_or_tau_max&#39;</span><span class="p">)</span>

        <span class="c1"># Residuals only exist after tau_max</span>
        <span class="n">residuals</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">values</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>

        <span class="k">for</span> <span class="n">index</span><span class="p">,</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">([</span><span class="n">j</span> <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span><span class="p">]):</span> <span class="c1"># if len(parents[j]) &gt; 0]):</span>
            <span class="n">residuals</span><span class="p">[</span><span class="nb">list</span><span class="p">(</span><span class="n">used_indices</span><span class="p">[</span><span class="n">j</span><span class="p">]),</span> <span class="n">j</span><span class="p">]</span> <span class="o">-=</span> <span class="n">predicted</span><span class="p">[</span><span class="n">index</span><span class="p">]</span>
        
        <span class="n">overlapping_residuals</span> <span class="o">=</span> <span class="n">residuals</span><span class="p">[</span><span class="n">overlapping</span><span class="p">]</span>

        <span class="n">len_residuals</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">overlapping_residuals</span><span class="p">)</span>

        <span class="n">cov</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">cov</span><span class="p">(</span><span class="n">overlapping_residuals</span><span class="p">,</span> <span class="n">rowvar</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
        <span class="n">mean</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">overlapping_residuals</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>   <span class="c1"># residuals should have zero mean due to prediction including constant</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">residuals</span> <span class="o">=</span> <span class="n">overlapping_residuals</span>

        <span class="k">return</span> <span class="n">cov</span><span class="p">,</span> <span class="n">mean</span></div>
</div>


<div class="viewcode-block" id="LinearMediation">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">LinearMediation</span><span class="p">(</span><span class="n">Models</span><span class="p">):</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Linear mediation analysis for time series models.</span>

<span class="sd">    Fits linear model to parents and provides functions to return measures such</span>
<span class="sd">    as causal effect, mediated causal effect, average causal effect, etc. as</span>
<span class="sd">    described in [4]_. Also allows for contemporaneous links.</span>

<span class="sd">    For general linear and nonlinear causal effect analysis including latent</span>
<span class="sd">    variables and further functionality use the CausalEffects class.</span>

<span class="sd">    Notes</span>
<span class="sd">    -----</span>
<span class="sd">    This class implements the following causal mediation measures introduced in</span>
<span class="sd">    [4]_:</span>

<span class="sd">      * causal effect (CE)</span>
<span class="sd">      * mediated causal effect (MCE)</span>
<span class="sd">      * average causal effect (ACE)</span>
<span class="sd">      * average causal susceptibility (ACS)</span>
<span class="sd">      * average mediated causal effect (AMCE)</span>

<span class="sd">    Consider a simple model of a causal chain as given in the Example with</span>

<span class="sd">    .. math:: X_t &amp;= \eta^X_t \\</span>
<span class="sd">              Y_t &amp;= 0.5 X_{t-1} +  \eta^Y_t \\</span>
<span class="sd">              Z_t &amp;= 0.5 Y_{t-1} +  \eta^Z_t</span>

<span class="sd">    Here the link coefficient of :math:`X_{t-2} \to Z_t` is zero while the</span>
<span class="sd">    causal effect is 0.25. MCE through :math:`Y` is 0.25 implying that *all*</span>
<span class="sd">    of the the CE is explained by :math:`Y`. ACE from :math:`X` is 0.37 since it</span>
<span class="sd">    has CE 0.5 on :math:`Y` and 0.25 on :math:`Z`.</span>

<span class="sd">    Examples</span>
<span class="sd">    --------</span>
<span class="sd">    &gt;&gt;&gt; links_coeffs = {0: [], 1: [((0, -1), 0.5)], 2: [((1, -1), 0.5)]}</span>
<span class="sd">    &gt;&gt;&gt; data, true_parents = toys.var_process(links_coeffs, T=1000, seed=42)</span>
<span class="sd">    &gt;&gt;&gt; dataframe = pp.DataFrame(data)</span>
<span class="sd">    &gt;&gt;&gt; med = LinearMediation(dataframe=dataframe)</span>
<span class="sd">    &gt;&gt;&gt; med.fit_model(all_parents=true_parents, tau_max=3)</span>
<span class="sd">    &gt;&gt;&gt; print &quot;Link coefficient (0, -2) --&gt; 2: &quot;, med.get_coeff(</span>
<span class="sd">    i=0, tau=-2, j=2)</span>
<span class="sd">    &gt;&gt;&gt; print &quot;Causal effect (0, -2) --&gt; 2: &quot;, med.get_ce(i=0, tau=-2, j=2)</span>
<span class="sd">    &gt;&gt;&gt; print &quot;Mediated Causal effect (0, -2) --&gt; 2 through 1: &quot;, med.get_mce(</span>
<span class="sd">    i=0, tau=-2, j=2, k=1)</span>
<span class="sd">    &gt;&gt;&gt; print &quot;Average Causal Effect: &quot;, med.get_all_ace()</span>
<span class="sd">    &gt;&gt;&gt; print &quot;Average Causal Susceptibility: &quot;, med.get_all_acs()</span>
<span class="sd">    &gt;&gt;&gt; print &quot;Average Mediated Causal Effect: &quot;, med.get_all_amce()</span>
<span class="sd">    Link coefficient (0, -2) --&gt; 2:  0.0</span>
<span class="sd">    Causal effect (0, -2) --&gt; 2:  0.250648072987</span>
<span class="sd">    Mediated Causal effect (0, -2) --&gt; 2 through 1:  0.250648072987</span>
<span class="sd">    Average Causal Effect:  [ 0.36897445  0.25718002  0.        ]</span>
<span class="sd">    Average Causal Susceptibility:  [ 0.          0.24365041  0.38250406]</span>
<span class="sd">    Average Mediated Causal Effect:  [ 0.          0.12532404  0.        ]</span>

<span class="sd">    References</span>
<span class="sd">    ----------</span>
<span class="sd">    .. [4]  J. Runge et al. (2015): Identifying causal gateways and mediators in</span>
<span class="sd">            complex spatio-temporal systems.</span>
<span class="sd">            Nature Communications, 6, 8502. http://doi.org/10.1038/ncomms9502</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    dataframe : data object</span>
<span class="sd">        Tigramite dataframe object. It must have the attributes dataframe.values</span>
<span class="sd">        yielding a numpy array of shape (observations T, variables N) and</span>
<span class="sd">        optionally a mask of the same shape and a missing values flag.</span>
<span class="sd">    model_params : dictionary, optional (default: None)</span>
<span class="sd">        Optional parameters passed on to sklearn model</span>
<span class="sd">    data_transform : sklearn preprocessing object, optional (default: StandardScaler)</span>
<span class="sd">        Used to transform data prior to fitting. For example,</span>
<span class="sd">        sklearn.preprocessing.StandardScaler for simple standardization. The</span>
<span class="sd">        fitted parameters are stored.</span>
<span class="sd">    mask_type : {None, &#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">        Masking mode: Indicators for which variables in the dependence</span>
<span class="sd">        measure I(X; Y | Z) the samples should be masked. If None, the mask</span>
<span class="sd">        is not used. Explained in tutorial on masking and missing values.</span>
<span class="sd">    verbosity : int, optional (default: 0)</span>
<span class="sd">        Level of verbosity.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">dataframe</span><span class="p">,</span>
                 <span class="n">model_params</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">data_transform</span><span class="o">=</span><span class="n">sklearn</span><span class="o">.</span><span class="n">preprocessing</span><span class="o">.</span><span class="n">StandardScaler</span><span class="p">(),</span>
                 <span class="n">mask_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
        <span class="c1"># Initialize the member variables to None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">phi</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">psi</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span> <span class="o">=</span> <span class="n">dataframe</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="o">=</span> <span class="n">mask_type</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span> <span class="o">=</span> <span class="n">data_transform</span>
        <span class="k">if</span> <span class="n">model_params</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">model_params</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">model_params</span> <span class="o">=</span> <span class="n">model_params</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap_available</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="c1"># Build the model using the parameters</span>
        <span class="k">if</span> <span class="n">model_params</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">model_params</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="n">this_model</span> <span class="o">=</span> <span class="n">sklearn</span><span class="o">.</span><span class="n">linear_model</span><span class="o">.</span><span class="n">LinearRegression</span><span class="p">(</span><span class="o">**</span><span class="n">model_params</span><span class="p">)</span>
        <span class="n">Models</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                        <span class="n">dataframe</span><span class="o">=</span><span class="n">dataframe</span><span class="p">,</span>
                        <span class="n">model</span><span class="o">=</span><span class="n">this_model</span><span class="p">,</span>
                        <span class="n">data_transform</span><span class="o">=</span><span class="n">data_transform</span><span class="p">,</span>
                        <span class="n">mask_type</span><span class="o">=</span><span class="n">mask_type</span><span class="p">,</span>
                        <span class="n">verbosity</span><span class="o">=</span><span class="n">verbosity</span><span class="p">)</span>

<div class="viewcode-block" id="LinearMediation.fit_model">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.fit_model">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">fit_model</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">all_parents</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">return_data</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Fit linear time series model.</span>

<span class="sd">        Fits a sklearn.linear_model.LinearRegression model to the parents of</span>
<span class="sd">        each variable and computes the coefficient matrices :math:`\Phi` and</span>
<span class="sd">        :math:`\Psi` as described in [4]_. Does accept contemporaneous links.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        all_parents : dictionary</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, 0), ...], 1:[], ...} containing</span>
<span class="sd">            the parents estimated with PCMCI.</span>
<span class="sd">        tau_max : int, optional (default: None)</span>
<span class="sd">            Maximum time lag. If None, the maximum lag in all_parents is used.</span>
<span class="sd">        return_data : bool, optional (default: False)</span>
<span class="sd">            Whether to save the data array. Needed to get residuals.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="c1"># Fit the model using the base class</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fit_results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fit_full_model</span><span class="p">(</span><span class="n">all_parents</span><span class="o">=</span><span class="n">all_parents</span><span class="p">,</span>
                                        <span class="n">selected_variables</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                        <span class="n">return_data</span><span class="o">=</span><span class="n">return_data</span><span class="p">,</span>
                                        <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">)</span>
        <span class="c1"># Cache the results in the member variables</span>
        <span class="n">coeffs</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_coefs</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">phi</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_phi</span><span class="p">(</span><span class="n">coeffs</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">psi</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_psi</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_psi_k</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span> <span class="o">=</span> <span class="n">all_parents</span></div>

        <span class="c1"># self.tau_max = tau_max</span>

<div class="viewcode-block" id="LinearMediation.fit_model_bootstrap">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.fit_model_bootstrap">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">fit_model_bootstrap</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
            <span class="n">boot_blocklength</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
            <span class="n">seed</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
            <span class="n">boot_samples</span><span class="o">=</span><span class="mi">100</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Fits boostrap-versions of Phi, Psi, etc.</span>

<span class="sd">        Random draws are generated</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        boot_blocklength : int, or in {&#39;cube_root&#39;, &#39;from_autocorrelation&#39;}</span>
<span class="sd">            Block length for block-bootstrap. If &#39;cube_root&#39; it is the cube </span>
<span class="sd">            root of the time series length.</span>
<span class="sd">        seed : int, optional(default = None)</span>
<span class="sd">            Seed for RandomState (default_rng)</span>
<span class="sd">        boot_samples : int</span>
<span class="sd">            Number of bootstrap samples.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">phi_boots</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">((</span><span class="n">boot_samples</span><span class="p">,)</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">psi_boots</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">((</span><span class="n">boot_samples</span><span class="p">,)</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k_boots</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">((</span><span class="n">boot_samples</span><span class="p">,)</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">## Generating bootstrap samples of Phi, Psi, etc &quot;</span>  <span class="o">+</span>
                  <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">+</span>
                  <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">boot_samples = </span><span class="si">%s</span><span class="s2"> </span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">boot_samples</span> <span class="o">+</span>
                  <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">boot_blocklength = </span><span class="si">%s</span><span class="s2"> </span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">boot_blocklength</span>
                  <span class="p">)</span>


        <span class="k">for</span> <span class="n">b</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">boot_samples</span><span class="p">):</span>
            <span class="c1"># # Replace dataframe in method args by bootstrapped dataframe</span>
            <span class="c1"># method_args_bootstrap[&#39;dataframe&#39;].bootstrap = boot_draw</span>
            <span class="k">if</span> <span class="n">seed</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">random_state</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="kc">None</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">random_state</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="o">+</span><span class="n">b</span><span class="p">)</span>

            <span class="n">dataframe_here</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="p">)</span>

            <span class="n">dataframe_here</span><span class="o">.</span><span class="n">bootstrap</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;boot_blocklength&#39;</span><span class="p">:</span><span class="n">boot_blocklength</span><span class="p">,</span>
                                        <span class="s1">&#39;random_state&#39;</span><span class="p">:</span><span class="n">random_state</span><span class="p">}</span>
            <span class="n">model</span> <span class="o">=</span> <span class="n">Models</span><span class="p">(</span><span class="n">dataframe</span><span class="o">=</span><span class="n">dataframe_here</span><span class="p">,</span>
                           <span class="n">model</span><span class="o">=</span><span class="n">sklearn</span><span class="o">.</span><span class="n">linear_model</span><span class="o">.</span><span class="n">LinearRegression</span><span class="p">(</span><span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">model_params</span><span class="p">),</span>
                           <span class="n">data_transform</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">data_transform</span><span class="p">,</span>
                           <span class="n">mask_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">,</span>
                           <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>

            <span class="n">model</span><span class="o">.</span><span class="n">fit_full_model</span><span class="p">(</span><span class="n">all_parents</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">all_parents</span><span class="p">,</span>
                           <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">)</span>
            <span class="c1"># Cache the results in the member variables</span>
            <span class="n">coeffs</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">get_coefs</span><span class="p">()</span>
            <span class="n">phi</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_phi</span><span class="p">(</span><span class="n">coeffs</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">phi_boots</span><span class="p">[</span><span class="n">b</span><span class="p">]</span> <span class="o">=</span> <span class="n">phi</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">psi_boots</span><span class="p">[</span><span class="n">b</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_psi</span><span class="p">(</span><span class="n">phi</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k_boots</span><span class="p">[</span><span class="n">b</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_all_psi_k</span><span class="p">(</span><span class="n">phi</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap_available</span> <span class="o">=</span> <span class="kc">True</span>

        <span class="k">return</span> <span class="bp">self</span></div>


<div class="viewcode-block" id="LinearMediation.get_bootstrap_of">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_bootstrap_of">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_bootstrap_of</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">function</span><span class="p">,</span> <span class="n">function_args</span><span class="p">,</span> <span class="n">conf_lev</span><span class="o">=</span><span class="mf">0.9</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Applies bootstrap-versions of Phi, Psi, etc. to any function in </span>
<span class="sd">        this class.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        function : string</span>
<span class="sd">            Valid function from LinearMediation class</span>
<span class="sd">        function_args : dict</span>
<span class="sd">            Optional function arguments.</span>
<span class="sd">        conf_lev : float</span>
<span class="sd">            Confidence interval.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Upper/Lower confidence interval of function.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">valid_functions</span> <span class="o">=</span> <span class="p">[</span>
            <span class="s1">&#39;get_coeff&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_ce&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_ce_max&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_joint_ce&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_joint_ce_matrix&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_mce&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_conditional_mce&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_joint_mce&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_ace&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_all_ace&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_acs&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_all_acs&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_amce&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_all_amce&#39;</span><span class="p">,</span>
            <span class="s1">&#39;get_val_matrix&#39;</span><span class="p">,</span>
            <span class="p">]</span>

        <span class="k">if</span> <span class="n">function</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">valid_functions</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;function must be in </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span><span class="n">valid_functions</span><span class="p">)</span>

        <span class="n">realizations</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">phi_boots</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="n">original_phi</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="p">)</span>
        <span class="n">original_psi</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">)</span>
        <span class="n">original_all_psi_k</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">r</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">realizations</span><span class="p">):</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">phi</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">phi_boots</span><span class="p">[</span><span class="n">r</span><span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">psi</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">psi_boots</span><span class="p">[</span><span class="n">r</span><span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k_boots</span><span class="p">[</span><span class="n">r</span><span class="p">]</span>

            <span class="n">boot_effect</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">function</span><span class="p">)(</span><span class="o">**</span><span class="n">function_args</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">r</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">bootstrap_result</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">((</span><span class="n">realizations</span><span class="p">,)</span> <span class="o">+</span> <span class="n">boot_effect</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>

            <span class="n">bootstrap_result</span><span class="p">[</span><span class="n">r</span><span class="p">]</span> <span class="o">=</span> <span class="n">boot_effect</span>

        <span class="c1"># Confidence intervals for val_matrix; interval is two-sided</span>
        <span class="n">c_int</span> <span class="o">=</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">conf_lev</span><span class="p">)</span><span class="o">/</span><span class="mf">2.</span><span class="p">)</span>
        <span class="n">confidence_interval</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">percentile</span><span class="p">(</span>
                <span class="n">bootstrap_result</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                <span class="n">q</span> <span class="o">=</span> <span class="p">[</span><span class="mi">100</span><span class="o">*</span><span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">c_int</span><span class="p">),</span> <span class="mi">100</span><span class="o">*</span><span class="n">c_int</span><span class="p">])</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">phi</span> <span class="o">=</span> <span class="n">original_phi</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">psi</span> <span class="o">=</span> <span class="n">original_psi</span> 
        <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span> <span class="o">=</span> <span class="n">original_all_psi_k</span> 
        <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap_result</span> <span class="o">=</span> <span class="n">bootstrap_result</span>

        <span class="k">return</span> <span class="n">confidence_interval</span></div>



    <span class="k">def</span><span class="w"> </span><span class="nf">_check_sanity</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Checks validity of some parameters.&quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">X</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">1</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;X must be of form [(i, -tau)] and Y = [(j, 0)], &quot;</span>
                             <span class="s2">&quot;but are X = </span><span class="si">%s</span><span class="s2">, Y=</span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">))</span>

        <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">X</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="k">if</span> <span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">)</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;X must be of form [(i, -tau)] with&quot;</span>
                             <span class="s2">&quot; tau &lt;= tau_max&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">k</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="p">(</span><span class="n">k</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="ow">or</span> <span class="n">k</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;k must be in [0, N)&quot;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_phi</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">coeffs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the linear coefficient matrices for different lags.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        coeffs : dictionary</span>
<span class="sd">            Dictionary of coefficients for each parent.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        phi : array-like, shape (tau_max + 1, N, N)</span>
<span class="sd">            Matrices of coefficients for each time lag.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">phi</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">))</span>
        <span class="c1"># phi[0] = np.identity(self.N)</span>

        <span class="c1"># Also includes contemporaneous lags</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">coeffs</span><span class="p">):</span>
            <span class="k">for</span> <span class="n">par</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">coeffs</span><span class="p">[</span><span class="n">j</span><span class="p">]):</span>
                <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">par</span>
                <span class="n">phi</span><span class="p">[</span><span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">coeffs</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="n">par</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">phi</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_psi</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">phi</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the linear causal effect matrices for different lags incl</span>
<span class="sd">        lag zero.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        phi : array-like</span>
<span class="sd">            Coefficient matrices at different lags.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        psi : array-like, shape (tau_max + 1, N, N)</span>
<span class="sd">            Matrices of causal effects for each time lag incl contemporaneous links.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">psi</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">))</span>

        <span class="n">psi</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">linalg</span><span class="o">.</span><span class="n">pinv</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">identity</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span> <span class="o">-</span> <span class="n">phi</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
        <span class="k">for</span> <span class="n">tau</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
            <span class="k">for</span> <span class="n">s</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">tau</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                <span class="n">psi</span><span class="p">[</span><span class="n">tau</span><span class="p">]</span> <span class="o">+=</span> <span class="n">np</span><span class="o">.</span><span class="n">matmul</span><span class="p">(</span><span class="n">psi</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">np</span><span class="o">.</span><span class="n">matmul</span><span class="p">(</span><span class="n">phi</span><span class="p">[</span><span class="n">s</span><span class="p">],</span> <span class="n">psi</span><span class="p">[</span><span class="n">tau</span> <span class="o">-</span> <span class="n">s</span><span class="p">])</span> <span class="p">)</span> 

        <span class="c1"># Lagged-only effects:</span>
        <span class="c1"># psi = np.zeros((self.tau_max + 1, self.N, self.N))</span>

        <span class="c1"># psi[0] = np.identity(self.N)</span>
        <span class="c1"># for n in range(1, self.tau_max + 1):</span>
        <span class="c1">#     psi[n] = np.zeros((self.N, self.N))</span>
        <span class="c1">#     for s in range(1, n + 1):</span>
        <span class="c1">#         psi[n] += np.dot(phi[s], psi[n - s])</span>

        <span class="k">return</span> <span class="n">psi</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_psi_k</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">phi</span><span class="p">,</span> <span class="n">k</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the linear causal effect matrices excluding variable k.</span>

<span class="sd">        Essentially, this blocks all path through parents of variable k</span>
<span class="sd">        at any lag.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        phi : array-like</span>
<span class="sd">            Coefficient matrices at different lags.</span>
<span class="sd">        k : int or list of ints</span>
<span class="sd">            Variable indices to exclude causal effects through.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        psi_k : array-like, shape (tau_max + 1, N, N)</span>
<span class="sd">            Matrices of causal effects excluding k.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">psi_k</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">))</span>
        
        <span class="n">phi_k</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">copy</span><span class="p">(</span><span class="n">phi</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="n">phi_k</span><span class="p">[:,</span> <span class="n">k</span><span class="p">,</span> <span class="p">:]</span> <span class="o">=</span> <span class="mf">0.</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">k_here</span> <span class="ow">in</span> <span class="n">k</span><span class="p">:</span>
                <span class="n">phi_k</span><span class="p">[:,</span> <span class="n">k_here</span><span class="p">,</span> <span class="p">:]</span> <span class="o">=</span> <span class="mf">0.</span>


        <span class="n">psi_k</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">linalg</span><span class="o">.</span><span class="n">pinv</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">identity</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span> <span class="o">-</span> <span class="n">phi_k</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
        <span class="k">for</span> <span class="n">tau</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
            <span class="c1"># psi_k[tau] = np.matmul(psi_k[0], np.matmul(phi_k[tau], psi_k[0]))</span>
            <span class="k">for</span> <span class="n">s</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">tau</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                <span class="n">psi_k</span><span class="p">[</span><span class="n">tau</span><span class="p">]</span> <span class="o">+=</span> <span class="n">np</span><span class="o">.</span><span class="n">matmul</span><span class="p">(</span><span class="n">psi_k</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">np</span><span class="o">.</span><span class="n">matmul</span><span class="p">(</span><span class="n">phi_k</span><span class="p">[</span><span class="n">s</span><span class="p">],</span> <span class="n">psi_k</span><span class="p">[</span><span class="n">tau</span> <span class="o">-</span> <span class="n">s</span><span class="p">]))</span> 


        <span class="c1"># psi_k[0] = np.identity(self.N)</span>
        <span class="c1"># phi_k = np.copy(phi)</span>
        <span class="c1"># phi_k[:, k, :] = 0.</span>
        <span class="c1"># for n in range(1, self.tau_max + 1):</span>
        <span class="c1">#     psi_k[n] = np.zeros((self.N, self.N))</span>
        <span class="c1">#     for s in range(1, n + 1):</span>
        <span class="c1">#         psi_k[n] += np.dot(phi_k[s], psi_k[n - s])</span>

        <span class="k">return</span> <span class="n">psi_k</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_all_psi_k</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">phi</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the linear causal effect matrices excluding variables.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        phi : array-like</span>
<span class="sd">            Coefficient matrices at different lags.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        all_psi_k : array-like, shape (N, tau_max + 1, N, N)</span>
<span class="sd">            Matrices of causal effects where for each row another variable is</span>
<span class="sd">            excluded.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">all_psi_k</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">))</span>

        <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">):</span>
            <span class="n">all_psi_k</span><span class="p">[</span><span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_psi_k</span><span class="p">(</span><span class="n">phi</span><span class="p">,</span> <span class="n">k</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">all_psi_k</span>

<div class="viewcode-block" id="LinearMediation.get_coeff">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_coeff">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_coeff</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tau</span><span class="p">,</span> <span class="n">j</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns link coefficient.</span>

<span class="sd">        This is the direct causal effect for a particular link (i, -tau) --&gt; j.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        tau : int</span>
<span class="sd">            Lag of cause variable (incl lag zero).</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        coeff : float</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="p">[</span><span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span></div>


<div class="viewcode-block" id="LinearMediation.get_ce">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_ce">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_ce</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tau</span><span class="p">,</span> <span class="n">j</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the causal effect.</span>

<span class="sd">        This is the causal effect for  (i, -tau) -- --&gt; j.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        tau : int</span>
<span class="sd">            Lag of cause variable (incl lag zero).</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        ce : float</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[</span><span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span></div>


<div class="viewcode-block" id="LinearMediation.get_ce_max">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_ce_max">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_ce_max</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the causal effect.</span>

<span class="sd">        This is the maximum absolute causal effect for  i --&gt; j across all</span>
<span class="sd">        lags (incl lag zero).</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        ce : float</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">argmax</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[:,</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">])</span><span class="o">.</span><span class="n">argmax</span><span class="p">()</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[:,</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">][</span><span class="n">argmax</span><span class="p">]</span></div>


<div class="viewcode-block" id="LinearMediation.get_joint_ce">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_joint_ce">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_joint_ce</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the joint causal effect.</span>

<span class="sd">        This is the causal effect from all lags [t, ..., t-tau_max]</span>
<span class="sd">        of i on j at time t. Note that the joint effect does not</span>
<span class="sd">        count links passing through parents of i itself.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        joint_ce : array of shape (tau_max + 1)</span>
<span class="sd">            Causal effect from each lag [t, ..., t-tau_max] of i on j.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">joint_ce</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="p">:,</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">joint_ce</span></div>


<div class="viewcode-block" id="LinearMediation.get_joint_ce_matrix">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_joint_ce_matrix">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_joint_ce_matrix</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the joint causal effect matrix of i on j.</span>

<span class="sd">        This is the causal effect from all lags [t, ..., t-tau_max]</span>
<span class="sd">        of i on j at times [t, ..., t-tau_max]. Note that the joint effect does not</span>
<span class="sd">        count links passing through parents of i itself.</span>

<span class="sd">        An entry (taui, tauj) stands for the effect of i at t-taui on j at t-tauj.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        joint_ce_matrix : 2d array of shape (tau_max + 1, tau_max + 1)</span>
<span class="sd">            Causal effect matrix from each lag of i on each lag of j.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">joint_ce_matrix</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">))</span>
        <span class="k">for</span> <span class="n">tauj</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
            <span class="n">joint_ce_matrix</span><span class="p">[</span><span class="n">tauj</span><span class="p">:,</span> <span class="n">tauj</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">tauj</span><span class="p">:,</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">][::</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">joint_ce_matrix</span></div>


<div class="viewcode-block" id="LinearMediation.get_mce">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_mce">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_mce</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tau</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the mediated causal effect.</span>

<span class="sd">        This is the causal effect for  i --&gt; j minus the causal effect not going</span>
<span class="sd">        through k.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        tau : int</span>
<span class="sd">            Lag of cause variable.</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>
<span class="sd">        k : int or list of ints</span>
<span class="sd">            Indices of mediator variables.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        mce : float</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="n">effect_without_k</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span><span class="p">[</span><span class="n">k</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">effect_without_k</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_psi_k</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="n">k</span><span class="p">)[</span><span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span>

        <span class="n">mce</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[</span><span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span> <span class="o">-</span> <span class="n">effect_without_k</span>
        <span class="k">return</span> <span class="n">mce</span></div>


<div class="viewcode-block" id="LinearMediation.get_conditional_mce">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_conditional_mce">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_conditional_mce</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tau</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">,</span> <span class="n">notk</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the conditional mediated causal effect.</span>

<span class="sd">        This is the causal effect for  i --&gt; j for all paths going through k, but not through notk.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        tau : int</span>
<span class="sd">            Lag of cause variable.</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>
<span class="sd">        k : int or list of ints</span>
<span class="sd">            Indices of mediator variables.</span>
<span class="sd">        notk : int or list of ints</span>
<span class="sd">            Indices of mediator variables to exclude.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        mce : float</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="n">k</span> <span class="o">=</span> <span class="nb">set</span><span class="p">([</span><span class="n">k</span><span class="p">])</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">k</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">k</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">notk</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="n">notk</span> <span class="o">=</span> <span class="nb">set</span><span class="p">([</span><span class="n">notk</span><span class="p">])</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">notk</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">notk</span><span class="p">)</span>

        <span class="n">bothk</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">k</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">notk</span><span class="p">))</span>
        <span class="n">notk</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">notk</span><span class="p">)</span>
  
        <span class="n">effect_without_bothk</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_psi_k</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="n">bothk</span><span class="p">)[</span><span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span>
        <span class="n">effect_without_notk</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_psi_k</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="n">notk</span><span class="p">)[</span><span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span>

        <span class="c1"># mce = self.psi[abs(tau), j, i] - effect_without_k</span>
        <span class="n">mce</span> <span class="o">=</span> <span class="n">effect_without_notk</span> <span class="o">-</span> <span class="n">effect_without_bothk</span>

        <span class="k">return</span> <span class="n">mce</span></div>



<div class="viewcode-block" id="LinearMediation.get_joint_mce">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_joint_mce">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_joint_mce</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the joint causal effect mediated through k.</span>

<span class="sd">        This is the mediated causal effect from all lags [t, ..., t-tau_max]</span>
<span class="sd">        of i on j at time t for paths through k. Note that the joint effect</span>
<span class="sd">        does not count links passing through parents of i itself.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>
<span class="sd">        k : int or list of ints</span>
<span class="sd">            Indices of mediator variables.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        joint_mce : array of shape (tau_max + 1)</span>
<span class="sd">            Mediated causal effect from each lag [t, ..., t-tau_max] of i on j through k.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="n">k_here</span> <span class="o">=</span> <span class="p">[</span><span class="n">k</span><span class="p">]</span>

        <span class="n">effect_without_k</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_psi_k</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="p">,</span> <span class="n">k</span><span class="o">=</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">+</span> <span class="n">k_here</span><span class="p">)</span>

        <span class="n">joint_mce</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="p">:,</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span> <span class="o">-</span> <span class="n">effect_without_k</span><span class="p">[:,</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">joint_mce</span></div>


<div class="viewcode-block" id="LinearMediation.get_ace">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_ace">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_ace</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">lag_mode</span><span class="o">=</span><span class="s1">&#39;absmax&#39;</span><span class="p">,</span> <span class="n">exclude_i</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the average causal effect.</span>

<span class="sd">        This is the average causal effect (ACE) emanating from variable i to any</span>
<span class="sd">        other variable. With lag_mode=&#39;absmax&#39; this is based on the lag of</span>
<span class="sd">        maximum CE for each pair.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        lag_mode : {&#39;absmax&#39;, &#39;all_lags&#39;}</span>
<span class="sd">            Lag mode. Either average across all lags between each pair or only</span>
<span class="sd">            at the lag of maximum absolute causal effect.</span>
<span class="sd">        exclude_i : bool, optional (default: True)</span>
<span class="sd">            Whether to exclude causal effects on the variable itself at later</span>
<span class="sd">            lags.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        ace :float</span>
<span class="sd">            Average Causal Effect.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">all_but_i</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;bool&#39;</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">exclude_i</span><span class="p">:</span>
            <span class="n">all_but_i</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="k">if</span> <span class="n">lag_mode</span> <span class="o">==</span> <span class="s1">&#39;absmax&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[:,</span> <span class="n">all_but_i</span><span class="p">,</span> <span class="n">i</span><span class="p">])</span><span class="o">.</span><span class="n">max</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span><span class="o">.</span><span class="n">mean</span><span class="p">()</span>
        <span class="k">elif</span> <span class="n">lag_mode</span> <span class="o">==</span> <span class="s1">&#39;all_lags&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[:,</span> <span class="n">all_but_i</span><span class="p">,</span> <span class="n">i</span><span class="p">])</span><span class="o">.</span><span class="n">mean</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;lag_mode = </span><span class="si">%s</span><span class="s2"> not implemented&quot;</span> <span class="o">%</span> <span class="n">lag_mode</span><span class="p">)</span></div>


<div class="viewcode-block" id="LinearMediation.get_all_ace">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_all_ace">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_all_ace</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lag_mode</span><span class="o">=</span><span class="s1">&#39;absmax&#39;</span><span class="p">,</span> <span class="n">exclude_i</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the average causal effect for all variables.</span>

<span class="sd">        This is the average causal effect (ACE) emanating from variable i to any</span>
<span class="sd">        other variable. With lag_mode=&#39;absmax&#39; this is based on the lag of</span>
<span class="sd">        maximum CE for each pair.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        lag_mode : {&#39;absmax&#39;, &#39;all_lags&#39;}</span>
<span class="sd">            Lag mode. Either average across all lags between each pair or only</span>
<span class="sd">            at the lag of maximum absolute causal effect.</span>
<span class="sd">        exclude_i : bool, optional (default: True)</span>
<span class="sd">            Whether to exclude causal effects on the variable itself at later</span>
<span class="sd">            lags.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        ace : array of shape (N,)</span>
<span class="sd">            Average Causal Effect for each variable.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">ace</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">):</span>
            <span class="n">ace</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_ace</span><span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">lag_mode</span><span class="o">=</span><span class="n">lag_mode</span><span class="p">,</span> <span class="n">exclude_i</span><span class="o">=</span><span class="n">exclude_i</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">ace</span></div>


<div class="viewcode-block" id="LinearMediation.get_acs">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_acs">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_acs</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">lag_mode</span><span class="o">=</span><span class="s1">&#39;absmax&#39;</span><span class="p">,</span> <span class="n">exclude_j</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the average causal susceptibility.</span>

<span class="sd">        This is the Average Causal Susceptibility (ACS) affecting a variable j</span>
<span class="sd">        from any other variable. With lag_mode=&#39;absmax&#39; this is based on the lag</span>
<span class="sd">        of maximum CE for each pair.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of variable.</span>
<span class="sd">        lag_mode : {&#39;absmax&#39;, &#39;all_lags&#39;}</span>
<span class="sd">            Lag mode. Either average across all lags between each pair or only</span>
<span class="sd">            at the lag of maximum absolute causal effect.</span>
<span class="sd">        exclude_j : bool, optional (default: True)</span>
<span class="sd">            Whether to exclude causal effects on the variable itself at previous</span>
<span class="sd">            lags.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        acs : float</span>
<span class="sd">            Average Causal Susceptibility.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">all_but_j</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;bool&#39;</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">exclude_j</span><span class="p">:</span>
            <span class="n">all_but_j</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="k">if</span> <span class="n">lag_mode</span> <span class="o">==</span> <span class="s1">&#39;absmax&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[:,</span> <span class="n">j</span><span class="p">,</span> <span class="n">all_but_j</span><span class="p">])</span><span class="o">.</span><span class="n">max</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span><span class="o">.</span><span class="n">mean</span><span class="p">()</span>
        <span class="k">elif</span> <span class="n">lag_mode</span> <span class="o">==</span> <span class="s1">&#39;all_lags&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[:,</span> <span class="n">j</span><span class="p">,</span> <span class="n">all_but_j</span><span class="p">])</span><span class="o">.</span><span class="n">mean</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;lag_mode = </span><span class="si">%s</span><span class="s2"> not implemented&quot;</span> <span class="o">%</span> <span class="n">lag_mode</span><span class="p">)</span></div>


<div class="viewcode-block" id="LinearMediation.get_all_acs">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_all_acs">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_all_acs</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lag_mode</span><span class="o">=</span><span class="s1">&#39;absmax&#39;</span><span class="p">,</span> <span class="n">exclude_j</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the average causal susceptibility.</span>

<span class="sd">        This is the Average Causal Susceptibility (ACS) for each variable from</span>
<span class="sd">        any other variable. With lag_mode=&#39;absmax&#39; this is based on the lag of</span>
<span class="sd">        maximum CE for each pair.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        lag_mode : {&#39;absmax&#39;, &#39;all_lags&#39;}</span>
<span class="sd">            Lag mode. Either average across all lags between each pair or only</span>
<span class="sd">            at the lag of maximum absolute causal effect.</span>
<span class="sd">        exclude_j : bool, optional (default: True)</span>
<span class="sd">            Whether to exclude causal effects on the variable itself at previous</span>
<span class="sd">            lags.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        acs : array of shape (N,)</span>
<span class="sd">            Average Causal Susceptibility.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">acs</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">):</span>
            <span class="n">acs</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_acs</span><span class="p">(</span><span class="n">j</span><span class="p">,</span> <span class="n">lag_mode</span><span class="o">=</span><span class="n">lag_mode</span><span class="p">,</span> <span class="n">exclude_j</span><span class="o">=</span><span class="n">exclude_j</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">acs</span></div>


<div class="viewcode-block" id="LinearMediation.get_amce">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_amce">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_amce</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">k</span><span class="p">,</span> <span class="n">lag_mode</span><span class="o">=</span><span class="s1">&#39;absmax&#39;</span><span class="p">,</span>
                 <span class="n">exclude_k</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exclude_self_effects</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the average mediated causal effect.</span>

<span class="sd">        This is the Average Mediated Causal Effect (AMCE) through a variable k</span>
<span class="sd">        With lag_mode=&#39;absmax&#39; this is based on the lag of maximum CE for each</span>
<span class="sd">        pair.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        k : int</span>
<span class="sd">            Index of variable.</span>
<span class="sd">        lag_mode : {&#39;absmax&#39;, &#39;all_lags&#39;}</span>
<span class="sd">            Lag mode. Either average across all lags between each pair or only</span>
<span class="sd">            at the lag of maximum absolute causal effect.</span>
<span class="sd">        exclude_k : bool, optional (default: True)</span>
<span class="sd">            Whether to exclude causal effects through the variable itself at</span>
<span class="sd">            previous lags.</span>
<span class="sd">        exclude_self_effects : bool, optional (default: True)</span>
<span class="sd">            Whether to exclude causal self effects of variables on themselves.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        amce : float</span>
<span class="sd">            Average Mediated Causal Effect.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">all_but_k</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;bool&#39;</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">exclude_k</span><span class="p">:</span>
            <span class="n">all_but_k</span><span class="p">[</span><span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="kc">False</span>
            <span class="n">N_new</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span> <span class="o">-</span> <span class="mi">1</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">N_new</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span>

        <span class="k">if</span> <span class="n">exclude_self_effects</span><span class="p">:</span>
            <span class="n">weights</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">identity</span><span class="p">(</span><span class="n">N_new</span><span class="p">)</span> <span class="o">==</span> <span class="kc">False</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">weights</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">((</span><span class="n">N_new</span><span class="p">,</span> <span class="n">N_new</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;bool&#39;</span><span class="p">)</span>

        <span class="c1"># if self.tau_max &lt; 2:</span>
        <span class="c1">#     raise ValueError(&quot;Mediation only nonzero for tau_max &gt;= 2&quot;)</span>

        <span class="n">all_mce</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="p">[:,</span> <span class="p">:,</span> <span class="p">:]</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span><span class="p">[</span><span class="n">k</span><span class="p">,</span> <span class="p">:,</span> <span class="p">:,</span> <span class="p">:]</span>
        <span class="c1"># all_mce[:, range(self.N), range(self.N)] = 0.</span>

        <span class="k">if</span> <span class="n">lag_mode</span> <span class="o">==</span> <span class="s1">&#39;absmax&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">average</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">all_mce</span><span class="p">[:,</span> <span class="n">all_but_k</span><span class="p">,</span> <span class="p">:]</span>
                                     <span class="p">[:,</span> <span class="p">:,</span> <span class="n">all_but_k</span><span class="p">]</span>
                                     <span class="p">)</span><span class="o">.</span><span class="n">max</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">),</span> <span class="n">weights</span><span class="o">=</span><span class="n">weights</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">lag_mode</span> <span class="o">==</span> <span class="s1">&#39;all_lags&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">all_mce</span><span class="p">[:,</span> <span class="n">all_but_k</span><span class="p">,</span> <span class="p">:][:,</span> <span class="p">:,</span> <span class="n">all_but_k</span><span class="p">])</span><span class="o">.</span><span class="n">mean</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;lag_mode = </span><span class="si">%s</span><span class="s2"> not implemented&quot;</span> <span class="o">%</span> <span class="n">lag_mode</span><span class="p">)</span></div>


<div class="viewcode-block" id="LinearMediation.get_all_amce">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_all_amce">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_all_amce</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lag_mode</span><span class="o">=</span><span class="s1">&#39;absmax&#39;</span><span class="p">,</span>
                     <span class="n">exclude_k</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">exclude_self_effects</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the average mediated causal effect.</span>

<span class="sd">        This is the Average Mediated Causal Effect (AMCE) through all variables</span>
<span class="sd">        With lag_mode=&#39;absmax&#39; this is based on the lag of maximum CE for each</span>
<span class="sd">        pair.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        lag_mode : {&#39;absmax&#39;, &#39;all_lags&#39;}</span>
<span class="sd">            Lag mode. Either average across all lags between each pair or only</span>
<span class="sd">            at the lag of maximum absolute causal effect.</span>
<span class="sd">        exclude_k : bool, optional (default: True)</span>
<span class="sd">            Whether to exclude causal effects through the variable itself at</span>
<span class="sd">            previous lags.</span>
<span class="sd">        exclude_self_effects : bool, optional (default: True)</span>
<span class="sd">            Whether to exclude causal self effects of variables on themselves.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        amce : array of shape (N,)</span>
<span class="sd">            Average Mediated Causal Effect.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">amce</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">):</span>
            <span class="n">amce</span><span class="p">[</span><span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_amce</span><span class="p">(</span><span class="n">k</span><span class="p">,</span>
                                    <span class="n">lag_mode</span><span class="o">=</span><span class="n">lag_mode</span><span class="p">,</span>
                                    <span class="n">exclude_k</span><span class="o">=</span><span class="n">exclude_k</span><span class="p">,</span>
                                    <span class="n">exclude_self_effects</span><span class="o">=</span><span class="n">exclude_self_effects</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">amce</span></div>



<div class="viewcode-block" id="LinearMediation.get_val_matrix">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_val_matrix">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_val_matrix</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">symmetrize</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns the matrix of linear coefficients.</span>

<span class="sd">        Requires fit_model() before. An entry val_matrix[i,j,tau] gives the</span>
<span class="sd">        coefficient of the link from i to j at lag tau. Lag=0 is always set</span>
<span class="sd">        to zero for LinearMediation, use Models class for contemporaneous </span>
<span class="sd">        models.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        symmetrize : bool</span>
<span class="sd">            If True, the lag-zero entries will be symmetrized such that</span>
<span class="sd">            no zeros appear. Useful since other parts of tigramite </span>
<span class="sd">            through an error for non-symmetric val_matrix, eg plotting.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        val_matrix : array</span>
<span class="sd">            Matrix of linear coefficients, shape (N, N, tau_max + 1).</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">val_matrix</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">copy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="o">.</span><span class="n">transpose</span><span class="p">())</span>
        <span class="n">N</span> <span class="o">=</span> <span class="n">val_matrix</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="k">if</span> <span class="n">symmetrize</span><span class="p">:</span>
            <span class="c1"># Symmetrize since otherwise other parts of tigramite through an error</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">):</span>
                <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">):</span>
                    <span class="k">if</span> <span class="n">val_matrix</span><span class="p">[</span><span class="n">i</span><span class="p">,</span><span class="n">j</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="mf">0.</span><span class="p">:</span>
                        <span class="n">val_matrix</span><span class="p">[</span><span class="n">i</span><span class="p">,</span><span class="n">j</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="n">val_matrix</span><span class="p">[</span><span class="n">j</span><span class="p">,</span><span class="n">i</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">val_matrix</span></div>


<div class="viewcode-block" id="LinearMediation.net_to_tsg">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.net_to_tsg">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">net_to_tsg</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">row</span><span class="p">,</span> <span class="n">lag</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to translate from network to time series graph.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">row</span> <span class="o">*</span> <span class="n">max_lag</span> <span class="o">+</span> <span class="n">lag</span></div>


<div class="viewcode-block" id="LinearMediation.tsg_to_net">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.tsg_to_net">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">tsg_to_net</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">node</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to translate from time series graph to network.&quot;&quot;&quot;</span>
        <span class="n">row</span> <span class="o">=</span> <span class="n">node</span> <span class="o">//</span> <span class="n">max_lag</span>
        <span class="n">lag</span> <span class="o">=</span> <span class="n">node</span> <span class="o">%</span> <span class="n">max_lag</span>
        <span class="k">return</span> <span class="p">(</span><span class="n">row</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">)</span></div>


<div class="viewcode-block" id="LinearMediation.get_tsg">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_tsg">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_tsg</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_matrix</span><span class="p">,</span> <span class="n">val_matrix</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">include_neighbors</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns time series graph matrix.</span>

<span class="sd">        Constructs a matrix of shape (N*tau_max, N*tau_max) from link_matrix.</span>
<span class="sd">        This matrix can be used for plotting the time series graph and analyzing</span>
<span class="sd">        causal pathways.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        link_matrix : bool array-like, optional (default: None)</span>
<span class="sd">            Matrix of significant links. Must be of same shape as val_matrix.</span>
<span class="sd">            Either sig_thres or link_matrix has to be provided.</span>
<span class="sd">        val_matrix : array_like</span>
<span class="sd">            Matrix of shape (N, N, tau_max+1) containing test statistic values.</span>
<span class="sd">        include_neighbors : bool, optional (default: False)</span>
<span class="sd">            Whether to include causal paths emanating from neighbors of i</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        tsg : array of shape (N*tau_max, N*tau_max)</span>
<span class="sd">            Time series graph matrix.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">N</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">link_matrix</span><span class="p">)</span>
        <span class="n">max_lag</span> <span class="o">=</span> <span class="n">link_matrix</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span>

        <span class="c1"># Create TSG</span>
        <span class="n">tsg</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">N</span> <span class="o">*</span> <span class="n">max_lag</span><span class="p">,</span> <span class="n">N</span> <span class="o">*</span> <span class="n">max_lag</span><span class="p">))</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="ow">in</span> <span class="n">np</span><span class="o">.</span><span class="n">column_stack</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">link_matrix</span><span class="p">)):</span>
            <span class="c1"># if tau &gt; 0 or include_neighbors:</span>
                <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">max_lag</span><span class="p">):</span>
                    <span class="n">link_start</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">net_to_tsg</span><span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">t</span> <span class="o">-</span> <span class="n">tau</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">)</span>
                    <span class="n">link_end</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">net_to_tsg</span><span class="p">(</span><span class="n">j</span><span class="p">,</span> <span class="n">t</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">)</span>
                    <span class="k">if</span> <span class="p">(</span><span class="mi">0</span> <span class="o">&lt;=</span> <span class="n">link_start</span> <span class="ow">and</span>
                            <span class="p">(</span><span class="n">link_start</span> <span class="o">%</span> <span class="n">max_lag</span><span class="p">)</span> <span class="o">&lt;=</span> <span class="p">(</span><span class="n">link_end</span> <span class="o">%</span> <span class="n">max_lag</span><span class="p">)):</span>
                        <span class="k">if</span> <span class="n">val_matrix</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                            <span class="n">tsg</span><span class="p">[</span><span class="n">link_start</span><span class="p">,</span> <span class="n">link_end</span><span class="p">]</span> <span class="o">=</span> <span class="n">val_matrix</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">tau</span><span class="p">]</span>
                        <span class="k">else</span><span class="p">:</span>
                            <span class="n">tsg</span><span class="p">[</span><span class="n">link_start</span><span class="p">,</span> <span class="n">link_end</span><span class="p">]</span> <span class="o">=</span> <span class="mi">1</span>
        <span class="k">return</span> <span class="n">tsg</span></div>


<div class="viewcode-block" id="LinearMediation.get_mediation_graph_data">
<a class="viewcode-back" href="../../index.html#tigramite.models.LinearMediation.get_mediation_graph_data">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_mediation_graph_data</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tau</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">include_neighbors</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Returns link and node weights for mediation analysis.</span>

<span class="sd">        Returns array with non-zero entries for links that are on causal</span>
<span class="sd">        paths between :math:`i` and :math:`j` at lag :math:`\tau`.</span>
<span class="sd">        ``path_val_matrix`` contains the corresponding path coefficients and</span>
<span class="sd">        ``path_node_array`` the MCE values. ``tsg_path_val_matrix`` contains the</span>
<span class="sd">        corresponding values in the time series graph format.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        i : int</span>
<span class="sd">            Index of cause variable.</span>
<span class="sd">        tau : int</span>
<span class="sd">            Lag of cause variable.</span>
<span class="sd">        j : int</span>
<span class="sd">            Index of effect variable.</span>
<span class="sd">        include_neighbors : bool, optional (default: False)</span>
<span class="sd">            Whether to include causal paths emanating from neighbors of i</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        graph_data : dictionary</span>
<span class="sd">            Dictionary of matrices for coloring mediation graph plots.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">path_link_matrix</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">))</span>
        <span class="n">path_val_matrix</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">))</span>

        <span class="c1"># Get mediation of path variables</span>
        <span class="n">path_node_array</span> <span class="o">=</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">psi</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
                           <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_psi_k</span><span class="p">)[:,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">),</span> <span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">]</span>

        <span class="c1"># Get involved links</span>
        <span class="n">val_matrix</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">phi</span><span class="o">.</span><span class="n">transpose</span><span class="p">()</span>
        <span class="n">link_matrix</span> <span class="o">=</span> <span class="n">val_matrix</span> <span class="o">!=</span> <span class="mf">0.</span>

        <span class="n">max_lag</span> <span class="o">=</span> <span class="n">link_matrix</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span>

        <span class="c1"># include_neighbors = False because True would allow</span>
        <span class="c1"># --&gt; o -- motifs in networkx.all_simple_paths as paths, but</span>
        <span class="c1"># these are blocked...</span>
        <span class="n">tsg</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_tsg</span><span class="p">(</span><span class="n">link_matrix</span><span class="p">,</span> <span class="n">val_matrix</span><span class="o">=</span><span class="n">val_matrix</span><span class="p">,</span>
                           <span class="n">include_neighbors</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">include_neighbors</span><span class="p">:</span>
            <span class="c1"># Add contemporaneous links only at source node</span>
            <span class="k">for</span> <span class="n">m</span><span class="p">,</span> <span class="n">n</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">link_matrix</span><span class="p">[:,</span> <span class="p">:,</span> <span class="mi">0</span><span class="p">])):</span>
                <span class="c1"># print m,n</span>
                <span class="k">if</span> <span class="n">m</span> <span class="o">!=</span> <span class="n">n</span><span class="p">:</span>
                    <span class="n">tsg</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">net_to_tsg</span><span class="p">(</span><span class="n">m</span><span class="p">,</span> <span class="n">max_lag</span> <span class="o">-</span> <span class="n">tau</span> <span class="o">-</span> <span class="mi">1</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">),</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">net_to_tsg</span><span class="p">(</span><span class="n">n</span><span class="p">,</span> <span class="n">max_lag</span> <span class="o">-</span> <span class="n">tau</span> <span class="o">-</span> <span class="mi">1</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">)</span>
                    <span class="p">]</span> <span class="o">=</span> <span class="n">val_matrix</span><span class="p">[</span><span class="n">m</span><span class="p">,</span> <span class="n">n</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span>

        <span class="n">tsg_path_val_matrix</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">tsg</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>

        <span class="n">graph</span> <span class="o">=</span> <span class="n">networkx</span><span class="o">.</span><span class="n">DiGraph</span><span class="p">(</span><span class="n">tsg</span><span class="p">)</span>
        <span class="n">pathways</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">networkx</span><span class="o">.</span><span class="n">all_simple_paths</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span>
                                              <span class="n">source</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">net_to_tsg</span><span class="p">(</span><span class="n">i</span><span class="p">,</span>
                                                                     <span class="n">max_lag</span> <span class="o">-</span> <span class="n">tau</span> <span class="o">-</span> <span class="mi">1</span><span class="p">,</span>
                                                                     <span class="n">max_lag</span><span class="p">),</span>
                                              <span class="n">target</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">net_to_tsg</span><span class="p">(</span><span class="n">j</span><span class="p">,</span>
                                                                     <span class="n">max_lag</span> <span class="o">-</span> <span class="mi">0</span> <span class="o">-</span> <span class="mi">1</span><span class="p">,</span>
                                                                     <span class="n">max_lag</span><span class="p">)):</span>
            <span class="n">pathways</span><span class="o">.</span><span class="n">append</span><span class="p">([</span><span class="bp">self</span><span class="o">.</span><span class="n">tsg_to_net</span><span class="p">(</span><span class="n">p</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">)</span> <span class="k">for</span> <span class="n">p</span> <span class="ow">in</span> <span class="n">path</span><span class="p">])</span>
            <span class="k">for</span> <span class="n">ip</span><span class="p">,</span> <span class="n">p</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">path</span><span class="p">[</span><span class="mi">1</span><span class="p">:]):</span>
                <span class="n">tsg_path_val_matrix</span><span class="p">[</span><span class="n">path</span><span class="p">[</span><span class="n">ip</span><span class="p">],</span> <span class="n">p</span><span class="p">]</span> <span class="o">=</span> <span class="n">tsg</span><span class="p">[</span><span class="n">path</span><span class="p">[</span><span class="n">ip</span><span class="p">],</span> <span class="n">p</span><span class="p">]</span>

                <span class="n">k</span><span class="p">,</span> <span class="n">tau_k</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">tsg_to_net</span><span class="p">(</span><span class="n">p</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">)</span>
                <span class="n">link_start</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">tsg_to_net</span><span class="p">(</span><span class="n">path</span><span class="p">[</span><span class="n">ip</span><span class="p">],</span> <span class="n">max_lag</span><span class="p">)</span>
                <span class="n">link_end</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">tsg_to_net</span><span class="p">(</span><span class="n">p</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">)</span>
                <span class="n">delta_tau</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">link_end</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">-</span> <span class="n">link_start</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
                <span class="n">path_val_matrix</span><span class="p">[</span><span class="n">link_start</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                                <span class="n">link_end</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                                <span class="n">delta_tau</span><span class="p">]</span> <span class="o">=</span> <span class="n">val_matrix</span><span class="p">[</span><span class="n">link_start</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                                                        <span class="n">link_end</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                                                        <span class="n">delta_tau</span><span class="p">]</span>

        <span class="n">graph_data</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;path_node_array&#39;</span><span class="p">:</span> <span class="n">path_node_array</span><span class="p">,</span>
                      <span class="s1">&#39;path_val_matrix&#39;</span><span class="p">:</span> <span class="n">path_val_matrix</span><span class="p">,</span>
                      <span class="s1">&#39;tsg_path_val_matrix&#39;</span><span class="p">:</span> <span class="n">tsg_path_val_matrix</span><span class="p">}</span>

        <span class="k">return</span> <span class="n">graph_data</span></div>
</div>



<div class="viewcode-block" id="Prediction">
<a class="viewcode-back" href="../../index.html#tigramite.models.Prediction">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">Prediction</span><span class="p">(</span><span class="n">Models</span><span class="p">,</span> <span class="n">PCMCI</span><span class="p">):</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Prediction class for time series models.</span>

<span class="sd">    Allows to fit and predict from any sklearn model. The optimal predictors can</span>
<span class="sd">    be estimated using PCMCI. Also takes care of missing values, masking and</span>
<span class="sd">    preprocessing.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    dataframe : data object</span>
<span class="sd">        Tigramite dataframe object. It must have the attributes dataframe.values</span>
<span class="sd">        yielding a numpy array of shape (observations T, variables N) and</span>
<span class="sd">        optionally a mask of the same shape and a missing values flag.</span>
<span class="sd">    train_indices : array-like</span>
<span class="sd">        Either boolean array or time indices marking the training data.</span>
<span class="sd">    test_indices : array-like</span>
<span class="sd">        Either boolean array or time indices marking the test data.</span>
<span class="sd">    prediction_model : sklearn model object</span>
<span class="sd">        For example, sklearn.linear_model.LinearRegression() for a linear</span>
<span class="sd">        regression model.</span>
<span class="sd">    cond_ind_test : Conditional independence test object, optional</span>
<span class="sd">        Only needed if predictors are estimated with causal algorithm.</span>
<span class="sd">        The class will be initialized with masking set to the training data.</span>
<span class="sd">    data_transform : sklearn preprocessing object, optional (default: None)</span>
<span class="sd">        Used to transform data prior to fitting. For example,</span>
<span class="sd">        sklearn.preprocessing.StandardScaler for simple standardization. The</span>
<span class="sd">        fitted parameters are stored.</span>
<span class="sd">    verbosity : int, optional (default: 0)</span>
<span class="sd">        Level of verbosity.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">dataframe</span><span class="p">,</span>
                 <span class="n">train_indices</span><span class="p">,</span>
                 <span class="n">test_indices</span><span class="p">,</span>
                 <span class="n">prediction_model</span><span class="p">,</span>
                 <span class="n">cond_ind_test</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">data_transform</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>

        <span class="k">if</span> <span class="n">dataframe</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">!=</span> <span class="s1">&#39;single&#39;</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Prediction class currently only supports single &quot;</span>
                             <span class="s2">&quot;datasets.&quot;</span><span class="p">)</span>

        <span class="c1"># dataframe.values = {0: dataframe.values[0]}</span>

        <span class="c1"># Default value for the mask</span>
        <span class="k">if</span> <span class="n">dataframe</span><span class="o">.</span><span class="n">mask</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">mask</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="n">dataframe</span><span class="o">.</span><span class="n">mask</span><span class="p">[</span><span class="mi">0</span><span class="p">]}</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">mask</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">dataframe</span><span class="o">.</span><span class="n">values</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;bool&#39;</span><span class="p">)}</span>
        <span class="c1"># Get the dataframe shape</span>
        <span class="n">T</span> <span class="o">=</span> <span class="n">dataframe</span><span class="o">.</span><span class="n">T</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="c1"># Have the default dataframe be the training data frame</span>
        <span class="n">train_mask</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">mask</span><span class="p">)</span>
        <span class="n">train_mask</span><span class="p">[</span><span class="mi">0</span><span class="p">][[</span><span class="n">t</span> <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">T</span><span class="p">)</span> <span class="k">if</span> <span class="n">t</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">train_indices</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">dataframe</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">mask</span> <span class="o">=</span> <span class="n">train_mask</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">_initialized_from</span> <span class="o">=</span> <span class="s1">&#39;dict&#39;</span>
                 <span class="c1"># = DataFrame(dataframe.values[0],</span>
                 <span class="c1">#                   mask=train_mask,</span>
                 <span class="c1">#                   missing_flag=dataframe.missing_flag)</span>
        <span class="c1"># Initialize the models baseclass with the training dataframe</span>
        <span class="n">Models</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                        <span class="n">dataframe</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="p">,</span>
                        <span class="n">model</span><span class="o">=</span><span class="n">prediction_model</span><span class="p">,</span>
                        <span class="n">data_transform</span><span class="o">=</span><span class="n">data_transform</span><span class="p">,</span>
                        <span class="n">mask_type</span><span class="o">=</span><span class="s1">&#39;y&#39;</span><span class="p">,</span>
                        <span class="n">verbosity</span><span class="o">=</span><span class="n">verbosity</span><span class="p">)</span>

        <span class="c1"># Build the testing dataframe as well</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">test_mask</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">mask</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">test_mask</span><span class="p">[</span><span class="mi">0</span><span class="p">][[</span><span class="n">t</span> <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">T</span><span class="p">)</span> <span class="k">if</span> <span class="n">t</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">test_indices</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">True</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">train_indices</span> <span class="o">=</span> <span class="n">train_indices</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">test_indices</span> <span class="o">=</span> <span class="n">test_indices</span>

        <span class="c1"># Setup the PCMCI instance</span>
        <span class="k">if</span> <span class="n">cond_ind_test</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># Force the masking</span>
            <span class="n">cond_ind_test</span><span class="o">.</span><span class="n">set_mask_type</span><span class="p">(</span><span class="s1">&#39;y&#39;</span><span class="p">)</span>
            <span class="n">cond_ind_test</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">=</span> <span class="n">verbosity</span>
            <span class="c1"># PCMCI.__init__(self,</span>
            <span class="c1">#                dataframe=self.dataframe,</span>
            <span class="c1">#                cond_ind_test=cond_ind_test,</span>
            <span class="c1">#                verbosity=verbosity)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">pcmci</span> <span class="o">=</span> <span class="n">PCMCI</span><span class="p">(</span><span class="n">dataframe</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="p">,</span>
                               <span class="n">cond_ind_test</span><span class="o">=</span><span class="n">cond_ind_test</span><span class="p">,</span>
                               <span class="n">verbosity</span><span class="o">=</span><span class="n">verbosity</span><span class="p">)</span>

        <span class="c1"># Set the member variables</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">cond_ind_test</span> <span class="o">=</span> <span class="n">cond_ind_test</span>
        <span class="c1"># Initialize member varialbes that are set outside</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">target_predictors</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">selected_targets</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">fitted_model</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">test_array</span> <span class="o">=</span> <span class="kc">None</span>

<div class="viewcode-block" id="Prediction.get_predictors">
<a class="viewcode-back" href="../../index.html#tigramite.models.Prediction.get_predictors">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_predictors</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                       <span class="n">selected_targets</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                       <span class="n">selected_links</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                       <span class="n">steps_ahead</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                       <span class="n">tau_max</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                       <span class="n">pc_alpha</span><span class="o">=</span><span class="mf">0.2</span><span class="p">,</span>
                       <span class="n">max_conds_dim</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                       <span class="n">max_combinations</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Estimate predictors using PC1 algorithm.</span>

<span class="sd">        Wrapper around PCMCI.run_pc_stable that estimates causal predictors.</span>
<span class="sd">        The lead time can be specified by ``steps_ahead``.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        selected_targets : list of ints, optional (default: None)</span>
<span class="sd">            List of variables to estimate predictors of. If None, predictors of</span>
<span class="sd">            all variables are estimated.</span>
<span class="sd">        selected_links : dict or None</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...}</span>
<span class="sd">            specifying whether only selected links should be tested. If None is</span>
<span class="sd">            passed, all links are tested</span>
<span class="sd">        steps_ahead : int, default: 1</span>
<span class="sd">            Minimum time lag to test. Useful for multi-step ahead predictions.</span>
<span class="sd">        tau_max : int, default: 1</span>
<span class="sd">            Maximum time lag. Must be larger or equal to tau_min.</span>
<span class="sd">        pc_alpha : float or list of floats, default: 0.2</span>
<span class="sd">            Significance level in algorithm. If a list or None is passed, the</span>
<span class="sd">            pc_alpha level is optimized for every variable across the given</span>
<span class="sd">            pc_alpha values using the score computed in</span>
<span class="sd">            cond_ind_test.get_model_selection_criterion()</span>
<span class="sd">        max_conds_dim : int or None</span>
<span class="sd">            Maximum number of conditions to test. If None is passed, this number</span>
<span class="sd">            is unrestricted.</span>
<span class="sd">        max_combinations : int, default: 1</span>
<span class="sd">            Maximum number of combinations of conditions of current cardinality</span>
<span class="sd">            to test. Defaults to 1 for PC_1 algorithm. For original PC algorithm</span>
<span class="sd">            a larger number, such as 10, can be used.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        predictors : dict</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...}</span>
<span class="sd">            containing estimated predictors.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">selected_links</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">link_assumptions</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="n">selected_links</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span>
                <span class="n">link_assumptions</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">=</span> <span class="p">{(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">):</span><span class="s2">&quot;-?&gt;&quot;</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span> <span class="k">for</span> <span class="n">tau</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">+</span><span class="mi">1</span><span class="p">)}</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">link_assumptions</span> <span class="o">=</span> <span class="kc">None</span>

        <span class="c1"># Ensure an independence model is given</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">cond_ind_test</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;No cond_ind_test given!&quot;</span><span class="p">)</span>
        <span class="c1"># Set the selected variables</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span> <span class="o">=</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">selected_targets</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">selected_variables</span> <span class="o">=</span> <span class="n">selected_targets</span>
        
        <span class="n">predictors</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">pcmci</span><span class="o">.</span><span class="n">run_pc_stable</span><span class="p">(</span><span class="n">link_assumptions</span><span class="o">=</span><span class="n">link_assumptions</span><span class="p">,</span>
                                        <span class="n">tau_min</span><span class="o">=</span><span class="n">steps_ahead</span><span class="p">,</span>
                                        <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                                        <span class="n">save_iterations</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                        <span class="n">pc_alpha</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">,</span>
                                        <span class="n">max_conds_dim</span><span class="o">=</span><span class="n">max_conds_dim</span><span class="p">,</span>
                                        <span class="n">max_combinations</span><span class="o">=</span><span class="n">max_combinations</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">predictors</span></div>


<div class="viewcode-block" id="Prediction.fit">
<a class="viewcode-back" href="../../index.html#tigramite.models.Prediction.fit">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">fit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">target_predictors</span><span class="p">,</span>
            <span class="n">selected_targets</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">return_data</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Fit time series model.</span>

<span class="sd">        Wrapper around ``Models.fit_full_model()``. To each variable in</span>
<span class="sd">        ``selected_targets``, the sklearn model is fitted with :math:`y` given</span>
<span class="sd">        by the target variable, and :math:`X` given by its predictors. The</span>
<span class="sd">        fitted model class is returned for later use.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        target_predictors : dictionary</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...} containing</span>
<span class="sd">            the predictors estimated with PCMCI.</span>
<span class="sd">        selected_targets : list of integers, optional (default: range(N))</span>
<span class="sd">            Specify to fit model only for selected targets. If None is</span>
<span class="sd">            passed, models are estimated for all variables.</span>
<span class="sd">        tau_max : int, optional (default: None)</span>
<span class="sd">            Maximum time lag. If None, the maximum lag in target_predictors is</span>
<span class="sd">            used.</span>
<span class="sd">        return_data : bool, optional (default: False)</span>
<span class="sd">            Whether to save the data array.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        self : instance of self</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">selected_targets</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">selected_targets</span> <span class="o">=</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">selected_targets</span> <span class="o">=</span> <span class="n">selected_targets</span>

        <span class="k">if</span> <span class="n">tau_max</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># Find the maximal parents lag</span>
            <span class="n">max_parents_lag</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">selected_targets</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">target_predictors</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                    <span class="n">this_parent_lag</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">target_predictors</span><span class="p">[</span><span class="n">j</span><span class="p">])[:,</span> <span class="mi">1</span><span class="p">])</span><span class="o">.</span><span class="n">max</span><span class="p">()</span>
                    <span class="n">max_parents_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">max_parents_lag</span><span class="p">,</span> <span class="n">this_parent_lag</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">max_parents_lag</span> <span class="o">=</span> <span class="n">tau_max</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">test_indices</span><span class="p">)</span> <span class="o">-</span> <span class="n">max_parents_lag</span><span class="p">)</span>
                <span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">train_indices</span><span class="p">))</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;test_indices - maxlag(predictors) [or tau_max] &quot;</span>
                <span class="s2">&quot;overlaps with train_indices: Choose test_indices &quot;</span>
                <span class="s2">&quot;such that there is a gap of max_lag to train_indices!&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">target_predictors</span> <span class="o">=</span> <span class="n">target_predictors</span>

        <span class="k">for</span> <span class="n">target</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">selected_targets</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">target</span> <span class="ow">not</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">target_predictors</span><span class="p">):</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;No predictors given for target </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="n">target</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">fitted_model</span> <span class="o">=</span> \
            <span class="bp">self</span><span class="o">.</span><span class="n">fit_full_model</span><span class="p">(</span><span class="n">all_parents</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">target_predictors</span><span class="p">,</span>
                         <span class="n">selected_variables</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">selected_targets</span><span class="p">,</span>
                         <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                         <span class="n">return_data</span><span class="o">=</span><span class="n">return_data</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span></div>


<div class="viewcode-block" id="Prediction.predict">
<a class="viewcode-back" href="../../index.html#tigramite.models.Prediction.predict">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">predict</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">target</span><span class="p">,</span>
                <span class="n">new_data</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">pred_params</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;max_lag_or_tau_max&#39;</span><span class="p">):</span>
<span class="w">        </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Predict target variable with fitted model.</span>

<span class="sd">        Uses the model.predict() function of the sklearn model.</span>

<span class="sd">        If target is an int, the predicted time series is returned. If target</span>
<span class="sd">        is a list of integers, then a list of predicted time series is returned.</span>
<span class="sd">        If the list of integers equals range(N), then an array of shape (T, N)</span>
<span class="sd">        of the predicted series is returned.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        target : int or list of integers</span>
<span class="sd">            Index or indices of target variable(s).</span>
<span class="sd">        new_data : data object, optional</span>
<span class="sd">            New Tigramite dataframe object with optional new mask. Note that</span>
<span class="sd">            the data will be cut off according to cut_off, see parameter</span>
<span class="sd">            `cut_off` below.</span>
<span class="sd">        pred_params : dict, optional</span>
<span class="sd">            Optional parameters passed on to sklearn prediction function.</span>
<span class="sd">        cut_off : {&#39;2xtau_max&#39;, &#39;max_lag&#39;, &#39;max_lag_or_tau_max&#39;}</span>
<span class="sd">            How many samples to cutoff at the beginning. The default is</span>
<span class="sd">            &#39;2xtau_max&#39;, which guarantees that MCI tests are all conducted on</span>
<span class="sd">            the same samples.  For modeling, &#39;max_lag_or_tau_max&#39; can be used,</span>
<span class="sd">            which uses the maximum of tau_max and the conditions, which is</span>
<span class="sd">            useful to compare multiple models on the same sample. Last,</span>
<span class="sd">            &#39;max_lag&#39; uses as much samples as possible.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        Results from prediction.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">target</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="n">target_list</span> <span class="o">=</span> <span class="p">[</span><span class="n">target</span><span class="p">]</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">target</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
            <span class="n">target_list</span> <span class="o">=</span> <span class="n">target</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;target must be either int or list of integers &quot;</span>
                             <span class="s2">&quot;indicating the index of the variables to &quot;</span>
                             <span class="s2">&quot;predict.&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">target_list</span> <span class="o">==</span> <span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)):</span>
            <span class="n">return_type</span> <span class="o">=</span> <span class="s1">&#39;array&#39;</span>
        <span class="k">elif</span> <span class="nb">len</span><span class="p">(</span><span class="n">target_list</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="n">return_type</span> <span class="o">=</span> <span class="s1">&#39;series&#39;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">return_type</span> <span class="o">=</span> <span class="s1">&#39;list&#39;</span>

        <span class="n">pred_list</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">stored_test_array</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">target</span> <span class="ow">in</span> <span class="n">target_list</span><span class="p">:</span>
            <span class="c1"># Print message</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">## Predicting target </span><span class="si">%s</span><span class="se">\n</span><span class="s2">##&quot;</span> <span class="o">%</span> <span class="n">target</span><span class="p">)</span>
                <span class="k">if</span> <span class="n">pred_params</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">key</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="n">pred_params</span><span class="p">):</span>
                        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">pred_params</span><span class="p">[</span><span class="n">key</span><span class="p">]))</span>
            <span class="c1"># Default value for pred_params</span>
            <span class="k">if</span> <span class="n">pred_params</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">pred_params</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="c1"># Check this is a valid target</span>
            <span class="k">if</span> <span class="n">target</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">selected_targets</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Target </span><span class="si">%s</span><span class="s2"> not yet fitted&quot;</span> <span class="o">%</span> <span class="n">target</span><span class="p">)</span>
            <span class="c1"># Construct the array form of the data</span>
            <span class="n">Y</span> <span class="o">=</span> <span class="p">[(</span><span class="n">target</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span>  <span class="c1"># dummy</span>
            <span class="n">X</span> <span class="o">=</span> <span class="p">[(</span><span class="n">target</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span>  <span class="c1"># dummy</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">target_predictors</span><span class="p">[</span><span class="n">target</span><span class="p">]</span>

            <span class="c1"># Check if we&#39;ve passed a new dataframe object</span>
            <span class="k">if</span> <span class="n">new_data</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="c1"># if new_data.mask is None:</span>
                <span class="c1">#     # if no mask is supplied, use the same mask as for the fitted array</span>
                <span class="c1">#     new_data_mask = self.test_mask</span>
                <span class="c1"># else:</span>
                <span class="n">new_data_mask</span> <span class="o">=</span> <span class="n">new_data</span><span class="o">.</span><span class="n">mask</span>
                <span class="n">test_array</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">new_data</span><span class="o">.</span><span class="n">construct_array</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span>
                                                         <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                                                         <span class="n">mask</span><span class="o">=</span><span class="n">new_data_mask</span><span class="p">,</span>
                                                         <span class="n">mask_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">,</span>
                                                         <span class="n">cut_off</span><span class="o">=</span><span class="n">cut_off</span><span class="p">,</span>
                                                         <span class="n">remove_overlaps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                                         <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>
            <span class="c1"># Otherwise use the default values</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">test_array</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> \
                    <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">construct_array</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span>
                                                   <span class="n">tau_max</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="p">,</span>
                                                   <span class="n">mask</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">test_mask</span><span class="p">,</span>
                                                   <span class="n">mask_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">,</span>
                                                   <span class="n">cut_off</span><span class="o">=</span><span class="n">cut_off</span><span class="p">,</span>
                                                   <span class="n">remove_overlaps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                                   <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>
            <span class="c1"># Transform the data if needed</span>
            <span class="n">a_transform</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fitted_model</span><span class="p">[</span><span class="n">target</span><span class="p">][</span><span class="s1">&#39;data_transform&#39;</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">a_transform</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">test_array</span> <span class="o">=</span> <span class="n">a_transform</span><span class="o">.</span><span class="n">transform</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">test_array</span><span class="o">.</span><span class="n">T</span><span class="p">)</span><span class="o">.</span><span class="n">T</span>
            <span class="c1"># Cache the test array</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">stored_test_array</span><span class="p">[</span><span class="n">target</span><span class="p">]</span> <span class="o">=</span> <span class="n">test_array</span>
            <span class="c1"># Run the predictor</span>
            <span class="n">predicted</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">fitted_model</span><span class="p">[</span><span class="n">target</span><span class="p">][</span><span class="s1">&#39;model&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">predict</span><span class="p">(</span>
                <span class="n">X</span><span class="o">=</span><span class="n">test_array</span><span class="p">[</span><span class="mi">2</span><span class="p">:]</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="o">**</span><span class="n">pred_params</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">test_array</span><span class="p">[</span><span class="mi">2</span><span class="p">:]</span><span class="o">.</span><span class="n">size</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="c1"># If there are no predictors, return the value of </span>
                <span class="c1"># empty_predictors_function, which is np.mean </span>
                <span class="c1"># and expand to the test array length</span>
                <span class="n">predicted</span> <span class="o">=</span> <span class="n">predicted</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">test_array</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>

            <span class="n">pred_list</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">predicted</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">return_type</span> <span class="o">==</span> <span class="s1">&#39;series&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">pred_list</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
        <span class="k">elif</span> <span class="n">return_type</span> <span class="o">==</span> <span class="s1">&#39;list&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">pred_list</span>
        <span class="k">elif</span> <span class="n">return_type</span> <span class="o">==</span> <span class="s1">&#39;array&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">pred_list</span><span class="p">)</span><span class="o">.</span><span class="n">transpose</span><span class="p">()</span></div>


<div class="viewcode-block" id="Prediction.get_train_array">
<a class="viewcode-back" href="../../index.html#tigramite.models.Prediction.get_train_array">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_train_array</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">j</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns training array for variable j.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">fitted_model</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="s1">&#39;data&#39;</span><span class="p">]</span></div>


<div class="viewcode-block" id="Prediction.get_test_array">
<a class="viewcode-back" href="../../index.html#tigramite.models.Prediction.get_test_array">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_test_array</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">j</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns test array for variable j.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">stored_test_array</span><span class="p">[</span><span class="n">j</span><span class="p">]</span></div>
</div>


<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
   
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.data_processing</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">pp</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.toymodels</span><span class="w"> </span><span class="kn">import</span> <span class="n">structural_causal_processes</span> <span class="k">as</span> <span class="n">toys</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.independence_tests.parcorr</span><span class="w"> </span><span class="kn">import</span> <span class="n">ParCorr</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.plotting</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">tp</span>

    <span class="kn">from</span><span class="w"> </span><span class="nn">sklearn.linear_model</span><span class="w"> </span><span class="kn">import</span> <span class="n">LinearRegression</span><span class="p">,</span> <span class="n">LogisticRegression</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">sklearn.multioutput</span><span class="w"> </span><span class="kn">import</span> <span class="n">MultiOutputRegressor</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">lin_f</span><span class="p">(</span><span class="n">x</span><span class="p">):</span> <span class="k">return</span> <span class="n">x</span>
 

    <span class="n">T</span> <span class="o">=</span> <span class="mi">1000</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">lin_f</span><span class="p">(</span><span class="n">x</span><span class="p">):</span> <span class="k">return</span> <span class="n">x</span>
    <span class="n">auto_coeff</span> <span class="o">=</span> <span class="mf">0.</span>
    <span class="n">coeff</span> <span class="o">=</span> <span class="mf">2.</span>
    <span class="n">links</span> <span class="o">=</span> <span class="p">{</span>
            <span class="mi">0</span><span class="p">:</span> <span class="p">[((</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="n">auto_coeff</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">)],</span> 
            <span class="mi">1</span><span class="p">:</span> <span class="p">[((</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="n">auto_coeff</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">),</span> <span class="p">((</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="n">coeff</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">)],</span>
            <span class="p">}</span>
    <span class="n">data</span><span class="p">,</span> <span class="n">nonstat</span> <span class="o">=</span> <span class="n">toys</span><span class="o">.</span><span class="n">structural_causal_process</span><span class="p">(</span><span class="n">links</span><span class="p">,</span> <span class="n">T</span><span class="o">=</span><span class="n">T</span><span class="p">,</span> 
                                <span class="n">noises</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">seed</span><span class="o">=</span><span class="mi">7</span><span class="p">)</span>

    <span class="c1"># data[:,1] = data[:,1] &gt; 0.</span>

    <span class="c1"># # Create some missing values</span>
    <span class="c1"># data[-10:,:] = 999.</span>
    <span class="c1"># var_names = range(2)</span>

    <span class="c1"># graph = np.array([[&#39;&#39;, &#39;--&gt;&#39;],</span>
    <span class="c1">#                   [&#39;&lt;--&#39;, &#39;&#39;]], </span>
    <span class="c1">#                   dtype=&#39;&lt;U3&#39;)</span>
    <span class="nb">print</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">data</span><span class="o">.</span><span class="n">mean</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">))</span>
    <span class="n">dataframe</span> <span class="o">=</span> <span class="n">pp</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">(</span><span class="n">data</span><span class="p">,</span>
                    <span class="c1"># vector_vars={0:[(0,0), (1,0)], 1:[(2,0), (3,0)]}</span>
                    <span class="p">)</span> 
    <span class="n">graph</span> <span class="o">=</span> <span class="n">toys</span><span class="o">.</span><span class="n">links_to_graph</span><span class="p">(</span><span class="n">links</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span>
    
    <span class="c1"># # We are interested in lagged total effect of X on Y</span>
    <span class="n">X</span> <span class="o">=</span> <span class="p">[(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">)]</span>
    <span class="n">Y</span> <span class="o">=</span> <span class="p">[(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">)]</span>

    <span class="n">model</span> <span class="o">=</span> <span class="n">Models</span><span class="p">(</span><span class="n">dataframe</span><span class="o">=</span><span class="n">dataframe</span><span class="p">,</span> 
        <span class="n">model</span> <span class="o">=</span> <span class="n">LinearRegression</span><span class="p">(),</span>
        <span class="c1"># model = LogisticRegression(),</span>
        <span class="c1"># model = MultiOutputRegressor(LogisticRegression()),</span>

        <span class="p">)</span>

    <span class="n">model</span><span class="o">.</span><span class="n">get_general_fitted_model</span><span class="p">(</span> 
                    <span class="n">Y</span><span class="o">=</span><span class="n">Y</span><span class="p">,</span> <span class="n">X</span><span class="o">=</span><span class="n">X</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="p">[(</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">2</span><span class="p">)],</span>
                    <span class="n">conditions</span><span class="o">=</span><span class="p">[(</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">3</span><span class="p">)],</span>
                    <span class="n">tau_max</span><span class="o">=</span><span class="mi">7</span><span class="p">,</span>
                    <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;tau_max&#39;</span><span class="p">,</span>
                    <span class="n">empty_predictors_function</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">mean</span><span class="p">,</span>
                    <span class="n">return_data</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>

    <span class="c1"># print(model.fit_results[(1, 0)][&#39;model&#39;].coef_)</span>

    <span class="n">dox_vals</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mf">0.</span><span class="p">])</span>   <span class="c1">#np.linspace(-1., 1., 1)</span>
    <span class="n">intervention_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">tile</span><span class="p">(</span><span class="n">dox_vals</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">dox_vals</span><span class="p">),</span> <span class="mi">1</span><span class="p">),</span> <span class="nb">len</span><span class="p">(</span><span class="n">X</span><span class="p">))</span>

    <span class="n">conditions_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">tile</span><span class="p">(</span><span class="mf">1.</span> <span class="o">+</span> <span class="n">dox_vals</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">dox_vals</span><span class="p">),</span> <span class="mi">1</span><span class="p">),</span> <span class="mi">1</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">aggregation_func</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">bins</span><span class="o">=</span><span class="mi">2</span><span class="p">):</span>
        <span class="n">x</span> <span class="o">=</span> <span class="n">x</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;int64&#39;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">apply_along_axis</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">bincount</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="n">axis</span><span class="p">,</span> <span class="n">arr</span><span class="o">=</span><span class="n">x</span><span class="p">,</span> <span class="n">minlength</span><span class="o">=</span><span class="n">bins</span><span class="p">)</span><span class="o">.</span><span class="n">T</span>
    <span class="n">aggregation_func</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">mean</span>

    <span class="n">pred</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">get_general_prediction</span><span class="p">(</span>
                <span class="n">intervention_data</span><span class="o">=</span><span class="n">intervention_data</span><span class="p">,</span>
                <span class="n">conditions_data</span><span class="o">=</span><span class="n">conditions_data</span><span class="p">,</span>
                <span class="n">pred_params</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                <span class="n">transform_interventions_and_prediction</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                <span class="n">return_further_pred_results</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                <span class="n">aggregation_func</span><span class="o">=</span><span class="n">aggregation_func</span><span class="p">,</span>
                <span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">pred</span><span class="p">)</span>

    <span class="c1"># T = 1000</span>
    
    <span class="c1"># links = {0: [((0, -1), 0.9, lin_f)],</span>
    <span class="c1">#          1: [((1, -1), 0.9, lin_f), ((0, 0), -0.8, lin_f)],</span>
    <span class="c1">#          2: [((2, -1), 0.9, lin_f), ((0, 0), 0.9, lin_f),  ((1, 0), 0.8, lin_f)],</span>
    <span class="c1">#          # 3: [((3, -1), 0.9, lin_f), ((1, 0), 0.8, lin_f),  ((2, 0), -0.9, lin_f)]</span>
    <span class="c1">#          }</span>
    <span class="c1"># # noises = [np.random.randn for j in links.keys()]</span>
    <span class="c1"># data, nonstat = toys.structural_causal_process(links, T=T, noises=None, seed=7)</span>

    <span class="c1"># missing_flag = 999</span>
    <span class="c1"># for i in range(0, 20):</span>
    <span class="c1">#     data[i::100] = missing_flag</span>

    <span class="c1"># # mask = data&gt;0</span>

    <span class="c1"># parents = toys._get_true_parent_neighbor_dict(links)</span>
    <span class="c1"># dataframe = pp.DataFrame(data,  missing_flag = missing_flag)</span>



    <span class="c1"># model = LinearRegression()</span>
    <span class="c1"># model.fit(X=np.random.randn(10,2), y=np.random.randn(10))</span>
    <span class="c1"># model.predict(X=np.random.randn(10,2)[:,2:])</span>
    <span class="c1"># sys.exit(0)</span>

    <span class="c1"># med = LinearMediation(dataframe=dataframe, #mask_type=&#39;y&#39;,</span>
    <span class="c1">#     data_transform=None)</span>
    <span class="c1"># med.fit_model(all_parents=parents, tau_max=None,  return_data=True)</span>

    <span class="c1"># print(med.get_residuals_cov_mean())</span>

    <span class="c1"># med.fit_model_bootstrap( </span>
    <span class="c1">#             boot_blocklength=&#39;cube_root&#39;,</span>
    <span class="c1">#             seed = 42,</span>
    <span class="c1">#             )</span>

    <span class="c1"># # print(med.get_val_matrix())</span>

    <span class="c1"># print (med.get_ce(i=0, tau=0,  j=3))</span>
    <span class="c1"># print(med.get_bootstrap_of(function=&#39;get_ce&#39;, </span>
    <span class="c1">#     function_args={&#39;i&#39;:0, &#39;tau&#39;:0,   &#39;j&#39;:3}, conf_lev=0.9))</span>

    <span class="c1"># print (med.get_coeff(i=0, tau=-2, j=1))</span>

    <span class="c1"># print (med.get_ce_max(i=0, j=2))</span>
    <span class="c1"># print (med.get_ce(i=0, tau=0, j=3))</span>
    <span class="c1"># print (med.get_mce(i=0, tau=0, k=[2], j=3))</span>
    <span class="c1"># print (med.get_mce(i=0, tau=0, k=[1,2], j=3) - med.get_mce(i=0, tau=0, k=[1], j=3))</span>
    <span class="c1"># print (med.get_conditional_mce(i=0, tau=0, k=[2], notk=[1], j=3))</span>
    <span class="c1"># print (med.get_bootstrap_of(&#39;get_conditional_mce&#39;, {&#39;i&#39;:0, &#39;tau&#39;:0, &#39;k&#39;:[2], &#39;notk&#39;:[1], &#39;j&#39;:3}))</span>

    <span class="c1"># print(med.get_joint_ce(i=0, j=2))</span>
    <span class="c1"># print(med.get_joint_mce(i=0, j=2, k=1))</span>

    <span class="c1"># print(med.get_joint_ce_matrix(i=0, j=2))</span>

    <span class="c1"># i=0; tau=4; j=2</span>
    <span class="c1"># graph_data = med.get_mediation_graph_data(i=i, tau=tau, j=j)</span>
    <span class="c1"># tp.plot_mediation_time_series_graph(</span>
    <span class="c1">#     # var_names=var_names,</span>
    <span class="c1">#     path_node_array=graph_data[&#39;path_node_array&#39;],</span>
    <span class="c1">#     tsg_path_val_matrix=graph_data[&#39;tsg_path_val_matrix&#39;]</span>
    <span class="c1">#     )</span>
    <span class="c1"># tp.plot_mediation_graph(</span>
    <span class="c1">#                     # var_names=var_names,</span>
    <span class="c1">#                     path_val_matrix=graph_data[&#39;path_val_matrix&#39;], </span>
    <span class="c1">#                     path_node_array=graph_data[&#39;path_node_array&#39;],</span>
    <span class="c1">#                     ); </span>
    <span class="c1"># plt.show()</span>

    <span class="c1"># print (&quot;Average Causal Effect X=%.2f, Y=%.2f, Z=%.2f &quot; % tuple(med.get_all_ace()))</span>
    <span class="c1"># print (&quot;Average Causal Susceptibility X=%.2f, Y=%.2f, Z=%.2f &quot; % tuple(med.get_all_acs()))</span>
    <span class="c1"># print (&quot;Average Mediated Causal Effect X=%.2f, Y=%.2f, Z=%.2f &quot; % tuple(med.get_all_amce()))</span>
    <span class="c1"># med = Models(dataframe=dataframe, model=sklearn.linear_model.LinearRegression(), data_transform=None)</span>
    <span class="c1"># # Fit the model</span>
    <span class="c1"># med.get_fit(all_parents=true_parents, tau_max=3)</span>

    <span class="c1"># print(med.get_val_matrix())</span>

    <span class="c1"># for j, i, tau, coeff in toys._iter_coeffs(links):</span>
    <span class="c1">#     print(i, j, tau, coeff, med.get_coeff(i=i, tau=tau, j=j))</span>

    <span class="c1"># for causal_coeff in [med.get_ce(i=0, tau=-2, j=2),</span>
    <span class="c1">#                      med.get_mce(i=0, tau=-2, j=2, k=1)]:</span>
    <span class="c1">#     print(causal_coeff)</span>


    <span class="c1"># pred = Prediction(dataframe=dataframe,</span>
    <span class="c1">#         cond_ind_test=ParCorr(),   #CMIknn ParCorr</span>
    <span class="c1">#         prediction_model = sklearn.linear_model.LinearRegression(),</span>
    <span class="c1"># #         prediction_model = sklearn.gaussian_process.GaussianProcessRegressor(),</span>
    <span class="c1">#         # prediction_model = sklearn.neighbors.KNeighborsRegressor(),</span>
    <span class="c1">#     data_transform=sklearn.preprocessing.StandardScaler(),</span>
    <span class="c1">#     train_indices= list(range(int(0.8*T))),</span>
    <span class="c1">#     test_indices= list(range(int(0.8*T), T)),</span>
    <span class="c1">#     verbosity=0</span>
    <span class="c1">#     )</span>

    <span class="c1"># # predictors = pred.get_predictors(</span>
    <span class="c1"># #                        selected_targets=[2],</span>
    <span class="c1"># #                        selected_links=None,</span>
    <span class="c1"># #                        steps_ahead=1,</span>
    <span class="c1"># #                        tau_max=1,</span>
    <span class="c1"># #                        pc_alpha=0.2,</span>
    <span class="c1"># #                        max_conds_dim=None,</span>
    <span class="c1"># #                        max_combinations=1)</span>
    <span class="c1"># predictors = {0: [], # [(0, -1)],</span>
    <span class="c1">#              1: [(1, -1), (0, -1)],</span>
    <span class="c1">#              2: [(2, -1), (1, 0)]}</span>
    <span class="c1"># pred.fit(target_predictors=predictors,</span>
    <span class="c1">#         selected_targets=None, tau_max=None, return_data=False)</span>

    <span class="c1"># res = pred.predict(target=0,</span>
    <span class="c1">#             new_data=None,</span>
    <span class="c1">#             pred_params=None,</span>
    <span class="c1">#             cut_off=&#39;max_lag_or_tau_max&#39;)</span>

    <span class="c1"># print(data[:,2])</span>
    <span class="c1"># print(res)</span>


</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../index.html">Documentation overview</a><ul>
  <li><a href="../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2023, Jakob Runge.
      
      |
      Powered by <a href="https://www.sphinx-doc.org/">Sphinx 8.2.3</a>
      &amp; <a href="https://alabaster.readthedocs.io">Alabaster 0.7.16</a>
      
    </div>

    

    
  </body>
</html>