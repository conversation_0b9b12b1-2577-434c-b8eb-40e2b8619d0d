<!DOCTYPE html>

<html lang="en" data-content_root="../../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.jpcmciplus &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=db26dd79" />
    <link rel="stylesheet" type="text/css" href="../../_static/alabaster.css?v=19da42e6" />
    <script src="../../_static/documentation_options.js?v=625b3a9a"></script>
    <script src="../../_static/doctools.js?v=aa79a7b1"></script>
    <script src="../../_static/sphinx_highlight.js?v=4825356b"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
   
  <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.jpcmciplus</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite causal discovery for time series.&quot;&quot;&quot;</span>

<span class="c1"># Authors: <AUTHORS>
<span class="c1">#</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">__future__</span><span class="w"> </span><span class="kn">import</span> <span class="n">print_function</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">numpy</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">np</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.pcmci</span><span class="w"> </span><span class="kn">import</span> <span class="n">PCMCI</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">copy</span><span class="w"> </span><span class="kn">import</span> <span class="n">deepcopy</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">itertools</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.toymodels.context_model</span><span class="w"> </span><span class="kn">import</span> <span class="n">_group_links</span>


<div class="viewcode-block" id="JPCMCIplus">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">JPCMCIplus</span><span class="p">(</span><span class="n">PCMCI</span><span class="p">):</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;J-PCMCIplus causal discovery for time series datasets from multiple contexts.</span>
<span class="sd">        </span>
<span class="sd">    This class is based on the PCMCI framework as described in</span>
<span class="sd">    [i]. JPCMCIplus enables causal discovery for time series data from</span>
<span class="sd">    different contexts, i.e. datasets, where some of the variables</span>
<span class="sd">    describing the context might be unobserved. The method is described</span>
<span class="sd">    in detail in [ii]. See the tutorial for guidance in applying the</span>
<span class="sd">    method.</span>

<span class="sd">    References</span>
<span class="sd">    ----------</span>
<span class="sd">    .. [i] J. Runge, P. Nowack, M. Kretschmer, S. Flaxman, D. Sejdinovic,</span>
<span class="sd">       Detecting and quantifying causal associations in large nonlinear</span>
<span class="sd">       time series datasets. Sci. Adv. 5, eaau4996</span>
<span class="sd">       (2019) https://advances.sciencemag.org/content/5/11/eaau4996</span>
<span class="sd">    </span>
<span class="sd">    .. [ii] W. Günther, U. Ninad, J. Runge, Causal discovery for time</span>
<span class="sd">       series from multiple datasets with latent contexts. UAI 2023</span>
<span class="sd">    </span>
<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    node_classification : dictionary</span>
<span class="sd">        Classification of nodes into system, context, or dummy nodes.</span>
<span class="sd">        Keys of the dictionary are from {0, ..., N-1} where N is the number of nodes.</span>
<span class="sd">        Options for the values are &quot;system&quot;, &quot;time_context&quot;, &quot;space_context&quot;, &quot;time_dummy&quot;, or &quot;space_dummy&quot;.</span>
<span class="sd">    </span>
<span class="sd">    Attributes</span>
<span class="sd">    ----------</span>
<span class="sd">    all_parents : dictionary</span>
<span class="sd">        Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...} containing</span>
<span class="sd">        the conditioning-parents estimated with PC algorithm.</span>
<span class="sd">    val_min : dictionary</span>
<span class="sd">        Dictionary of form val_min[j][(i, -tau)] = float</span>
<span class="sd">        containing the minimum test statistic value for each link estimated in</span>
<span class="sd">        the PC algorithm.</span>
<span class="sd">    pval_max : dictionary</span>
<span class="sd">        Dictionary of form pval_max[j][(i, -tau)] = float containing the maximum</span>
<span class="sd">        p-value for each link estimated in the PC algorithm.</span>
<span class="sd">    iterations : dictionary</span>
<span class="sd">        Dictionary containing further information on algorithm steps.</span>
<span class="sd">    N : int</span>
<span class="sd">        Number of variables.</span>
<span class="sd">    T : dict</span>
<span class="sd">        Time series sample length of dataset(s).</span>
<span class="sd">    dummy_parents : dictionary or None</span>
<span class="sd">        Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...} containing</span>
<span class="sd">        the dependence of the system nodes on the dummy nodes.</span>
<span class="sd">    observed_context_parents : dictionary or None</span>
<span class="sd">        Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...} containing</span>
<span class="sd">        the dependence of the system nodes on the observed context nodes.</span>
<span class="sd">    dummy_ci_test : conditional independence test object</span>
<span class="sd">        Conditional independence test used to test dependence between system nodes and dummy nodes.</span>
<span class="sd">        Currently, ParCorr is used with one-hot encoded dummies.</span>
<span class="sd">    mode : &quot;system_search&quot; or &quot;context_search&quot; or &quot;dummy_search&quot; (default: &quot;system_search&quot;)</span>
<span class="sd">    time_context_nodes : list</span>
<span class="sd">        List with entries from {0, ..., N-1} where N is the number of nodes.</span>
<span class="sd">        This is the list of the temporal context nodes which are assumed to be constant over the different datasets.</span>
<span class="sd">    space_context_nodes :</span>
<span class="sd">        List with entries from {0, ..., N-1} where N is the number of nodes.</span>
<span class="sd">        This is the list of the spatial context nodes which are assumed to be constant over time.</span>
<span class="sd">    time_dummy : int or None (default: None)</span>
<span class="sd">        Node corresponding to the temporal dummy variable.</span>
<span class="sd">    space_dummy : int or None (default: None)</span>
<span class="sd">        Node corresponding to the spatial dummy variable.</span>
<span class="sd">    system_nodes : list</span>
<span class="sd">        List with entries from {0, ..., N-1} where N is the number of nodes.</span>
<span class="sd">        This is the list of the system nodes.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">node_classification</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="c1"># Init base class</span>
        <span class="n">PCMCI</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">group_nodes</span><span class="p">(</span><span class="n">node_classification</span><span class="p">,</span> <span class="s2">&quot;system&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">group_nodes</span><span class="p">(</span><span class="n">node_classification</span><span class="p">,</span> <span class="s2">&quot;time_context&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">group_nodes</span><span class="p">(</span><span class="n">node_classification</span><span class="p">,</span> <span class="s2">&quot;space_context&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">group_nodes</span><span class="p">(</span><span class="n">node_classification</span><span class="p">,</span> <span class="s2">&quot;time_dummy&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">group_nodes</span><span class="p">(</span><span class="n">node_classification</span><span class="p">,</span> <span class="s2">&quot;space_dummy&quot;</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">dummy_parents</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="p">[]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">observed_context_parents</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="p">[]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">=</span> <span class="s2">&quot;system_search&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">group_nodes</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">node_types</span><span class="p">,</span> <span class="n">node_type</span><span class="p">):</span>
        <span class="n">nodes</span> <span class="o">=</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span>
        <span class="k">return</span> <span class="p">[</span><span class="n">node</span> <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">nodes</span> <span class="k">if</span> <span class="n">node_types</span><span class="p">[</span><span class="n">node</span><span class="p">]</span> <span class="o">==</span> <span class="n">node_type</span><span class="p">]</span>

<div class="viewcode-block" id="JPCMCIplus.run_jpcmciplus">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.run_jpcmciplus">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">run_jpcmciplus</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                       <span class="n">contemp_collider_rule</span><span class="o">=</span><span class="s1">&#39;majority&#39;</span><span class="p">,</span>
                       <span class="n">link_assumptions</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                       <span class="n">tau_min</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                       <span class="n">tau_max</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
                       <span class="n">pc_alpha</span><span class="o">=</span><span class="mf">0.01</span><span class="p">,</span>
                       <span class="n">conflict_resolution</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                       <span class="n">reset_lagged_links</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                       <span class="n">max_conds_dim</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                       <span class="n">max_combinations</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                       <span class="n">max_conds_py</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                       <span class="n">max_conds_px</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                       <span class="n">max_conds_px_lagged</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                       <span class="n">fdr_method</span><span class="o">=</span><span class="s1">&#39;none&#39;</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Runs JPCMCIplus time-lagged and contemporaneous causal discovery for time series from multiple contexts.</span>
<span class="sd">        </span>
<span class="sd">        Method described in: W. Günther, U. Ninad, J. Runge, Causal discovery</span>
<span class="sd">        for time series from multiple datasets with latent contexts. UAI</span>
<span class="sd">        2023</span>
<span class="sd">        </span>
<span class="sd">        Notes</span>
<span class="sd">        -----</span>
<span class="sd">        The JPCMCIplus causal discovery method is described in [ii], where</span>
<span class="sd">        also analytical and numerical results are presented. JPCMCIplus can identify the joint causal graph</span>
<span class="sd">        over multiple datasets containing time series data from different contexts under the standard assumptions</span>
<span class="sd">        of Causal Sufficiency, Faithfulness and the Markov condition, as well as some background knowledge assumptions.</span>
<span class="sd">        JPCMCIplus estimates time-lagged and contemporaneous causal links from context to system</span>
<span class="sd">        variables and in between system variables by a four-step procedure:</span>

<span class="sd">        1.  **Discovery of supersets of the lagged parents of the system and observed temporal context nodes** by</span>
<span class="sd">        running the :math:`PC_1` lagged phase on this subset of nodes to obtain :math:`\\hat{\\mathcal{B}}^-_t(X_t^j)`.</span>

<span class="sd">        2.  Next, the **MCI test is run on pairs of system and context nodes conditional on subsets of system</span>
<span class="sd">        and context**, i.e. perform MCI tests for pairs :math:`((C^j_{t-\\tau}, X^i_t))_{\\tau &gt; 0}`,</span>
<span class="sd">        :math:`(C_t^j, X_t^i)`, :math:`(X_t^i, C_t^j)` for all :math:`i,j`,</span>

<span class="sd">        .. math:: C_{t-\\tau}^i \\perp X_t^j | \\mathbf{S}, \\hat{\\mathcal{B}}^-_t(X_t^j)</span>
<span class="sd">                    \\setminus \\{ C_{t-\\tau}^i \\}, \\hat{\\mathcal{B}}^-_{t-\\tau}(C_{t-\\tau}^i)</span>

<span class="sd">        with :math:`\\mathbf{S}` being a subset of the contemporaneous adjacencies :math:`\\mathcal{A}_t(X_t^j)` and</span>
<span class="sd">        :math:`\\hat{\\mathcal{B}}^-_t(X_t^j)` are the lagged adjacencies from step one. If :math:`C` is a</span>
<span class="sd">        spatial context variable, we only have to test the contemporaneous pairs</span>
<span class="sd">        :math:`(C_t^j, X_t^i)`, :math:`(X_t^i, C_t^j)` for all :math:`i,j`.</span>
<span class="sd">        If :math:`C_t^j` and :math:`X_t^i` are conditionally independent, all lagged links between :math:`C_t^j` and</span>
<span class="sd">        :math:`X^j_{t-\\tau}` are also removed for all :math:`\\tau`.</span>

<span class="sd">        3.  **Perform MCI tests on all system-dummy pairs conditional on the superset of lagged links, the discovered</span>
<span class="sd">        contemporaneous context adjacencies, as well as on subsets of contemporaneous system links**, i.e. test</span>
<span class="sd">        for :math:`(D, X_t^i)`, :math:`(X_t^i, D)` for all :math:`i`, i.e.</span>

<span class="sd">        .. math:: D \\perp X_t^j | \\mathbf{S}, \\hat{\\mathcal{B}}^C_t(X_t^j),</span>

<span class="sd">        where :math:`\\mathbf{S} \\subset \\mathcal{A}_t(X_t^i)` and :math:`\\hat{\\mathcal{B}}^C_t(X_t^j)`</span>
<span class="sd">        are the lagged and contextual adjacencies found in the previous step.</span>
<span class="sd">        If :math:`D` and :math:`X_t^j` are found to be conditionally independence, links between :math:`D` and</span>
<span class="sd">        :math:`X^j_{t-\\tau}` are removed for all :math:`\\tau`.</span>
<span class="sd">        By assumption context node is the parent in all system-context links.</span>

<span class="sd">        4.  Finally, we **perform  MCI tests on all system pairs conditional on discovered lagged, context and dummy</span>
<span class="sd">        adjacencies, as well as on subsets of contemporaneous system links** and **orientation phase**. In more detail,</span>
<span class="sd">        we perform MCI test for pairs :math:`((X^j_{t-\\tau}, X_t^i))_{\\tau &gt; 0}`, :math:`(X_t^i, X_t^j)` for all</span>
<span class="sd">        :math:`i, j`, i.e.</span>

<span class="sd">        .. math:: X^i_{t-\\tau} \\perp X_t^j | \\mathbf{S}, \\hat{\\mathcal{B}}^{CD}_t(X_t^j)</span>
<span class="sd">                    \\setminus \\{ X_{t-\\tau}^i \\},\\hat{\\mathcal{B}}^{CD}_t(X_{t-\\tau}^i)</span>

<span class="sd">        where :math:`\\mathbf{S} \\subset \\mathcal{A}_t(X_t^i)` and :math:`\\hat{\\mathcal{B}}^{CD}_t(X_t^j)`</span>
<span class="sd">        are the lagged, contextual, and dummy adjacencies found in the previous steps.</span>
<span class="sd">        Finally, all remaining edges (without expert knowledge) are oriented using the PCMCI+ orientation phase while</span>
<span class="sd">        making use of all triples involving one context or dummy variable and two system variables as in the non-time</span>
<span class="sd">        series case.</span>

<span class="sd">        JPCMCIplus can be flexibly combined with any kind of conditional</span>
<span class="sd">        independence test statistic adapted to the kind of data (continuous</span>
<span class="sd">        or discrete) and its assumed dependency types. These are available in</span>
<span class="sd">        ``tigramite.independence_tests``.</span>
<span class="sd">        See PCMCIplus for a description of the parameters of JPCMCIplus. Also, guidance on best practices for</span>
<span class="sd">        setting these parameters is given there.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        link_assumptions : dict</span>
<span class="sd">            Dictionary of form {j:{(i, -tau): link_type, ...}, ...} specifying</span>
<span class="sd">            assumptions about links. This initializes the graph with entries</span>
<span class="sd">            graph[i,j,tau] = link_type. For example, graph[i,j,0] = &#39;--&gt;&#39;</span>
<span class="sd">            implies that a directed link from i to j at lag 0 must exist.</span>
<span class="sd">            Valid link types are &#39;o-o&#39;, &#39;--&gt;&#39;, &#39;&lt;--&#39;. In addition, the middle</span>
<span class="sd">            mark can be &#39;?&#39; instead of &#39;-&#39;. Then &#39;-?&gt;&#39; implies that this link</span>
<span class="sd">            may not exist, but if it exists, its orientation is &#39;--&gt;&#39;. Link</span>
<span class="sd">            assumptions need to be consistent, i.e., graph[i,j,0] = &#39;--&gt;&#39;</span>
<span class="sd">            requires graph[j,i,0] = &#39;&lt;--&#39; and acyclicity must hold. If a link</span>
<span class="sd">            does not appear in the dictionary, it is assumed absent. That is,</span>
<span class="sd">            if link_assumptions is not None, then all links have to be specified</span>
<span class="sd">            or the links are assumed absent.</span>
<span class="sd">        tau_min : int, optional (default: 0)</span>
<span class="sd">            Minimum time lag to test.</span>
<span class="sd">        tau_max : int, optional (default: 1)</span>
<span class="sd">            Maximum time lag. Must be larger or equal to tau_min.</span>
<span class="sd">        pc_alpha : float or list of floats, default: 0.01</span>
<span class="sd">            Significance level in algorithm. If a list or None is passed, the</span>
<span class="sd">            pc_alpha level is optimized for every graph across the given</span>
<span class="sd">            pc_alpha values ([0.001, 0.005, 0.01, 0.025, 0.05] for None) using</span>
<span class="sd">            the score computed in cond_ind_test.get_model_selection_criterion().</span>
<span class="sd">        contemp_collider_rule : {&#39;majority&#39;, &#39;conservative&#39;, &#39;none&#39;}</span>
<span class="sd">            Rule for collider phase to use. See the paper for details. Only</span>
<span class="sd">            &#39;majority&#39; and &#39;conservative&#39; lead to an order-independent</span>
<span class="sd">            algorithm.</span>
<span class="sd">        conflict_resolution : bool, optional (default: True)</span>
<span class="sd">            Whether to mark conflicts in orientation rules. Only for True</span>
<span class="sd">            this leads to an order-independent algorithm.</span>
<span class="sd">        reset_lagged_links : bool, optional (default: False)</span>
<span class="sd">            Restricts the detection of lagged causal links in Step 2 to the</span>
<span class="sd">            significant adjacencies found in the PC1 algorithm in Step 1. For</span>
<span class="sd">            True, *all* lagged links are considered again, which improves</span>
<span class="sd">            detection power for lagged links, but also leads to larger</span>
<span class="sd">            runtimes.</span>
<span class="sd">        max_conds_dim : int, optional (default: None)</span>
<span class="sd">            Maximum number of conditions to test. If None is passed, this number</span>
<span class="sd">            is unrestricted.</span>
<span class="sd">        max_combinations : int, optional (default: 1)</span>
<span class="sd">            Maximum number of combinations of conditions of current cardinality</span>
<span class="sd">            to test. Defaults to 1 for PC_1 algorithm. For original PC algorithm</span>
<span class="sd">            a larger number, such as 10, can be used.</span>
<span class="sd">        max_conds_py : int, optional (default: None)</span>
<span class="sd">            Maximum number of lagged conditions of Y to use in MCI tests. If</span>
<span class="sd">            None is passed, this number is unrestricted.</span>
<span class="sd">        max_conds_px : int, optional (default: None)</span>
<span class="sd">            Maximum number of lagged conditions of X to use in MCI tests. If</span>
<span class="sd">            None is passed, this number is unrestricted.</span>
<span class="sd">        max_conds_px_lagged : int, optional (default: None)</span>
<span class="sd">            Maximum number of lagged conditions of X when X is lagged in MCI</span>
<span class="sd">            tests. If None is passed, this number is equal to max_conds_px.</span>
<span class="sd">        fdr_method : str, optional (default: &#39;none&#39;)</span>
<span class="sd">            Correction method, default is Benjamini-Hochberg False Discovery</span>
<span class="sd">            Rate method.</span>
<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        graph : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Resulting causal graph, see description above for interpretation.</span>
<span class="sd">        val_matrix : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Estimated matrix of test statistic values regarding adjacencies.</span>
<span class="sd">        p_matrix : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Estimated matrix of p-values regarding adjacencies.</span>
<span class="sd">        sepset : dictionary</span>
<span class="sd">            Separating sets. See paper for details.</span>
<span class="sd">        ambiguous_triples : list</span>
<span class="sd">            List of ambiguous triples, only relevant for &#39;majority&#39; and</span>
<span class="sd">            &#39;conservative&#39; rules, see paper for details.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">observed_context_nodes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span>

        <span class="c1"># initialize / clean link_assumptions</span>
        <span class="k">if</span> <span class="n">link_assumptions</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">_link_assumptions</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">link_assumptions</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">_link_assumptions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_set_link_assumptions</span><span class="p">(</span><span class="n">link_assumptions</span><span class="p">,</span> <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span>
                       <span class="n">remove_contemp</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
            <span class="c1"># {j: {(i, -tau): (&quot;-?&gt;&quot; if tau &gt; 0 else &quot;o?o&quot;) for i in range(self.N)</span>
            <span class="c1">#                          for tau in range(tau_max + 1)} for j in range(self.N)}</span>

        <span class="n">_link_assumptions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">assume_exogenous_context</span><span class="p">(</span><span class="n">_link_assumptions</span><span class="p">,</span> <span class="n">observed_context_nodes</span><span class="p">)</span>
        <span class="n">_link_assumptions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">clean_link_assumptions</span><span class="p">(</span><span class="n">_link_assumptions</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">)</span>

        <span class="c1"># for j in _link_assumptions:</span>
        <span class="c1">#     print(j, _link_assumptions[j])</span>
        <span class="c1"># self._set_link_assumptions(_link_assumptions, tau_min, tau_max,</span>
        <span class="c1">#                        remove_contemp=False)</span>

        <span class="c1"># Check if pc_alpha is chosen to optimize over a list</span>
        <span class="k">if</span> <span class="n">pc_alpha</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">or</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">pc_alpha</span><span class="p">,</span> <span class="p">(</span><span class="nb">list</span><span class="p">,</span> <span class="nb">tuple</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">)):</span>
            <span class="c1"># Call optimizer wrapper around run_pcmciplus()</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_optimize_pcmciplus_alpha</span><span class="p">(</span>
                <span class="n">link_assumptions</span><span class="o">=</span><span class="n">link_assumptions</span><span class="p">,</span>
                <span class="n">tau_min</span><span class="o">=</span><span class="n">tau_min</span><span class="p">,</span>
                <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                <span class="n">pc_alpha</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">,</span>
                <span class="n">contemp_collider_rule</span><span class="o">=</span><span class="n">contemp_collider_rule</span><span class="p">,</span>
                <span class="n">conflict_resolution</span><span class="o">=</span><span class="n">conflict_resolution</span><span class="p">,</span>
                <span class="n">reset_lagged_links</span><span class="o">=</span><span class="n">reset_lagged_links</span><span class="p">,</span>
                <span class="n">max_conds_dim</span><span class="o">=</span><span class="n">max_conds_dim</span><span class="p">,</span>
                <span class="n">max_combinations</span><span class="o">=</span><span class="n">max_combinations</span><span class="p">,</span>
                <span class="n">max_conds_py</span><span class="o">=</span><span class="n">max_conds_py</span><span class="p">,</span>
                <span class="n">max_conds_px</span><span class="o">=</span><span class="n">max_conds_px</span><span class="p">,</span>
                <span class="n">max_conds_px_lagged</span><span class="o">=</span><span class="n">max_conds_px_lagged</span><span class="p">,</span>
                <span class="n">fdr_method</span><span class="o">=</span><span class="n">fdr_method</span><span class="p">)</span>

        <span class="k">elif</span> <span class="n">pc_alpha</span> <span class="o">&lt;</span> <span class="mf">0.</span> <span class="ow">or</span> <span class="n">pc_alpha</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Choose 0 &lt;= pc_alpha &lt;= 1&quot;</span><span class="p">)</span>

        <span class="c1"># Check the limits on tau</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_check_tau_limits</span><span class="p">(</span><span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">)</span>

        <span class="c1"># Step 0 and 1:</span>
        <span class="n">context_results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">discover_lagged_context_system_links</span><span class="p">(</span>
            <span class="n">_link_assumptions</span><span class="p">,</span>
            <span class="n">tau_min</span><span class="o">=</span><span class="n">tau_min</span><span class="p">,</span>
            <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
            <span class="n">pc_alpha</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">,</span>
            <span class="n">reset_lagged_links</span><span class="o">=</span><span class="n">reset_lagged_links</span><span class="p">,</span>
            <span class="n">max_conds_dim</span><span class="o">=</span><span class="n">max_conds_dim</span><span class="p">,</span>
            <span class="n">max_combinations</span><span class="o">=</span><span class="n">max_combinations</span><span class="p">,</span>
            <span class="n">max_conds_py</span><span class="o">=</span><span class="n">max_conds_py</span><span class="p">,</span>
            <span class="n">max_conds_px</span><span class="o">=</span><span class="n">max_conds_px</span><span class="p">,</span>
            <span class="n">max_conds_px_lagged</span><span class="o">=</span><span class="n">max_conds_px_lagged</span><span class="p">,</span>
            <span class="n">fdr_method</span><span class="o">=</span><span class="n">fdr_method</span>
        <span class="p">)</span>
        <span class="n">ctxt_res</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">context_results</span><span class="p">)</span>
        <span class="c1"># Store the parents in the pcmci member</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">observed_context_parents</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">context_results</span><span class="p">[</span><span class="s1">&#39;parents&#39;</span><span class="p">])</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">all_lagged_parents</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span>
            <span class="n">context_results</span><span class="p">[</span><span class="s1">&#39;lagged_parents&#39;</span><span class="p">])</span>  <span class="c1"># remove context nodes from lagged parents</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">all_lagged_parents</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="p">[</span><span class="n">el</span> <span class="k">for</span> <span class="n">el</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">all_lagged_parents</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="k">if</span> <span class="n">el</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span><span class="p">]</span>
                                   <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span>
                                   <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)}</span>

        <span class="c1"># if self.verbosity &gt; 0:</span>
        <span class="c1">#     print(&quot;\nDiscovered observed context parents: &quot;, context_results[&#39;parents&#39;])</span>

        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="c1"># step 2:</span>
            <span class="n">dummy_system_results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">discover_dummy_system_links</span><span class="p">(</span>
                <span class="n">_link_assumptions</span><span class="p">,</span>
                <span class="n">ctxt_res</span><span class="p">,</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">all_lagged_parents</span><span class="p">,</span>
                <span class="n">tau_min</span><span class="o">=</span><span class="n">tau_min</span><span class="p">,</span>
                <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                <span class="n">pc_alpha</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">,</span>
                <span class="n">reset_lagged_links</span><span class="o">=</span><span class="n">reset_lagged_links</span><span class="p">,</span>
                <span class="n">max_conds_dim</span><span class="o">=</span><span class="n">max_conds_dim</span><span class="p">,</span>
                <span class="n">max_conds_py</span><span class="o">=</span><span class="n">max_conds_py</span><span class="p">,</span>
                <span class="n">max_conds_px</span><span class="o">=</span><span class="n">max_conds_px</span><span class="p">,</span>
                <span class="n">max_conds_px_lagged</span><span class="o">=</span><span class="n">max_conds_px_lagged</span><span class="p">,</span>
                <span class="n">fdr_method</span><span class="o">=</span><span class="n">fdr_method</span>
            <span class="p">)</span>
            <span class="c1"># Store the parents in the pcmci member</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">dummy_parents</span> <span class="o">=</span> <span class="n">dummy_system_results</span><span class="p">[</span><span class="s1">&#39;parents&#39;</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">dummy_system_results</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">context_results</span><span class="p">)</span>

        <span class="c1"># if self.verbosity &gt; 0:</span>
        <span class="c1">#     print(&quot;Discovered dummy parents: &quot;, self.dummy_parents)</span>

        <span class="c1"># step 3:</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">=</span> <span class="s2">&quot;system_search&quot;</span>

        <span class="n">lagged_context_dummy_parents</span> <span class="o">=</span> <span class="p">{</span>
            <span class="n">i</span><span class="p">:</span> <span class="nb">list</span><span class="p">(</span>
                <span class="nb">dict</span><span class="o">.</span><span class="n">fromkeys</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">all_lagged_parents</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">observed_context_parents</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">dummy_parents</span><span class="p">[</span><span class="n">i</span><span class="p">]))</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span><span class="p">}</span>
        <span class="c1"># we only care about the parents of system nodes</span>
        <span class="n">lagged_context_dummy_parents</span><span class="o">.</span><span class="n">update</span><span class="p">(</span>
            <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="p">[]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">observed_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span><span class="p">})</span>

        <span class="n">dummy_system_results_copy</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">dummy_system_results</span><span class="p">)</span>

        <span class="c1"># step 4:</span>
        <span class="n">system_skeleton_results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">discover_system_system_links</span><span class="p">(</span><span class="n">link_assumptions</span><span class="o">=</span><span class="n">_link_assumptions</span><span class="p">,</span>
                                                                    <span class="n">lagged_context_dummy_parents</span><span class="o">=</span><span class="n">lagged_context_dummy_parents</span><span class="p">,</span>
                                                                    <span class="n">tau_min</span><span class="o">=</span><span class="n">tau_min</span><span class="p">,</span>
                                                                    <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                                                                    <span class="n">pc_alpha</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">,</span>
                                                                    <span class="n">reset_lagged_links</span><span class="o">=</span><span class="n">reset_lagged_links</span><span class="p">,</span>
                                                                    <span class="n">max_conds_dim</span><span class="o">=</span><span class="n">max_conds_dim</span><span class="p">,</span>
                                                                    <span class="n">max_conds_py</span><span class="o">=</span><span class="n">max_conds_py</span><span class="p">,</span>
                                                                    <span class="n">max_conds_px</span><span class="o">=</span><span class="n">max_conds_px</span><span class="p">,</span>
                                                                    <span class="n">max_conds_px_lagged</span><span class="o">=</span><span class="n">max_conds_px_lagged</span><span class="p">,</span>
                                                                    <span class="n">fdr_method</span><span class="o">=</span><span class="n">fdr_method</span><span class="p">)</span>

        <span class="c1"># orientation phase</span>
        <span class="n">colliders_step_results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_pcmciplus_collider_phase</span><span class="p">(</span>
            <span class="n">system_skeleton_results</span><span class="p">[</span><span class="s1">&#39;graph&#39;</span><span class="p">],</span> <span class="n">system_skeleton_results</span><span class="p">[</span><span class="s1">&#39;sepsets&#39;</span><span class="p">],</span>
            <span class="n">lagged_context_dummy_parents</span><span class="p">,</span> <span class="n">pc_alpha</span><span class="p">,</span>
            <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">max_conds_py</span><span class="p">,</span> <span class="n">max_conds_px</span><span class="p">,</span> <span class="n">max_conds_px_lagged</span><span class="p">,</span>
            <span class="n">conflict_resolution</span><span class="p">,</span> <span class="n">contemp_collider_rule</span><span class="p">)</span>

        <span class="n">final_graph</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_pcmciplus_rule_orientation_phase</span><span class="p">(</span><span class="n">colliders_step_results</span><span class="p">[</span><span class="s1">&#39;graph&#39;</span><span class="p">],</span>
                                                             <span class="n">colliders_step_results</span><span class="p">[</span><span class="s1">&#39;ambiguous_triples&#39;</span><span class="p">],</span>
                                                             <span class="n">conflict_resolution</span><span class="p">)</span>

        <span class="c1"># add context-system and dummy-system values and pvalues back in (lost because of link_assumption)</span>
        <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">observed_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">):</span>
                <span class="k">for</span> <span class="n">lag</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                    <span class="c1"># add context-system links to results</span>
                    <span class="n">system_skeleton_results</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">][</span><span class="n">c</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">lag</span><span class="p">]</span> <span class="o">=</span> <span class="n">dummy_system_results_copy</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">][</span>
                        <span class="n">c</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">lag</span><span class="p">]</span>
                    <span class="n">system_skeleton_results</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">][</span><span class="n">j</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">lag</span><span class="p">]</span> <span class="o">=</span> <span class="n">dummy_system_results_copy</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">][</span>
                        <span class="n">j</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">lag</span><span class="p">]</span>

                    <span class="n">system_skeleton_results</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">][</span><span class="n">c</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">lag</span><span class="p">]</span> <span class="o">=</span> <span class="n">dummy_system_results_copy</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">][</span><span class="n">c</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">lag</span><span class="p">]</span>
                    <span class="n">system_skeleton_results</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">][</span><span class="n">j</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">lag</span><span class="p">]</span> <span class="o">=</span> <span class="n">dummy_system_results_copy</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">][</span><span class="n">j</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">lag</span><span class="p">]</span>

        <span class="c1"># No confidence interval estimation here</span>
        <span class="n">return_dict</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;graph&#39;</span><span class="p">:</span> <span class="n">final_graph</span><span class="p">,</span> <span class="s1">&#39;p_matrix&#39;</span><span class="p">:</span> <span class="n">system_skeleton_results</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">],</span>
                       <span class="s1">&#39;val_matrix&#39;</span><span class="p">:</span> <span class="n">system_skeleton_results</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">],</span>
                       <span class="s1">&#39;sepsets&#39;</span><span class="p">:</span> <span class="n">colliders_step_results</span><span class="p">[</span><span class="s1">&#39;sepsets&#39;</span><span class="p">],</span>
                       <span class="s1">&#39;ambiguous_triples&#39;</span><span class="p">:</span> <span class="n">colliders_step_results</span><span class="p">[</span><span class="s1">&#39;ambiguous_triples&#39;</span><span class="p">],</span> 
                       <span class="s1">&#39;conf_matrix&#39;</span><span class="p">:</span> <span class="kc">None</span><span class="p">}</span>

        <span class="c1"># Print the results</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">print_results</span><span class="p">(</span><span class="n">return_dict</span><span class="p">,</span> <span class="n">alpha_level</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">)</span>

        <span class="c1"># Return the dictionary</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">results</span> <span class="o">=</span> <span class="n">return_dict</span>

        <span class="c1"># Return the dictionary</span>
        <span class="k">return</span> <span class="n">return_dict</span></div>


<div class="viewcode-block" id="JPCMCIplus.assume_exogenous_context">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.assume_exogenous_context">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">assume_exogenous_context</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_assumptions</span><span class="p">,</span> <span class="n">observed_context_nodes</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to amend the link_assumptions to ensure that all context-system links are oriented</span>
<span class="sd">        such that the context variable is the parent.&quot;&quot;&quot;</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="n">link_assumptions</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">link</span> <span class="ow">in</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                    <span class="n">i</span><span class="p">,</span> <span class="n">lag</span> <span class="o">=</span> <span class="n">link</span>
                    <span class="k">if</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">observed_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span><span class="p">:</span>  <span class="c1"># is context var</span>
                        <span class="n">link_type</span> <span class="o">=</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="n">link</span><span class="p">]</span>
                        <span class="n">link_assumptions</span><span class="p">[</span><span class="n">j</span><span class="p">][</span><span class="n">link</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;-&#39;</span> <span class="o">+</span> <span class="n">link_type</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">+</span> <span class="s1">&#39;&gt;&#39;</span>
        <span class="k">return</span> <span class="n">link_assumptions</span></div>


<div class="viewcode-block" id="JPCMCIplus.clean_link_assumptions">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.clean_link_assumptions">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">clean_link_assumptions</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_assumptions</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to amend the link_assumptions in the following ways</span>
<span class="sd">            * remove any links where dummy is the child</span>
<span class="sd">            * remove any lagged links to dummy, and space_context (not to observed time context)</span>
<span class="sd">            * and system - context links where context is the child</span>
<span class="sd">            * and any links between spatial and temporal context</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span><span class="p">:</span>
            <span class="n">link_assumptions</span><span class="p">[</span><span class="n">node</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>

        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">lag</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span><span class="p">:</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">((</span><span class="n">c</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">lag</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">c</span><span class="p">]:</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">c</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">((</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>
            <span class="k">if</span> <span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="ow">in</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">c</span><span class="p">]:</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">c</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">((</span><span class="n">c</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>  <span class="c1"># remove self-links</span>

        <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">lag</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">c</span><span class="p">]:</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">c</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">((</span><span class="n">k</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span><span class="p">:</span>
                <span class="k">if</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="ow">in</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">c</span><span class="p">]:</span> <span class="n">link_assumptions</span><span class="p">[</span><span class="n">c</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">((</span><span class="n">k</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">link_assumptions</span></div>


<div class="viewcode-block" id="JPCMCIplus.remove_dummy_link_assumptions">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.remove_dummy_link_assumptions">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">remove_dummy_link_assumptions</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_assumptions</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to remove any links to dummy from link_assumptions.&quot;&quot;&quot;</span>
        <span class="n">link_assumptions_wo_dummy</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">link_assumptions</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">dummy_node</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span><span class="p">:</span>
                <span class="k">if</span> <span class="p">(</span><span class="n">dummy_node</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="ow">in</span> <span class="n">link_assumptions_wo_dummy</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                    <span class="n">link_assumptions_wo_dummy</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">((</span><span class="n">dummy_node</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">link_assumptions_wo_dummy</span></div>


<div class="viewcode-block" id="JPCMCIplus.add_found_context_link_assumptions">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.add_found_context_link_assumptions">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">add_found_context_link_assumptions</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_assumptions</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to add discovered links between system and observed context nodes to link_assumptions.&quot;&quot;&quot;</span>
        <span class="n">link_assumptions_dummy</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">link_assumptions</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span><span class="p">:</span>
            <span class="n">link_assumptions_dummy</span><span class="p">[</span><span class="n">c</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">lag</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="n">link_assumptions_dummy</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                        <span class="n">link_assumptions_dummy</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">((</span><span class="n">c</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>
            <span class="n">link_assumptions_dummy</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">update</span><span class="p">({</span><span class="n">parent</span><span class="p">:</span> <span class="s1">&#39;--&gt;&#39;</span> <span class="k">for</span> <span class="n">parent</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">observed_context_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]})</span>

        <span class="k">return</span> <span class="n">link_assumptions_dummy</span></div>


<div class="viewcode-block" id="JPCMCIplus.clean_system_link_assumptions">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.clean_system_link_assumptions">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">clean_system_link_assumptions</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_assumptions</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to remove any links to dummy and observed context nodes from link_assumptions.</span>
<span class="sd">            Add discovered links to contextual parents (from steps 1 and 2) to the link_assumptions.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">dummy_vars</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span>
        <span class="n">observed_context_nodes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span>
        <span class="n">system_links</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">link_assumptions</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">C</span> <span class="ow">in</span> <span class="n">dummy_vars</span> <span class="o">+</span> <span class="n">observed_context_nodes</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">lag</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">C</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="n">system_links</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                        <span class="n">system_links</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">((</span><span class="n">C</span><span class="p">,</span> <span class="o">-</span><span class="n">lag</span><span class="p">),</span> <span class="kc">None</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="n">system_links</span><span class="p">:</span>
            <span class="n">system_links</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">update</span><span class="p">(</span>
                <span class="p">{</span><span class="n">parent</span><span class="p">:</span> <span class="s1">&#39;--&gt;&#39;</span> <span class="k">for</span> <span class="n">parent</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">observed_context_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">dummy_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]})</span>

        <span class="k">for</span> <span class="n">C</span> <span class="ow">in</span> <span class="n">observed_context_nodes</span> <span class="o">+</span> <span class="n">dummy_vars</span><span class="p">:</span>
            <span class="c1"># we are not interested in links between context variables (thus system_links[C] = {})</span>
            <span class="n">system_links</span><span class="p">[</span><span class="n">C</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="k">return</span> <span class="n">system_links</span></div>


<div class="viewcode-block" id="JPCMCIplus.discover_lagged_context_system_links">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.discover_lagged_context_system_links">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">discover_lagged_context_system_links</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_assumptions</span><span class="p">,</span>
                                             <span class="n">tau_min</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                                             <span class="n">tau_max</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">pc_alpha</span><span class="o">=</span><span class="mf">0.01</span><span class="p">,</span>
                                             <span class="n">reset_lagged_links</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                             <span class="n">max_conds_dim</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                             <span class="n">max_combinations</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                                             <span class="n">max_conds_py</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                             <span class="n">max_conds_px</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                             <span class="n">max_conds_px_lagged</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                             <span class="n">fdr_method</span><span class="o">=</span><span class="s1">&#39;none&#39;</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Step 1 of JPCMCIplus, i.e. discovery of links between observed context nodes and system nodes through an</span>
<span class="sd">        application of the skeleton phase of PCMCIplus to this subset of nodes (observed context nodes and system</span>
<span class="sd">        nodes).</span>
<span class="sd">        See run_jpcmciplus for a description of the parameters.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        graph : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Resulting causal graph, see description above for interpretation.</span>
<span class="sd">        val_matrix : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Estimated matrix of test statistic values regarding adjacencies.</span>
<span class="sd">        p_matrix : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Estimated matrix of p-values regarding adjacencies.</span>
<span class="sd">        parents : dictionary</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...} containing</span>
<span class="sd">            the estimated context parents of the system nodes.</span>
<span class="sd">        lagged_parents : dictionary</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...} containing</span>
<span class="sd">            the conditioning-parents estimated with PC algorithm.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="c1"># Initializing</span>
        <span class="n">context_parents</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="p">[]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)}</span>

        <span class="c1"># find links between expressive context, and between expressive context and system nodes</span>
        <span class="c1"># here, we exclude any links to dummy</span>
        <span class="n">_link_assumptions_wo_dummy</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">remove_dummy_link_assumptions</span><span class="p">(</span><span class="n">link_assumptions</span><span class="p">)</span>
        <span class="n">_int_link_assumptions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_set_link_assumptions</span><span class="p">(</span><span class="n">_link_assumptions_wo_dummy</span><span class="p">,</span> <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">)</span>

        <span class="c1"># Step 1: Get a superset of lagged parents from run_pc_stable</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">## J-PCMCI+ Step 1: Selecting lagged conditioning sets</span><span class="se">\n</span><span class="s2">##&quot;</span><span class="p">)</span>

        <span class="n">lagged_parents</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">run_pc_stable</span><span class="p">(</span><span class="n">link_assumptions</span><span class="o">=</span><span class="n">link_assumptions</span><span class="p">,</span>
                                            <span class="n">tau_min</span><span class="o">=</span><span class="n">tau_min</span><span class="p">,</span>
                                            <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                                            <span class="n">pc_alpha</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">,</span>
                                            <span class="n">max_conds_dim</span><span class="o">=</span><span class="n">max_conds_dim</span><span class="p">,</span>
                                            <span class="n">max_combinations</span><span class="o">=</span><span class="n">max_combinations</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">=</span> <span class="s2">&quot;context_search&quot;</span>

        <span class="n">p_matrix</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">p_matrix</span>
        <span class="n">val_matrix</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">val_matrix</span>

        <span class="c1"># run PCMCI+ skeleton phase on subset of links to discover context-system links</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">## J-PCMCI+ Step 2: Discovering context-system links</span><span class="se">\n</span><span class="s2">##&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">link_assumptions</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">With link_assumptions = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">_int_link_assumptions</span><span class="p">))</span>

        <span class="n">skeleton_results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_pcmciplus_mci_skeleton_phase</span><span class="p">(</span>
            <span class="n">lagged_parents</span><span class="p">,</span> <span class="n">_int_link_assumptions</span><span class="p">,</span> <span class="n">pc_alpha</span><span class="p">,</span>
            <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">max_conds_dim</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">max_conds_py</span><span class="p">,</span> <span class="n">max_conds_px</span><span class="p">,</span> <span class="n">max_conds_px_lagged</span><span class="p">,</span>
            <span class="n">reset_lagged_links</span><span class="p">,</span> <span class="n">fdr_method</span><span class="p">,</span>
            <span class="n">p_matrix</span><span class="p">,</span> <span class="n">val_matrix</span>
        <span class="p">)</span>

        <span class="n">skeleton_graph</span> <span class="o">=</span> <span class="n">skeleton_results</span><span class="p">[</span><span class="s1">&#39;graph&#39;</span><span class="p">]</span>

        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span><span class="p">:</span>
                <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                    <span class="k">if</span> <span class="n">skeleton_graph</span><span class="p">[</span><span class="n">c</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;o?o&#39;</span> <span class="ow">or</span> <span class="n">skeleton_graph</span><span class="p">[</span><span class="n">c</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;-?&gt;&#39;</span> <span class="ow">or</span> <span class="n">skeleton_graph</span><span class="p">[</span>
                        <span class="n">c</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;o-o&#39;</span> <span class="ow">or</span> <span class="n">skeleton_graph</span><span class="p">[</span><span class="n">c</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;--&gt;&#39;</span><span class="p">:</span>
                        <span class="n">context_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">c</span><span class="p">,</span> <span class="o">-</span><span class="n">k</span><span class="p">))</span>

        <span class="n">return_dict</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;graph&#39;</span><span class="p">:</span> <span class="n">skeleton_results</span><span class="p">[</span><span class="s1">&#39;graph&#39;</span><span class="p">],</span> <span class="s1">&#39;p_matrix&#39;</span><span class="p">:</span> <span class="n">skeleton_results</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">],</span>
                       <span class="s1">&#39;val_matrix&#39;</span><span class="p">:</span> <span class="n">skeleton_results</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">],</span>
                       <span class="s1">&#39;parents&#39;</span><span class="p">:</span> <span class="n">context_parents</span><span class="p">,</span> <span class="s1">&#39;lagged_parents&#39;</span><span class="p">:</span> <span class="n">lagged_parents</span><span class="p">}</span>

        <span class="c1"># Print the results</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">print_results</span><span class="p">(</span><span class="n">return_dict</span><span class="p">,</span> <span class="n">alpha_level</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">return_dict</span></div>


<div class="viewcode-block" id="JPCMCIplus.discover_dummy_system_links">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.discover_dummy_system_links">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">discover_dummy_system_links</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_assumptions</span><span class="p">,</span>
                                    <span class="n">context_system_results</span><span class="p">,</span>
                                    <span class="n">lagged_parents</span><span class="p">,</span>
                                    <span class="n">tau_min</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                                    <span class="n">tau_max</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                                    <span class="n">pc_alpha</span><span class="o">=</span><span class="mf">0.01</span><span class="p">,</span>
                                    <span class="n">reset_lagged_links</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                    <span class="n">max_conds_dim</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                    <span class="n">max_conds_py</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                    <span class="n">max_conds_px</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                    <span class="n">max_conds_px_lagged</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                    <span class="n">fdr_method</span><span class="o">=</span><span class="s1">&#39;none&#39;</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Step 2 of JPCMCIplus, i.e. discovery of links between observed (time and space) dummy nodes and system nodes</span>
<span class="sd">        through an application of the skeleton phase of PCMCIplus to this subset of nodes (dummy nodes and</span>
<span class="sd">        system nodes).</span>
<span class="sd">        See run_jpcmciplus for a description of the parameters.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        context_system_results : dictionary</span>
<span class="sd">            Output of discover_lagged_and_context_system_links, i.e. lagged and context parents together with the</span>
<span class="sd">            corresponding estimated test statistic values regarding adjacencies.</span>
<span class="sd">        lagged_parents : dictionary</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...} containing the conditioning-parents</span>
<span class="sd">            estimated with PC algorithm.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        graph : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Resulting causal graph, see description above for interpretation.</span>
<span class="sd">        val_matrix : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Estimated matrix of test statistic values regarding adjacencies.</span>
<span class="sd">        p_matrix : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Estimated matrix of p-values regarding adjacencies.</span>
<span class="sd">        parents : dictionary</span>
<span class="sd">            Dictionary of form {0:[(0, -1), (3, -2), ...], 1:[], ...} containing</span>
<span class="sd">            the estimated dummy parents of the system nodes.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">lagged_context_parents</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="nb">list</span><span class="p">(</span><span class="nb">dict</span><span class="o">.</span><span class="n">fromkeys</span><span class="p">(</span><span class="n">context_system_results</span><span class="p">[</span><span class="s1">&#39;parents&#39;</span><span class="p">][</span><span class="n">i</span><span class="p">]</span> <span class="o">+</span> <span class="n">lagged_parents</span><span class="p">[</span><span class="n">i</span><span class="p">]))</span> <span class="k">for</span>
                                  <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)}</span>
        <span class="n">dummy_parents</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="p">[]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)}</span>
        <span class="n">p_matrix</span> <span class="o">=</span> <span class="n">context_system_results</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">]</span>

        <span class="c1"># setup link assumptions without the observed context nodes</span>
        <span class="n">_link_assumptions_dummy</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">add_found_context_link_assumptions</span><span class="p">(</span><span class="n">link_assumptions</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">)</span>
        <span class="n">_int_link_assumptions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_set_link_assumptions</span><span class="p">(</span><span class="n">_link_assumptions_dummy</span><span class="p">,</span> <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">=</span> <span class="s2">&quot;dummy_search&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">## J-PCMCI+ Step 3: Discovering dummy-system links</span><span class="se">\n</span><span class="s2">##&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">_link_assumptions_dummy</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">With link_assumptions = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">_int_link_assumptions</span><span class="p">))</span>

        <span class="n">skeleton_results_dummy</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_pcmciplus_mci_skeleton_phase</span><span class="p">(</span>
            <span class="n">lagged_context_parents</span><span class="p">,</span> <span class="n">_int_link_assumptions</span><span class="p">,</span> <span class="n">pc_alpha</span><span class="p">,</span>
            <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">max_conds_dim</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">max_conds_py</span><span class="p">,</span> <span class="n">max_conds_px</span><span class="p">,</span> <span class="n">max_conds_px_lagged</span><span class="p">,</span>
            <span class="n">reset_lagged_links</span><span class="p">,</span> <span class="n">fdr_method</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">p_matrix</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">val_matrix</span>
        <span class="p">)</span>

        <span class="n">skeleton_graph_dummy</span> <span class="o">=</span> <span class="n">skeleton_results_dummy</span><span class="p">[</span><span class="s1">&#39;graph&#39;</span><span class="p">]</span>

        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">system_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                <span class="k">for</span> <span class="n">dummy_node</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">skeleton_graph_dummy</span><span class="p">[</span><span class="n">dummy_node</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;o?o&#39;</span> <span class="ow">or</span> \
                            <span class="n">skeleton_graph_dummy</span><span class="p">[</span><span class="n">dummy_node</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;-?&gt;&#39;</span> <span class="ow">or</span> \
                            <span class="n">skeleton_graph_dummy</span><span class="p">[</span><span class="n">dummy_node</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;o-o&#39;</span> <span class="ow">or</span> \
                            <span class="n">skeleton_graph_dummy</span><span class="p">[</span><span class="n">dummy_node</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;--&gt;&#39;</span><span class="p">:</span>
                        <span class="n">dummy_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">dummy_node</span><span class="p">,</span> <span class="n">k</span><span class="p">))</span>
                <span class="k">for</span> <span class="n">context_node</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span><span class="p">:</span>
                    <span class="n">skeleton_results_dummy</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">][</span><span class="n">context_node</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="n">context_system_results</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">][</span>
                        <span class="n">context_node</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span>
                    <span class="n">skeleton_results_dummy</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">][</span><span class="n">j</span><span class="p">,</span> <span class="n">context_node</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="n">context_system_results</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">][</span>
                        <span class="n">j</span><span class="p">,</span> <span class="n">context_node</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span>

                    <span class="n">skeleton_results_dummy</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">][</span><span class="n">context_node</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="n">p_matrix</span><span class="p">[</span><span class="n">context_node</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span>
                    <span class="n">skeleton_results_dummy</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">][</span><span class="n">j</span><span class="p">,</span> <span class="n">context_node</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="n">p_matrix</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">context_node</span><span class="p">,</span> <span class="n">k</span><span class="p">]</span>

        <span class="n">return_dict</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;graph&#39;</span><span class="p">:</span> <span class="n">skeleton_results_dummy</span><span class="p">[</span><span class="s1">&#39;graph&#39;</span><span class="p">],</span> <span class="s1">&#39;p_matrix&#39;</span><span class="p">:</span> <span class="n">skeleton_results_dummy</span><span class="p">[</span><span class="s1">&#39;p_matrix&#39;</span><span class="p">],</span>
                       <span class="s1">&#39;val_matrix&#39;</span><span class="p">:</span> <span class="n">skeleton_results_dummy</span><span class="p">[</span><span class="s1">&#39;val_matrix&#39;</span><span class="p">],</span> <span class="s1">&#39;parents&#39;</span><span class="p">:</span> <span class="n">dummy_parents</span><span class="p">}</span>

        <span class="c1"># Print the results</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">print_results</span><span class="p">(</span><span class="n">return_dict</span><span class="p">,</span> <span class="n">alpha_level</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">return_dict</span></div>


<div class="viewcode-block" id="JPCMCIplus.discover_system_system_links">
<a class="viewcode-back" href="../../index.html#tigramite.jpcmciplus.JPCMCIplus.discover_system_system_links">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">discover_system_system_links</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link_assumptions</span><span class="p">,</span>
                                     <span class="n">lagged_context_dummy_parents</span><span class="p">,</span>
                                     <span class="n">tau_min</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                                     <span class="n">tau_max</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                                     <span class="n">pc_alpha</span><span class="o">=</span><span class="mf">0.01</span><span class="p">,</span>
                                     <span class="n">reset_lagged_links</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                     <span class="n">max_conds_dim</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                     <span class="n">max_conds_py</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                     <span class="n">max_conds_px</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                     <span class="n">max_conds_px_lagged</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                     <span class="n">fdr_method</span><span class="o">=</span><span class="s1">&#39;none&#39;</span>
                                     <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Step 4 of JPCMCIplus and orientation phase, i.e. discovery of links between system nodes given the knowledge</span>
<span class="sd">        about their context parents through an application of PCMCIplus to this subset of nodes (system nodes).</span>
<span class="sd">        See run_jpcmciplus for a description of the other parameters.</span>


<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        lagged_context_dummy_parents : dictionary</span>
<span class="sd">            Dictionary containing lagged and (dummy and observed) context parents of the system nodes estimated during</span>
<span class="sd">            step 1 and step 2 of J-PCMCI+.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        graph : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Resulting causal graph, see description above for interpretation.</span>
<span class="sd">        val_matrix : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Estimated matrix of test statistic values regarding adjacencies.</span>
<span class="sd">        p_matrix : array of shape [N, N, tau_max+1]</span>
<span class="sd">            Estimated matrix of p-values regarding adjacencies.</span>
<span class="sd">        sepset : dictionary</span>
<span class="sd">            Separating sets. See paper for details.</span>
<span class="sd">        ambiguous_triples : list</span>
<span class="sd">            List of ambiguous triples, only relevant for &#39;majority&#39; and</span>
<span class="sd">            &#39;conservative&#39; rules, see paper for details.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">=</span> <span class="s2">&quot;system_search&quot;</span>

        <span class="c1"># Get the parents from run_pc_stable only on the system links</span>
        <span class="n">system_links</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">clean_system_link_assumptions</span><span class="p">(</span><span class="n">link_assumptions</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">)</span>
        <span class="c1"># Set the selected links</span>
        <span class="n">_int_link_assumptions</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_set_link_assumptions</span><span class="p">(</span><span class="n">system_links</span><span class="p">,</span> <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">##</span><span class="se">\n</span><span class="s2">## J-PCMCI+ Step 4: Discovering system-system links </span><span class="se">\n</span><span class="s2">##&quot;</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">system_links</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">With link_assumptions = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">_int_link_assumptions</span><span class="p">))</span>

        <span class="n">skeleton_results</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_pcmciplus_mci_skeleton_phase</span><span class="p">(</span>
            <span class="n">lagged_context_dummy_parents</span><span class="p">,</span> <span class="n">_int_link_assumptions</span><span class="p">,</span> <span class="n">pc_alpha</span><span class="p">,</span>
            <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">max_conds_dim</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span>
            <span class="n">max_conds_py</span><span class="p">,</span> <span class="n">max_conds_px</span><span class="p">,</span> <span class="n">max_conds_px_lagged</span><span class="p">,</span>
            <span class="n">reset_lagged_links</span><span class="p">,</span> <span class="n">fdr_method</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">p_matrix</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">val_matrix</span>
        <span class="p">)</span>

        <span class="k">return</span> <span class="n">skeleton_results</span></div>


    <span class="k">def</span><span class="w"> </span><span class="nf">_remaining_pairs</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph</span><span class="p">,</span> <span class="n">adjt</span><span class="p">,</span> <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">p</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function returning the remaining pairs that still need to be</span>
<span class="sd">        tested depending on the JPCMCIplus step, i.e. discovery of context-system links (step 1),</span>
<span class="sd">        dummy-context links (step 2) or system-system links in which case the function of the parent class is called.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">all_context_nodes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_context_nodes</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_context_nodes</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">==</span> <span class="s2">&quot;context_search&quot;</span><span class="p">:</span>
            <span class="c1"># during discovery of context-system links we are only</span>
            <span class="c1"># interested in context-context and context-system pairs</span>
            <span class="n">N</span> <span class="o">=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">pairs</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">)</span> <span class="ow">in</span> <span class="n">itertools</span><span class="o">.</span><span class="n">product</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">),</span> <span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">)):</span>
                <span class="k">for</span> <span class="n">abstau</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">abstau</span><span class="p">]</span> <span class="o">!=</span> <span class="s2">&quot;&quot;</span>
                            <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span>
                                <span class="p">[</span><span class="n">a</span> <span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">adjt</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="k">if</span> <span class="n">a</span> <span class="o">!=</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">abstau</span><span class="p">)])</span> <span class="o">&gt;=</span> <span class="n">p</span>
                            <span class="ow">and</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">all_context_nodes</span><span class="p">):</span>
                        <span class="n">pairs</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">abstau</span><span class="p">))</span>
            <span class="k">return</span> <span class="n">pairs</span>
        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">==</span> <span class="s2">&quot;dummy_search&quot;</span><span class="p">:</span>
            <span class="c1"># during discovery of dummy-system links we are only</span>
            <span class="c1"># interested in dummy-system pairs</span>
            <span class="n">N</span> <span class="o">=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">pairs</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">)</span> <span class="ow">in</span> <span class="n">itertools</span><span class="o">.</span><span class="n">product</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">),</span> <span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">)):</span>
                <span class="k">for</span> <span class="n">abstau</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">abstau</span><span class="p">]</span> <span class="o">!=</span> <span class="s2">&quot;&quot;</span>
                            <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span>
                                <span class="p">[</span><span class="n">a</span> <span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">adjt</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="k">if</span> <span class="n">a</span> <span class="o">!=</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">abstau</span><span class="p">)])</span> <span class="o">&gt;=</span> <span class="n">p</span>
                            <span class="ow">and</span> <span class="n">i</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_dummy</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">space_dummy</span><span class="p">):</span>
                        <span class="n">pairs</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">abstau</span><span class="p">))</span>
            <span class="k">return</span> <span class="n">pairs</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">_remaining_pairs</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">adjt</span><span class="p">,</span> <span class="n">tau_min</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">p</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_run_pcalg_test</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">abstau</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">S</span><span class="p">,</span> <span class="n">lagged_parents</span><span class="p">,</span> <span class="n">max_conds_py</span><span class="p">,</span>
                        <span class="n">max_conds_px</span><span class="p">,</span> <span class="n">max_conds_px_lagged</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">alpha_or_thres</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;MCI conditional independence tests within PCMCIplus or PC algorithm. Depending on the JPCMCIplus step</span>
<span class="sd">        the setup is adapted slightly. During the discovery of dummy-system links (step 2) we are using</span>
<span class="sd">        the dummy_ci_test and condition on the parents found during step 1; during the discovery of system-system links</span>
<span class="sd">        (step 3) we are conditioning on the found contextual parents.</span>
<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        graph : array</span>
<span class="sd">            ...</span>
<span class="sd">        i : int</span>
<span class="sd">            Variable index.</span>
<span class="sd">        abstau : int</span>
<span class="sd">            Time lag (absolute value).</span>
<span class="sd">        j : int</span>
<span class="sd">            Variable index.</span>
<span class="sd">        S : list</span>
<span class="sd">            List of contemporaneous conditions.</span>
<span class="sd">        lagged_parents : dictionary of lists</span>
<span class="sd">            Dictionary of lagged parents for each node.</span>
<span class="sd">        max_conds_py : int</span>
<span class="sd">            Max number of lagged parents for node j.</span>
<span class="sd">        max_conds_px : int</span>
<span class="sd">            Max number of lagged parents for lagged node i.</span>
<span class="sd">        max_conds_px_lagged : int</span>
<span class="sd">            Maximum number of lagged conditions of X when X is lagged in MCI</span>
<span class="sd">            tests. If None is passed, this number is equal to max_conds_px.</span>
<span class="sd">        tau_max : int</span>
<span class="sd">            Maximum time lag.</span>
<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        val : float</span>
<span class="sd">            Test statistic value.</span>
<span class="sd">        pval : float</span>
<span class="sd">            Test statistic p-value.</span>
<span class="sd">        Z : list</span>
<span class="sd">            List of conditions.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">==</span> <span class="s1">&#39;dummy_search&#39;</span><span class="p">:</span>
            <span class="c1"># during discovery of dummy-system links we condition on the found contextual parents from step 1.</span>
            <span class="n">cond</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">S</span><span class="p">)</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">observed_context_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]</span>
            <span class="n">cond</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">dict</span><span class="o">.</span><span class="n">fromkeys</span><span class="p">(</span><span class="n">cond</span><span class="p">))</span>  <span class="c1"># remove overlapps</span>
            <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">_run_pcalg_test</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">abstau</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">cond</span><span class="p">,</span> <span class="n">lagged_parents</span><span class="p">,</span> <span class="n">max_conds_py</span><span class="p">,</span>
                                           <span class="n">max_conds_px</span><span class="p">,</span> <span class="n">max_conds_px_lagged</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">alpha_or_thres</span><span class="p">)</span>

        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">mode</span> <span class="o">==</span> <span class="s1">&#39;system_search&#39;</span><span class="p">:</span>
            <span class="c1"># during discovery of system-system links we are conditioning on the found contextual parents</span>
            <span class="n">cond</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">S</span><span class="p">)</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">dummy_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">observed_context_parents</span><span class="p">[</span><span class="n">j</span><span class="p">]</span>
            <span class="n">cond</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">dict</span><span class="o">.</span><span class="n">fromkeys</span><span class="p">(</span><span class="n">cond</span><span class="p">))</span>  <span class="c1"># remove overlapps</span>
            <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">_run_pcalg_test</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">abstau</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">cond</span><span class="p">,</span> <span class="n">lagged_parents</span><span class="p">,</span> <span class="n">max_conds_py</span><span class="p">,</span>
                                           <span class="n">max_conds_px</span><span class="p">,</span> <span class="n">max_conds_px_lagged</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">alpha_or_thres</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">_run_pcalg_test</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">abstau</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">S</span><span class="p">,</span> <span class="n">lagged_parents</span><span class="p">,</span> <span class="n">max_conds_py</span><span class="p">,</span>
                                           <span class="n">max_conds_px</span><span class="p">,</span> <span class="n">max_conds_px_lagged</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span> <span class="n">alpha_or_thres</span><span class="p">)</span></div>


<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    <span class="c1"># Imports</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">numpy.random</span><span class="w"> </span><span class="kn">import</span> <span class="n">SeedSequence</span><span class="p">,</span> <span class="n">default_rng</span>

    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.toymodels</span><span class="w"> </span><span class="kn">import</span> <span class="n">structural_causal_processes</span> <span class="k">as</span> <span class="n">toys</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.toymodels.context_model</span><span class="w"> </span><span class="kn">import</span> <span class="n">ContextModel</span>
    <span class="c1"># from tigramite.jpcmciplus import JPCMCIplus</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.independence_tests.parcorr_mult</span><span class="w"> </span><span class="kn">import</span> <span class="n">ParCorrMult</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.data_processing</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">pp</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.plotting</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">tp</span>


    <span class="c1"># Set seeds for reproducibility</span>
    <span class="n">ss</span> <span class="o">=</span> <span class="n">SeedSequence</span><span class="p">(</span><span class="mi">12345</span><span class="p">)</span>
    <span class="n">child_seeds</span> <span class="o">=</span> <span class="n">ss</span><span class="o">.</span><span class="n">spawn</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>

    <span class="n">model_seed</span> <span class="o">=</span> <span class="n">child_seeds</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
    <span class="n">context_seed</span> <span class="o">=</span> <span class="n">child_seeds</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>

    <span class="n">random_state</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">model_seed</span><span class="p">)</span>

    <span class="c1"># Choose the time series length and number of spatial contexts</span>
    <span class="n">T</span> <span class="o">=</span> <span class="mi">100</span>
    <span class="n">nb_domains</span> <span class="o">=</span> <span class="mi">50</span>

    <span class="n">transient_fraction</span><span class="o">=</span><span class="mf">0.2</span>
    <span class="n">tau_max</span> <span class="o">=</span> <span class="mi">2</span>
    <span class="n">frac_observed</span> <span class="o">=</span> <span class="mf">0.5</span>

    <span class="c1"># Specify the model</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">lin</span><span class="p">(</span><span class="n">x</span><span class="p">):</span> <span class="k">return</span> <span class="n">x</span>

    <span class="n">links</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="p">[((</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.3</span><span class="p">,</span> <span class="n">lin</span><span class="p">),</span> <span class="p">((</span><span class="mi">3</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.7</span><span class="p">,</span> <span class="n">lin</span><span class="p">),</span> <span class="p">((</span><span class="mi">4</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="mf">0.9</span><span class="p">,</span> <span class="n">lin</span><span class="p">)],</span>
             <span class="mi">1</span><span class="p">:</span> <span class="p">[((</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.4</span><span class="p">,</span> <span class="n">lin</span><span class="p">),</span> <span class="p">((</span><span class="mi">3</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.8</span><span class="p">,</span> <span class="n">lin</span><span class="p">)],</span>
             <span class="mi">2</span><span class="p">:</span> <span class="p">[((</span><span class="mi">2</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.3</span><span class="p">,</span> <span class="n">lin</span><span class="p">),</span> <span class="p">((</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="o">-</span><span class="mf">0.5</span><span class="p">,</span> <span class="n">lin</span><span class="p">),</span> <span class="p">((</span><span class="mi">4</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="mf">0.5</span><span class="p">,</span> <span class="n">lin</span><span class="p">),</span> <span class="p">((</span><span class="mi">5</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="mf">0.6</span><span class="p">,</span> <span class="n">lin</span><span class="p">)]</span> <span class="p">,</span>
             <span class="mi">3</span><span class="p">:</span> <span class="p">[],</span> 
             <span class="mi">4</span><span class="p">:</span> <span class="p">[],</span> 
             <span class="mi">5</span><span class="p">:</span> <span class="p">[]</span>
                <span class="p">}</span>

    <span class="c1"># Specify which node is a context node via node_type (can be &quot;system&quot;, &quot;time_context&quot;, or &quot;space_context&quot;)</span>
    <span class="n">node_classification</span> <span class="o">=</span> <span class="p">{</span>
        <span class="mi">0</span><span class="p">:</span> <span class="s2">&quot;system&quot;</span><span class="p">,</span>
        <span class="mi">1</span><span class="p">:</span> <span class="s2">&quot;system&quot;</span><span class="p">,</span>
        <span class="mi">2</span><span class="p">:</span> <span class="s2">&quot;system&quot;</span><span class="p">,</span>
        <span class="mi">3</span><span class="p">:</span> <span class="s2">&quot;time_context&quot;</span><span class="p">,</span>
        <span class="mi">4</span><span class="p">:</span> <span class="s2">&quot;time_context&quot;</span><span class="p">,</span>
        <span class="mi">5</span><span class="p">:</span> <span class="s2">&quot;space_context&quot;</span>
    <span class="p">}</span>

    <span class="c1"># Specify dynamical noise term distributions, here unit variance Gaussians</span>
    <span class="c1">#random_state = np.random.RandomState(seed)</span>
    <span class="n">noises</span> <span class="o">=</span> <span class="p">[</span><span class="n">random_state</span><span class="o">.</span><span class="n">standard_normal</span> <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">6</span><span class="p">)]</span>

    <span class="n">contextmodel</span> <span class="o">=</span> <span class="n">ContextModel</span><span class="p">(</span><span class="n">links</span><span class="o">=</span><span class="n">links</span><span class="p">,</span> <span class="n">node_classification</span><span class="o">=</span><span class="n">node_classification</span><span class="p">,</span>
                                <span class="n">noises</span><span class="o">=</span><span class="n">noises</span><span class="p">,</span> 
                                <span class="n">seed</span><span class="o">=</span><span class="n">context_seed</span><span class="p">)</span>

    <span class="n">data_ens</span><span class="p">,</span> <span class="n">nonstationary</span> <span class="o">=</span> <span class="n">contextmodel</span><span class="o">.</span><span class="n">generate_data</span><span class="p">(</span><span class="n">nb_domains</span><span class="p">,</span> <span class="n">T</span><span class="p">)</span>

    <span class="k">assert</span> <span class="ow">not</span> <span class="n">nonstationary</span>

    <span class="n">system_indices</span> <span class="o">=</span> <span class="p">[</span><span class="mi">0</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="mi">2</span><span class="p">]</span>
    <span class="c1"># decide which context variables should be latent, and which are observed</span>
    <span class="n">observed_indices_time</span> <span class="o">=</span> <span class="p">[</span><span class="mi">4</span><span class="p">]</span>
    <span class="n">latent_indices_time</span> <span class="o">=</span> <span class="p">[</span><span class="mi">3</span><span class="p">]</span>

    <span class="n">observed_indices_space</span> <span class="o">=</span> <span class="p">[</span><span class="mi">5</span><span class="p">]</span>
    <span class="n">latent_indices_space</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="c1"># all system variables are also observed, thus we get the following observed data</span>
    <span class="n">observed_indices</span> <span class="o">=</span> <span class="n">system_indices</span> <span class="o">+</span> <span class="n">observed_indices_time</span> <span class="o">+</span> <span class="n">observed_indices_space</span>
    <span class="n">data_observed</span> <span class="o">=</span> <span class="p">{</span><span class="n">key</span><span class="p">:</span> <span class="n">data_ens</span><span class="p">[</span><span class="n">key</span><span class="p">][:,</span><span class="n">observed_indices</span><span class="p">]</span> <span class="k">for</span> <span class="n">key</span> <span class="ow">in</span> <span class="n">data_ens</span><span class="p">}</span>


    <span class="c1"># Add one-hot-encoding of time-steps and dataset index to the observational data. </span>
    <span class="c1"># These are the values of the time and space dummy variables.</span>
    <span class="n">dummy_data_time</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">identity</span><span class="p">(</span><span class="n">T</span><span class="p">)</span>

    <span class="n">data_dict</span> <span class="o">=</span> <span class="p">{}</span>
    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">nb_domains</span><span class="p">):</span>
        <span class="n">dummy_data_space</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">T</span><span class="p">,</span> <span class="n">nb_domains</span><span class="p">))</span>
        <span class="n">dummy_data_space</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="mf">1.</span>
        <span class="n">data_dict</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">hstack</span><span class="p">((</span><span class="n">data_observed</span><span class="p">[</span><span class="n">i</span><span class="p">],</span> <span class="n">dummy_data_time</span><span class="p">,</span> <span class="n">dummy_data_space</span><span class="p">))</span>

    <span class="c1"># Define vector-valued variables including dummy variables as well as observed (system and context) variables</span>
    <span class="n">nb_observed_context_nodes</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">observed_indices_time</span><span class="p">)</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">observed_indices_space</span><span class="p">)</span>
    <span class="n">N</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">system_indices</span><span class="p">)</span>
    <span class="n">process_vars</span> <span class="o">=</span> <span class="n">system_indices</span>
    <span class="n">observed_temporal_context_nodes</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">,</span> <span class="n">N</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">observed_indices_time</span><span class="p">)))</span>
    <span class="n">observed_spatial_context_nodes</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">N</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">observed_indices_time</span><span class="p">),</span> 
                                                <span class="n">N</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">observed_indices_time</span><span class="p">)</span> <span class="o">+</span> <span class="nb">len</span><span class="p">(</span><span class="n">observed_indices_space</span><span class="p">)))</span>
    <span class="n">time_dummy_index</span> <span class="o">=</span> <span class="n">N</span> <span class="o">+</span> <span class="n">nb_observed_context_nodes</span>
    <span class="n">space_dummy_index</span> <span class="o">=</span> <span class="n">N</span> <span class="o">+</span> <span class="n">nb_observed_context_nodes</span> <span class="o">+</span> <span class="mi">1</span>
    <span class="n">time_dummy</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">time_dummy_index</span><span class="p">,</span> <span class="n">time_dummy_index</span> <span class="o">+</span> <span class="n">T</span><span class="p">))</span>
    <span class="n">space_dummy</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="n">time_dummy_index</span> <span class="o">+</span> <span class="n">T</span><span class="p">,</span> <span class="n">time_dummy_index</span> <span class="o">+</span> <span class="n">T</span> <span class="o">+</span> <span class="n">nb_domains</span><span class="p">))</span>

    <span class="n">vector_vars</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="p">[(</span><span class="n">i</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">process_vars</span> <span class="o">+</span> <span class="n">observed_temporal_context_nodes</span> <span class="o">+</span> <span class="n">observed_spatial_context_nodes</span><span class="p">}</span>
    <span class="n">vector_vars</span><span class="p">[</span><span class="n">time_dummy_index</span><span class="p">]</span> <span class="o">=</span> <span class="p">[(</span><span class="n">i</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">time_dummy</span><span class="p">]</span>
    <span class="n">vector_vars</span><span class="p">[</span><span class="n">space_dummy_index</span><span class="p">]</span> <span class="o">=</span> <span class="p">[(</span><span class="n">i</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">space_dummy</span><span class="p">]</span>

    <span class="c1"># Name all the variables and initialize the dataframe object</span>
    <span class="c1"># Be careful to use analysis_mode = &#39;multiple&#39;</span>
    <span class="n">sys_var_names</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;X_&#39;</span> <span class="o">+</span> <span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">process_vars</span><span class="p">]</span>
    <span class="n">context_var_names</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;t-C_&#39;</span><span class="o">+</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">observed_indices_time</span><span class="p">]</span> <span class="o">+</span> <span class="p">[</span><span class="s1">&#39;s-C_&#39;</span><span class="o">+</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">)</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">observed_indices_space</span><span class="p">]</span>
    <span class="n">var_names</span> <span class="o">=</span> <span class="n">sys_var_names</span> <span class="o">+</span> <span class="n">context_var_names</span> <span class="o">+</span> <span class="p">[</span><span class="s1">&#39;t-dummy&#39;</span><span class="p">,</span> <span class="s1">&#39;s-dummy&#39;</span><span class="p">]</span>

    <span class="n">dataframe</span> <span class="o">=</span> <span class="n">pp</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">(</span>
        <span class="n">data</span><span class="o">=</span><span class="n">data_dict</span><span class="p">,</span>
        <span class="n">vector_vars</span> <span class="o">=</span> <span class="n">vector_vars</span><span class="p">,</span>
        <span class="n">analysis_mode</span> <span class="o">=</span> <span class="s1">&#39;multiple&#39;</span><span class="p">,</span>
        <span class="n">var_names</span> <span class="o">=</span> <span class="n">var_names</span>
        <span class="p">)</span>


    <span class="c1"># Classify all the nodes into system, context, or dummy</span>
    <span class="n">node_classification_jpcmci</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="n">node_classification</span><span class="p">[</span><span class="n">var</span><span class="p">]</span> <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">var</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">observed_indices</span><span class="p">)}</span>
    <span class="n">node_classification_jpcmci</span><span class="o">.</span><span class="n">update</span><span class="p">({</span><span class="n">time_dummy_index</span> <span class="p">:</span> <span class="s2">&quot;time_dummy&quot;</span><span class="p">,</span> <span class="n">space_dummy_index</span> <span class="p">:</span> <span class="s2">&quot;space_dummy&quot;</span><span class="p">})</span>

    <span class="c1"># Create a J-PCMCI+ object, passing the dataframe and (conditional)</span>
    <span class="c1"># independence test objects, as well as the observed temporal and spatial context nodes </span>
    <span class="c1"># and the indices of the dummies.</span>
    <span class="n">JPCMCIplus</span> <span class="o">=</span> <span class="n">JPCMCIplus</span><span class="p">(</span><span class="n">dataframe</span><span class="o">=</span><span class="n">dataframe</span><span class="p">,</span>
                              <span class="n">cond_ind_test</span><span class="o">=</span><span class="n">ParCorrMult</span><span class="p">(</span><span class="n">significance</span><span class="o">=</span><span class="s1">&#39;analytic&#39;</span><span class="p">),</span> 
                              <span class="n">node_classification</span><span class="o">=</span><span class="n">node_classification_jpcmci</span><span class="p">,</span>
                              <span class="n">verbosity</span><span class="o">=</span><span class="mi">1</span><span class="p">,)</span>

    <span class="c1"># Define the analysis parameters.</span>
    <span class="n">tau_max</span> <span class="o">=</span> <span class="mi">2</span>
    <span class="n">pc_alpha</span> <span class="o">=</span> <span class="mf">0.01</span>

    <span class="c1"># Run J-PCMCI+</span>
    <span class="n">results</span> <span class="o">=</span> <span class="n">JPCMCIplus</span><span class="o">.</span><span class="n">run_jpcmciplus</span><span class="p">(</span><span class="n">tau_min</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> 
                                  <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span> 
                                  <span class="n">pc_alpha</span><span class="o">=</span><span class="n">pc_alpha</span><span class="p">)</span>
</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../index.html">Documentation overview</a><ul>
  <li><a href="../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2023, Jakob Runge.
      
      |
      Powered by <a href="https://www.sphinx-doc.org/">Sphinx 8.2.3</a>
      &amp; <a href="https://alabaster.readthedocs.io">Alabaster 0.7.16</a>
      
    </div>

    

    
  </body>
</html>