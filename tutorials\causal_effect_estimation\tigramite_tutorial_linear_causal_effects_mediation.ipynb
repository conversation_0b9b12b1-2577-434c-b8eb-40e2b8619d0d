{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Linear causal effect mediation with `TIGRAMITE`\n", "\n", "TIGRAMITE is a time series analysis python module. It allows to reconstruct graphical models (conditional independence graphs) from discrete or continuously-valued time series based on the PCMCI framework and create high-quality plots of the results. \n", "\n", "PCMCI is described here:\n", "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, \n", "Detecting and quantifying causal associations in large nonlinear time series datasets. Sci. Adv. 5, eaau4996 (2019) \n", "https://advances.sciencemag.org/content/5/11/eaau4996\n", "\n", "For further versions of PCMCI (e.g., PCMCI+, LPCMCI, etc.), see the corresponding tutorials.\n", "\n", "This tutorial explains the LinearMediation class covering linear lagged as well as contemporaneous causal effect and mediation analysis. See the following paper for theoretical background:\n", "<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. 2015. “Identifying Causal Gateways and Mediators in Complex Spatio-Temporal Systems.” Nature Communications 6: 8502. https://doi.org/10.1038/ncomms9502.\n", "\n", "__Note 1:__ The above paper only covers the lagged case which is here extended.\n", "\n", "__Note 2:__ For general linear and nonlinear causal effect analysis including latent variables and further functionality use the CausalEffects class and the tutorial ``tigramite_tutorial_general_causal_effect_analysis.pynb``. However, the LinearMediation class is much faster for its task.\n", "\n", "Last, the following Nature Communications Perspective paper provides an overview of causal inference methods in general, identifies promising applications, and discusses methodological challenges (exemplified in Earth system sciences): \n", "https://www.nature.com/articles/s41467-019-10105-3"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import numpy as np\n", "import matplotlib\n", "from matplotlib import pyplot as plt\n", "%matplotlib inline     \n", "## use `%matplotlib notebook` for interactive figures\n", "# plt.style.use('ggplot')\n", "import sklearn\n", "\n", "import tigramite\n", "from tigramite import data_processing as pp\n", "from tigramite.toymodels import structural_causal_processes as toys\n", "from tigramite import plotting as tp\n", "\n", "from tigramite.models import LinearMediation, Prediction\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Causal effects and mediation\n", "\n", "The preceding sections were concerned with estimating causal links. In this section we discuss how the estimated time series graph can be used to evaluate causal effects and causal mediation in a linear framework as discussed in more detail in <PERSON><PERSON> et al, Nature Communications 2015. \n", "\n", "__Note:__ For a general causal effect analysis please refer to the ``CausalEffects`` class and tutorial.\n", "\n", "Consider the following model of a simple causal chain with an added direct link:\n", "\n", "\\begin{align*}\n", "              X_t &= \\eta^X_t \\\\\n", "              Y_t &= 0.5 X_{t-1} +  \\eta^Y_t \\\\\n", "              Z_t &= 0.5 Y_{t} -  0.5 X_{t-1} +  \\eta^Z_t\n", "\\end{align*}"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# links_coeffs = {0: [],\n", "#                 1: [((0, -1), 0.5)],\n", "#                 2: [((1, -1), 0.5)],\n", "#                 }\n", "def lin_f(x): return x\n", "links_coeffs = {0: [((0,-1), 0.8, lin_f)],\n", "                1: [((1,-1), 0.8, lin_f), ((0, -1), 0.5, lin_f)],\n", "                2: [((2,-1), 0.8, lin_f), ((1, 0), 0.5, lin_f), ((0, -1), -0.25, lin_f)],\n", "                }\n", "var_names = [r\"$X$\", r\"$Y$\", r\"$Z$\"]\n", "    \n", "data, _ = toys.structural_causal_process(links_coeffs, T=1000, seed=3)\n", "true_parents = toys._get_true_parent_neighbor_dict(links_coeffs)\n", "\n", "# Initialize dataframe object, specify variable names\n", "dataframe = pp.DataFrame(data, \n", "                         var_names=var_names)\n", "med = LinearMediation(dataframe=dataframe)\n", "med.fit_model(all_parents=true_parents, tau_max=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We fit the linear mediation model based on the true parents here, in practice these could be either known by expert knowledge or estimated with a causal discovery method, eg PCMCIplus. Then you can use a convencience function to turn the graph string array into the parents dictionary:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#parents = toys.dag_to_links(graph_output_from_pcmciplus)\n", "#print(parents)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that you need to supply an entirely *oriented* graph, ie, all edges need to be directed and they cannot be ``o-o`` (unoriented) or ``x-x`` (conflicting). You have to decide their orientation then. \n", "\n", "If the assumption of a linear model is justified *and* causal sufficiency is fulfilled, the *link coefficient* of $X_{t-\\tau}\\to Y_t$ estimated from standardized time series (default in ``LinearMediation`` class) corresponds to the change in the expected value of $Y_t$ (in units of its standard deviation) caused by a perturbation of one standard deviation in $X_{t-\\tau}$. Let's check the link coefficient of $X_{t-2}\\to Z_t$"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Link coefficient (0, -2) --> 2:  0.0\n"]}], "source": ["print (\"Link coefficient (0, -2) --> 2: \", med.get_coeff(i=0, tau=-2, j=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The link coefficient is non-zero only for direct links. But the direct link occurs at lag -1:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Link coefficient (0, -1) --> 2:  -0.06613437086343765\n"]}], "source": ["print (\"Link coefficient (0, -1) --> 2: \", med.get_coeff(i=0, tau=-1, j=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can get a matrix of all link coefficients as follows:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[ 0.          0.78263658  0.          0.          0.        ]\n", "  [ 0.          0.2430355   0.          0.          0.        ]\n", "  [ 0.         -0.06613437  0.          0.          0.        ]]\n", "\n", " [[ 0.          0.          0.          0.          0.        ]\n", "  [ 0.          0.80133677  0.          0.          0.        ]\n", "  [ 0.27238637  0.          0.          0.          0.        ]]\n", "\n", " [[ 0.          0.          0.          0.          0.        ]\n", "  [ 0.27238637  0.          0.          0.          0.        ]\n", "  [ 0.          0.79684198  0.          0.          0.        ]]]\n"]}], "source": ["val_matrix = med.get_val_matrix(symmetrize=True)\n", "print(val_matrix)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can plot the graph with edges colored by the direct link effects. Since only one contemporaneous edge can be plotted between two nodes, there can only be one val_matrix value. This means that the entries need to be symmetric: ``val_matrix[i,j,0]=val_matrix[j,i,0]``. Here we do this by setting all zero contemporaneous entries in ``val_matrix[i,j,0]`` to the non-zero value in ``val_matrix[j,i,0]`` via ``symmetrize=True``."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Get graph array from parents dictionary\n", "graph = toys.links_to_graph(true_parents)\n", "\n", "tp.plot_graph(graph=graph, val_matrix=val_matrix, link_colorbar_label='cross-link coeff. (edges)', node_colorbar_label='auto-link coeff. (nodes)'); plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The *(total) causal effect* evaluates, in addition, *indirect* effects and is now simply computed by summing over the products of link coefficients along all possible paths between the two variables. For example, here the causal effect of $X_{t-2}\\to Z_t$ is"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Causal effect (0, -2) --> 2:  0.****************\n"]}], "source": ["print (\"Causal effect (0, -2) --> 2: \", med.get_ce(i=0, tau=-2, j=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The *mediated causal effect* quantifies the contribution of an intermediate variable to the causal effect. For example, let's look at the contribution of $Y$ on the causal effect of $X_{t-2}\\to Z_t$."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mediated Causal effect (0, -2) --> 2 through 1:  0.15760891959684017\n"]}], "source": ["print (\"Mediated Causal effect (0, -2) --> 2 through 1: \", med.get_mce(i=0, tau=-2, j=2, k=1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since $Y$ mediates the causal effect with a positive sign, the MCE is larger. Mediation analysis is a powerful tool to quantify not only direct causal links, but also indirect pathways. This can answer questions such as \"How important is one process for the causal mechanism between two others?\". In the ``tigramite.plotting`` module there are functions to visualize causal pathways both in the aggregated network and in the time series graph. Here we look at all causal pathways between $X_{t-4}$ and $Z_t$:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAhIAAAHSCAYAAAC5PSKWAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAACksElEQVR4nOydeZwcZZ3/P09V9d3TPfdMZnLfCQECCZBwJYAcAeRSQUQO8QJFxRV31xUU5XJ1vdeVZRUVPFbEn7qiiAqIqOE+ExIScl9z39NnVT2/P6q7M5OZnqmn+qjuqu/79YoO3VX1PPOdp6q+z/dknHMOgiAIgiAIC0h2T4AgCIIgiOqFFAmCIAiCICxDigRBEARBEJYhRYIgCIIgCMuQIkEQBEEQhGVIkSAIgiAIwjKkSBAEQRAEYRlSJAiCIAiCsAwpEgRBEARBWIYUCYIgCIIgLFPVigTnHF/84hfx1FNPlWyMv/zlL2CMTfrvmWeeKdm45aYcsjyS733ve2CMIRwOl23MclEOeb7yyiu44IILMHv2bAQCAdTX12Pt2rX48Y9/XLIx7aIc8nziiSdw/fXXY+nSpQiFQmhvb8fFF1+MF198sWRj2kU55Dk8PIx//ud/xjnnnIOmpiYwxnD77beXbLxqxY5nb7GpakVi27Zt+PznP49Dhw6VfKy7774bGzduHPdvxYoVJR+3XJRTlgBw4MAB3HLLLWhrayvLeOWmHPIcGBjArFmzcPfdd+P3v/89HnjgAcydOxdXX3017rzzzpKNawflkOd3v/td7N69G5/4xCfw+9//Ht/85jfR1dWFNWvW4IknnijZuHZQDnn29vbivvvuQzKZxCWXXFKycaqdcj97S4Fi9wQKIbtTWLVqVcnHWrRoEdasWVPyceyinLIEgBtuuAGnn3466uvr8fDDD5dlzHJSDnmuX78e69evH/fZhRdeiF27duG+++7DrbfeWrKxy0055Pmd73wHzc3N4z4777zzsHDhQtx9990488wzSzZ2uSmHPOfMmYP+/n4wxtDT04Pvfe97JRurmin3s7cUVK0isWrVKrz00ksAgMWLFwMAwuEwhoaGwBizc2pVR7ll+eMf/xhPPfUU3njjDUe97LLYvTYbGxvR1dVV8nHKRbnkeaQSkR1n+fLl2LdvX9HGsZtyyZOew9Nj97OiWFStInHffffhhhtugK7r+M53vgMACAQCJRP+Rz/6Ubz73e9GMBjE2rVrcdttt+HUU08tyVjlppyy7Orqws0334wvfelLmDlzZtGvXwmUe23qug5d19Hf349f/OIXeOyxx/Cf//mfJRnLDsotz7EMDg7ipZdecpQ1wk55EuNxzN+CVzHNzc384x//eEnHeOmll/gnPvEJ/qtf/Yr/9a9/5ffffz9ftmwZl2WZ/+EPfyjp2OWkHLLknPN3vOMd/OSTT+a6rnPOOb/22mt5KBQq+bjlplzy5JzzD3/4wxwAB8C9Xi//r//6r7KMW07KKc+xXHXVVVxRFP7CCy+UfexSUm55dnd3cwD885//fNnGrBbsWtvFpGoVib1793IA/Ec/+lHeY5588sncA3a6fy+//LLpsfv7+/nMmTP5McccU4TfxH7KJcuHH36Ye71evnnz5txnTlQkyr029+zZw59//nn+u9/9jt9www1ckiT+la98pci/lX3Yda/feuutHAD/9re/XaTfpDKwQ56kSEyOmb9FNVC1rg0zASpLlizB//zP/5i63uzZs02PXVtbiwsvvBD33nsv4vE4AoGA6XMrkXLIcmRkBB/96EfxsY99DG1tbRgYGAAApFIpAEYGgsfjQSgUEpx95VHutTl79uzcMeeffz4A4DOf+QyuvfZaNDU1mRqjkrHjXv/CF76AO++8E3fddRduuukmcxOtEux8dhLjcUKgJYDqdW3ceuutPBgMclVVbRk/a06Ox+O2jF9MyiHLXbt2Tbuzufjii0s2fjmxe23ef//9HAB/5plnbBm/2JRbnrfffjsHwG+//fayjFdu7FifZJGYHLufFcWiai0Sr732GpYuXQpZlss+dn9/Px555BGsXLkSfr+/7OMXm3LIsrW1FU8++eSEz7/0pS/hqaeewqOPPorGxsaSjV9O7FybAPDkk09CkiTMnz/flvGLTTnleccdd+D222/Hrbfeis9//vMlH88O7F6fxGGc8reoWkWitrYWTz31FH7zm9+gpaUFM2bMwJw5c4o+znve8x7Mnj0bq1evRmNjI7Zv346vfvWr6OzsxA9/+MOij2cH5ZCl3++fUPMAAH74wx9CluVJv6tWyrU2P/ShDyESieDEE09ES0sLenp68Itf/AI///nP8elPf9oRbg2gfPL86le/is997nM477zzcMEFF0yoXOuUOjLlkicAPProoxgdHcXw8DAA4I033sjVjTn//PMRDAZLMm61UM6/RUmx2yRilR07dvB169bxUCjEAfCvfvWrJRnnnnvu4StXruTRaJTLssybmpr4pZdeyp977rmSjGcH5ZLlZDgx2LJc8rz//vv5aaedxhsbG7miKLy2tpavW7eOP/jggyUZzy7KJc9169ZN6XpzCuW83+fMmZNXnrt27SrZuNWCnc/eYsI457zMugtBEARBEA6hqnttEARBEARhL6RIEARBEARhGVIkCIIgCIKwDCkSBEEQBEFYhhQJgiAIgiAsQ4oEQRAEQRCWIUWCIAiCIAjLkCJBEARBEIRlSJEgCIIgCMIypEgQBEEQBGEZUiQIgiAIgrAMKRIEQRAEQViGFAmCIAiCICxDigRBEARBEJYhRYJwPd1/3Ygd9/4AWiJp91QcwaFH/4zdP/xf6Om03VNxBPt/8Rvse+g34Jpm91QIYlJIkSBcT7KzC1vv+jqeOuNiHPjV78B13e4pVTXx/Yew+ba78de3XYaOPzwBzrndU6pqRnftxWufug1Pb7gC3X/daPd0CGICpEgQrkeNxQEA8f0H8crHP4O/v/0q9G58weZZVS9aRp6jO/fgxQ/ejGfe+T4MvPy6zbOqXrLyHN6yDc9d9WE8994bMLRlm82zIojDKHZPoFxwzgFNBVdT4LoKcA4wBsYkMMUHKB4wxuyeZtVwWJ5JcF0bI08ZzOMF5OqRpxaPj/vvwdc245nLr0frhrNw1J2fhb+5seRzMOSZzqzPMfKUZDClyuQZi437777nXsLfL7oK7e+4EMs//8/w1tWWfA45eaZT4HysPJWMPJWqkad6xPrsfuof6H76Gcy+6p1Y9m+fhBIOlXwOnHNATRnrk+vj5enxAlL1yLMS4FwHV9OGTMfKU/YY61OSq0qejlYkOOfg6QT02DB4Kg5gGhOr4oMcrAHzBcEYGWuOhHMOnopDjw+DpxKYTp7M44cUyMqzcm8KbTQ26ecdjz6O3o0v4Kgv/ivaLjm/6L8D5xw8GTPkmU5MczQD82bk6Q1UtDyzFp4jOfDLR9Dz9DM4+p7b0HLOGUUfl3N9jDyni3dhYN5ARp7+ipbnpOtT17H3wYfQ/eTfcMxXbkfjqWuKPi7nOnhiFFp8GFBTUx/MsvKMgHl8FS1Pu+C6Dj0xAj0xDKjTxA8xyZBnMAKmeCtenow70IFpPKBHoY30A7qFACXGIAWjkILRiv8DlgPOOXhiBNrogEV5SpBCUeMhU0J5qqMxJDo6kR4YgpZMQk+mMv+S0JIp6CnjZz2tQvJ6IHm9kLxedPz+T+h6/K9TXrvl3DNx9D23wdfUUPA8OefQ40PQRwcBbiEeQ5INefprSirP9PAIkp3dSA9m5ZmEnkgZP6fSxn8nk+CaDub1QPIYMt3/i9+g75mpXUPtl12I5bf/C7x10YLnyTmHHhuEHhs0dnaiSDLkUB2YP1QyeXLOoQ4NI9HZjfTQcEZ2xvo8vFaT0FOpnDxlrxeS14PdP/gZBl9/Y8rrz776ciz77D9BCQWLMFcd+ugg9NgQpt18TYasQA7XQfKV3lJSDXBdhz7aDz0+Amvy9EAO10PyBYo+t2LhOEWCayq04d6MBaJAZAVypAmSx1f4taoUrqahDfeY2OGZQPFAiTQZpjvReXCOREcnhrdsR2zvfiQ6upDo7DL+v6MLyc5uqMMjhc9xCjx1tVhxx2cw46LzLL9wuJqCOtQz/Q7PDIoPSqQRTPGIz4NzxPcfxPDW7YjtO4BkZ3dGlp1IZH7OZ6kpFr7mJhz9759Dy9vWWb6Gnk5CG+oBtMIzRJjXD7mmEUwWN9RyXUds7wEMb92O+P4DORlm12aioxNafDqrU2EEZrXhmP/4IhpPPtHyNfRUwpCnrhY8H+YNQo40gElywdeqVvRkDNpwr7UN2BEwfwhyuL4i5ekoRUJPJaANdlrblUyBFK6HHIwU9ZrVgJ6MQRvshiUtegrkSCMkfzjv92oshpE3d2Bo6zYMb9mOoS3bMLxlG9KDQ0Wdh1Vaz38bVtz1WfgaxawTemIU2lB30ecjR5sh+fLvRNWRUQxt3Y7hLdsMWW7djuGt20uueJll5jsvwvLb/xmeqNg9pseHjYd0MWEMcrQFktef95D04BCGt27H0JbtGN56WKZaHpdOuZlz3ZVY+plPQAmat04YVp0h6KP9xZ0MkyDXtrhuM8Y5hz46YFjJiokkQ6ltsbQZKyWOUST0VBzaQGfJri+F6yAHCzfDVgt6cjSjRJQGuaYBUqAGnHPEdu9F33Mvo+/5l9H/wssY3bmn6MpgsfHW1+GoO/8NbW8/19TxenwE2nBPyeYjR5og+UPgnGPkrV3of/4lQ57Pv4LYnn0lG7dY+FqaccyXP4/mM08zdbwWG4Q+UuSX3hjk2hZI3gC4rmP4zbfQ99xL6H/+ZfS/+Cri+w+WbNxiEZw9E8f8xxfRsHa1qeO1kf7iv/RyMMh1LZA8+ZUzJ8E5hzbcB54YLs0ATIJS11pRyoQjFAmupqD2HUKxd85HMt1O2ino6QS0/o6SjyNHm7Hv57/Fps/eVfKxSsWMC8/BUXf+G3wN9XmPMSw7XSWfi1zbih33PoBtX/nPko9VKmZefgmWf+6WKa0TpbLsHIlS14Ytd38Tu773YMnHKhVzr38PlvzLx6e0TmixIegjfaWdCGNQ6tvAZHE3XLWhjQ5AHx0o7SBMysizMvIlqj41gXMOtQTm98nQhnrBtcJ9h5UM5zq0wdLtnMeiDfWgdtUxZRmrVBx65I/461mXojNPsCbXNcPnXAa0oW7UrTq2LGOViv0P/Rp/fdtl6N34/KTfGzFQ5ZGnOtSN2tUryzJWqdh9/0/x9DnvwsArmyb9nqvp0isRAMA51MEexxcn09PJ0isRAMB1IxawQuRZ9YqEPjpQlEArc3BoQ90V88crBdpwf1ECrUzBdQTb6uApQ12BUpLq7ccL7/sYtn/zvglrQxvutZaZYQVdQ83iWZB8lWPytEKiowvPXvkh7PrBT8fJk3NuKGXluv+0NOqOWViesUpIbM8+bHznddj/8P+N+9zIJCm9ZSeHmoQer4w4p1LAuV4WS1luvFQcPFEZcU5VrUhwXSuhXy/PmOlkpoaC8+BaunR+vXykE6g/6fjyjlkKOMe2//hPvHTDp6Bmsh14OgmeLG3mw5FIPIW646vbygMAXNPwxue+hNdu+XyuBwpPJ0zU2yguio+hZtmiso5ZCvRkCq9+8la8cfuXoavGRoEnY8XJHhKZx8iAY0vQ64kRoMwWa22kvyI2tpYUibvvvhuMMbz88ssTvrvmmmugKAp+97vfFTy56dDjZX7p5cZ1plZtlzzrVx1ly7iloOP3f8Y/Ln4vRnfvg2bTOqlbXf2KRJb9D/0az1x+PRIdXZm6BuWn/oTqdheNZdf3f4zn3nsDUn390GyRJzdeuA4jm/VS/oH1sm9WJsOSInHTTTehtrYWd99997jPP/e5z+HBBx/Et7/9bVxwwQVFmWA+jKI+9rz4eCoOXjZ3SnngXLdNnnUrl9gybqlI9Q9i530/gjZcXmtZlrqVi20Zt1QkOrux63sPQk+M2jJ+3cqltoxbKuL7DmL3D39WdutOFj0+VBG76GLC04myWyOy2KMQjsdSyGckEsHHPvYx3HXXXdi6dSuWLl2KH/zgB7jjjjvwz//8z7jxxhuLPc+JaOmiFPmwip5KQA44JwKZp1O2pVyG5s2Ct7EeqZ4yBH2VgED7DNSvWYX6NavRcNIqBOfOBk/FSpo+OxXRoxZBDvhLXgCpVATnzMrI8njUr1mN4Kz20tSMMEnd8csBSQKq1CQfWjAXDWtWo/6kVahfswqBGa3QRgeLXzPCLJpqPLsrJOOgGBSlAKJV1CS4roNJ9kUqWP5L3nzzzfj617+Oe+65B1dffTU+/OEP4/LLL8eXvvSlYs4vL0WptFjo+IEaW+dQTLhqnzwZY6g/cSU6fv9EUa7nbaiDUlMDyeuB7PNB8vuMktg+H2SfF0yWjRLP6TS0WBx9z74oPEagfQZmXHQe2i7egMjyJRMqXerp8vqexyJ5PKg9/mj0/n3yzAchGIOvsR5yKATZ54Xk80HyeSH5vIZsvR5AksDTKvRUCunhEQy8+KrwMMG5s9F28Qa0XXQeahYvmPA9L7MvfyyemjAiyxdhaNObhV9MkuBrrIcSCuXW5Dh5+rwAY+CZ9ZnqG8Dga5uFhwkvXoC2iwx5hubNnvC9nfd7dvxKSV0sBra/j9QU2BRF1EqN5b9kfX09brzxRnz961/Hr3/9a5x00kl44IEHytabws4HC2D/wik2dv8+9auPNqVIMI+CmkULEF44D/62VvhbmuBvbYGvtRn+1mb4m5uMl5tJkr19+PPK9aaO9TbUYcaF56Lt4g2oW3XslDsA++V5jClFQvJ5EV68AOEF8+Cf0WLIMPevBb6mBkge8/IceWsXnjrjYlPH+lubMePthjIWPWb5lM8O3SYzfJb6E441pUhIfj9qlixEeOFc+Fsz8mw5LFNvUwMkxfxjt++FV7Dx0mtMHRuY1WYoDxdvQM3SRVPK0+71ydMpwCG9OIzmkDa/j9QkUI2KBABceeWV+MpXvoJwOIzf/OY38PnKVwaV2+jWAGCrW6UUcM3e36fu+BUTPvO3NqNm2WJEli1GzdJFiCxbjNCCuUIvtumYrp+EUhNG63lnoe3iDWg45UTTLwFerhTaPNStPnrCZ4H2GahZtgg1SzMyXbYYoXmzhV5s06FOI09PbRQzLjwHbRdtQP1Jx5s3x9q8PutXH4PdP3ho3GeB2e2ILF2cWaOLDHnOmQUmF68XwnTr09fUkFNua48/xvxGzubnl933R3HhKEcdoymx+f6w/AQZGhrC+973PgBAT08PYrEY6uvzV/crOnYH69g9ftGx9/cJzp6BpvWnIDR/DupPOB51JxwHf0tTycedrD+C5POi5ez1aLtoA5rOOBWy34KCbPPyiCxbiPq1JyB69LKMPFdOWX2zWEwmTzngR+uGt6Ht4g1oPG2NRUXQXoHWHb8CdScch/oTjkPdCcehbtXKonQunY7J5KnUhNF6/tlov3gD6teuLqoiWDac9PisiHeBvXOwtALT6TTe8Y53YOfOnfj1r3+Nd77znfjKV76Cb37zm8WeX37sbu9t9/hFx97fhzEJJz743bKPq8YO7/iUmjDmXHMF5l5/FfzNjYVd2OblIXkUrH3o+2UfV4sffvF56mox7/qrMOfadxfhpctg58NSCYdw8v/7UdnHHbs+fc1NmPfB92L2Ve+Cp6bKS/U76fFZEe8Ce+dgSZH4wAc+gL/85S945JFHcO655+K9730v/ud//gef/exn0dzcXOw5TgqTZHt1sAps5VoITFbsDbgsojlYBG00Dl9zI+Z98OqiPqCZpNhaTt2uQDZ1NIZA+wzM+/C1mHXFJUIdKKdElgHVvqwJu1o3a6MxhObNwfwbr0P7ZW+HXKyqpZJsq3uDSVVoRckLM5QJOy0TNj0/swg37br11ltx11134Xvf+x7e//73AwC2b9+OZcuW4dOf/jTuueeekkz0SErdTXE6mD8MJVLgrrWCKHU3xemQghHI4TK6xjKk+gchB/zW3BdTUNpuitMjheogh8rfrTbV1w+lJlzUOBbAKDVuV50T4HC32nKT7OmFt6626Iq2OthlayEjOdoEySHBlgCg9nfYVpcDMBr2STYGWwolnt5333246667cOutt+aUCABYtGgR3vWud+G//uu/MDAwUOw5Tgrz2NtPQPKUL7C0HDDF3t/HrvG9ddGiKxEAwGxeH3bdH976uqIrEQBsb5ls19/T19hQEmud7evT5udNsbFfnja/D80e+Pvf/x4f+chH8N73vhd33HHHhO8/+9nPYnh4GN/61reKOsG8yB5b3Qt25uyWAubx2urrc548bXywMGb7g63YMG/AvsEl2XjeOAjJTnnKivNcw3bKU/HZWowKsODaqCTsqs7GvAEotS1lH7fUaCN9ttSLZ74Q5Ehj2WqQlAt1qMeW7nxSIAIpXOc8eQ50gafKb46XQrWQglHHyTPddwiwIS5KqmmA5A87Sp6cc6h9B8vYifowcqQJkt9eN1FVd/+UAvZELkvBiC3jlhopYM/vJYFDe3Oj47qqyjatE6aloG17xvaibcVGCtpUSTYxCv2t520Nni0FtqxPxoCRfug7X7K/FlARYYzZJE8JzFekgOYCqGpFgkkypFBtecf0+ME8zjLDZ2GyUvaAMgZA3/MaEB+Ctv3ZiuhkVyyY4gUrc0CZIc/XgdggtO3P2RoAVmyYx192EzLjHHzfJvCRPmhvPe8o5Yz5gkCZfetM08D3vwE+1A19x4uOUs6YP1x2F5gcrq8Iy05VKxIAIAWjgFKmPx5jjjTBj0UK1wHlSs3SVKBz5+Gueam4oUw4qM2wXFMPsDLdZqoKdLx1OK0vMWIoE3Y2FCoijDHINQ3li+VRU8Ch7YfT+mKDhjLhkPL4jDEokdIXfcuNl04CHdtz/81H+qDteMExnZQNeZYvk495A2A2uzSyVL0icfhmKP3DRa5pdFSjmclgTIIcLcPDRUtDHRnEaP388fVA0knj5Wdjql8xYZJcHnmqKaRjQxitnzdenskYtG3OsfQwWYFcU/qHNU8nkYrHEaufO/6L+LCjLD1M8RjKWYnhqTiSiSTitUc0EBsdgLbdOZYe5vFBCtWVYSCpoja1VR1sORY9lYA20FGy60vhett83nagJ2PQBrtKcm2ua0hoDMmaVgBAoGcH/IMHxh8keyAvXA0WLH8thFKgJ0agDZWm7gnXVMS5glTYKAYX7HoTvuHO8QcpPsiLTjDMrw5Aiw1BHylN23ldTSPOfEiHDIUldGgTvLEjxvIGDXnaGa1fRLTRAeijAyW5tq6mEZcCSAfrAc4RPvgaPIkjaqz4w5AXnuCIbCPOOfSRfujxEgWuMwlKXavtKZ9jcYwiAQB6OgltoBPgxa2AZ1cxGrvRU3FoA10oZmlijUuIKSFonjEPYF1H5MBLkI+MyJcUyAtXgZVDwy8DenIU2mB3Ua+pQkJMDkMfG7ejq4jsexHykRH5itd4WDtkLevxYWjDvUW9pgoJo0oN+Jg6B0xNIbL/RUhHmuA9fsiLTqyIYLdiUIqidGnIiHlqwOXDLz0pnUBk34tg/IhgS1/QkKcDYtA459BHB4pflE6SodS2gpXLnW8SRykSgLE704b7ipMmJnugRBodoSVbhWtpaEO9BZtyOYCUJ4S4FJg0ZkBOjqBm/8tgRyotsgJ54YlgDrEGcTUNdain4LQ7DiDpqUFC8k8aM6DEBxE++OpEh5/ihbzopIrxrRYKTycNeRboZ59Onp7RHoQ63pgoT68f8qI1jqmDoqcThuWswCBIDiDhiSApTy4X71AHQt3bJn7hDxvKRAXttgtBT8UNeRYhQ4X5w0Zwpc01IybDcYpEFj0xCm2039oNwSRIwYgjc8etwDkHz8rTwg2hMxkxXx3UaUJy/P17EejbPfELxZN5+TnDLM85hx4fNnYrVuQpeRDzRqeVZ6B3J/wD+yd+4fFDXnySY8zynHPosSFDnhaskZrkRcwbhTZNnNWkLiMA8IUyO2lnbDg416GPDhqmeQuvB1X2I+apgT6NPEOHNsMbm8SiFIgYbiOHFAEz5DkAPTYMS9ZdxQs5XGdvEbFpcKwiAWRegOkk9PiQUaNgyoeMUQ1QCtSA+YKkQEyCIc8E9NgweDo+zUOGgXl9UL0RjGgmn0eco+bgq1ASk/gWPT5DmXCIGRnIyDMVhx4fztTQmEJIjIF5Akh7wxhVTT6OuI6a/S9DSY1O/M4XNOTpkJcfkJFnMmbIM53EdPKEN4C0pwajqslHoK4isu8lyOok1rlAjWE5qzCTcyFwrh8hzylgEuANIOUJI2ZSnkxLIbJvEpcRAITqjBgpB1XA5Fw3NmTxYSMjaCqYBMkXNN5HVXCPOlqRGAvnHNA1cDUJrmkwHjIMTJIMM5rsIeVBAEOeKng6lSksk5WnnJGngmRaw3BCLBpbSscR2ffSRP8pAHgDxk7aAT7UI+GcA5oKrk4iT48XkBTEUypGk2Im/LwuI8BxZuSxGPJMZ+SpIydPWTb6PEgyYsk0Yikxi2VelxEAhGohL1jtyMyu/PJUjPUjyRhJpJFIi8nTM9qLUMfmSeXJahogzV9Vkab8QuGcA2pGnvwIeXp8VadAuUaRIMqPzjn6RxLQBZeYd+gQQt3bJ//SwS+/6dB1jr7RuLC12de/F8HJXEYAEIwaAZgOfPlNh6br6B9JCBub87qMALBwA6QFx1fdi6AYqJqO/lHxWKpg1zb4hifPuGPRFkjzjgUrVy0WwhL01yFKhsQYIgHxF34qMgPpQO3kXyZGoL3lnCI2IkgSQ42FTqXJ2llQfXniS2KDjitXbBZZkhD2i6/PeN2c8VlHY+AjvdB3v5rZZboLRZYQ8om7dmIN86HLk/8d+GAn9L2bQPvdyoYUCaKkeBQZAa/4bjfWtAg83y4kPgRthztffj6PDL9HcLfLGGJNi8HzBL/xkT7ou17JmKzdhc8jw6sIylOSM/KcHD7YBX3P6658+QW8Cjyy4GtFVoz7Pc/XvO8g9P1bXCnPaoEUCaLkhHweyJJY/InuCSB+ZFXBsYxmG/+47+UX9nshCcbzaL4wEnWz8n7Ph7qh73nNdQ9rxhhq/F4ILk+ogSiSkRl5v+f9h6Dv2+xOeQa8wnWG06EGpMP5K8Dynr3QD+VxdxK2Q4oEUXIYY4gELJjko+1QffnrR/Bhd5qRmUWXUaJuNjRv/qwXPtDhSjOyJDGELbiM4g3zoSn5z+O9+6EffNN18pQlCWEL6zPWuAC6lN81wjt3Qu/YUcjUiBJBigRRFiz5TxnDaPNi8Cl23271oXoUGUFRlxGTMDqFSR4AeN8B6Ae2uk6ehstIUJ7TuDgAgHftBu9038vPp8jwCbqMuOxFrHHBlMfoh7ZD795TyNSIEkCKBFE2rPhPdW8Qibo5Ux7jVh9q0OeBImiT1/wRJKPtUx7Du/dA73irkKlVJWG/uAtODdYhVdMy5TH6obegd+0uYGbVB2Ms44ITOy9d04xUsH7KY/T9W6D3HpjyGKK8kCJBlI2s/1SURO0sqN6pq1oaPlR3vfwMeVowydfPhTZNLQ7escO1Lz9R4lNkHWTRD2x13cvPcBlZcHE0LYI+Tfqsvvd16CVqKkiIQ4oEUVZkyZqLI9acP+sgC+/cAb3vYAGzqz4UWRJ3cZgwyQOZl1+Rm4xVOl5F3MXBZQ9ijQunl+e+TeBFbopV6fg8iriLQ/Eh3jC1iwOAER8VH7Y6NaKIkCJBlJ2AV4Ei6OLQfGEkamdOe5y+dxN4idohVypBC1kxaqAWqSmyDrLou18FT4xYnVpVEvJ7hLNi0uHGXNvxvHAObdfL4Kl4AbOrPsJ+72R90KYkFWnNX0smi65B2/ni9OW7iZJDigRRdrIpd6Ik6udA80zTa4Pr0Ha+nOld4Q6syjPWMA/6FFkHAABdNWp2TNcbwEFIFl1wsaaF0KVprBlqCtrOl8AL7K5ZTVh3cSzOX0smSyoBzaU1UCoJUiQIW1BkCUFhF4dkZHFMd5yahLbLXQWrLBX+khSMTlEIKEcqBn2Xu9JsvYoMn2DhLy57EZ8m6wAAEB92XcEqnyLDqwgGWnv8iNfPm/7A0X7o+99wlTwrDVIkCNsIehVhk7yZrAMAQGzIdWmhIZ+4SV4N1iMVbp72OD7SC33/VqtTq0osmeRrWqbNOgAyacsuyozJBrKKFqpKRtug+vPXksnCe/eDU1qobZAiQdhGQVkHyvQdQHn/IfDOnVamVpVYzYqJNy6ALk9vHeI9e6H37LUytapEsuoyaloEbqJpF+/YAb3/kJWpVSWyJCEkKk/GjNonJjQ6/cBW6EM9FmdHFAIpEoSteGTJgklezmRxTI9+aDv0gU5Lc6tGCsk6MIO+bwv04V4rU6tKfB5FuBcHV3yINcw3day+53Xw2KCVqVUlfo9ckloyuWN3v+K64OBKgBQJwnas9OJQA7XTFgLKou95zVVpYtayDpqQCjaYOJIbDb6SMWuTq0JqLGUdzEDaH53+wGxwsEsyDwqrJROa/kBNNYJZVfd1B7YTUiQI27GadRBvmDd9lDyQSRN7CTztjswDyWphpcYF00fJA4CWdlXmgSQxhH1WekcsnLb2CQAgnTDk6ZLgYKu1ZOImanUAAJIxwzLhouBguyFFgqgIrGQdcNmLxFQdQseSihs5/C5JE/N5xLMOdI8fidr8HULHkRjJNExzRzCr0W5c0CTvCyEZbTN3cGwQ+l73dAu1UktGDURNBQYDmYZ+B960MjXCAqRIEBWDlayDZGTGtOWzc7gsTSzsEzfJJ2pnmQpkBTKtxw9uszCz6sNq1kG8bs605bOz8P6D4C4pS27dCjnfVCArkOkZ07tfeAxCHFIkiIrBkv+UMcSaTJo8kUkTc0nmgSRZeFhLEmKNC8zLs2uXa8qSG1kHgiZ5WUG8wUQthAz6wTdd00PCSi0ZrngRNxl4CQD6vs3gI32iUyMEIUWCqCiMrAMxk7zmj5gOvAQAff9W10TKexVZOOtADTUgbaIWQhZ932bXRMr7PeIdbFM1LeYCLzPoe153TWXWoFcR7mCbjLabC7wEjLLku191VWVWOyBFgqg4Qhai5E0HXgIAMg8XFwQLGiZ5j7hJvnGhucBLwAhm3f2aK+JPLHcIFbDyQEsbmUYucMExxhC2YIU0HXgJAOmk64rTlRtSJIiKQ2LiUfJG4KV5kyeSMej7twjOrDqRJXETslDgJQDEh6Afcke8hGKh9onmC5sPvATAR/rAO3eJTq0q8cjitU9EAi8BgA92gVO8RMkgRYKoSHweWTiqOxlpM2/yBMD7DkDv7xCdWlUSsFCOXCTwEgB4127XVBa0Ehgcr5trqoJoFv3QdvBRd7jgQj6PBSuk+cBLAND3b3GNC67ckCJBVCSWTMiCgZcAoO/b5Iq2zpbkKUnmmlCNQd/zuiv80VmXkRCygrjJipcGHNoed7jgJIkhJGqFFAy8BNdd44IrN6RIEBWLR5aETZ6aPyoUeAlNheYSf7RXkeETDLxMhxpMNaHKobrHH+1VxMs9G4GX0zehyuEiF5zfI0ORBK2Q0XZo3qD5E1zkgisnpEgQFU3Ib8XkKRJ4CWCk3zXNvUKlDrxE1h+9T3CU6sN64KWY1cwtLjgj8FK84mWscZGYPF3kgisXpEgQFY3ELJg8RQMvAeiH3gIfHRA6pxopS+AlMim2LvBHlyPwEnCPC84IvBRMVxYMvATc44IrF6RIEBWPvwyBl0ZK6Guu8EdbCrysEwu8NPzRr7rCH20p8LJeLPDSTS64kIWKrPGG+eBMQAFxkQuuHJAiQVQ81gMvxUyeSLnDH21NnhLiJluN54gPu8IfbSnwUhINvIRrXHBG4KWFipeCVki3uODKASkSRFVgLfAyImzyNPzRh4TOqUasBV7WIx2sEzrHLf5oq4GXqq9G6By3uOD8HsVa4KUnIHSOW1xwpYYUCaJqsBJ4mWhcAC54kr5vsyv80cJ9IwDERa08yPijHd7C3XLgZfNiQXm6wwVnNfAy3rRY7BwXueBKCSkSRNVgJfBSlz1Imm01nkVTjYe1w/2nsiQJm5A1xY9UbbvYQGrSCBZ0uDytBF6q3hDSglYzt7jgrARepgNRpAO1YgPFh13TxbZUkCJBVBVWAi8TtTOhS4K7m9F+8B7n+08tBV42zBdKBwUy/ugB56cwWgq8bBa38vC+A65wGYV84q3bE63LxOXZvds1VURLASkSRFVhxYTMwZCceYzwWPqhbeDppPB51YQVeepgSLavEB5L37/VHSZ5QZeRzmSkZiwTHkvf/wa4rgmfV01IEhN2wamSB2qTWEVWAND2bXa81axUkCJBVB1WAi8TnhD0iEDFSwDQVOgH3hQ7pwqxEniZ8NVCD4kFXkJNQj+0XeycKsRK4GU81ATuD4sNlIy5orGXEXgpZpeIRdvBPQLpygAQHwLv2St2DgGAFAmiSgn5xCs0JlqWACK5+wB4/0How72CI1Ufors+DiDZtgIQaJoEALx7D3jM2SZkS1YzDqRmHQsIrmq9cyd4clTonGqDMYaQqNWMA6nZK4XH0g9uB08nhM9zO6RIEFWJJDEEBAMFkzqgzz5aeCx93xuOj+qWJfFAwYTOwNuXC4+l7XvD8SZkRZbgEwwUjOky0LZEbCCuG+vT4fL0KjK8iqCVh3mBZsFaHboKff9WsXMIUiSI6iXoVYQD22KeCNAwU2yg5Ch4l/NNyEGvuJUnFmwEqxV0GcUGXRHIKpoRAwDxyAywsECTNAB8uNclgaziVp5E/RwgINAkDQAf6HBFIGsxIUWCqFoYY8J9I9KaDq1lMaD4hM7TO3aAJ2NC51QbkiQuz5SqQWtbBshi1gw3BLJasvKkNegzVwCCWTFuCGRVZEk4HTSe1oBZKyDsMnJBIGsxIUWCqGr8Hlk4fXE0rYHNXCo2ENeNh4vDTcgBK1YeFWBtgoWAXBLIGvSJF1GLcRlSq2DWgUsCWUUVXQCISz6wJrHy2W4JZC0WpEgQVQ2zUKRK0zlSoUawmgah8/hQD/hgp9A51YYhT3ErjxptA4JRofPcEMgqMYagV9zKozbMAXwiTefcEcgqSxKCVqw8LfMBj6AV0gWBrMWCFAmi6vEqknC6XSypgs1cbsGEvMXxJmSfFStPMg1p5lHCY7khkNWKlWc0lVmfgrghkDVgxcqT1iHNFKzV4ZJA1mJAigRR9RjpYYJFgDhHgnkhtQhGdaedb0K2kr6o6RxJT9CCCdn5gaxW1qeq6VADUbC6NrHBXBDIKlmwmqVUHelQI1ikSeg8twSyFgopEoQj8MjiRZViyTR48zzAFxQ6j3fvBY8NCZ1TbXhkK1aeNFjrQnETsgsCWX2KLNzNcjSZNmJPKJB1An6PeGn3WFIFa19GgawlgBQJwjFYKaoUT2uQhE3I3PHldK0VAeKIa4DUbiGQ1eEmZCtWCU3nSEKGRIGsE7ASy6PqOlKylwJZSwApEoRjsJJuF0+pQLgBrG6G2GCxQfC+g2LnVBkeC0WV4sk0eLQFrKZR6Dw+3AM+1C10TrVhpahSLKkC9TMtBbLykX6hc6oNryLewC+WTANNc60FssaHhc5xE6RIEI7CUlGlVNrYRYuakDvecnygoOiuj8OIkpdmWTAhH9zuaKsEIF5USeccSVWDPMtCIOshZ8uTMYaw4PrUdI60ziHNEg9k1Q+9JXyOWyBFgnAUVooqxVMquOyFNGOR2GCpOHjvfrFzqgxrVp40uDcoHsiaGHZ8YJuVokqxpAoEaoQDWflIH7jD02s9igyvYGzUaDINFq4XDmTlg52OT6+1CikShOOwVFQplQZrnA0Excrp6h07HF8BLyjYII1zIJFSwVrmCweyGrtoZ1t5RBVdnXPDyjNjkXggq8OtEoC41UzTOVKqBql9ibgV8iDFSkwGKRKE47BSOjueUsE5xE3IahK829mthyUL8oyl0gBjkETlmYw5PvbEipUnlkwDkgypXbAWQmwQfLBL7Jwqw7DyiMlzNJkGFK9wICsf7gEf6RM6xw2QIkE4Er9HtmaVCEYNy4QAeudOx6eHBbyKUBEgzg3lTKppAKttFRpLP+T82BPRWJ6sVYLVtghXZHWDVSLoE1MkslYJ1jBLOJBVc0EsjyikSBCOxKpVQte5kR4mCfhdtTR4126xCVYZzEKp51gqDc55Ztcn8NpMJ8B7HV5USWLwW7FKAJBEW40nRsD7D4mdU2XIkpXYk6w8BdNrR/vBh6k76FhIkSAci2WrhEe8yY/etQtcTQmdU21YtUowXxCsUax1u96xw/FWHlHFTOccybQGFoyIW3k63qLYkyNQs7ESNQ1gYUErD1klxkGKBOFYrFglElmrRPM8sUAsXYPu8G6BBVklWhaIpYOqKfAeh8eeSEw4VmI0mZHnjIVigyVj4L0HxM6pMqxaJQyrmWDGVnzI8Q38RCBFgnA0olYJDiN9kSkeQ5kQgHfvAU8nBGdYXVi2Snj9YE2isSe7wNW04AyrC8tWCX8YrL5d7FyXZBiJkLVKsFAtWLRZ6Fw3xJ6YhRQJwtEYVgnxapc654Z7QxEoIMR16B07BWdYXRRmlZgvHHuid+8Wm2CVYcUqkZNn6wIIaXXphOMbelmzSqgZK4+gVSIxCt7v7Awjs5AiQTgev0esrgSHUeqZyYpwUSXeu8/xDaj8Vq0Sihesea7QWLxrN3ja2bEnooqZpmesEr6gkXUggBsyjMStEjpSqg4WqBEule+GDCMzkCJBOJ6CrBKNswGP3/yJnEPv2CE4w+pCYgwBwZefUaeDQ2qeC8gC5+oa9E5nW3kKt0oIxp507xGcYXUhS+I9YnKxEq0LIZRhlIqD9zm7uq0ZSJEgXIElq0RKBZMk4W6BvO8AeGJEcIbVRcCrWKiDoILJHkgtgrEnPXvBU86OPbFklVC1AjKMKPZkLKquI63pYP4QWAPFnohCigThCixZJZJpwyrR0C5e6tnhsRISYwiIVrvM+KKN2BOBUs9ch97l7IwYS1aJ7C66ZR4gCZyrqY63SigWOtfmMmKEY0+Sju+5Mx2kSBCuwYpVIpFSwZiUMXmahw8cckcGh8DxueqMkgypVTT2ZL/jffuiikSuOqPiNVxGAug9+xzv2xe2SmgZq4Q3IF7dtnuPqzM4SJEgXIPVWAnOuRGE5Q+bP5Fz6A6PkLdilYhnfPusYRbgFYg90TXH7/qsdVo1lCsmGnuiJl3RaVXUKpGVp3CGUTIGPuTeapekSBCuQtQqofOML5ox8QY/Pfsc7zsVtUpoOjd2fZK4lccNuz5RRSKt6UhrmqUMI71rt+PlKWqVSKkaNF23FHvCHZ6qPBWkSBCugjFxX3Q869uPNIk1+FFTju9xIFmQZ7bHAatvA3wh8yem4uBD3UJjVRtGHQTx9QnAKPgl0mY8PgSMDgiNVW0osgSfIl5XAoBwdVs+3Ased3aQdT5IkSBch2h1xlxEN2Piuz437KJ9Yp0s05oOVdON2BPRDA6HN0cDxDtZJrO7aEmG1DRX6Fzd4UGXgHhdiUQ6k/qteCzFSrgRUiQI18Es1kEAYJTR9QYEThwGRvuFxqo2JCbeyTKeylgl6mYIVQ/lI33g8WGhsaoNK3UQcuuzYaaQb58PdIKn4kJjVRuKLMGriL3qEtlYicbZEKkrwfsOOL5532SQIkG4koCg+TjnO2UMknDevvN3KaLujURag65zMEkGaxSszuiCXZ+ooptIjdlFC/Xg4NC7nd0cDRCXZy7I2usHq20xfyLXHR8UPBmkSBCuRJKYpZr8AIy6EiK7vsFOx5fNliUJXkFfdNYqITXOEsrb530HHb/r88gSPLL5x3M2VRkAJMHmaG5IrfXIEmRJMMg6bQRKC6fWdu91fMv2IyFFgnAtwru+rO9U9ohXv3N4S2wACIq6N9KZXZ/HD1bbav5Erju++RQgbuXJ7aL9YbCaRvMnamnHN5+y0mwul6ocqhULsk4nwAe6xCZY5ZAiQbiWwnyngqlhLtj1KbIERWQXzQ0XBwDxIMEe5+/6vIosvItOqYY8WbOg+80FQcE+jwwBcULNpCoDEHdnuiwVlBQJwtVY9p36Q0Y6qFk0FbzvgODsqgtLqbW5XV8UCNWaPzGdBO93dkElK/KMZYMuaxrFUmsTo+DDvUJjVRuMMfitBlnXtoql1o4OgMcGhcaqZkiRIFxNIb5T0ZbYrtj1KbJQwS+jzLPVXZ/zgy79HsFUZU1HOltATdAq4fT+G4C1IGs1W0BNNBXUBUHWWUiRIFxNQb7TcL1Y2WwXlNG1apUAYETHi7Rsjw2CO7ygEmNM+OWX20XXtYkVVBrqdnzXWiPI2qI8G2YJtWx3Q7+dLKRIEK7Hqu/USiqoG3Z9fsGy2eMKVAlmHLjCKiGo6OYKVMmK8fITwB2poKKpyqqRquzxGtVYzeKCfjtZSJEgXI813+mYMs8CzZL4cA94ytm7FCsFqmJZeTbMFNz1dYJraaGxqg1ZYtabTzUJFlTqP+T4rqBGkLWgPNNZeQpuHPoOON6dCZAiQRAArPhOM7toCwWVnN5/AxDf9SWzBaoU0V2fDj7QKTi76kPU/ZbIFVQKgNU2mz9RS4MPO9v9BliwSmTdmYEaw6VpllTC8f1MAFIkCAKANd9pIm2tjK7uAkVClsSbJSWs7vpcIE/FSoEqi6m1vM/ZNSUAI8haEQqyxuHUWtGgYIfX6ABIkSCIHFZ8p5bK6MaHHB/UBhQgz0ANWE2D6fP4cK8rgtqslM0GYKTVBiKmz+ODXY6veVLOfju8v8PxNU9IkSCIDKK+Uz52l9IwU2gsvc/5u2iPIgsVqNLGFAAS6xcBx9eUAACvIpaqrOrZIFYGSaQSq0vcRUaQtXl5pjX9cL8dkfWppR2frUWKBEGMwUpZYgBgNfViXSz7D7oiCEu0bHZi7K5PoJ+JG8zHBe2ia1shGnTpdKykKufWZ/0MofOcLk9SJAhiDKIFqg7vUiSjJbZZUnHABZXvvIpYam1SzQRdyoqhTJglNgSeGBWfYJXh98hCqbXJXD8TH1hExF3UA55Oik+wyhCPi9IMefpCQv03nO4uIkWCIMbAmPWCNZKIIgFAd0FQmxV5ZoMuWZ1A9gbcY5XwCciTA4crsQrK0+m7aMAIshYJCh7bz0Toftc18EHnNvIiRYIgjsDKi49zbuxQvEHT5/EB5wdhARCuKZELuow0CLqLDrnCXSQqz2wNBHF3kfMVCcDa+gQgZoEEHN1hlRQJgjgC0V1KNuiSMSbmO1VTjm+UBBipoCKpi1qucqgk1l48GXOFu8hIXTQvTzVbOVTYXTQInnS+u8gjS0JBlyk14870+MSyi4Z6HesuIkWCICZBeNeXc28Imo9dkL0BWEsFBQBJMKiNdtGTY3kX7YL1aSno0pK7iIMPODO7iBQJgpgEy0GX/hAQFMnZ73R0EFYWryILdbHMVrpEsFYwZ/+QK9xFoiWzc5UuI41CJd11l2QXCbszs/KsbREq6e7UuChSJAhiEiwFCVqxSuga+FC30DjViNWgS8aY2K5PTYEP9wnOrvqQGINfQJnIBl0a2UWC7qL4kPgEqwxJYkI1ZIygS6vuopiFGVY2pEgQRB4sp4aJPKjhjpLEgPUgVlH3hpOD2sZiNehS1P3m1F30kVh1vwn1hoEzs2FIkSCIPFhNDWMev2AQVg+4mrIyxapCtF+EpnMjSNAfFivxPNAJrmtWplhVKJJYv4hs0CVCtRZKPDvfvSEedJlp114j6C5yoGJGigRBTEF5gto4+KDz3RtAIbtowZx9F2TDMAvt2g+7i0Syi5JAbEBsclWINXlqYJJodtGo43rtkCJBEFNgOTVMMAjLDXESAOCzEnTJuXi2gUsUM5HiVMAYd5Fo8TSXyFMk7gQ4HHQp7H5zmDxJkSCIKbCaGsZkD1i0yfQ5fKgHXHd+toGVoMtkWjU6rIbrTZ/Dh7pcYY4XDrrkRhlyFqgBAjXmz3PYiy8fsiTWuE/nmUZzoTrA4zd9ntM2DqRIEMQ0WHnxGalhAuZOXQUf7RecWXUiLs9szr6APNNJID4sNE61YlWeksj6TAyDp+JC41QrlhRdxoTudz7SD66mRadWsZAiQRDTIJoapukcmp7J2RfpuOjgWvxjEQ26zNXoiAik2cF5u758KII1T1Jqxl0kkrYI91glvIqYOzOZzdYSsEACHHzYOa3FSZEgCBNYSQ1jsgcsXGf6HD7Y7QpzPCAedJlMa2Bev5A5XneJYmbF/ZZMa4A/LGiOd488RWt0pFTNuNdlgYZqDlqfpEgQhAlEgy4P71IEdn2pGJDpbcDTSeh9B6HtfhXantdFp1vx+BTxdtgAxKwSscFcbwOeTkDv3Q9t1yvQ9m8RGLk68CkWzfEicTzDfbkqrDwVh96zD9rOl6Ed2i40djVgSdFlkpEKahI+1JOrwsqTo9C790Db8SL0rt1CY1cCYtIiCJditG+Wcz01pkPnRg0EJdIEHNhqehx93xvGw3pMNUHWskB4vpUOYwxej5zz10+HmqkpIUeboHXuMD2Ovvd18FQCGJNuJ7UvFZ5vpWO43ySkVHMBu4a7iINFmsF79pkbhOvQ97wGnhjNKbwAIAm6SKoBWZKgyJJRd8MEyTHuItP9NLQ09N2vgseGjU1EBtY4y8qUbYUsEgRhEuH+BmnN6L3hC5k+h4/0TShJzAR6d1QTVoLaEIyKtRYf6hmnRAAAEyhuVU2IpoIm0ypYTb1Qa3E+2DVOiQCcvD7F7vdUWsvERZmHD3SOUyKA6pQnKRIEYRJFEnRvqKqFIKyJMIG4gGrCI0tiNSXUTPZGpDB5IuhMeYpUYQUy5nhJFqrCOgEmAX7zinI1IewuUlUwxWukglpF8QKKz/r5NkGKBEGYRDgIiwOp2CigFVCuWfYIlTOuJhhjQg9rTedIx4aBQrp7eoNgAuWMqwlDnubXp6rrUEcGCxs0UAMmUHitmpAkJpRdlFJ1aMP9YCLa8RGwQKSg8+3CmSuAIEqEcCXBgW7wXpM+6ElggZqqfLCYRbiSYG9HQU2PmEOtEVlE3W/x7gMFZQ841U2URbjRXNdewz1plSp0awCkSBCEEIos1igpFWoAL2THFoxaP7cKUERLkIebUEiCLHO4PL2C2TCpmkLlWZ0vPrN4ReMkwoUFnlarPEmRIAhBhKwSkoJ00Hxp5yMRqUNRjWSzYczCFR9Uv3VlgBXiv64CROWpe0PQvNZjHJy+PiUmVoxODUShFxDjUK3rkxQJghBE1HycClsPDmShWsvnVgui5uNUjcVdH2NVazoWQdT9Znl9yh6hjKRqRTh7w6o8vUEwT/UFWgKkSBCEMLIkWOI52AAukGKXwxcyosAdjiwxoRLP6VAjuJABP0MwCmbl71BliBZPS4ebLbk3WKjW0fE7WYTdRRbdGyxca+m8SoAUCYKwgJBVQpKQConllwPONxtnMczxAqWFZQ/SQXHZVKvZWBRh94bHD80vbqlx0/oUiZXQfGFoHvFMq2pen6RIEIQFRHPMrexS3ODWyCJuPiZ5ToW4+82KPKv3xSeKsPvNZfIkRYIgLJAtSWwWNVALXRJ7GFXzg0UUWZKgSALuolADuKBZ3U3yVCTBjqCi2TAuiTfJIlo8TTiOR/ZUdWEvUiQIwiJCVgnGkI62mj9e8QK+oPikqhgxd5EMVSSozRcC8zg/3iSLaHEqLnugBQQULZfEm2QRLZ6mewJC2TDVHm9CigRBWEQ0x1xtnGe6zXC1P1isIJptoLYsMt0nwk1ujSzC8mxbCpgMK3STdSeLqPtNnbnC9LHVLk9SJAjCIqI55mmdgc09DmYe1tX+YLGCLFqSGArYnKNNHeuWwMCxKLKge0PyQpp9lKlj3aiYiRZPSysBSG1LTB1b7euTFAmCKAChYkoAtGAtpFnLpz22mlPBCkFEMdM5h17TDGnGommPdaNiBog18tJ0Dl7XBtY8d9pj3ShPJrpx0HTwpjlg9e3TXbjq401IkSCIAhB5sABGx0WpcRZY05z8BzEJCDi7lHM+hLMN0ipYy3yw+rb8B7kw3iSLV7i1uAapbQlYdIpgQV/QVfEmYxFdn2lNhzTrKGAqi4MD4k1IkSCIApCYaIdADZxzSO1L87fDDkbBBDIYnIQsmm2gamCMQZq1Im/7ZjfGm2RRJCbWyyQrzznHAHna17vRGpHFI0tixanSGpgkQZ53HOCdXJl1gjzd+bQiiCIiao7XdG48rOceC/jDE46RIg3FnF7VIdYKm0PT9TEP64mFgFiNe+VpxRyvcw4mK5DnH29Yc468ZkS8uJpTEJVnduPAFC/kBccDk6SAO2F9kiJBEAUi7N5QNQAwHtbzjpuQecBqqvtBrSVTBZ0vKs9UVp4eL+R5Kw3X0BhInhbl6Q0Yyu4RsHB1v/jKKU8OQzkDAOYPQzoyOJhJVR9oCZAiQRAFIxwdn9ZyPzN/CNLsMQ8XWan6wKt9//srPHfdxzC8fael8xXB4j/JsfIMRiHNXHb4S6+/6uMjdtz7Q7z4kX9GbN8BS+eLFE4Dxq9PqaYB0ozFh78MRKo+PmLrl7+NVz71OSQ6uiydb1UxAwCptmVcMCsL11V9fARAigRBFAWhVsO6Dk0/XEdQqmvNBV+ycAMYq+7bUo3F0PHYk/jLGZfitc/ciWRPr9D5osWUsub43PkNM3PBl6ymserjI9SRURz41e/xxKkXYvMX/wOpgUGh862a43Pnt8zLxfM4wQyvDo1g70//Hx4/+QJs/cp/Qh2NCZ0vCaYpJ9Pj5Sm1Lc7F8zhBngApEgRRFERefMD4XQoASO1LgFAtmAPiI7IPZq5p2P3D/8Xja8/Htm/eBzUWN30N0V1feow8jeDLowB/uOrdGgCgxQ256ak0dnz3h3j85POx474HoafSpq8hVOUSh83xAA4HX3oDjoiPyK5PLR7Htq/di8fXbsDuB38BXVVNX8NKXFQWxiTI844FFK8j1icAMD5WVXIoXNfAh3qhD/WAD/VAj48AugpIMpjXDynSCBZphBRpqnqzXTngmmrIMSvPxCiga4Y8fQFIkcaMTJvAFI/d0y0LnHP0jsRh9m7yKhKiQb9xrpo25DjQCX24DzwVz8lT8ocya9NYo8xkZUw72fzF/8CO7/5wwufBWe1Y+fU70HjKidNeg3OOnmHziofPIyMS8Bnnqinogz3g/YegjwyApxMZeSqQAkfIswrMyq/8023Y+7NfTfg8vGg+jvvmXag7bvqiXLpurE+zBLwKwn7jWcjTSeiD3YflqSYBXTfkGaw5LM+ahqrINnruuo+j47EnJnweXbEMx33rLkSWLZ7krPGomo7+0YTpMYM+D0I+41nIUwnog12GPEeHwNNJgOuArEAKRsbIs75qrJOV/1QqAH10ENr+LdD2bQXUTIANYxj7tOdg0A/tBMABJkGaMR/KrOVgtS1VbxItNvpwH7R9W6Ad2AZomd3QkfJkDPrBt4z/kGTIbYsgz1oGKSrQF6EKyZqPx/rrpyKl6tAGe6DvewPawe3Gi8640Dh5aowBB7YZ/yErkNuXGPKsqS/2r1A08pmKY/sO4B/vvB5z33clln/2k1BC+WMXDHlKSKl63mPGklI1aP2d0Pa9Af3QDuPBnEvUO0Ke+980/kPxQp65FPKspZAquFJjPnmObN+Jpy+8Cgs/8j4sueWjkH35N0GSxKDIElTNnDyTaQ3+0YPQ970BvXNXZk1OIs++MevV44c8axmUWUvB8qSOVgJqbHJ5Dm7agqfOvRxL/ulGLLzp/ZCm6K2RjYsaa2mYilRahX+ow1ifXXthyJABR7RK05iUWbsAfEEos5ZDnrkErMIbejnSIsFTCaS3boR+cPuEB/O0ZI5n0WZ4jl4HyQERtYXCkzGkN/8Netdu6/Ksb4NnxemQqjyQcCqSaRVDcfMR4YEtf4EycMCSPKXmOfAsP7XoDxg9nUaisweJjk4kOrqQ6OhCenAIWiIJPZmCnkpBS2Z+Tho/c1WD5PVA8noheb3of/k1xHbvm3Kc4OyZhnXi5BPyHhNPqRhJCMjz9cegjPRYk+eMhfAsOxnM6zd/rgn0VBqJzi7ED3Ui2dmN+KEupIeGxskvJ9dEEnoqBa7phjw9hkz7nn0R8YMdU45Ts3gBVn7zTtStzG+diCXTGE2ad4cEX/4/yIlhMXlmXo7SzKXwLF0DNkn6aCFoiSQSnV1IHOrK/X96eBh6Kg09kYSWSmVkm4SWkTHX9cPr0+NB9183ItXbN+U40WOW47hv3oXI0vxVU0cSKcRT5t0hoed/CUlNWJAnIM89Gsqi1RVrkXScIqF17kJ601+BdApHantCZKwRysLVkOcfWzUmpmKjHdiG9Bt/N1xBhSwVxgAmQVlyEuTZRznS2qNzjl4Bc7yn8y34dzxjbTDGAEmGsuwUyO2LheSpxmIY3voWht7YhqGt2xDbeyD3YE729BX2dxZk3vvfg2X/djOU4ETrhKZz9AmY470HNsO352VrE2EMULzwHHUa5Nb5Qqemh0cwvHV7Rp7bEdt3AInObiQOdU37wiomTJax8KPXY/E/3TipdULUHO/d8xJ8B96wOhvA64fn6PWQm2YJnZkaGMTwlu0Y2mLIM77/UE5pSPUPWJyPOJLXgyWf+ggWfOR9k1on0qqGgVjS9PV8bz0Db9dblufDAmF4jj4DUv0My9coFY5SJNSdr0Dd9lzRrys1z4Vn5VlV4U8tFpxzqNuehbbrtaJfW2pfDM9Rp1eFP1WUgdHEuEC1qWCpGEIv/D+hSnmTIc89GsqSNROUCc45Ynv3Y3DT1sMvui3bMLp7X1mVhekIzp2F475+JxrWrJrwXf9IAqpuTp5SbAChVx4peD7KotWQ5x83qTxHd+7B0Btv5mQ59MY2y2mZpaJm6SIc9407UXvs+AZcnHP0jyZMm+PlwU4EN/+p4Pkoy06GMmdiJ0yu6xh5a9cEeU5nfSk3tStX4Lhv3IWaJQvGfS4aF6X07kXgzb8WMBNjPXqOWQ+5bfr+MuXEMYqEuuMlqNtfKNHVGaSmWfAcd44jX35HwjmHuvUZaHteL9kYUttCeI4+w3GWiXgqjZGEoPk4PlTwuPKcFVCWrsXorr3oevyv6H3uZfQ99xKSXT0FX7ssMIb5H3gvlv7rx6EED1enHE2mERMwx4eefxhS2vyuOx/KwlVQFq7C0JtvoevJv6HvmZfQ9/zLSPX1F3ztcsBkGYs+9gEs/uQNkLyHA56FzPG6hvBzD4Hp5uJ+piKrTAy+vgVdf/k7+p57CX3Pv4L0YOFrvxxIXg+WfPomLLjh2nHWiaF40nRcFNQkws89DFaIpTyD55gzKkqZcIQioXXsRPqVP5d8HHnO0fAsW1vycexG3bcF6uanSz6Osmg1lAXHl3yccqLpOvpGzL/IfDufg7djW1HGVpafir2PvYhNn/v3olzPDkLz52Dl1+9Aw4nGuhA1x/vffBqe3j1FmYvn2LOw/cFHse3r9xblenZQs2wRjvvGXag9xug4m1I1DAqY4wOb/wxlsDgWAs/q87H5az/C7h/8rCjXs4O644/Bym/ciZpFhvtLNC4q+OrvIY8Ww93F4F1zMaTaKZqrlZGq317zZMyIiSgD2p7XofUeLMtYdqHHhqBu+UdZxlK3vwh9qEp2zCYRbTqlRVuKNra6dSPqj5++RXklM7pzD/5+ybXY+pX/BNd1yIJNp7Roa9Hmkt78NBpOOKZo17OD4S3b8fQF78Fb9/4QnHPhplNFledrT6LhpOOKdj076H/pNTx19jux5ycPAwA8gvVOiiZPBqRffQJcMx/sWUqqXpFIb/7b4VTEksOQfq1y/njFhnOO9Gt/OZx+VAbSrz4BbtIHXi0IVbmMtBbB0JmB6/An9sBTV1usK9oD59j2tXvx3Ps+DnVkFB6BEs9qEV980NII+wchTZFWWQ1wVcUbX/gPvPyxz0BPJO2TZyqBaH3hbhK70ZMpvHrL7XjtM3cCaRWKQJVLtVgbB87B48NQtz9fnOsVSFUrEvpIv5GSWDbvDAeSMSPv34HwgU7wgY6yypOPDkDvKo4pulIQKZ8Ljw96sEgpxpwDwz1oWD0xsK0a6fzjX/D0Be9Bes9+0+fwQA10X5FSYjkHGzyEupXVbeXJsv+Xj+BvF18DTaDHhB6uB5eLVVSOQxk9hMjShUW6nr3s/uH/YuMVHwQEMkm0SDN40TIAObQ9m8FThccEFYql3+j8889HMBhET89Es/TevXvR1taGk046CbE8hT+KhbZnM4S6+xRr3N2b4IDQkgmoezbZIE8GtYRBnXYgbO6sLeKujzHULawMv2kxGNm+E89e/F70P2Xe3aZFiucuAmOoWzKzeNezmcHX38BzF12FoRdMpskyqbjyBEPdUXOLeD176X3mBTx/8XsxsnmruRNkD7Ridk/lOrQDbxbvehaxpEjcdtttiMfj+Pa3vz3u88HBwZyS8cgjjyA4SW54seCaagjQhhc6H+0HH7DWOa5S4akE9I5dNsiTg/d3QB8ZKPO4pUNiDIqAw6Ko5mPOUdde3d0uxxKaPwdtF54DfXDQtAus6PKcGZj+uCqhZvECzLjgbKg9vaY3Q0UzxwMAOOpmhYt4PXuJLF+MGeediXRXt+lzihl3AgDqns1FvZ4VLJXJWrt2Lc466yx85zvfwb/8y78gGAwilUrh0ksvRUdHBzZu3IimptKWROZDPYfLCpcdBn2gA1JdMW8we9EHOlFQAa+Cx++AFK61bfxioySHofrMVfE0zJ0MrEhKXKitDr6GWiR7B4pyvXLib2tB69nr0bB2NRpOWgV/q2FdGew6hJTJ1Gst2pIrQFwMonObIPl90BPmsx0qheCcmWh52+loWHMCGtYcD1+jsRse6DqEtEnrY7FffHULm8Ur5FYI4YXz0HzW6Whcuxr1Jx4Pb10UANDf1QmzkXNatAXYX0QrbGIEPDFqaxlty/U2b7vtNqxfvx7f//73cdNNN+H666/Hxo0b8fjjj2PRotLnt+qD9kb76w6zSOiD3fbd3EwCH+wGZi4t/9glQh7qAppMlgOXPdBDDZBHirOmmSSh/tjFOPSE9eJsSjgEf2sz/DNa4G9pghIOQfL5IPu8kHzewz97vWCSBD2dhp5MIdHVPWnDrqnw1teh7e3noP2S81F/4nGT1mqRBzsBf9TU9bgvBO6vAUsMC80jH5LHg7qjF6H3+U2Wr6FEahBobYa/tRm+rDy93ow8fZB8h39mjOXkObp3v3C6pK+lCe0XnYf2S89H7coVk9ZqkQc7kDYpTz1UB93jg5QujiLlCfkRWTIPQ1t3Wr9GbQT+1hZjjbY0QQ4GDsvS64XsP/wzY8woQZ5KYXjrW9j3818LjRVon4H2Szag/ZLzETlqyeTyHDgEtdVcfxGtpglckotSnyOLPtQDuRoViXXr1uG0007D1772Nezfvx8/+9nP8Itf/AInn3xyMeeXFz7cY6NWy40Xr4PgQ4I9Coo6uO44eUrdu4GGeYDJaqhqtLVoigQ4R93SmdMqEv62FkSWLUZk2WKEF81HYIbxYA7MaIEStvZQGnrzLVOKhBIOYcb5b0P7xRvQeNpJkDxTB/RJXTuBlum7MmZRo63wFkmRAID6o+ZOq0gEZrYhsjwjz4XzcvL0z2ietAS4GXqfedGUIuGpjaDtgnPQfun5aFizCkyeet3JXTuBliWm56FFWiD17jV9/JQwhoZjFkyrSATnzjLW5/LFCM+fO16xDVpzNx169HFTioS3sR5tbz8X7ZdsQP3qlVMWIuRch9y9C2g1uT4lGVpNU9Hqc4Ax4/ndPKc417NAQR1Abr31Vpx77rn48pe/jK9//eu47LLLijWvaTFar9poGlOrz8w5FXZH/to9ftFJxSAP95iuE6FFW4AD1ne8R1K/bEx/A0lCZPli1B5zVO5FF1m2OGeWLSZank6VACD5vGg5ez3aL9mAlrNOh+z3mb6ulBiGNNoPPWQuw0WLtgKdxcqu4mg4ai6yV2OyjOiKpYgesxyR5Usy8lwET6T4HS/zdf4EADkYQOu5Z6L90g1oXnfKuAqW08GG+8CSMXCfOQVHi7bCUyxFggP1R8/Hrocyc/EoqD32KESPXp5THCJLFlpWZqdiKnkqNWFDub1kAxpPPWnK7p/j0FRIIz1GGQKTGS5atKV4igSY8T60kYIUiXPOOQfBYBAnn3wybr755iJNySRlrHUw+fjV59+bEpJnceEc8mCHeUUikxbGivR3CLbUYtlnPoHoMctRt+pYeGrKE+CmxiY22WpYsxqzr7wUrRvOsj6PjDzNKxLFjZOoWdCGJZ++CfUnHoe644+2bGEQRZsk861p3cmYfeWlaDl7neV5MBjyVJvNNSgragArgLrl87H0Xz+OhhOPR+3KFZADxe26mg9tkvXZeu4ZmHX5xWg+8zQh5TYH52CcQx7qglbXbuoUNdoKH14VHyvvHOx9fhekSOzcuROxWAxr19pQNlqyuZ2q0xp4FS1X3BqV2h7XKkySIQ92ADjW3AlZc+dQZ3HG93ix6OMfLMq1RBj7oG4970wsvOn9qF9lUgZTIclQBjuRbltm6nDu8UMP1kKODRQ+NgDZ58eSf7qhKNcSIaeYSRLaLz4PCz9yPaIrihBLJMlQhswrEjwQge4NQkoVIaWfAZ6aMBZ/4kOFX0uQ7PpkHgUz33EhFt74PtQsXjDNWdOQeRcogx2mFQk93AAue8CKVUzR5udnQaO/9NJLAIDjjy9/vwQWitoa+ctCtbaMWyqkcC20shajGgsDc1DGBgCwcB3krr2Appq+ybVoa3EUCcZsy4DRU2nMuuISLPxIER7QY2DhWiPgkuuAyYI+WrS1OIoEY8bzxga4pmHutVdgwY3XITRHrB33VLBgxJCnAFq0BVL3rsIH57BNnmAM8z98LRZ86GoE2opjZWGyAngDmY2D2ZMkaJFmKP1F6BzLdbBgbeHXKYCiKBKrVk1s/1tqpGgzNLvM4UyCVOuc1E8AYNEmYN8W28aXos4pogQAUm0z9M7dGXNnm6lz1GgrfPuKYO7kHCxS2vTrfMy44G1ou/Dsol9XijaD93dAGumDXtNo6hw12grvIZOFgqaCc0hRe+Q5+8rLwN7zjqJfV6ptAT/wJlhiGNxvLrZDjbbCUwxFAtx43tjA/A9dXZKOw1JtC3jXHiCdBDzm3CNqtLU4igRg2/rMjV/IyS+++CIaGxsxa1bxNGWzSFFzD5OSwHV7xy8B9i5E+x4spcJ4kXOhXYpe0wBeJJedXX/PUrWFl6JNAOdCAWpapBm8SFESdilmpZInsyLPIsZJSBF7np8lW5+1zWCAkEWxaBVDmQQWLlKZfYsUpEi8/PLLtrg1AID5w2DRZhQvnEoASYbUNLv845YQFq4HC5qse1BsPD5I9TPsGbtESHWtgDcgFpnNJGjh+oLHZoEaMJse1KVCapwFyIqYOV7xQi94TTOwSCOkYPEzMuxEbp4LMCYkT+4LQfcWWuWTQWqYCWZy114tSC3zILxxCNWCFxprxxik1vlTpqeWg4JG7+rqwmOPPVasuQijzF2BsldjZAzyzCVgSnV3BDwSxhjkOUfbMTLk2UeBOSx4lUkSlNlHQRodEEoV1sKFKwDynMmLEFUzTPFAnrkU8ki3UEXbwuXJocy1474oLcwXgDRjAWTBmJxiyFOe64ymcmORQlFIjTPFFF0mFd53g3Moc+yXZ1V3/5Ra5gMFa8iCcA55tv1/uFIgty8qf/YGA5RZ5iLxqw151lIwBshD5ottmfX/5x9UgdxuvtBQNSHPPgpMUyGN9Jk+Ry/0xefxQ2o1l9lQbShzjoaUioPFh0yfoxf04mNggRrDuuRA5LnHQIoPgqXN18QpbH1mrGW19seXVbUiwSQJnhWnlXVMed6xjuoJMRameKEsK09l0izKohNsrRFfSpgvCGXxiZBHek2fU+gORVl6MpjHWdayLFIoCnn+SqEKoFqBipnnqNMcZy3LIkWbIM1cCnm4XPLkUFasc5y1LIvU0A65ZR4kkfu9prD73XPU6QWdXyyqWpEADF+f1LYYpY+VYGChWiiLVpd4HHuR2xcb8R+lvtkZA4s2Q553TGnHsRl57goo3Lwp3rIfmjFIjbMgz3SmNSKLsnAVFFVgxxeMWvRDM0gzFkJunWfh3OrBs3Qt5KT5UuJauMFiACuDPGcF5AZzGUzVCGMMnqNOgxzrN31OIa4iZeHxFRP0X/WKBAB4lp18uK5ESWCALMOz8m2O3Z1kYYzBs2Id4AuVVp6KF55jzwQzWROgWmFMgm++WEEm4YcLY4A3AI+Dd3tZmCTDN2e5yAniVh7GwIIReJafInZeFcIUD/ztAk0WZY94ACtjYJF6KItPFDuvCmFeP7wCPS+4LwTdI7pxYGD1MyDPP07wvNLhiKc483jhPfFCsECkBC8/Q4nwnnABpJrCI+qrAeYLwHvi2wFvsPjyZAzweOE98e2Q7MoSKTNKqAYSzJewFfJDZ5QI70kXgfnLU7bZbpRwrVDLdSE/NDP8+N4T3+64zIJ8eKINQoXoxBRdBhaug3f1BY6rXpsPX71YzIKYe4OB1bbAe/x5tmdqjKVyZlIgzBeEd83FYLXFrQkPv3FdpxWgmg4pWAPf2kvACvThHQkLROBdc4lrlLIsnmm6W45FxA/NwvXwrb3UNUoZkLGaecxbBkUe1CzaDO+aS1yjlAGGPBXFvDxFFDOpcaah5HrL00ujEpAYgyyZ34AJybN1PrwnnA+m2NvS4EgcpSIyrx/eEy+Etm8L1K3PAFyzVvI5U3pbnrPCCAassD9auWD+ELxrLoG253Wo254HwAuQJyAvWAllwfGOdw9NhkeWkEybi5XI+qFZvtRmxgAwyPOPhRSqRdlToCsARZaRUs1ZeabdQTMGMAnywuMh+ZwZ+DsdHlmCqpmU53SKGWOApEBZtMpIk3e4u20yFFmCZjJNeXrXm2HFVRatNp6dFegOdpQiAWS069nLITfNhrr7NWj73zTau4Jh+gcuAxggtcyFMvfYikirsRsmSVDmHQupeS603a9BO7Atk8c/nTwz3zMJ0oz5xjWKbN2oJhRZ4ObP+KHl2OCYDzPyVDyQ69sAWQb6DkDvOwCAQ25bXOQZVzYeAXlm/dBSemznx4w8PT7I9TMAiQHde6DDcJWy5rlFnnFlI7I+9aBRSImNfVFm+x55A5DrWwFw8M6dRgfWYA1YvblmVk6h4I1DVp6+EOS6FoBr4Ie2G/Ksaai4SsCOUySysEAYnmUnQ1l8IvSOndB69kMf6AImy5n2BiBFmyHVt0JuWwzmK3NtiipACkUhHXUalCUnQTu0A3rvAUOeiZGJB/uCGXm2QW5b6CqzZj4UQX+mFm48rEgEaiCFIkYgpZoC0nFgTNNA3t8BuEyREFLMYOyipb79xn8EI5CCEePBraWBIzpa6v2HILlMkRBRzLIBrMpQl/GfoVqwQPiwPJOj4w7X+zsguUyREFqfihd6IAI5PohsA0MpEDIa1GkqkBz/jOUDHUCFKRKMc7s6X9kDV9PgyVFAN7oIMq+fXnQFwNMp8FSM5GmC/tGEafOxPzWMwOB+8KHeCS+6yVCOPw/M4y65943EoenmHl+B5CD8A/vBh3uB6QoGMQnKqvNdExwIAJxz9I7ETXsuQ4k+eAcPGutzusqtigfK8Rscn6E1Fs45eobj0x+YIRzrgWfwEPhID6BO01rcF4Ry7NkVlaHlnjslA1M8YEqt3dNwDMzjdWwBpGIj4ofWdQ28Z5/pa/P+Dlea4836obmaBu8z2WmR6+CDXWD1zq15cCSMMXhkyXTciZ5OgvcdNHdxNQ0+3Oe4/i9TwRiDInC/81QMfOCQuYsnY0B8GKigAGv3qIgEYTMi5s6ULwoI9HPR+wWagzkEEXN8MlAnFKTmRnkqsvkg6FRQTCng/SZfkg5CZH2mQmLy1CtMnqRIEESZEPNDM/Co+ZRjPtgFrqkWZlW9iChmXJIBgVbgfKADLvP6Cq1PTfYAAp1q3amYmZdn2hME/GHTx/MKkycpEgRRJiTGhDLhtBqBrCGugw+bb2blBIQDWAUUM6gpYFzWjPMRDWDVReSZHAVPjE5/nIMQ2jgA4FHzNZD4aD+4mhKdUskgRYIgykTWD22WdKheyBzPBbqMOoGsH9os6ZBYpLs+6C55ihZSUsNi8nTb+hTeOAhYzACAD5lvtlZqSJEgiDIi4odWIQnli1fSg6VciJrjWbjO9PFulKeIYqZ6Q0LmeN1l8hTdOKh+sbioSlqfpEgQRBkRMcdrOgeLmHdvGObOaVLHHIaIPFVdB4sKyHO4B1w33yPFCYitTx2SiDyHelwXdyK0PjmE1mclKWakSBBEGVFksdxvXbAaKB/uFTq+2pFFAi45AIE+JtA18NEB4TlVMyIWCU3ngEhKZzoxeQE7ByOyPjVNF0uRjQ+Bp6ep4VEmSJEgiDIiMQYRVUL3hgDFfBdKt/mhFQGfPgBogSjFnUyBaACrHhJrvldJu+hyICJPDoALtryvFPcGKRIEUUYYY0K7FFXnQrsUtz2omWCAoAYm1NG2Uh7U5YIxsR5bmqQAwajp492mmImsTQDQPQHAa75FQ6XIkxQJgigzIrtoUUUCsUHwdOWkhZUDWcQPrYnJkw/3gpusnukEGGNifn1NhyRSn8NlcRKiiq5xv5uXZ6VsHEiRIIgyI/Li03SxBzVgBAm6CTHFTBd6UIPr4CP9FmZVvYiuTyFFV01N3jjRwYgGBEsi8kyMgKfM9/QoFaRIEESZEQ1o474gINCQq1LMneVCNKANwSggmW8z5DZ5igQEqxrPuIrMn1Mpu+hyIbY+xSwSQGW430iRIIgyI7LjAwCdg+IkpkA4oI0xsAjFSeRDZH3qnAOyAhaqNX2O6xQzQYsZvH7AHzJ9jl4B8iRFgiDKjMRE9m8Zc6dAYSrEh8Gna5XtIEQD2jTBuBM+0ueqPiaimRtGfQ6ROIleV8VJiG8cuHDcid2QIkEQZUa0tLMqml+Oyni4lAvhgDbBAEFwDj7inj4mksQgoptpggGs0NKu6mMiqugK3+/JGHjS3j4mpEgQhA2IBbRxMF8I8AVNn+OmFx8gHtCGYBSQPabPcZs8hTJhdB0sXC+UN6q7qMGckQlTwkwtwPaAYFIkCMIGxALajDLNQub4Uffs+AALAW2MkTynQNhiJiuGMmES7iKLBGChwqXHDwRqTJ9j9/okRYIgbEA0oI1zDkmkkFJs0FV+aNGANs65WGEqt734BC1mAITk6SbXBmDBYgYI3+92QooEQdiApYC2YK35EzQVSMbEJlXFWAloYwIVGZGMuaohmohipnMuLE8eG3JVQzRRxYxzLpYJM2rvxoEUCYKwASsBbQjUiPWJcNGuz1JAW0hAkYDL5Clgigcy5niBFx+47qoGXqLN+jSdC5Ueh5oEbGzgRYoEQdiEcAVBSRLzm7roxSca0KbpHEzxivU1cJE8JcYgCQRPajo3goFFCn25qLOqaLM+TdfBghGIJIrz2IDotIoGKRIEYRPCLZsBoV203QFY5Ua4kBIgbD52E0LN0HTdCGAVWZ8uUsyMFGWx9ckkGQiETZ9j5/okRYIgbEJox5d98Qn5od3zoAbEX3yAqDwHRKdU1QhZzGh9TovY+qwueZIiQRA2IfJg0bMvPhE/dCoObqPftNxIVjINROIk4sMuCxAUWZ/WLGZuyiySrCgSVWKBJEWCIGxCzNQJI5I7EBEag8fc02lRdMfHRTM3OHdV58pS76ChpYEK6FxZLkRjogCIZWolR8E1ezKLSJEgCJsQ2aEA2QBBD+Az39DHTeZ40cwNzmEEWyoCFS5dZI4XsfBka50gEBGqcOmmuBNRC4+wogv7Ng6kSBCETUiMiTxzq87cWW5EYk6AMQGCIn5oF8lTVDEzAgQpsygfIuuTZ/4xj2BmkU3rkxQJgrARWaAuhM6tBAi650HNRFMWKUBwShjEutRaCxAcEJpTNWOlSy1QHfIkRYIgbKTUAVhIjLgqoM1agGCt6XN43D1FlBhjFtdnrelzXCdPkcZmuQBrkYBge+RJigRB2EjJA9o4B1IJ0WlVLdbkKRDAqibBNVV0WlWLtQBBAXmm4qTo5sGSRcKmsvikSBCEjVhJAYXHL1YqO+WenhtiKaAZeQoErwJwWaaBBQuPiDx1DVBTotOqWixZeETkmU7YkqJMigRB2Iiloj+MGeWIzeKq5l1izaYAgMkKoHhNn2fXrs8OhHbQWcuCNyCWueEmeYq4NnhW0TUfbAnAFkWXFAmCsBGhSG4+5uUnoEjQg3pysrUkAJJnPsT6bWRcG4wJZRq4S9G1UDRN8QKyQA8TG+RJigRB2Ihwip0+ZtdnFheZ4kVrc+i5XbSAhcdF8hR58fFM0TQAYALydJfrzZqiK2SBtEGepEgQhI0Ipyxmd320g54U8VoSZJGYCqspi+R6mxzx2hzG/wspZmSRIAj3YS0Aq7IfLHZhdFkUDxAU8kO7TJ6WiqYJyNNN61NU0dWrZONAigRB2IyVAEExU6e7UuwsFaWq8Ae1nVgqmiYkT/e4iqwWTat0RZcUCYKwGaFIbl3cB+22FDuhZmgWLDx2pdjZhSULj1DMScxViq61lFqRmBPK2iAI1yHi2jgcHOinFLs8WJOnwIsPcFXApRV5Cilmmmp0AnUJJV+fqTg4L6+iS4oEQdiMUNncsbUkKMVuUizJU/EAskAXUJLnpGQNEkJrE6D1mQcr6cl2VLMlRYIgbMbKiw8QNXe66UFt/thxJvUK90PbhSXFTJLEula6KE7C0v2ueAFJNn1euRVdUiQIwmaYYFEqa7nlScFZVS9ijZEsKmZp9/QvEVmfJM/pEVufxv8LV7NNl/d+J0WCIGxGtIhSVo9gis/8OeSDnhSOMYqZgDzhosZdArGr4y08AmXH3SVPixZIofVZ3vudFAmCsBkxNWLMw0WgbK6rHtSiufq5nhvmYyRInpMzTjETkqd7FF0hCw8fU91SpEx2mdcnKRIEYTOiueU6PaiLSs4ar4g8qN0jT8uKmYA83aWYiR2fs0kolavokiJBEBWAyMNlXNdKs7joQS2qmNEOemoYY0JWMyuKLilm+eGW7ndybRCE67AU0CawQ3HTgxqwFnBJill+xNZn5geS56QIK2a6FcWMLBIE4TosBWDRgzovIgGC1nbQbpOnhRRQsvDkpfT3O1kkCMJ1WCpSQw/qvFDMSXEpvavIZYqZlSJfFSxPUiQIogIo/YtPdVU/A9HIeABk4ZkCKzE8YlkG7lLMrKxPipEgCGJKrBSpEXrxAYDunpdf6WMk0q5SzCwV+SLFLC+ltvBQjARBuJCS+0wBQHXPw1qoKJUVCw/nQJkbI9lJyWMkVHdZJITkaUUxK7M8SZEgiArASpEaJskAE7iFXWQ+tuaDFlTMXCTPkruKuO6q1uzW0r0pRoIgiCmwXKSmgs2ddiIac8I5N5oiieT4kzwnxVIMD0DyzMPhxl0Ciple3pgoUiQIogKwWqQGsvmOgOCa0BjVjMgOetw5Ah0WobtHnmI+/cwPImsToPWZh5w8JUGLWRldb6RIEEQFIPriyz1chHYd4i/XakXYwpOTp8hZ7pGnqF7GOReUJeAueVqI4RG2MJRPnqRIEESFIHLbWzJaWtiluwUOC5qEi+QpVosxK0XBVeoqeZrncCO0ypUnKRIEUSGUfpfiogd1OSw8bnrxWbJIVO4O2m6suN7E5Vk+SJEgiApB5NlCL77pEZOnFd+Gu+QpgrGBFt1Bl2QqFYmwYjbmf82PQRYJgnAdIuZjK6b4cj5YKgExeWZ/EFHMhKZT1Yg2miLXxtSI/qbCMSdlliUpEgRRIZTcIuEyRC0S4uly7nnxARZcbxRsmRdrrrfKtZaRIkEQFYKlGAkKDsyLkDytDWDlrKpFXNEli8RUCMkTgjEnZJEgCHdiKWuD0hXzIiRPCg6cFmHnBslzSoRcb8IxJ6RIEIQrKXlwoOt2fKJFf2gHPRVkkSgu4vd75cbvkCJBEBVCyVPC6EGdF+O9RzvoqSipKd6FCLveKtj6KFhzkyCIUiFsijd+KtEI1Y+Y6djCS89d4hQ3xVdwumIlIHa/A5VsfSRFgiAqBFFTvLBf313PaXFTvHDdA3cJtLSmeHfJEhAPrhZSdkmRIAh3Imw6Fu2WKNKQygGImY45oArKk5E888EBcFWgzbrLZAlYcL0JybO8UQukSBBEhSBsOlZTYgMoXrHjqxxR0zHXBOQpK2CSu0LMhF1vIuvT4661CVhwvQnIk3l8VqZkGXfdCQRRwQgXUBJ5UMsesDLvUuxGuC6HiDxdppQBFrJgRF58rpSn+WM5B7iaNH9CmeXpricLQVQwwlHc9OKbEnHTsciLr7w7vkpA1PUmpOi6cn2Kut4qV56kSBBEFcI5gLT5HYord3wiB4taeFwoTxGJCrveXChPsfWJirbwkCJBEBWCcCMf8kEXFwHFzI3yFM4DSFeuT78a4QLyLPf6JEWCIKoV2vEVDdGoeDdaeEQhC0/xEHdlUrAlQRBmqGBTZ2Ug2GGxgoPZqhKSZ1ERUczItUEQbkXQdiy24yPT8bTQDnpqSrg+3ajoCteMquD1SYoEQVQhHFzQB00P6ukQevG5UJ4icEBofbpSMROlgtcnKRIEUTGImuLpQV1UhF58ZOGZEuECSrQ+p0TXjH9mIYsEQRCmIEWieIi++EieU8M1gOvmjyd5TgkTiTcBSJEgCLcilleuA+mE+WvTg3pKmJamF980iKxPJrA2AbhSniIIyVNSwMrcV4cUCYKoQqRkXOwEr780E3EILDkqcDBzZR0JEaRkzPzBiqfsL77KwLxqxkTk6S2/240UCYKoQqSU4IOadnxTIiRPX9B1fUtEEVEkmC9cwpk4AyawPpm//PKku4EgqhCRFx89qKeH5FlcpJSAhccfKt1EHIKIPJmv/PIkRYIgqhB6UE+PiE9fSJFwqTxFqPQddCUgFHMi4togiwRBEGaQ0uZjJNz6oBZByLXhWnmaf/WRYlZEOK94eZIiQRBViJCpkx7UU1MFD+qqQtfBBBRd9ypm5mBaCkxXzR9PFgmCcC+mKzHqOiSRdDC3PqhNypNp6Yp/UFcTUjouZrZ3q2JmUkhSSkApYwzwBazNpwBIkSCIKoMe1MVFyK1h04O6EjCr6ArF7yheyiiaBiF5+kK2ZBSRIkEQVYbYg5pSP6dD7EFNqZ/TIQtlwJCSOx1C8rRp00B3BEFUCJybO04WMHW6OlWxFPJ0sVuDm1ygYoGrLlYkTK5PscBqUiQIgjABpX6aw+RzGlK68nd81YSYPF2smJk8TsxiZo88SZEgiArB/IOFHtTFRBIpj+1iC4/Z9SmLVLV09fo0IVHOBV0bpEgQhLsxYzrmHHJiyPQl3byDNmWJ13XIyRHT13SzPM2895iagqSKZBS5V55m1qeUjoEJtA8n1wZBuBwz7z0pnYAkkqoYqLE+oapneonKyREw03ttd8vTjJTkxLDQNd1tkZgeIXkyybaMIlIkCKKKELFGgDGAXnxTIicF5Kl4AK87Uz8NzChmAvL0hcAUTwHzqW7MKWYC1sdg1LaMIlIkCKJCMGPqFNqhBCIubc+cocjyZMFaMNNVw5xHsdcnC0ULmI0DMCHQapGnYtvIBOFiUuphvycDwBiDzvXcZ1JyFJ6hDmiBKLRAFFw2dm4iOz43Pagnk6c25kEtJ4agjPQY8vRHDstTZMfnEnlyzpFS9VwBqsPrc4w8Y/1Q4gPQ/FGo/gggG68SMXnWFnHWlYshTy2nhDIGMDDoY/QIZaQXcnIYamZ9QpLF46FslCcpEgRhA2lNRyyZzvu97vHB37sTLPPw1jwBaIEo5NiA6TFYsLbAWVYPKVVDPJU/dkRXfAh0v5X7b80bhOaPClsk3ABjDElVRTKdP8iPy14EurYbPwPQfSGo/qhYRlHQHYoZYwzxlIq0puc9hksyAl3bjJ/BoPvC0PxhSFr+Z8QEbJQnuTYIwgYCnml0eEmBGqjL/aecjsM71CEWaOmSHTQABLxTy5MrPqi+w/EicioG79AhMJ7/4X4kbpJn0Dt17ILuDUJX/AAMi4WcHIVv8KBY6XaXWCQAIOibWp5aIAIuGWuYgUNODsM7eEhgBAYWjBQww8IgRYIgbECSGPzTKBNqqKGgMZismK5GWO3IkgSfMnU8iBouQJ6MAUxyjTwVWYJHnuL1wBjShaxP2QMIKHHVjkeWoEhTqFlMQjpUb30Arx8QsV4UGcbdcmcQRIWh6Tr6RvLn3MuJIdTseqawQRQvWKgOLFxr/H9Ng2Mj5dOajoHR/PJURnsR3vtiYYN4fGChWrBw3WF5ys70EKdUDYOxZN7vPUMdCB14rbBBPP7DsgzXgdXUOzZAOJFWMRxP5f3e278PwY4thQ3iDYyRZy1YuAFMKr29gCwSBGETsiTBO8UuWvPVQJcLbLilpsAHO6EfeBPa7lfNN/SoQjzT7KLVQC14oelx6ST4QCf0/Vuh7d1U2LUqnOl20WqoQaACRx7SCfD+Q9D3vwF9/xuFXq2i8SkypCmyfgq1QAIAUnHwvoPQ922GfnC7+ZatBUKKBEHYSNA3xW6WMajBuvzfi8AkKItPAvM4uxNoYCrfviRDDdQWZyBZMeTpUGsEYAQJBqbw7XPZA81XpDolHh/kRSc61hoBZOQ5RSzP2LiTgvEFIS9YXbZ0ZVIkCMJGPLI85S5aCxQnwE+et9IVwW1eRYI8xS66aPJcsNoVVRmn20UXRZ6MQV54ApgLin0FvMqUAalqMeQpyVAWnVjWTQMpEgRhM1PtoovxYJFa5kNqml3wdaoBxtiUGQdFkWf7Ekh1rQVfpxqYbhddDEVCmr0CUqSx4OtUA+WQpx2bBlIkCMJmptpFa/4IuFBS3XhYTQOk2Sssn1+N+Dz5d9GFPqhZbQuk9qUFXaPa8E+xiy7UVcQaZkJqmV/QNaqNUm4cpJb5kBpnFXQNS+OWfUSCIMZh7FLyPFwkGZrfoh/aF8z4nd11m0+16+OKD5rHoh86ECmr37lSkBiDP488dW8wV/9AFBauhzz/OPfJU2LweyaPBSlk48CizZDm2LNpcNcThiAqFL9HRj7XvqVdtOyBsmQtmMdX2MSqlKl20ZqVXbTHD2XJGsemzk5HXnM8Y9Z20b4Q5MUnOTq4ciqKvnEIRow4E2raRRDuhbH8BaqEH9RMMh7SLu78KTEGX7HkKcmGEuELFmFm1clUBb+EFV3F3UouMHXBL2F5evxQFq+1VcklRYIgKoR85mPRB4s8/zjXBK9NRb5dtLA8F57gioyX6cgnTyHFjEmQF68BCzg/42U6iiJPSckoufZmvJAiQRAVQr5dn+4JQpfN7TakmUttCbaqRPLt+jRfBNykX16ac4xrMjSmQ8lToErzm3/xyfOPh1RThMJLDsCbJ7VWRNGVF62uCCWXFAmCqCAmtUowZuphzRpnQ2pbUoJZVS+T7vokyVQhJal1AeRWd2UUTAVjDP5JfPtc8ULzTO/2kWYug9Q4sxRTq0ryBQWb3ThIc4+FVFsZSi4pEgRRQXjkyVNBpzN3skijkT/usgj46ci/66ud8jxWN8N1abNm8HvkSYNYp9tFs6bZkNoWl2ZSVcykcVEmNg7SjIWQW+aVaFbikCJBEBVEvl3KlA/qQI0r0zzNwPKkLk6lmLFQHeQFq0gpmwRL8ow0QZ5LSu5kSBKDb5JU0CnlWdcGadZRpZyWMPTkIYgKw++ZmLrIQnkeLB5fJi3R2T00CiEwya5PyudX9gUhL3F2D41CmUzRlcO1eQ4mJXc6JpOnkkeeLFQHeWHlKbn01yWICmOyXV8oFAaOVBYk2YiA94XKOLvpGd29D+nhEbunkUOS2IQg1mAkOrEzouKBsngNmNWCVSVi5K1d0GJxu6eRQ5bGB7EyAIHIJM3lPH4jzbPCam8Mb9sBPZW2exo5PLIMZYyiJTEGf6R24oG+EOQlayqy9gYpEgRRgYz1nfo9CjyKPL4uRCaNTgoXqTtoEel64mk8efIF2P2jn0NPV8YDe+yuL+hVoCgKMLbplqRAXnIyWDBiw+ym5uBv/oAnT3s79j30G3Bdt3s6AMYXVAr6PJC9PmBsXQjFC2XpyRVZe2PPg7/AX9ZfjIO/fQycF9wIvSiMXZ8hn8doCDe2uJQ3AGXZyRVbe4MUCYKoQLKpi4wBIb/x0M4pEoxBXnQCpGiTjTPMjxaLI9Xbh02fuRNPnXkZOh570vYHdjZ1UWIMQd+R8pQgL6lMpQwA1FgciUOdePXmW/H0uVeg+68b7Z4SvIoEiTHI0uGYnpw8ZcVQIipQKQOM9RnbvQ8vffgW/OOiq9H3/Ct2Twk+jwzGjHVq/MyArDxzSlllWR7H4kpFgnOe+0cUjiFLneRZJLJr0+9REPJ5DmcdZB4s8vxVkOpm2DjDqdFisdzPozt244X3fRwb3/E+DL72hi3zya5Lv0dByO/J+ZdZoMZQyhafWNEFvMbKc2jzVjz77g/h2ffeiOFtO2yZT06eXhlhv3e8PCUZ8pK1FVHbIB/a6GF59r/4Kv5x8dV44YP/hNE9+2yZz9j1GT5yfWaVsgqvUuv4iCLOOZAcAWKD4MlRIDECaIfNrVySAV8I8IWNP1awtuICWSoJzjmQGAaPDQLJUUO2mnr4e0kB/GPkGYiSPKeAcw7Eh8DjQ4YsE6OAbsjTAwCyAt0XNtaoJBu54xWei69O4s/ve+ZF/O2C92DBR6/Hok/eANlXmuBQzjkQGwCPDxvyTI4CugYA8IIZ8vSHjd2d4oU0f1XF5OLnY7L4iO4n/oan//YsFn/qI5h/43WQlNI8yjnXgdEB8MSw8exMxXLy9IOBKR7ovhCYLwx4A5AXnVjxBacmW58dv/sTup/4G5b+2ycw931Xliw4lOs6MNpvyDM5avzjhrsqkJWnPwzmC4P5Q5AqXCnLwrhDt5Fc14DhHvDBDiBlJlCJAeCA7AEizWDRFoqEHwPXVWCo25BnOmnijIw8FS9YpAWINoOZrM7oBrimAkOd4IOdgJoydw7nYB4/WG0rUNNUsZkFr336duz9yS/zfl+zdCGO/cadqD2meClsXE0BQ12GPLU0cutvqnM4B/MGwWpbDHlWYBAbALzwwX9Cx+/+lPf76MoVWPmNO1GzeEHRxuTpJPhQJzDYlVFsTcrTF8qsz0bbGkhNx8Z3Xo/efzyf9/v6tatx7Ne+iNCc4lWI5emEsTaHujKKmEl5BmrAoq1AuKGiN2SOVCR4bBC8a4fpB/SkMAmscY6hVFTwH7Ac8NF+8K6d4yw5wkgyWNO8ir8hSg3nHBjpBe/eldvZWUJWwJrng4XqizKndN8AEp1dSHR0IXEo8/8dnUh0diM9MAQ9mYSWTEFPpYyfE9mfU+CqCsnrgeT1QvJ6ocZi0BNTK5tMlrHwY+/HoptvgJSvE6LJuWO4G7x7d25nZwnFC9a8ACxooZPlkXPSdaT6+pHo6D4sx0NdSHR2I9HRCXVweLwsk4Yc9WQSeioNrmmGPD2GTNWRkWmzDCSf17BO3HBtQdYJzjkw2AHeuxco5NXg8RvyLIJJnus6kj19SHZk12VGphn5poeHoSfTGfmlMvJMZmSaAtf1w+vT40F6eBg8rU45phwMYNlnP4k5115RkHWCcx3oPwTetx/TKQ5T4g2CtSyo2DgJRykSnOvGA2Woq3gX9deAtS5ypXWC65qhQIz0Fu+iwVqwloUVu5suJVxTwTvfAmIDxbtouMFQKEzsprmuI77/IIbe2IahLdswvGUbhrZsR3z/QejJApTuAqhZtggrv34noscsFz6XqylDnvGh4k0o0gzWNNfUbprrOkZ378Pw1u0YeiMjz62GPKd7UZWK2uOPwcqv34HwIvHS3jydBO/YZpjbizahGWANs01tHnRVRWzXXgxtyazPN7Zj+M3tiB/sBFftkWfDKSfi2K99EcFZ7cLn8lTckKcpi7g5WP1MoK694jZjjlEkuK6BH9oGxAeLf3HFC9a+vOLyy0sJ11Twg1uK+1DJ4vEb8nSRcsbVNPjBN4r6UMnhC4G1LRunnHFdx/Cbb6H/+VcwuHkrht/YhuE334I6UoK/Z4EwRcHCj30Aiz7xIdPWCZ5Ogh94A1DNuNkECUTAZiwZp5xxTcPQ5jfR98IrOaVheOtb0OKVU98hi+TzYsmnb8L8D18DJptz1/BU3JBnIVbHfITqjM3YGOVMT6cxuGkr+l98FcOb3zQUh207prVk2YEcDGDZbZ/CnKvfZdo6wRMjxvOzEKtjPmqajM1DBSkTjlAkONfBD75ZGiUii+wFm7XCFS8/rmvGQ6UUSkQWjx9s5lGuiJvgmgq+fzOQLuFLxxcCa1+O7r/8A7t/+L/of/5lpAeHSzdeCYgsX4Jjv3EnoiuWTnkcV1Pg+zcV5rqcjkAErG0pDj3yJ+z76f9D/4uvVqQSNhV1q47FsV+/A+GFU/dk4OmEIU+thLv+UB1Y62Lsf+g32P/LRzDw4msVqYRNReOpJ+GYr30RwZltUx7HkzHjfuclUCKyVJgyUZnRMILwvgOlVSIAQEuBd2x3RYoj79lTWiUCANIJ8K6d7pBn987SKhEAkBwF79mDVG8/uv7816pTIgBg6I038bfzr8S2r343byErzrnhziilEgEYmTS9+5E41Inup/5RdUoEYKQ2/vWcd2HHvT8C1yZ/qXHOwTu2l1aJAIDRfmDgEGJ79qH3b89WnRIBAD1/exZPnXEp9jz4UN7nFtd18I43S6tEAMBwt/GvQqh6RYInRoD+A+UZLDEMDHaWZyyb4LGB4saYTMVof3HjLyoQPtwDjPSVZ7ChLtQfV90dFrmqYttX/wvPXP4BJLt7Jh4w1FncmIipGDiI+lWV1RxJFD2RxJYv/geeu/YmpAcnkVv/gdJvGjLw3n2oP+HYsoxVKrTRGF7/lzvw0g2fhjqmvkcW3rvXZFZb4fDu3eDpRFnGmo6qViRyu5Nyjtm7x0g1cyCc6+Cd5S1yw7t2GqmQDoTrmpGdUUb88giCc2eXdcxS0PfsS3j6vHdj4LXNuc+4mjKsZWWkph7w1BaeyWE33U/8DX+74D3jiljxVCKTTVAuOGpnhiCVqIZIOTn028fwj4uuQWzf4U0sT4wAgx3lmwTXy/58yUdVKxKIDQLl1sg4N/KBnchIX2mCraaC6xVloisqQ92lCbaaCk1FwwlHl3fMEpE41Il/XHIt9v+/R4wPhroKS0m0ANPSaDixunfRWUZ37sHfL7wKHX94AgCMmjBlRmZp1B63ouzjloKhN97E0+e9Gz1/exYAwAcOlX8SsUHwUgRwC2JJkWhqagJjbMp/73vf+4o91wnYcSMAAAY7jfxgh2HLjQCAD3Q4LlaCc26bPBuOnjq4rprQE0m8ctNnsPkLX4Hef9CWOdQfu9CWcUuBOjKKF67/BLZ97bu2bYgaVla3+20s6f4BPHvlh7Hzfx4onwvzCCphYyuczK9pGn77299O+t2ePXtw/fXXQ9f1kisSXEsXNx9fBF01rCGhymzyYwWeSpTNVzoBNWmU363wevJCJEdKk5pogvoVc2wZt9jULF2I+jWr0bB2NeqOXgCmThIzUQYaVlS/qwiMIXLUUjSsXYX6NatRv3wOWNomeR41C9ttGbl4MFlG5OhlaMiuz8UzgLQ9igSGusAb59iawSGsSMiyjDVr1kz4fN++fXjPe94DTdPwm9/8BqeffnpRJpiXhL1R1DwxAuYgRQLJEfvHd5IiYeP69DfWIjR/NkZ37rVtDlZQIjWYseEstJyzHvUnrYK3vjb3nd67D+i3Z17huW3wNtQh1WvTBCziqatF29vPQfNZp6P+xOPgiR7uxql37wYGpy/TXAqiy+ZB8vsqsmbEVPiaGzHj7eei+azTUL96JZTw4SqTesd2oMxe4RxcN1z83oBNEyhS064DBw7gjDPOwMGDB/GrX/0K5557bjEuOzV2v/gSNo9fZHhiBGbqv5dy/MrIiC4O3Nb1ydCw+ihhRYIpCnzNjfDPaIa/tQX+1ubMvyYooRAkvw+yzyiDLfkyP/u8gCRBT6Whp1KI7d6HF67/hOkxJb8freesR9vF56HpzNPyN/OyUZ6MMTSsXoFDjz0tdp5Hgb8lI8MZWVm2wNfSaMjT583I0AfJm/nZ7wMYy8lz6PUteOUTnzU9phwKonXDWWi/ZAMaT1sDyZOnTktiGHbd67LXg/rjlqNn48tC50k+L/wtTYfXZkamvuYmyMEAZJ8Pkt+bkaUPUnZ9MmaUy06l0PuP57H5ti+ZHtMTrUHr+Wej/ZINaDj5hPwFvux+HyRHq1uROHjwIM444wzs27cPv/zlL7Fhw4ZizGtabA8wSVVfXvmUpGKw68ECwD63SqlITkwNKx8cDSuXYO9Dj076reT3oWbJQkSWLUbN8sWILFuM8MJ58DU1FN71UJ8+dogpCprWn4L2Szag5dwzoISC01/XVnkC9ccvy6tIyIEAapYtQmTZItQsW4zIckOe3vq6guVpxgoi+bxoPut0tF2yAS1nnQ45YKICr83Pz/pVR+VVJJRwKCfHyLLFqFm2GOH5c+CpL7wz8+jO6bN+5EAALeesR9slG9C0/pRpO9VyrtvmxjRg4MkYmI0G3YIUiY6ODpx55pnYvXs3Hn74YVx44YXFmtf0lDsafsL4Dgu2tFueTgtetVme9cctAQBIXg+ix65A/QkrET32KESWLUZo3mzTpZNFUUfzvKAYQ8PJJ6Dtkg2Ycf7Z8NYJplTavD4ajjd6gUh+H2qPOxr1JxyH6DHLEVm2GME5M0vWdnqyFuKA4aNvPG0N2i7ZgNbzzoQnIvgWsVuemfoccjCAulXHou6E4xA9ehkiyxcjMLOtZP5+dXRyhZR5FDSfcSraLt6AlnPXQwmaUG6zVMK7wOa/p2VForOzE2eeeSZ27tyJhx56CBdddFEx52UCuw3hdo9PEPnx1UVw6u9/hpqliyD7fWUbVzuiSI+3oR7zPnAVZl1xCfytzWWbR7EJzZ2BUx75CaIrlhXUrVSUI198/hnNmPfBazDznRfC19hQtnkUm+jyhTj10f9FZPni/O6XEnCkYhacMxPzP3QN2i49H14H1AuxC0uKRHd3N8466yxs374dP//5z3HJJZcUeVomMNHtsLTjV3cJjglINnfjtPvvWWxkGbCzzpasoHaleEfNQlEzD+rg7HbMv/F9mHX5xeZM7dMhybZaeZisoO748ssz++ILLZiLhR+9Hu2XXVgcRYbJpS/jnH9wSD4vao8V71BaKFnFLLJ8CRbc9H7MuPDsgtquA6iMd4HNz09hCfb09ODMM8/Em2++iZ/+9Ke47LLLSjGvaWG+ILid8S0V2hfeMr5gpvSwHXESDPCFbRi3hPjCNvr1mW3r01dfh+O+8++Y8fZzCn9Aj7twqPT9NaaAee2Rp7+tBau+/w20nntGcd0nvmAm4NIOOJhN6zM0fw5O/Ml30bT+lKK5TxiTwD3+8hdHzMHBfAKumBIgdKf39vbibW97G7Zs2YKf/OQneNe73lWqeU2PzS9y5nfWi4/5w+C2BVva92ApFcwXsjF01T551p90fEmuy3xh8FEb0y/99sizef0ppbmwP5zJNLBpldq0PmdsOKs0F/bX2KhIwPb3oWlFYnR0FGeffTZeffVVfPzjH8ecOXPwzDPPTDguHA5jxYoylEC1+8XjMEXCdouA0+Rp9+9j9/jFxu7fx+77o8jYunFgzLCIOAjmD4PbVepfUgClfHFQk2Fakdi4cSNeftlI1/nWt76Fb33rW5Me9+53vxs/+9nPijO7KWCyAh5usKd7pOwBAs4KzGEeH3ggUr7OimPxBuxXDIuNN2j8S9ng3vDXgHmKEJdQSQQigOK1x70RqgeTbY4hKjahOvviTmqawFgFxBUUk3AD0LO77L1gAADRZlurWgICisTb3va2iuuHwKKt4DYoEqx2hu1/uFLAameA26BIOFGejDGgdgZ4V3m7qQKGPJ0GYwyIzgDvLW/3TwBgta1lH7PUMCaBR1uNNuLlHjvqQHnKCnhNk9FYrtxjR1rKPuaRVLda6A+XfyfLJCDSVN4xy0WwFij3TlZWDG3eiYQbDOtVOVF8juoBM45IU/mj030hw//tQFi0xXAzlJNA1PbAwFJhiwIfbgDz2OvWAKpckWCMgbUsRDlrOrDm+WDlfjmUicPyLOOYzQvBnJb6mYFJUvnl2brQcdadLExWwJrKmTLIwJoXOFeeihescW4ZB5TAmsuf8lkumDcAVj+rfANKCljT3PKNNwVVrUgAmT9eY5m684XqnLt7zsD8YaCuvTyDRZrBQrXlGcsmWDAKRMpUiKmuDcyhu+csrKahbPcga5jl2N1zjkizEX9SBljT3IrYPZeUurayWckraVNb9YoEACDaCoQbSzuGN+Do3clYWH274eYoJf4wWKMz2l1PB2ucW3rzeDAKVj+ztGNUCKxpnhHIWkrCDYADY02OxLBCLgJK/YKPNAM1DnUJj4ExBta6uPQuzbp2sHB9accQwBGKhHEzLABqSqRMeINg7cudF7mdB8YksBmLS+dr99eAtS11rEvjSJgkgbUtLd3OL1gL1rrEeZHweWCyAta+rHQ7v3ADWItzXURHwhQPWPtRpYuPijSDNc1zjzw9PrCZRxlZRqWgrr3iNg2MV1oqRgFwzoH+g+B9+1G0QivhRrDmea556Y2Fcw7etw/oP1i8i0aawRrnlqzJUSXDuQ7eswcY7CzeRWtngDXMds1Deixc18C7dwHDPUW7JqufaTyo3ShPTTWyjIpV+IsxsIbZQLTVnfJUU+CdO4D4YHEuyCTDPVQuV6kAjlIksvBUzPgDFtKaWvYYPiinRsALwBMjxgOmkNbDig+sZQFYmfyxlQyPDxnrs5DWw56AIU+7CzVVAHx0wFifWtr6RXwhw3Xp9JgIE/CRPvCunYBeQLMYf40hT6/D6pkIwjkHhnvAu3cX1tskWGtsaG0uPJUPRyoSQOYPGBsAH+wEYgOZTxmmtVR4g0beeLjBlVaIfHCuA6MD4IMdY4pW5ZPnmM/9YSPNLNTgSitEPjjXgZE+Y33meh6YkGcgkpFnnWtcGWbgugaM9BrynHYDMUaewahR1yBY68pdcz64rhovwMFOExuIMfIM1RnrMxAleY6Bayow3A0+0GFiA5GVJwPC9YY8/TUVLU/HKhJj4ekkEB8CT44a9eXVpFGBjDGjjoEvbPQmCNQ4rudDKeDpxBHyTI2Rp8dQHrLyLHVQnAPgqfh4eWrpw/JUvGPWZ8T1OzwzGHIcHiNPdbw8/TWG5SEQdX4WQYFwzg3FLCfPUcNSkZWnx29Yc3whQykrVVyAQ+CcG2syOXJ4feraeHlmn5/BaMVkZUyHKxQJgiAIgiBKA9lGCYIgCIKwDCkSBEEQBEFYhhQJgiAIgiAsQ4oEQRAEQRCWIUWCIAiCIAjLkCJBEARBEIRlSJEgCIIgCMIypEgQBEEQBGEZUiQIgiAIgrAMKRIEQRAEQViGFAmCIAiCICxDigRBEARBEJYhRYIgCIIgCMuQIkEQBEEQhGVIkSAIgiAIwjKK3RMoBt7jrgeTZDBJhqR4wWQZUvZnSTr8nceb+1lWDv888TsFTGJgjEFWJDCJQWIMTMr+N/J/xxiYhNzPkmR851UkyBKDLDH4xvwsS9Lh75hxnJL5zjvuuPH/7ZEkyAyQMud5JGMsKfOznPlZZoBHliAxGMfJ2c8zx8oMjAESGGQJ438GjN8tc67EMP5nwLiuxHLHSuAA18G4DnAd0LUxP6uAnv87xnVAP+JzroOraUDXAF0DTxs/c10DdB1cTRn/r2mAmgLPnA9dy51nfJcG1zVwXQfXdOhpFVwzftbS6TE/H/6c6zr0lAo9d87h4/TcNTh0TYeWzpyjcegah5bWct/pKeN4rhvf6SkNusbBdR1aKns9Dk3VoXEOjQMa50jpY39G3u/SYz7XODLfGT/fy3fbe3MWAbq/6f6m+7uy72+ySBAEQRAEYRlSJAiCIAiCsAwpEgRBEARBWIYUCYIgCIIgLEOKBEEQBEEQliFFgiAIgiAIy5AiQRAEQRCEZUiRIAiCIAjCMqRIEARBEARhGVIkCIIgCIKwDCkSBEEQBEFYhhQJgiAIgiAsQ4oEQRAEQRCWIUWCIAiCIAjLkCJBEARBEIRlSJEgCIIgCMIypEgQBEEQBGEZUiQIgiAIgrAMKRIEQRAEQViGFAmCIAiCICxDigRBEARBEJYhRYIgCIIgCMuQIkEQBEEQhGVIkSAIgiAIwjKkSBAEQRAEYR1OjCORSPDPf/7zPJFI2D2VSank+VXy3Din+RGVL+NKnl8lz41zmp+dMM45t1uZqSSGhoYQjUYxODiISCRi93QmUMnzq+S5ATQ/ovJlXMnzq+S5ATQ/OyHXBkEQBEEQliFFgiAIgiAIy5AiQRAEQRCEZUiROAKfz4fPf/7z8Pl8dk9lUip5fpU8N4DmR1S+jCt5fpU8N4DmZycUbEkQBEEQhGXIIkEQBEEQhGVIkSAIgiAIwjKkSBAEQRAEYRlSJMZwyy234LTTTsNVV12FVCo17rt4PI4LL7wQ69atw9lnn42+vr6Kml+We+65B6tXr7Z1Pqqq4rrrrsNpp52GT3ziE2WZi8j8spRTVpORb36VsNacCN3fxZkP3d/mcNP9TYpEhpdffhkdHR14+umnsXz5cjz88MPjvn/00UexYsUKPPXUU7j88svx4IMPVtT8AGB4eBibNm2yfT6//e1vMXPmTDz99NOIxWL4xz/+UZY5mZ0fUF5ZTcZU87N7rTkRur+LNx+6v6fHbfc3KRIZNm7ciHPOOQcAcN555024ORYtWoRYLAYAGBgYQFNTU0XNDwC++c1v4qMf/ajt8zEzVzvnB5RXVpMx1fzsXmtOhO7v4s2H7u/pcdv9rdg9gUphYGAAbW1tAIBoNDrB3LRgwQJs2rQJK1asAGMMzz77bEXNb3BwEK+//jpuvfVW2+czMDCQqyU/2Vztnl+5ZTUZU83P7rXmROj+Lt586P6eHrfd366zSHR0dODUU0+d8I9zjqGhIQDGIqivrx933o9+9COsX78emzZtwhe+8AV88YtfrKj5feMb38BNN91UkjlNRl1dXd75TPVdJcyv3LKajKnmV6615kTo/i4OdH8Xhtvub9cpEq2trfjb3/424d/555+PP/7xjwCAxx57DKeccsqEc7OLoba2FgMDAxU1v7feegt33XUXzjvvPGzfvh1f+tKXSjK/LGvWrMk7n6m+KxdTzaHcshKdH1CeteZE6P4uDnR/l25+gAPvb/s6mFcen/rUp/ipp57K3/Oe9/BkMsk55/xDH/oQ55zzwcFBfv755/N169bxU045hb/55psVNb+xrFq1ypb5ZOeSTqf5Nddcw0899VT+sY99rCxzEZnfWMolq8nIN79KWGtOhO7vwuZD97cYbrq/qUQ2QRAEQRCWcZ1rgyAIgiCI4kGKBEEQBEEQliFFgiAIgiAIy5AiYZEf/vCHYIyBMYa//OUvE77nnGPhwoVgjGH9+vUTvh8aGsJdd92F1atXIxKJwOfzYe7cubj++uvx0ksvTTrOZP8mG3syzjrrLNxwww0Wf9vCWL9+/aQyqASy8t29e3dRr3v11VfjkksuKeo1iYlM9ve77rrrMHfuXEvXu/3228EYQ09PT3EmWAb6+vrw7ne/G83NzWCM5dbd7t27ccEFF6C+vh6MMdx8881C1x0YGEBjYyP+93//t2hzLdX9Vm6y66TYnH766cJ/p0qAClIVSE1NDb7//e9PeFE+9dRT2LFjB2pqaiacs2PHDpxzzjno6urCDTfcgC984QsIh8PYvXs3HnroIaxatQoDAwOIRqO5c37wgx9g6dKlE661fPnyaef4m9/8Bn//+9/xwAMPiP+ChCVuv/12LF26FE888QTOPPNMu6fjKm677TZbe0CUmzvuuAO/+tWvcP/992PBggW51MJPfvKTePbZZ3H//fejtbUVM2bMELruF77wBbS1teGKK64oxbSJSbjjjjtw9tln48Ybb8SSJUvsno5pSJEokCuuuAI/+clP8J3vfCdX7Q0Avv/972Pt2rW5oiRZNE3DpZdeip6eHmzcuBErVqzIfbdu3Tpce+21ePTRR+HxeMadt2LFCssNaO6++25ceumlaG9vt3Q+YZ5YLIZgMIgFCxbgvPPOw5e+9CVSJMrMggUL7J5CWdm0aRMWLFiAq666asLnJ554oiXLWF9fH/77v/8bX//610uy8ybGk31urFu3DkuWLMFXv/pV3HfffXZPyzTk2iiQK6+8EgDws5/9LPfZ4OAgfvnLX+L666+fcPyvf/1rvP766/jMZz4zTokYy4YNGxAMBosyv5dffhnPPfccrr766nGfZ02MTz75JG688UY0NjaioaEBl112GQ4ePDjuWF3X8eUvfxlLly6Fz+dDc3MzrrnmGuzfv3/ccZxzfPnLX8acOXPg9/tx/PHH49FHH510XkNDQ7jlllswb948eL1etLe34+abb8bo6Kip3+sPf/gDzjrrLESjUQSDQSxbtgz33HPPuGP+7//+D2vXrkUwGERNTQ3OPvtsbNy40dT177//fhx77LHw+/2or6/HpZdeii1btow75rrrrkM4HMbrr7+Oc845BzU1NTjrrLNy31999dX485//jB07dpgakygOk7k2GGO46aab8OCDD2LZsmUIBoM49thj8cgjj0x7va1bt2L+/Pk46aST0NXVNe2xV155JVpaWuDz+TB79mxcc801SCaTuWM2bdqEiy++GHV1dfD7/Vi5ciV+9KMfTbjWdPfI7t27wRjDn//8Z2zZsmWcu5MxhrfeeguPPvpo7nMRd8IPf/hDqKo6qTXihRdewEUXXYT6+nr4/X4cd9xxeOihhyYc98wzz+CUU06B3+9HW1sbPvOZzyCdTk84LplM4lOf+hRaW1sRDAZx+umn48UXX8TcuXNx3XXXjTu2o6MDH/7whzFz5kx4vV7MmzcPX/jCF6Cqqqnf66c//SnWrl2LcDiMcDiMlStX4vvf//64Y8zc+5Nh9jm5fv16rFixAn/9619x8sknIxgMjntXXH311fjpT3+K4eFhU79TRWBvGYvq5Qc/+AEHwJ9//nl+9dVX8xNPPDH33Xe/+10eCoX40NAQP+qoo/i6dety333oQx/iAPiWLVuExnnmmWd4Op0e909V1WnP/+IXv8hlWebDw8OTXnf+/Pn8Yx/7GH/sscf49773PV5XV8fPOOOMccdm53zTTTfxP/zhD/zee+/lTU1NfNasWby7uzt33Oc//3kOgL///e/njz76KL/vvvt4e3s7b21tHSeD0dFRvnLlSt7Y2Mi/9rWv8T//+c/8m9/8Jo9Go/zMM8/kuq5P+Tt973vf44wxvn79ev7Tn/6U//nPf+b/9V//xT/ykY/kjvnJT37CAfBzzjmH//rXv+Y///nP+apVq7jX6+VPP/30BDns2rUr99ndd9/NAfArr7yS/+53v+MPPPAAnz9/Po9Go3zbtm2546699lru8Xj43Llz+T333MMff/xx/thjj+W+7+zs5AD4t771ran/SIRlJvv7XXvttXzOnDnjjgPA586dy0888UT+0EMP8d///vd8/fr1XFEUvmPHjtxx2TWcXdd/+ctfeF1dHb/44ov56OjolHN55ZVXeDgc5nPnzuX33nsvf/zxx/mPf/xjfvnll/OhoSHOOedbt27lNTU1fMGCBfyBBx7gv/vd7/iVV17JAfB///d/z13LzD2SSCT4xo0b+XHHHcfnz5/PN27cyDdu3MgHBwf5xo0beWtrKz/llFNynycSCdNyPfPMM8c907I88cQT3Ov18tNOO43//Oc/53/4wx/4ddddxwHwH/zgB7njNm/ezIPBIF++fDn/2c9+xn/zm9/wc889l8+ePXvC3+vKK6/kkiTxf/3Xf+V//OMf+Te+8Q0+a9YsHo1G+bXXXps77tChQ3zWrFl8zpw5/L//+7/5n//8Z37HHXdwn8/Hr7vuuml/p9tuu40D4Jdddhn/xS9+wf/4xz/yr33ta/y2227LHWP23s+uk7GYfU6uW7eO19fX81mzZvFvf/vb/Mknn+RPPfVU7vtnn32WA+D/93//N+3vVCmQImGRsYrEk08+yQHwTZs2cc45P+GEE3IL+0hF4rzzzuMATN/U2XEm+yfL8rTnb9iwgS9dujTvdce+fDnn/Mtf/jIHwA8dOsQ553zLli2THpdd7P/2b//GOee8v7+f+/1+fumll4477u9//zsHME4G99xzD5ckiT///PPjjn344Yc5AP773/8+7+8zPDzMI5EIP/XUU/MqHJqm8ba2Nn700UdzTdPGndvc3MxPPvnkCXLIPtj6+/t5IBDg559//rhr7t27l/t8Pv6e97wn99m1117LAfD7778/73zb29v5FVdckfd7ojBEFImWlpbcC51zzjs6OrgkSfyee+7JfTZWkXjwwQe51+vlH//4x8eto3yceeaZvLa2lnd1deU95t3vfjf3+Xx879694z7fsGEDDwaDfGBggHMudo+sW7eOH3XUURPGmjNnDr/gggumnfdkBINBfsMNN0z4fOnSpfy4447j6XR63OcXXnghnzFjRk5OV1xxBQ8EAryjoyN3jKqqfOnSpeP+Xps3b+YA+L/8y7+Mu97PfvYzDmCcIvHhD3+Yh8NhvmfPnnHH/sd//AcHwDdv3pz399m5cyeXZZlfddVVeY8RufePVCTMPic5N/5eAPjjjz8+6TxSqRRnjE2QSSVDro0isG7dOixYsAD3338/Xn/9dTz//POTujUK4YEHHsDzzz8/7p+ZrnEHDx5Ec3Nz3u8vuuiicf99zDHHAAD27NkDAHjyyScBYIKJ8cQTT8SyZcvw+OOPAzDa5iYSiQl+2pNPPhlz5swZ99kjjzyCFStWYOXKlVBVNffv3HPPnTYT5R//+AeGhobwkY98JK/v9s0338TBgwdx9dVXQ5IOL/FwOIx3vOMdeOaZZ3JtfI9k48aNiMfjE37fWbNm4cwzz8z9vmN5xzvekXe+zc3NOHDgQN7vifJxxhlnjAt+bmlpQXNzc26tj+Wuu+7Cddddhy996Uv45je/OW4dTUYsFsNTTz2Fyy+/fMq20E888QTOOusszJo1a9zn1113HWKxWM71Vsg9UigDAwOIxWITnhtvvfUWtm7dmrvHx87r/PPPx6FDh/Dmm28CMJ4bZ511FlpaWnLny7I8wVXy1FNPAQAuv/zycZ+/853vhKKMD+F75JFHcMYZZ6CtrW3c2Bs2bBh3rcn405/+BE3TpmwtbuXez2L2OZmlrq4ub+yUx+NBbW1tVT03KNiyCDDG8L73vQ/f+ta3kEgksHjxYpx22mmTHjt79mwAwK5duybNwsjHsmXLLAVbxuPxcTfzkTQ0NIz7b5/PlzsPAHp7ewFg0ojvtra23EM4e1xra+uE4478rLOzE2+99daEgNIsU6XedXd3AwBmzpyZ95jp5qzrOvr7+yeNQ5nu3D/96U/jPgsGg+OCbI/E7/fnZEnYy5FrHTDW+2R/nx//+Mdob2/Hu9/9blPX7u/vh6ZpU65LwFhf+dZW9nugsHukULLy8Pv94z7v7OwEANxyyy245ZZbppxXb2+vqWdB9vc98hmlKMqEv1dnZyd++9vf2vbcOPLeFzn3SGV1ugyaantukCJRJK677jp87nOfw7333ou77ror73Hnnnsu7rvvPvz617/Gv/7rv5Z8Xo2Njejr67N8fvZmPnTo0ISb8ODBg2hsbBx3XEdHx4RrdHR0jAt+a2xsRCAQwP333593zvnI7vaODGDKN+cjOXjwICRJQl1dnaVzj5zbdBHtfX19lmsaEPbxhz/8AVdccQVOO+00PP744xOsakdSX18PWZanXJeAsb7yrS3g8Nov5B4plOw9cORzIzvmZz7zGVx22WWTnptNWWxoaMj7LJhsrM7OznFZZaqq5l7OY8c/5phj8j5fs8rYZIx9bhxpDTpyLmbv/XznTvWczDLdc6O/v7+kf+NiQ66NItHe3o5Pf/rTePvb345rr70273EXX3wxjj76aNxzzz3YtGnTpMc89thjeU3voixduhQ7d+60fH7W/PbjH/943OfPP/88tmzZkstSWLNmDfx+P37yk5+MO+4f//jHBG38wgsvxI4dO9DQ0IDVq1dP+DfVi/fkk09GNBrFvffeC56n39ySJUvQ3t6On/70p+OOGR0dxS9/+ctcJsdkrF27FoFAYMLvu3///pxZ2iyqqmLfvn2man0QlcWcOXPw9NNPw+fz4bTTTsP27dunPD4QCGDdunX4xS9+MeXO+KyzzsITTzwxITPqgQceQDAYxJo1awAUdo8Uitfrxfz58ydkGy1ZsgSLFi3Cq6++OumcVq9enXMdnXHGGXj88cdzVgzASH3/+c9/Pu6ap59+OgBM+Pzhhx+ekIlx4YUX5lJdJxt7KkXinHPOgSzL+O53v5v3mELufbPPSTMcPHgQiUSiqp4bZJEoImb63suyjF/96lc455xzsHbtWtx4440444wzEAqFsGfPHjz88MP47W9/i/7+/nHnbdq0adIUpwULFkzpk12/fj3uv/9+bNu2DYsXLxb+nZYsWYIPfehD+Pa3vw1JkrBhwwbs3r0bt912G2bNmoVPfvKTAAyf3y233II777wTH/jAB/Cud70L+/btw+233z7BnHnzzTfjl7/8JU4//XR88pOfxDHHHANd17F371788Y9/xKc+9SmcdNJJk84nHA7jq1/9Kj7wgQ/gbW97Gz74wQ+ipaUFb731Fl599VX853/+JyRJwpe//GVcddVVuPDCC/HhD38YyWQSX/nKVzAwMDDl36m2tha33XYb/u3f/g3XXHMNrrzySvT29uL/t3MvodC9cRzAv//eZgwzHI3JNbeSWcgIiYxcxgILSaGIzMZKFFFymWnMwq3BRrmkULOQjYhkkixcslBGWFhQViK5hQ2//0Jk3nmHMfLi9fvUbOacM+c5z5nn9Ouc53sMBgMkEgn0er3TfWe1WnF9fY2MjAynt2FfR0BAAJaWlpCVlYXU1FRYLBaHkW0A6O7uRkpKChITE9HQ0ICIiAgcHR1hamoKAwMD8PT0hF6vf3rWr9PpIJfLYTabMTMzg87OzqeX0L1njDiytLSEzMxM6HQ66HS6F9dNT0//Y3R7YGAAOTk5yMrKglarRVBQEE5PT7G7u4uNjQ1MTEwAAJqbmzE1NQWNRgOdTgcPDw/09fXZxbujoqJQXFwMk8mEX79+QaPRYHt7GyaTCYIg2MxNaW1thcViQXJyMqqrq6FUKnF7e4uDgwPMzs6iv7/f4aOLsLAwNDY2wmg04ubmBsXFxRAEATs7Ozg5OYHBYHjX2Hf2OumMtbU1APhe143Pnu35XT1Pbbzk99TGo7OzMzIajRQXF0cymYxEIhGFhIRQaWkpLS8v2+3H0WdoaOjF/Z+fn5NMJqPOzk6n2v+YQFlcXHz67u7ujjo6OigyMpJEIhEpFAoqLS2lw8NDm23v7++pra2NgoODSSwWk0qlounpaUpLS7Prg6urK2pubialUklisZgEQaDo6GiqqamxmentyOzsLKWlpZFUKn2KmT2PzxERTU5OUmJiIkkkEpJKpZSZmWnTt8/74fmsf6KHiKlKpXpqW15ent2s8PLycpJKpQ7b2NLSQgqF4k2xO/Y2b0ltVFZW2m0fGhpqkwz4Pf5J9DBW1Wo1yeXyV8f7zs4OFRYWko+PD4nFYgoJCSGtVmvzH9ja2qLc3FwSBIHEYjHFxMTYRCcfOTtGnE1tPI5tvV7/4jEQES0sLBAAWl9ft1u2ublJRUVF5OvrSyKRiPz9/Umj0VB/f7/NesvLy5SUlERubm7k7+9P9fX1NDg4aHe+bm9vqba2lnx9fUkikVBSUhKtrq6SIAhUU1Nj85vHx8dUXV1N4eHhJBKJSC6XU3x8PDU1NdHV1dWrxzU2NkYJCQkkkUhIJpNRbGysXd87M/b/FP909jrp6Hw9Kisro+jo6FeP5Sv5j8jB/WH2z6iqqsLCwgK2t7f5LXV/yd3dHSIiIlBSUvLinBnGviqVSgW1Wv3i44CPsrKyArVaDbPZjJKSkr++/89ycXGBwMBA9PT0oKKi4rOb4zQuJH6Ao6MjREZGYnh4GAUFBZ/dnB9hdHQUdXV12Nvbg7e392c3h7E3m5ubQ35+Pvb29l5No7yHxWLB6uoq4uPj4e7ujs3NTbS3t0MQBFitVrv0yL/MYDBgfHwcVqvVLv76lX2fljKX+fn5wWw22827YB/n/v4eZrOZiwj2bWVnZ6Orqwv7+/sfWkh4eXlhfn4evb29uLy8hEKhQE5ODtra2n5UEQE89MXIyMi3KiIAviPBGGOMsXfg+CdjjDHGXMaFBGOMMcZcxoUEY4wxxlzGhQRjjDHGXMaFBGOMMcZcxoUEY4wxxlzGhQRjjDHGXMaFBGOMMcZc9j/ZryWM5DVEcAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Contemporaneous I(1; 2)=0.272 != I(2; 1)=0.000 due to conditions, finite sample effects or masking, here edge color = larger (absolute) value.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["i=0; tau=4; j=2\n", "graph_data = med.get_mediation_graph_data(i=i, tau=tau, j=j)\n", "tp.plot_mediation_time_series_graph(\n", "    var_names=var_names,\n", "    path_node_array=graph_data['path_node_array'],\n", "    tsg_path_val_matrix=graph_data['tsg_path_val_matrix']\n", "    )\n", "tp.plot_mediation_graph(\n", "                    var_names=var_names,\n", "                    path_val_matrix=graph_data['path_val_matrix'], \n", "                    path_node_array=graph_data['path_node_array'],\n", "                    ); plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In both plots, the node color depicts the mediation of a variable and the link colors depict the link coefficients. The graph plot in the bottom panel is easier to visualize for more complex pathways, but it's harder to see the pathways across variables *and* in time."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Causal effect and mediation analysis can also be used for more aggregate measures. The Average Causal Effect (ACE) quantifies how strong the effect of a single variable on the whole system is and the Average Causal Susceptibility (ACS) quantifies how strong a single variable is effected by perturbations entering elsewhere in the system. Last, the Average Mediated Causal Effect (AMCE) quantifies how much a single variable mediates causal effects between any two other processes. In <PERSON> et al, Nature Communications (2015), these measures are compared with conventional complex network measures to show that causal effect measures are better interpretable alternatives. For example, *betweenness centrality* gives the average number of shortest paths going through a particular node. However, causal effects do not necessarily take shortest paths and betweenness also does not properly take into account the causal effect weights."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average Causal Effect X=0.34, Y=0.28, Z=0.00 \n", "Average Causal Susceptibility X=0.00, Y=0.24, Z=0.38 \n", "Average Mediated Causal Effect X=0.00, Y=0.17, Z=0.00 \n"]}], "source": ["print (\"Average Causal Effect X=%.2f, Y=%.2f, Z=%.2f \" % tuple(med.get_all_ace()))\n", "print (\"Average Causal Susceptibility X=%.2f, Y=%.2f, Z=%.2f \" % tuple(med.get_all_acs()))\n", "print (\"Average Mediated Causal Effect X=%.2f, Y=%.2f, Z=%.2f \" % tuple(med.get_all_amce()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that per default self-loops are excluded in these measures. The Mediation class only supports linear causal effects. For non-linear effects the problem is trickier and can be addressed with the ``CausalEffects`` class that also provides much further functionality. However, the LinearMediation class is much faster."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Bootstrap confidence intervals\n", "\n", "For most causal effect functions in ``LinearMediation`` you can also get confidence intervals via a bootstrap procedure. This requires to first apply fit_model_bootstrap(...) where a free parameter is ``boot_blocklength`` to account for dependency in the samples."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["<tigramite.models.LinearMediation at 0x7f8d80f59a90>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["med.fit_model_bootstrap(boot_blocklength=1, seed=42, boot_samples=100)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.****************\n", "[0.******** 0.********]\n"]}], "source": ["# Get Causal effect and 90% confidence interval\n", "print(med.get_ce(i=0, tau=2,  j=2))\n", "print(med.get_bootstrap_of(function='get_ce', \n", "    function_args={'i':0, 'tau':2,  'j':2}, conf_lev=0.9))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.00000000e+00 1.65451717e-01 5.55111512e-17]\n", "[[0.00000000e+00 1.51683822e-01 0.00000000e+00]\n", " [0.00000000e+00 1.83401850e-01 3.34454686e-16]]\n"]}], "source": ["# Get Average Mediated Causal Effect and 90% confidence intervals\n", "print(med.get_all_amce())\n", "print(med.get_bootstrap_of(function='get_all_amce', \n", "    function_args={}, conf_lev=0.9))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tigenv", "language": "python", "name": "tigenv"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}