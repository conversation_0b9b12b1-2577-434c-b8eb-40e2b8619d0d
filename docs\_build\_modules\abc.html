<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>abc &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=db26dd79" />
    <link rel="stylesheet" type="text/css" href="../_static/alabaster.css?v=19da42e6" />
    <script src="../_static/documentation_options.js?v=625b3a9a"></script>
    <script src="../_static/doctools.js?v=aa79a7b1"></script>
    <script src="../_static/sphinx_highlight.js?v=4825356b"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
   
  <link rel="stylesheet" href="../_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for abc</h1><div class="highlight"><pre>
<span></span><span class="c1"># Copyright 2007 Google, Inc. All Rights Reserved.</span>
<span class="c1"># Licensed to PSF under a Contributor Agreement.</span>

<span class="sd">&quot;&quot;&quot;Abstract Base Classes (ABCs) according to PEP 3119.&quot;&quot;&quot;</span>


<span class="k">def</span><span class="w"> </span><span class="nf">abstractmethod</span><span class="p">(</span><span class="n">funcobj</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;A decorator indicating abstract methods.</span>

<span class="sd">    Requires that the metaclass is ABCMeta or derived from it.  A</span>
<span class="sd">    class that has a metaclass derived from ABCMeta cannot be</span>
<span class="sd">    instantiated unless all of its abstract methods are overridden.</span>
<span class="sd">    The abstract methods can be called using any of the normal</span>
<span class="sd">    &#39;super&#39; call mechanisms.  abstractmethod() may be used to declare</span>
<span class="sd">    abstract methods for properties and descriptors.</span>

<span class="sd">    Usage:</span>

<span class="sd">        class C(metaclass=ABCMeta):</span>
<span class="sd">            @abstractmethod</span>
<span class="sd">            def my_abstract_method(self, arg1, arg2, argN):</span>
<span class="sd">                ...</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">funcobj</span><span class="o">.</span><span class="n">__isabstractmethod__</span> <span class="o">=</span> <span class="kc">True</span>
    <span class="k">return</span> <span class="n">funcobj</span>


<span class="k">class</span><span class="w"> </span><span class="nc">abstractclassmethod</span><span class="p">(</span><span class="nb">classmethod</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;A decorator indicating abstract classmethods.</span>

<span class="sd">    Deprecated, use &#39;classmethod&#39; with &#39;abstractmethod&#39; instead:</span>

<span class="sd">        class C(ABC):</span>
<span class="sd">            @classmethod</span>
<span class="sd">            @abstractmethod</span>
<span class="sd">            def my_abstract_classmethod(cls, ...):</span>
<span class="sd">                ...</span>

<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">__isabstractmethod__</span> <span class="o">=</span> <span class="kc">True</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="nb">callable</span><span class="p">):</span>
        <span class="nb">callable</span><span class="o">.</span><span class="n">__isabstractmethod__</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="nb">callable</span><span class="p">)</span>


<span class="k">class</span><span class="w"> </span><span class="nc">abstractstaticmethod</span><span class="p">(</span><span class="nb">staticmethod</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;A decorator indicating abstract staticmethods.</span>

<span class="sd">    Deprecated, use &#39;staticmethod&#39; with &#39;abstractmethod&#39; instead:</span>

<span class="sd">        class C(ABC):</span>
<span class="sd">            @staticmethod</span>
<span class="sd">            @abstractmethod</span>
<span class="sd">            def my_abstract_staticmethod(...):</span>
<span class="sd">                ...</span>

<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">__isabstractmethod__</span> <span class="o">=</span> <span class="kc">True</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="nb">callable</span><span class="p">):</span>
        <span class="nb">callable</span><span class="o">.</span><span class="n">__isabstractmethod__</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="nb">callable</span><span class="p">)</span>


<span class="k">class</span><span class="w"> </span><span class="nc">abstractproperty</span><span class="p">(</span><span class="nb">property</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;A decorator indicating abstract properties.</span>

<span class="sd">    Deprecated, use &#39;property&#39; with &#39;abstractmethod&#39; instead:</span>

<span class="sd">        class C(ABC):</span>
<span class="sd">            @property</span>
<span class="sd">            @abstractmethod</span>
<span class="sd">            def my_abstract_property(self):</span>
<span class="sd">                ...</span>

<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">__isabstractmethod__</span> <span class="o">=</span> <span class="kc">True</span>


<span class="k">try</span><span class="p">:</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">_abc</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span><span class="n">get_cache_token</span><span class="p">,</span> <span class="n">_abc_init</span><span class="p">,</span> <span class="n">_abc_register</span><span class="p">,</span>
                      <span class="n">_abc_instancecheck</span><span class="p">,</span> <span class="n">_abc_subclasscheck</span><span class="p">,</span> <span class="n">_get_dump</span><span class="p">,</span>
                      <span class="n">_reset_registry</span><span class="p">,</span> <span class="n">_reset_caches</span><span class="p">)</span>
<span class="k">except</span> <span class="ne">ImportError</span><span class="p">:</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">_py_abc</span><span class="w"> </span><span class="kn">import</span> <span class="n">ABCMeta</span><span class="p">,</span> <span class="n">get_cache_token</span>
    <span class="n">ABCMeta</span><span class="o">.</span><span class="vm">__module__</span> <span class="o">=</span> <span class="s1">&#39;abc&#39;</span>
<span class="k">else</span><span class="p">:</span>
    <span class="k">class</span><span class="w"> </span><span class="nc">ABCMeta</span><span class="p">(</span><span class="nb">type</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Metaclass for defining Abstract Base Classes (ABCs).</span>

<span class="sd">        Use this metaclass to create an ABC.  An ABC can be subclassed</span>
<span class="sd">        directly, and then acts as a mix-in class.  You can also register</span>
<span class="sd">        unrelated concrete classes (even built-in classes) and unrelated</span>
<span class="sd">        ABCs as &#39;virtual subclasses&#39; -- these and their descendants will</span>
<span class="sd">        be considered subclasses of the registering ABC by the built-in</span>
<span class="sd">        issubclass() function, but the registering ABC won&#39;t show up in</span>
<span class="sd">        their MRO (Method Resolution Order) nor will method</span>
<span class="sd">        implementations defined by the registering ABC be callable (not</span>
<span class="sd">        even via super()).</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">def</span><span class="w"> </span><span class="fm">__new__</span><span class="p">(</span><span class="n">mcls</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="n">bases</span><span class="p">,</span> <span class="n">namespace</span><span class="p">,</span> <span class="o">/</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
            <span class="bp">cls</span> <span class="o">=</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__new__</span><span class="p">(</span><span class="n">mcls</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="n">bases</span><span class="p">,</span> <span class="n">namespace</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
            <span class="n">_abc_init</span><span class="p">(</span><span class="bp">cls</span><span class="p">)</span>
            <span class="k">return</span> <span class="bp">cls</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">register</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">subclass</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Register a virtual subclass of an ABC.</span>

<span class="sd">            Returns the subclass, to allow usage as a class decorator.</span>
<span class="sd">            &quot;&quot;&quot;</span>
            <span class="k">return</span> <span class="n">_abc_register</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">subclass</span><span class="p">)</span>

        <span class="k">def</span><span class="w"> </span><span class="fm">__instancecheck__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">instance</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Override for isinstance(instance, cls).&quot;&quot;&quot;</span>
            <span class="k">return</span> <span class="n">_abc_instancecheck</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">instance</span><span class="p">)</span>

        <span class="k">def</span><span class="w"> </span><span class="fm">__subclasscheck__</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">subclass</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Override for issubclass(subclass, cls).&quot;&quot;&quot;</span>
            <span class="k">return</span> <span class="n">_abc_subclasscheck</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">subclass</span><span class="p">)</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">_dump_registry</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Debug helper to print the ABC registry.&quot;&quot;&quot;</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Class: </span><span class="si">{</span><span class="bp">cls</span><span class="o">.</span><span class="vm">__module__</span><span class="si">}</span><span class="s2">.</span><span class="si">{</span><span class="bp">cls</span><span class="o">.</span><span class="vm">__qualname__</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">file</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Inv. counter: </span><span class="si">{</span><span class="n">get_cache_token</span><span class="p">()</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">file</span><span class="p">)</span>
            <span class="p">(</span><span class="n">_abc_registry</span><span class="p">,</span> <span class="n">_abc_cache</span><span class="p">,</span> <span class="n">_abc_negative_cache</span><span class="p">,</span>
             <span class="n">_abc_negative_cache_version</span><span class="p">)</span> <span class="o">=</span> <span class="n">_get_dump</span><span class="p">(</span><span class="bp">cls</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;_abc_registry: </span><span class="si">{</span><span class="n">_abc_registry</span><span class="si">!r}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">file</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;_abc_cache: </span><span class="si">{</span><span class="n">_abc_cache</span><span class="si">!r}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">file</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;_abc_negative_cache: </span><span class="si">{</span><span class="n">_abc_negative_cache</span><span class="si">!r}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">file</span><span class="o">=</span><span class="n">file</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;_abc_negative_cache_version: </span><span class="si">{</span><span class="n">_abc_negative_cache_version</span><span class="si">!r}</span><span class="s2">&quot;</span><span class="p">,</span>
                  <span class="n">file</span><span class="o">=</span><span class="n">file</span><span class="p">)</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">_abc_registry_clear</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Clear the registry (for debugging or testing).&quot;&quot;&quot;</span>
            <span class="n">_reset_registry</span><span class="p">(</span><span class="bp">cls</span><span class="p">)</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">_abc_caches_clear</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Clear the caches (for debugging or testing).&quot;&quot;&quot;</span>
            <span class="n">_reset_caches</span><span class="p">(</span><span class="bp">cls</span><span class="p">)</span>


<span class="k">def</span><span class="w"> </span><span class="nf">update_abstractmethods</span><span class="p">(</span><span class="bp">cls</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Recalculate the set of abstract methods of an abstract class.</span>

<span class="sd">    If a class has had one of its abstract methods implemented after the</span>
<span class="sd">    class was created, the method will not be considered implemented until</span>
<span class="sd">    this function is called. Alternatively, if a new abstract method has been</span>
<span class="sd">    added to the class, it will only be considered an abstract method of the</span>
<span class="sd">    class after this function is called.</span>

<span class="sd">    This function should be called before any use is made of the class,</span>
<span class="sd">    usually in class decorators that add methods to the subject class.</span>

<span class="sd">    Returns cls, to allow usage as a class decorator.</span>

<span class="sd">    If cls is not an instance of ABCMeta, does nothing.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="nb">hasattr</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="s1">&#39;__abstractmethods__&#39;</span><span class="p">):</span>
        <span class="c1"># We check for __abstractmethods__ here because cls might by a C</span>
        <span class="c1"># implementation or a python implementation (especially during</span>
        <span class="c1"># testing), and we want to handle both cases.</span>
        <span class="k">return</span> <span class="bp">cls</span>

    <span class="n">abstracts</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
    <span class="c1"># Check the existing abstract methods of the parents, keep only the ones</span>
    <span class="c1"># that are not implemented.</span>
    <span class="k">for</span> <span class="n">scls</span> <span class="ow">in</span> <span class="bp">cls</span><span class="o">.</span><span class="vm">__bases__</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">name</span> <span class="ow">in</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">scls</span><span class="p">,</span> <span class="s1">&#39;__abstractmethods__&#39;</span><span class="p">,</span> <span class="p">()):</span>
            <span class="n">value</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span>
            <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="s2">&quot;__isabstractmethod__&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">):</span>
                <span class="n">abstracts</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">name</span><span class="p">)</span>
    <span class="c1"># Also add any other newly added abstract methods.</span>
    <span class="k">for</span> <span class="n">name</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="bp">cls</span><span class="o">.</span><span class="vm">__dict__</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="s2">&quot;__isabstractmethod__&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">):</span>
            <span class="n">abstracts</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">name</span><span class="p">)</span>
    <span class="bp">cls</span><span class="o">.</span><span class="n">__abstractmethods__</span> <span class="o">=</span> <span class="nb">frozenset</span><span class="p">(</span><span class="n">abstracts</span><span class="p">)</span>
    <span class="k">return</span> <span class="bp">cls</span>


<span class="k">class</span><span class="w"> </span><span class="nc">ABC</span><span class="p">(</span><span class="n">metaclass</span><span class="o">=</span><span class="n">ABCMeta</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Helper class that provides a standard way to create an ABC using</span>
<span class="sd">    inheritance.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="vm">__slots__</span> <span class="o">=</span> <span class="p">()</span>
</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../index.html">Documentation overview</a><ul>
  <li><a href="index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2023, Jakob Runge.
      
      |
      Powered by <a href="https://www.sphinx-doc.org/">Sphinx 8.2.3</a>
      &amp; <a href="https://alabaster.readthedocs.io">Alabaster 0.7.16</a>
      
    </div>

    

    
  </body>
</html>