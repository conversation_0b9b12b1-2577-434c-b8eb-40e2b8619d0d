<!DOCTYPE html>

<html lang="en" data-content_root="../../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.graphs &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=db26dd79" />
    <link rel="stylesheet" type="text/css" href="../../_static/alabaster.css?v=19da42e6" />
    <script src="../../_static/documentation_options.js?v=625b3a9a"></script>
    <script src="../../_static/doctools.js?v=aa79a7b1"></script>
    <script src="../../_static/sphinx_highlight.js?v=4825356b"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
   
  <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
  

  
  

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.graphs</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite causal inference for time series.&quot;&quot;&quot;</span>

<span class="c1"># Author: Jakob Runge &lt;<EMAIL>&gt;</span>
<span class="c1">#</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">numpy</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">np</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">math</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">itertools</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">copy</span><span class="w"> </span><span class="kn">import</span> <span class="n">deepcopy</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">collections</span><span class="w"> </span><span class="kn">import</span> <span class="n">defaultdict</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tigramite.models</span><span class="w"> </span><span class="kn">import</span> <span class="n">Models</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">struct</span>

<div class="viewcode-block" id="Graphs">
<a class="viewcode-back" href="../../index.html#tigramite.graphs.Graphs">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">Graphs</span><span class="p">():</span>
<span class="w">    </span><span class="sa">r</span><span class="sd">&quot;&quot;&quot;Graph class.</span>

<span class="sd">    Methods for dealing with causal graphs. Various graph types are </span>
<span class="sd">    supported, also including hidden variables.</span>
<span class="sd">    </span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    graph : array of either shape [N, N], [N, N, tau_max+1], or [N, N, tau_max+1, tau_max+1]</span>
<span class="sd">        Different graph types are supported, see tutorial.</span>
<span class="sd">    graph_type : str</span>
<span class="sd">        Type of graph.</span>
<span class="sd">    tau_max : int, optional (default: 0)</span>
<span class="sd">        Maximum time lag of graph.</span>
<span class="sd">    hidden_variables : list of tuples</span>
<span class="sd">        Hidden variables in format [(i, -tau), ...]. The internal graph is </span>
<span class="sd">        constructed by a latent projection.</span>
<span class="sd">    verbosity : int, optional (default: 0)</span>
<span class="sd">        Level of verbosity.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">graph</span><span class="p">,</span>
                 <span class="n">graph_type</span><span class="p">,</span>
                 <span class="n">tau_max</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                 <span class="n">hidden_variables</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
        
        <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">=</span> <span class="n">verbosity</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">N</span> <span class="o">=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">=</span> <span class="n">tau_max</span>

        <span class="c1"># </span>
        <span class="c1"># Checks regarding graph type</span>
        <span class="c1">#</span>
        <span class="n">supported_graphs</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;dag&#39;</span><span class="p">,</span> 
                            <span class="s1">&#39;admg&#39;</span><span class="p">,</span>
                            <span class="s1">&#39;tsg_dag&#39;</span><span class="p">,</span>
                            <span class="s1">&#39;tsg_admg&#39;</span><span class="p">,</span>
                            <span class="s1">&#39;stationary_dag&#39;</span><span class="p">,</span>
                            <span class="s1">&#39;stationary_admg&#39;</span><span class="p">,</span>

                            <span class="c1"># &#39;mag&#39;,</span>
                            <span class="c1"># &#39;tsg_mag&#39;,</span>
                            <span class="c1"># &#39;stationary_mag&#39;,</span>
                            <span class="c1"># &#39;pag&#39;,</span>
                            <span class="c1"># &#39;tsg_pag&#39;,</span>
                            <span class="c1"># &#39;stationary_pag&#39;,</span>
                            <span class="p">]</span>
        <span class="k">if</span> <span class="n">graph_type</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">supported_graphs</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Only graph types </span><span class="si">%s</span><span class="s2"> supported!&quot;</span> <span class="o">%</span><span class="n">supported_graphs</span><span class="p">)</span>

        <span class="c1"># TODO?: check that masking aligns with hidden samples in variables</span>
        <span class="k">if</span> <span class="n">hidden_variables</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">hidden_variables</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="c1"># Only needed for later extension to MAG/PAGs</span>
        <span class="k">if</span> <span class="s1">&#39;pag&#39;</span> <span class="ow">in</span> <span class="n">graph_type</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">possible</span> <span class="o">=</span> <span class="kc">True</span> 
            <span class="bp">self</span><span class="o">.</span><span class="n">definite_status</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">possible</span> <span class="o">=</span> <span class="kc">False</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">definite_status</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="c1"># Not needed for now...</span>
        <span class="c1"># self.ignore_time_bounds = False</span>

        <span class="c1"># Construct internal graph from input graph depending on graph type</span>
        <span class="c1"># and hidden variables</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_construct_graph</span><span class="p">(</span><span class="n">graph</span><span class="o">=</span><span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="o">=</span><span class="n">graph_type</span><span class="p">,</span>
                              <span class="n">hidden_variables</span><span class="o">=</span><span class="n">hidden_variables</span><span class="p">)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_check_graph</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="p">)</span>


    <span class="k">def</span><span class="w"> </span><span class="nf">_construct_graph</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="p">,</span> <span class="n">hidden_variables</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Construct internal graph object based on input graph and hidden variables.</span>

<span class="sd">           Uses the latent projection operation.</span>
<span class="sd">        &quot;&quot;&quot;</span>


        <span class="k">if</span> <span class="n">graph_type</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;dag&#39;</span><span class="p">,</span> <span class="s1">&#39;admg&#39;</span><span class="p">]:</span> 
            <span class="k">if</span> <span class="n">graph</span><span class="o">.</span><span class="n">ndim</span> <span class="o">!=</span> <span class="mi">2</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;graph_type in [&#39;dag&#39;, &#39;admg&#39;] assumes graph.shape=(N, N).&quot;</span><span class="p">)</span>

            <span class="n">allowed_edges</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;--&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;--&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;-+&quot;</span><span class="p">,</span> <span class="s2">&quot;+-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">]</span> 
            <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isin</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">allowed_edges</span><span class="p">)</span> <span class="o">==</span> <span class="kc">False</span><span class="p">):</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Graph contains invalid graph edge. &quot;</span> <span class="o">+</span>
                                 <span class="s2">&quot;For graph_type = </span><span class="si">%s</span><span class="s2"> only </span><span class="si">%s</span><span class="s2"> are allowed.&quot;</span> <span class="o">%</span><span class="p">(</span><span class="n">graph_type</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">allowed_edges</span><span class="p">)))</span>

            <span class="c1"># Convert to shape [N, N, 1, 1] with dummy dimension</span>
            <span class="c1"># to process as tsg_dag or tsg_admg with potential hidden variables</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">graph</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">expand_dims</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="mi">3</span><span class="p">))</span>
            
            <span class="c1"># tau_max needed in _get_latent_projection_graph</span>
            <span class="c1"># self.tau_max = 0</span>

            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">hidden_variables</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">graph</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_latent_projection_graph</span><span class="p">()</span> <span class="c1"># stationary=False)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span> <span class="o">=</span> <span class="s2">&quot;tsg_admg&quot;</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># graph = self.graph</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span> <span class="o">=</span> <span class="s1">&#39;tsg_&#39;</span> <span class="o">+</span> <span class="n">graph_type</span>

        <span class="k">elif</span> <span class="n">graph_type</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;tsg_dag&#39;</span><span class="p">,</span> <span class="s1">&#39;tsg_admg&#39;</span><span class="p">]:</span>
            <span class="k">if</span> <span class="n">graph</span><span class="o">.</span><span class="n">ndim</span> <span class="o">!=</span> <span class="mi">4</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;tsg-graph_type assumes graph.shape=(N, N, tau_max+1, tau_max+1).&quot;</span><span class="p">)</span>

            <span class="n">allowed_edges</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;--&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;--&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;-+&quot;</span><span class="p">,</span> <span class="s2">&quot;+-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">]</span> 
            <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isin</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">allowed_edges</span><span class="p">)</span> <span class="o">==</span> <span class="kc">False</span><span class="p">):</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Graph contains invalid graph edge. &quot;</span> <span class="o">+</span>
                                 <span class="s2">&quot;For graph_type = </span><span class="si">%s</span><span class="s2"> only </span><span class="si">%s</span><span class="s2"> are allowed.&quot;</span> <span class="o">%</span><span class="p">(</span><span class="n">graph_type</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">allowed_edges</span><span class="p">)))</span>

            <span class="c1"># Then tau_max is implicitely derived from</span>
            <span class="c1"># the dimensions </span>
            <span class="bp">self</span><span class="o">.</span><span class="n">graph</span> <span class="o">=</span> <span class="n">graph</span>
            <span class="c1"># self.tau_max = graph.shape[2] - 1</span>

            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">hidden_variables</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">graph</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_latent_projection_graph</span><span class="p">()</span> <span class="c1">#, stationary=False)</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span> <span class="o">=</span> <span class="s2">&quot;tsg_admg&quot;</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span> <span class="o">=</span> <span class="n">graph_type</span>   

        <span class="k">elif</span> <span class="n">graph_type</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;stationary_dag&#39;</span><span class="p">,</span> <span class="s1">&#39;stationary_admg&#39;</span><span class="p">]:</span>
            <span class="c1"># Currently only stationary_dag without hidden variables is supported</span>
            <span class="k">if</span> <span class="n">graph</span><span class="o">.</span><span class="n">ndim</span> <span class="o">!=</span> <span class="mi">3</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;stationary graph_type assumes graph.shape=(N, N, tau_max+1).&quot;</span><span class="p">)</span>
            
            <span class="n">allowed_edges</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;--&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;--&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;-+&quot;</span><span class="p">,</span> <span class="s2">&quot;+-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">]</span> 
            <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isin</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">allowed_edges</span><span class="p">)</span> <span class="o">==</span> <span class="kc">False</span><span class="p">):</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Graph contains invalid graph edge. &quot;</span> <span class="o">+</span>
                                 <span class="s2">&quot;For graph_type = </span><span class="si">%s</span><span class="s2"> only </span><span class="si">%s</span><span class="s2"> are allowed.&quot;</span> <span class="o">%</span><span class="p">(</span><span class="n">graph_type</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">allowed_edges</span><span class="p">)))</span>

            <span class="c1"># # TODO: remove if theory for stationary ADMGs is clear</span>
            <span class="c1"># if graph_type == &#39;stationary_dag&#39; and len(hidden_variables) &gt; 0:</span>
            <span class="c1">#     raise ValueError(&quot;Hidden variables currently not supported for &quot;</span>
            <span class="c1">#                      &quot;stationary_dag.&quot;)</span>

            <span class="c1"># For a stationary DAG without hidden variables it&#39;s sufficient to consider</span>
            <span class="c1"># a tau_max that includes the parents of X, Y, M, and S. A conservative</span>
            <span class="c1"># estimate thereof is simply the lag-dimension of the stationary DAG plus</span>
            <span class="c1"># the maximum lag of XYS.</span>
            <span class="c1"># statgraph_tau_max = graph.shape[2] - 1</span>
            <span class="c1"># maxlag_XYS = 0</span>
            <span class="c1"># for varlag in self.X.union(self.Y).union(self.S):</span>
            <span class="c1">#     maxlag_XYS = max(maxlag_XYS, abs(varlag[1]))</span>

            <span class="c1"># self.tau_max = maxlag_XYS + statgraph_tau_max</span>

            <span class="n">stat_graph</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="n">graph</span><span class="p">)</span>

            <span class="c1">#########################################		</span>
            <span class="c1"># Use this tau_max and construct ADMG by assuming paths of</span>
            <span class="c1"># maximal lag 10*tau_max... TO BE REVISED!</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">graph</span> <span class="o">=</span> <span class="n">graph</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">graph</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_latent_projection_graph</span><span class="p">(</span><span class="n">stationary</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span> <span class="o">=</span> <span class="s2">&quot;tsg_admg&quot;</span>
            <span class="c1">#########################################</span>

            <span class="c1"># Also create stationary graph extended to tau_max</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">stationary_graph</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;&lt;U3&#39;</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">stationary_graph</span><span class="p">[:,</span> <span class="p">:,</span> <span class="p">:</span><span class="n">stat_graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">2</span><span class="p">]]</span> <span class="o">=</span> <span class="n">stat_graph</span>

            <span class="c1"># allowed_edges = [&quot;--&gt;&quot;, &quot;&lt;--&quot;]</span>

            <span class="c1"># # Construct tsg_graph</span>
            <span class="c1"># graph = np.zeros((self.N, self.N, self.tau_max + 1, self.tau_max + 1), dtype=&#39;&lt;U3&#39;)</span>
            <span class="c1"># graph[:] = &quot;&quot;</span>
            <span class="c1"># for (i, j) in itertools.product(range(self.N), range(self.N)):</span>
            <span class="c1">#     for jt, tauj in enumerate(range(0, self.tau_max + 1)):</span>
            <span class="c1">#         for it, taui in enumerate(range(tauj, self.tau_max + 1)):</span>
            <span class="c1">#             tau = abs(taui - tauj)</span>
            <span class="c1">#             if tau == 0 and j == i:</span>
            <span class="c1">#                 continue</span>
            <span class="c1">#             if tau &gt; statgraph_tau_max:</span>
            <span class="c1">#                 continue                        </span>

            <span class="c1">#             # if tau == 0:</span>
            <span class="c1">#             #     if stat_graph[i, j, tau] == &#39;--&gt;&#39;:</span>
            <span class="c1">#             #         graph[i, j, taui, tauj] = &quot;--&gt;&quot; </span>
            <span class="c1">#             #         graph[j, i, tauj, taui] = &quot;&lt;--&quot; </span>

            <span class="c1">#             #     # elif stat_graph[i, j, tau] == &#39;&lt;--&#39;:</span>
            <span class="c1">#             #     #     graph[i, j, taui, tauj] = &quot;&lt;--&quot;</span>
            <span class="c1">#             #     #     graph[j, i, tauj, taui] = &quot;--&gt;&quot; </span>
            <span class="c1">#             # else:</span>
            <span class="c1">#             if stat_graph[i, j, tau] == &#39;--&gt;&#39;:</span>
            <span class="c1">#                 graph[i, j, taui, tauj] = &quot;--&gt;&quot; </span>
            <span class="c1">#                 graph[j, i, tauj, taui] = &quot;&lt;--&quot; </span>
            <span class="c1">#             elif stat_graph[i, j, tau] == &#39;&lt;--&#39;:</span>
            <span class="c1">#                 pass</span>
            <span class="c1">#             elif stat_graph[i, j, tau] == &#39;&#39;:</span>
            <span class="c1">#                 pass</span>
            <span class="c1">#             else:</span>
            <span class="c1">#                 edge = stat_graph[i, j, tau]</span>
            <span class="c1">#                 raise ValueError(&quot;Invalid graph edge %s. &quot; %(edge) +</span>
            <span class="c1">#                      &quot;For graph_type = %s only %s are allowed.&quot; %(graph_type, str(allowed_edges)))</span>
      
            <span class="c1">#             # elif stat_graph[i, j, tau] == &#39;&lt;--&#39;:</span>
            <span class="c1">#             #     graph[i, j, taui, tauj] = &quot;&lt;--&quot;</span>
            <span class="c1">#             #     graph[j, i, tauj, taui] = &quot;--&gt;&quot; </span>

            <span class="c1"># self.graph_type = &#39;tsg_dag&#39;</span>
            <span class="c1"># self.graph = graph</span>


        <span class="c1"># return (graph, graph_type, self.tau_max, hidden_variables)</span>

            <span class="c1"># max_lag = self._get_maximum_possible_lag(XYZ=list(X.union(Y).union(S)), graph=graph)</span>

            <span class="c1"># stat_mediators = self._get_mediators_stationary_graph(start=X, end=Y, max_lag=max_lag)</span>
            <span class="c1"># self.tau_max = self._get_maximum_possible_lag(XYZ=list(X.union(Y).union(S).union(stat_mediators)), graph=graph)</span>
            <span class="c1"># self.tau_max = graph_taumax</span>
            <span class="c1"># for varlag in X.union(Y).union(S):</span>
            <span class="c1">#     self.tau_max = max(self.tau_max, abs(varlag[1]))</span>

            <span class="c1"># if verbosity &gt; 0:</span>
            <span class="c1">#     print(&quot;Setting tau_max = &quot;, self.tau_max)</span>

            <span class="c1"># if tau_max is None:</span>
            <span class="c1">#     self.tau_max = graph_taumax</span>
            <span class="c1">#     for varlag in X.union(Y).union(S):</span>
            <span class="c1">#         self.tau_max = max(self.tau_max, abs(varlag[1]))</span>

            <span class="c1">#     if verbosity &gt; 0:</span>
            <span class="c1">#         print(&quot;Setting tau_max = &quot;, self.tau_max)</span>
            <span class="c1"># else:</span>
                <span class="c1"># self.tau_max = graph_taumax</span>
                <span class="c1"># # Repeat hidden variable pattern </span>
                <span class="c1"># # if larger tau_max is given</span>
                <span class="c1"># if self.tau_max &gt; graph_taumax:</span>
                <span class="c1">#     for lag in range(graph_taumax + 1, self.tau_max + 1):</span>
                <span class="c1">#         for j in range(self.N):</span>
                <span class="c1">#             if (j, -(lag % (graph_taumax+1))) in self.hidden_variables:</span>
                <span class="c1">#                 self.hidden_variables.add((j, -lag))</span>
            <span class="c1"># print(self.hidden_variables)</span>

        <span class="c1">#     self.graph = self._get_latent_projection_graph(self.graph, stationary=True)</span>
        <span class="c1">#     self.graph_type = &quot;tsg_admg&quot;</span>
        <span class="c1"># else:</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_check_graph</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Checks that graph contains no invalid entries/structure.</span>

<span class="sd">        Assumes graph.shape = (N, N, tau_max+1, tau_max+1)</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">allowed_edges</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;--&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;--&quot;</span><span class="p">]</span>
        <span class="k">if</span> <span class="s1">&#39;admg&#39;</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span><span class="p">:</span>
            <span class="n">allowed_edges</span> <span class="o">+=</span> <span class="p">[</span><span class="s2">&quot;&lt;-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;-+&quot;</span><span class="p">,</span> <span class="s2">&quot;+-&gt;&quot;</span><span class="p">]</span>
        <span class="k">elif</span> <span class="s1">&#39;mag&#39;</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span><span class="p">:</span>
            <span class="n">allowed_edges</span> <span class="o">+=</span> <span class="p">[</span><span class="s2">&quot;&lt;-&gt;&quot;</span><span class="p">]</span>
        <span class="k">elif</span> <span class="s1">&#39;pag&#39;</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span><span class="p">:</span>
            <span class="n">allowed_edges</span> <span class="o">+=</span> <span class="p">[</span><span class="s2">&quot;&lt;-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;o-o&quot;</span><span class="p">,</span> <span class="s2">&quot;o-&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;-o&quot;</span><span class="p">]</span>                         <span class="c1"># &quot;o--&quot;,</span>
                        <span class="c1"># &quot;--o&quot;,</span>
                        <span class="c1"># &quot;x-o&quot;,</span>
                        <span class="c1"># &quot;o-x&quot;,</span>
                        <span class="c1"># &quot;x--&quot;,</span>
                        <span class="c1"># &quot;--x&quot;,</span>
                        <span class="c1"># &quot;x-&gt;&quot;,</span>
                        <span class="c1"># &quot;&lt;-x&quot;,</span>
                        <span class="c1"># &quot;x-x&quot;,</span>
                    <span class="c1"># ]</span>

        <span class="n">graph_dict</span> <span class="o">=</span> <span class="n">defaultdict</span><span class="p">(</span><span class="nb">list</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">taui</span><span class="p">,</span> <span class="n">tauj</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">graph</span><span class="p">)):</span>
            <span class="n">edge</span> <span class="o">=</span> <span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">taui</span><span class="p">,</span> <span class="n">tauj</span><span class="p">]</span>
            <span class="c1"># print((i, -taui), edge, (j, -tauj), graph[j, i, tauj, taui])</span>
            <span class="k">if</span> <span class="n">edge</span> <span class="o">!=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_reverse_link</span><span class="p">(</span><span class="n">graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tauj</span><span class="p">,</span> <span class="n">taui</span><span class="p">]):</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span>
                    <span class="s2">&quot;graph needs to have consistent edges (eg&quot;</span>
                    <span class="s2">&quot; graph[i,j,taui,tauj]=&#39;--&gt;&#39; requires graph[j,i,tauj,taui]=&#39;&lt;--&#39;)&quot;</span>
                <span class="p">)</span>

            <span class="k">if</span> <span class="n">edge</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">allowed_edges</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Invalid graph edge </span><span class="si">%s</span><span class="s2">. &quot;</span> <span class="o">%</span><span class="p">(</span><span class="n">edge</span><span class="p">)</span> <span class="o">+</span>
                                 <span class="s2">&quot;For graph_type = </span><span class="si">%s</span><span class="s2"> only </span><span class="si">%s</span><span class="s2"> are allowed.&quot;</span> <span class="o">%</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">graph_type</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">allowed_edges</span><span class="p">)))</span>

            <span class="k">if</span> <span class="n">edge</span> <span class="o">==</span> <span class="s2">&quot;--&gt;&quot;</span> <span class="ow">or</span> <span class="n">edge</span> <span class="o">==</span> <span class="s2">&quot;+-&gt;&quot;</span><span class="p">:</span>
                <span class="c1"># Map to (i,-taui, j, tauj) graph</span>
                <span class="n">indexi</span> <span class="o">=</span> <span class="n">i</span> <span class="o">*</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">+</span> <span class="n">taui</span>
                <span class="n">indexj</span> <span class="o">=</span> <span class="n">j</span> <span class="o">*</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">+</span> <span class="n">tauj</span>

                <span class="n">graph_dict</span><span class="p">[</span><span class="n">indexj</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">indexi</span><span class="p">)</span>

        <span class="c1"># Check for cycles</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_cyclic</span><span class="p">(</span><span class="n">graph_dict</span><span class="p">):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;graph is cyclic.&quot;</span><span class="p">)</span>

        <span class="c1"># if MAG: check for almost cycles</span>
        <span class="c1"># if PAG???</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_check_cyclic</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph_dict</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Return True if the graph_dict has a cycle.</span>

<span class="sd">        graph_dict must be represented as a dictionary mapping vertices to</span>
<span class="sd">        iterables of neighbouring vertices. For example:</span>

<span class="sd">        &gt;&gt;&gt; cyclic({1: (2,), 2: (3,), 3: (1,)})</span>
<span class="sd">        True</span>
<span class="sd">        &gt;&gt;&gt; cyclic({1: (2,), 2: (3,), 3: (4,)})</span>
<span class="sd">        False</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">path</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
        <span class="n">visited</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">visit</span><span class="p">(</span><span class="n">vertex</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">vertex</span> <span class="ow">in</span> <span class="n">visited</span><span class="p">:</span>
                <span class="k">return</span> <span class="kc">False</span>
            <span class="n">visited</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">vertex</span><span class="p">)</span>
            <span class="n">path</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">vertex</span><span class="p">)</span>
            <span class="k">for</span> <span class="n">neighbour</span> <span class="ow">in</span> <span class="n">graph_dict</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">vertex</span><span class="p">,</span> <span class="p">()):</span>
                <span class="k">if</span> <span class="n">neighbour</span> <span class="ow">in</span> <span class="n">path</span> <span class="ow">or</span> <span class="n">visit</span><span class="p">(</span><span class="n">neighbour</span><span class="p">):</span>
                    <span class="k">return</span> <span class="kc">True</span>
            <span class="n">path</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="n">vertex</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">False</span>

        <span class="k">return</span> <span class="nb">any</span><span class="p">(</span><span class="n">visit</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="k">for</span> <span class="n">v</span> <span class="ow">in</span> <span class="n">graph_dict</span><span class="p">)</span>

<div class="viewcode-block" id="Graphs.get_mediators">
<a class="viewcode-back" href="../../index.html#tigramite.graphs.Graphs.get_mediators">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_mediators</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">start</span><span class="p">,</span> <span class="n">end</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns mediator variables on proper causal paths.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        start : set</span>
<span class="sd">            Set of start nodes.</span>
<span class="sd">        end : set</span>
<span class="sd">            Set of end nodes.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        mediators : set</span>
<span class="sd">            Mediators on causal paths from start to end.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">des_X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_descendants</span><span class="p">(</span><span class="n">start</span><span class="p">)</span>

        <span class="n">mediators</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>

        <span class="c1"># Walk along proper causal paths backwards from Y to X</span>
        <span class="c1"># potential_mediators = set()</span>
        <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="n">end</span><span class="p">:</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">y</span> 
            <span class="n">this_level</span> <span class="o">=</span> <span class="p">[</span><span class="n">y</span><span class="p">]</span>
            <span class="k">while</span> <span class="nb">len</span><span class="p">(</span><span class="n">this_level</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">next_level</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="n">this_level</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">parent</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_parents</span><span class="p">(</span><span class="n">varlag</span><span class="p">):</span>
                        <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">parent</span>
                        <span class="c1"># print(varlag, parent, des_X)</span>
                        <span class="k">if</span> <span class="p">(</span><span class="n">parent</span> <span class="ow">in</span> <span class="n">des_X</span>
                            <span class="ow">and</span> <span class="n">parent</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">mediators</span>
                            <span class="c1"># and parent not in potential_mediators</span>
                            <span class="ow">and</span> <span class="n">parent</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">start</span>
                            <span class="ow">and</span> <span class="n">parent</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">end</span>
                            <span class="ow">and</span> <span class="p">(</span><span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">)):</span> <span class="c1"># or self.ignore_time_bounds)):</span>
                            <span class="n">mediators</span> <span class="o">=</span> <span class="n">mediators</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">parent</span><span class="p">]))</span>
                            <span class="n">next_level</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">parent</span><span class="p">)</span>
                            
                <span class="n">this_level</span> <span class="o">=</span> <span class="n">next_level</span>  

        <span class="k">return</span> <span class="n">mediators</span></div>


    <span class="k">def</span><span class="w"> </span><span class="nf">_get_mediators_stationary_graph</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">start</span><span class="p">,</span> <span class="n">end</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns mediator variables on proper causal paths</span>
<span class="sd">           from X to Y in a stationary graph.&quot;&quot;&quot;</span>

        <span class="n">des_X</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_descendants_stationary_graph</span><span class="p">(</span><span class="n">start</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">)</span>

        <span class="n">mediators</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>

        <span class="c1"># Walk along proper causal paths backwards from Y to X</span>
        <span class="n">potential_mediators</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
        <span class="k">for</span> <span class="n">y</span> <span class="ow">in</span> <span class="n">end</span><span class="p">:</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">y</span> 
            <span class="n">this_level</span> <span class="o">=</span> <span class="p">[</span><span class="n">y</span><span class="p">]</span>
            <span class="k">while</span> <span class="nb">len</span><span class="p">(</span><span class="n">this_level</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">next_level</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="n">this_level</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">_</span><span class="p">,</span> <span class="n">parent</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_adjacents_stationary_graph</span><span class="p">(</span><span class="n">graph</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="p">,</span> 
                                <span class="n">node</span><span class="o">=</span><span class="n">varlag</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;&lt;*-&quot;</span><span class="p">,</span> <span class="s2">&quot;&lt;*+&quot;</span><span class="p">],</span> <span class="n">max_lag</span><span class="o">=</span><span class="n">max_lag</span><span class="p">,</span> <span class="n">exclude</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
                        <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">parent</span>
                        <span class="k">if</span> <span class="p">(</span><span class="n">parent</span> <span class="ow">in</span> <span class="n">des_X</span>
                            <span class="ow">and</span> <span class="n">parent</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">mediators</span>
                            <span class="c1"># and parent not in potential_mediators</span>
                            <span class="ow">and</span> <span class="n">parent</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">start</span>
                            <span class="ow">and</span> <span class="n">parent</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">end</span>
                            <span class="c1"># and (-self.tau_max &lt;= tau &lt;= 0 or self.ignore_time_bounds)</span>
                            <span class="p">):</span>
                            <span class="n">mediators</span> <span class="o">=</span> <span class="n">mediators</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">parent</span><span class="p">]))</span>
                            <span class="n">next_level</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">parent</span><span class="p">)</span>
                            
                <span class="n">this_level</span> <span class="o">=</span> <span class="n">next_level</span>  

        <span class="k">return</span> <span class="n">mediators</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_reverse_link</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">link</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Reverse a given link, taking care to replace &gt; with &lt; and vice versa.&quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">link</span> <span class="o">==</span> <span class="s2">&quot;&quot;</span><span class="p">:</span>
            <span class="k">return</span> <span class="s2">&quot;&quot;</span>

        <span class="k">if</span> <span class="n">link</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;&gt;&quot;</span><span class="p">:</span>
            <span class="n">left_mark</span> <span class="o">=</span> <span class="s2">&quot;&lt;&quot;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">left_mark</span> <span class="o">=</span> <span class="n">link</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>

        <span class="k">if</span> <span class="n">link</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;&lt;&quot;</span><span class="p">:</span>
            <span class="n">right_mark</span> <span class="o">=</span> <span class="s2">&quot;&gt;&quot;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">right_mark</span> <span class="o">=</span> <span class="n">link</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">left_mark</span> <span class="o">+</span> <span class="n">link</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">+</span> <span class="n">right_mark</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_match_link</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">pattern</span><span class="p">,</span> <span class="n">link</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Matches pattern including wildcards with link.</span>
<span class="sd">           </span>
<span class="sd">           In an ADMG we have edge types [&quot;--&gt;&quot;, &quot;&lt;--&quot;, &quot;&lt;-&gt;&quot;, &quot;+-&gt;&quot;, &quot;&lt;-+&quot;].</span>
<span class="sd">           Here +-&gt; corresponds to having both &quot;--&gt;&quot; and &quot;&lt;-&gt;&quot;.</span>

<span class="sd">           In a MAG we have edge types   [&quot;--&gt;&quot;, &quot;&lt;--&quot;, &quot;&lt;-&gt;&quot;, &quot;---&quot;].</span>
<span class="sd">        &quot;&quot;&quot;</span>
        
        <span class="k">if</span> <span class="n">pattern</span> <span class="o">==</span> <span class="s1">&#39;&#39;</span> <span class="ow">or</span> <span class="n">link</span> <span class="o">==</span> <span class="s1">&#39;&#39;</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">True</span> <span class="k">if</span> <span class="n">pattern</span> <span class="o">==</span> <span class="n">link</span> <span class="k">else</span> <span class="kc">False</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">left_mark</span><span class="p">,</span> <span class="n">middle_mark</span><span class="p">,</span> <span class="n">right_mark</span> <span class="o">=</span> <span class="n">pattern</span>
            <span class="k">if</span> <span class="n">left_mark</span> <span class="o">!=</span> <span class="s1">&#39;*&#39;</span><span class="p">:</span>
                <span class="c1"># if link[0] != &#39;+&#39;:</span>
                    <span class="k">if</span> <span class="n">link</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">!=</span> <span class="n">left_mark</span><span class="p">:</span> <span class="k">return</span> <span class="kc">False</span>

            <span class="k">if</span> <span class="n">right_mark</span> <span class="o">!=</span> <span class="s1">&#39;*&#39;</span><span class="p">:</span>
                <span class="c1"># if link[2] != &#39;+&#39;:</span>
                    <span class="k">if</span> <span class="n">link</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">!=</span> <span class="n">right_mark</span><span class="p">:</span> <span class="k">return</span> <span class="kc">False</span> 
            
            <span class="k">if</span> <span class="n">middle_mark</span> <span class="o">!=</span> <span class="s1">&#39;*&#39;</span> <span class="ow">and</span> <span class="n">link</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">!=</span> <span class="n">middle_mark</span><span class="p">:</span> <span class="k">return</span> <span class="kc">False</span>    
                       
            <span class="k">return</span> <span class="kc">True</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_find_adj</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">node</span><span class="p">,</span> <span class="n">patterns</span><span class="p">,</span> <span class="n">exclude</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">return_link</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Find adjacencies of node that match given patterns.&quot;&quot;&quot;</span>
        
        <span class="n">graph</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph</span>

        <span class="k">if</span> <span class="n">exclude</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">exclude</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="c1">#     exclude = self.hidden_variables</span>
        <span class="c1"># else:</span>
        <span class="c1">#     exclude = set(exclude).union(self.hidden_variables)</span>

        <span class="c1"># Setup</span>
        <span class="n">i</span><span class="p">,</span> <span class="n">lag_i</span> <span class="o">=</span> <span class="n">node</span>
        <span class="n">lag_i</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">lag_i</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">exclude</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span> <span class="n">exclude</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="nb">type</span><span class="p">(</span><span class="n">patterns</span><span class="p">)</span> <span class="o">==</span> <span class="nb">str</span><span class="p">:</span>
            <span class="n">patterns</span> <span class="o">=</span> <span class="p">[</span><span class="n">patterns</span><span class="p">]</span>

        <span class="c1"># Init</span>
        <span class="n">adj</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="c1"># Find adjacencies going forward/contemp</span>
        <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">lag_ik</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,:,</span><span class="n">lag_i</span><span class="p">,:])):</span>
            <span class="c1"># print((k, lag_ik), graph[i,k,lag_i,lag_ik]) </span>
            <span class="c1"># matches = [self._match_link(patt, graph[i,k,lag_i,lag_ik]) for patt in patterns]</span>
            <span class="c1"># if np.any(matches):</span>
            <span class="k">for</span> <span class="n">patt</span> <span class="ow">in</span> <span class="n">patterns</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="n">patt</span><span class="p">,</span> <span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span><span class="n">k</span><span class="p">,</span><span class="n">lag_i</span><span class="p">,</span><span class="n">lag_ik</span><span class="p">]):</span>
                    <span class="n">match</span> <span class="o">=</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="o">-</span><span class="n">lag_ik</span><span class="p">)</span>
                    <span class="k">if</span> <span class="n">match</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">exclude</span><span class="p">:</span>
                        <span class="k">if</span> <span class="n">return_link</span><span class="p">:</span>
                            <span class="n">adj</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span><span class="n">k</span><span class="p">,</span><span class="n">lag_i</span><span class="p">,</span><span class="n">lag_ik</span><span class="p">],</span> <span class="n">match</span><span class="p">))</span>
                        <span class="k">else</span><span class="p">:</span>
                            <span class="n">adj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">match</span><span class="p">)</span>
                    <span class="k">break</span>

        
        <span class="c1"># Find adjacencies going backward/contemp</span>
        <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">lag_ki</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">graph</span><span class="p">[:,</span><span class="n">i</span><span class="p">,:,</span><span class="n">lag_i</span><span class="p">])):</span>  
            <span class="c1"># print((k, lag_ki), graph[k,i,lag_ki,lag_i]) </span>
            <span class="c1"># matches = [self._match_link(self._reverse_link(patt), graph[k,i,lag_ki,lag_i]) for patt in patterns]</span>
            <span class="c1"># if np.any(matches):</span>
            <span class="k">for</span> <span class="n">patt</span> <span class="ow">in</span> <span class="n">patterns</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_reverse_link</span><span class="p">(</span><span class="n">patt</span><span class="p">),</span> <span class="n">graph</span><span class="p">[</span><span class="n">k</span><span class="p">,</span><span class="n">i</span><span class="p">,</span><span class="n">lag_ki</span><span class="p">,</span><span class="n">lag_i</span><span class="p">]):</span>
                    <span class="n">match</span> <span class="o">=</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="o">-</span><span class="n">lag_ki</span><span class="p">)</span>
                    <span class="k">if</span> <span class="n">match</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">exclude</span><span class="p">:</span>
                        <span class="k">if</span> <span class="n">return_link</span><span class="p">:</span>
                            <span class="n">adj</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">_reverse_link</span><span class="p">(</span><span class="n">graph</span><span class="p">[</span><span class="n">k</span><span class="p">,</span><span class="n">i</span><span class="p">,</span><span class="n">lag_ki</span><span class="p">,</span><span class="n">lag_i</span><span class="p">]),</span> <span class="n">match</span><span class="p">))</span>
                        <span class="k">else</span><span class="p">:</span>
                            <span class="n">adj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">match</span><span class="p">)</span>
                    <span class="k">break</span>
     
        <span class="n">adj</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">adj</span><span class="p">))</span>
        <span class="k">return</span> <span class="n">adj</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_is_match</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">nodei</span><span class="p">,</span> <span class="n">nodej</span><span class="p">,</span> <span class="n">pattern_ij</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check whether the link between X and Y agrees with pattern.&quot;&quot;&quot;</span>

        <span class="n">graph</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">graph</span>

        <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">lag_i</span><span class="p">)</span> <span class="o">=</span> <span class="n">nodei</span>
        <span class="p">(</span><span class="n">j</span><span class="p">,</span> <span class="n">lag_j</span><span class="p">)</span> <span class="o">=</span> <span class="n">nodej</span>
        <span class="n">tauij</span> <span class="o">=</span> <span class="n">lag_j</span> <span class="o">-</span> <span class="n">lag_i</span>
        <span class="k">if</span> <span class="nb">abs</span><span class="p">(</span><span class="n">tauij</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">2</span><span class="p">]:</span>
            <span class="k">return</span> <span class="kc">False</span>
        <span class="k">return</span> <span class="p">((</span><span class="n">tauij</span> <span class="o">&gt;=</span> <span class="mi">0</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="n">pattern_ij</span><span class="p">,</span> <span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">tauij</span><span class="p">]))</span> <span class="ow">or</span>
               <span class="p">(</span><span class="n">tauij</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_reverse_link</span><span class="p">(</span><span class="n">pattern_ij</span><span class="p">),</span> <span class="n">graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">tauij</span><span class="p">)])))</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_children</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">varlag</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns set of children (varlag --&gt; ...) for (lagged) varlag.&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">possible</span><span class="p">:</span>
            <span class="n">patterns</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;-*&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;o*o&#39;</span><span class="p">,</span> <span class="s1">&#39;o*&gt;&#39;</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">patterns</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;-*&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;+*&gt;&#39;</span><span class="p">]</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_find_adj</span><span class="p">(</span><span class="n">node</span><span class="o">=</span><span class="n">varlag</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="n">patterns</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_parents</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">varlag</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns set of parents (varlag &lt;-- ...) for (lagged) varlag.&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">possible</span><span class="p">:</span>
            <span class="n">patterns</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;&lt;*-&#39;</span><span class="p">,</span> <span class="s1">&#39;o*o&#39;</span><span class="p">,</span> <span class="s1">&#39;&lt;*o&#39;</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">patterns</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;&lt;*-&#39;</span><span class="p">,</span> <span class="s1">&#39;&lt;*+&#39;</span><span class="p">]</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_find_adj</span><span class="p">(</span><span class="n">node</span><span class="o">=</span><span class="n">varlag</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="n">patterns</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_spouses</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">varlag</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns set of spouses (varlag &lt;-&gt; ...)  for (lagged) varlag.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_find_adj</span><span class="p">(</span><span class="n">node</span><span class="o">=</span><span class="n">varlag</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;&lt;*&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;+*&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;&lt;*+&#39;</span><span class="p">])</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_neighbors</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">varlag</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns set of neighbors (varlag --- ...) for (lagged) varlag.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_find_adj</span><span class="p">(</span><span class="n">node</span><span class="o">=</span><span class="n">varlag</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;-*-&#39;</span><span class="p">])</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_ancestors</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">W</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get ancestors of nodes in W up to time tau_max.</span>
<span class="sd">        </span>
<span class="sd">        Includes the nodes themselves.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">ancestors</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">W</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">W</span><span class="p">:</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">w</span> 
            <span class="n">this_level</span> <span class="o">=</span> <span class="p">[</span><span class="n">w</span><span class="p">]</span>
            <span class="k">while</span> <span class="nb">len</span><span class="p">(</span><span class="n">this_level</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">next_level</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="n">this_level</span><span class="p">:</span>

                    <span class="k">for</span> <span class="n">par</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_parents</span><span class="p">(</span><span class="n">varlag</span><span class="p">):</span>
                        <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">par</span>
                        <span class="k">if</span> <span class="n">par</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">ancestors</span> <span class="ow">and</span> <span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
                            <span class="n">ancestors</span> <span class="o">=</span> <span class="n">ancestors</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">par</span><span class="p">]))</span>
                            <span class="n">next_level</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">par</span><span class="p">)</span>

                <span class="n">this_level</span> <span class="o">=</span> <span class="n">next_level</span>       

        <span class="k">return</span> <span class="n">ancestors</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_all_parents</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">W</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get parents of nodes in W up to time tau_max.</span>
<span class="sd">        </span>
<span class="sd">        Includes the nodes themselves.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">parents</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">W</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">W</span><span class="p">:</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">w</span> 
            <span class="k">for</span> <span class="n">par</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_parents</span><span class="p">(</span><span class="n">w</span><span class="p">):</span>
                <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">par</span>
                <span class="k">if</span> <span class="n">par</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">parents</span> <span class="ow">and</span> <span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="n">parents</span> <span class="o">=</span> <span class="n">parents</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">par</span><span class="p">]))</span>

        <span class="k">return</span> <span class="n">parents</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_all_spouses</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">W</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get spouses of nodes in W up to time tau_max.</span>
<span class="sd">        </span>
<span class="sd">        Includes the nodes themselves.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">spouses</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">W</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">W</span><span class="p">:</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">w</span> 
            <span class="k">for</span> <span class="n">spouse</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_spouses</span><span class="p">(</span><span class="n">w</span><span class="p">):</span>
                <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">spouse</span>
                <span class="k">if</span> <span class="n">spouse</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">spouses</span> <span class="ow">and</span> <span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="n">spouses</span> <span class="o">=</span> <span class="n">spouses</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">spouse</span><span class="p">]))</span>

        <span class="k">return</span> <span class="n">spouses</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_descendants_stationary_graph</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">W</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get descendants of nodes in W up to time t in stationary graph.</span>
<span class="sd">        </span>
<span class="sd">        Includes the nodes themselves.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">descendants</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">W</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">W</span><span class="p">:</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">w</span> 
            <span class="n">this_level</span> <span class="o">=</span> <span class="p">[</span><span class="n">w</span><span class="p">]</span>
            <span class="k">while</span> <span class="nb">len</span><span class="p">(</span><span class="n">this_level</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">next_level</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="n">this_level</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">_</span><span class="p">,</span> <span class="n">child</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_adjacents_stationary_graph</span><span class="p">(</span><span class="n">graph</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="p">,</span> 
                                <span class="n">node</span><span class="o">=</span><span class="n">varlag</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;-*&gt;&quot;</span><span class="p">,</span> <span class="s2">&quot;-*+&quot;</span><span class="p">],</span> <span class="n">max_lag</span><span class="o">=</span><span class="n">max_lag</span><span class="p">,</span> <span class="n">exclude</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
                        <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">child</span>
                        <span class="k">if</span> <span class="p">(</span><span class="n">child</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">descendants</span> 
                            <span class="c1"># and (-self.tau_max &lt;= tau &lt;= 0 or self.ignore_time_bounds)</span>
                            <span class="p">):</span>
                            <span class="n">descendants</span> <span class="o">=</span> <span class="n">descendants</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">child</span><span class="p">]))</span>
                            <span class="n">next_level</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">child</span><span class="p">)</span>

                <span class="n">this_level</span> <span class="o">=</span> <span class="n">next_level</span>       

        <span class="k">return</span> <span class="n">descendants</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_descendants</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">W</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get descendants of nodes in W up to time t.</span>
<span class="sd">        </span>
<span class="sd">        Includes the nodes themselves.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">descendants</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">W</span><span class="p">)</span>

        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">W</span><span class="p">:</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">w</span> 
            <span class="n">this_level</span> <span class="o">=</span> <span class="p">[</span><span class="n">w</span><span class="p">]</span>
            <span class="k">while</span> <span class="nb">len</span><span class="p">(</span><span class="n">this_level</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">next_level</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="n">this_level</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_children</span><span class="p">(</span><span class="n">varlag</span><span class="p">):</span>
                        <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">child</span>
                        <span class="k">if</span> <span class="p">(</span><span class="n">child</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">descendants</span> 
                            <span class="ow">and</span> <span class="p">(</span><span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">)):</span> <span class="c1"># or self.ignore_time_bounds)):</span>
                            <span class="n">descendants</span> <span class="o">=</span> <span class="n">descendants</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">child</span><span class="p">]))</span>
                            <span class="n">next_level</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">child</span><span class="p">)</span>

                <span class="n">this_level</span> <span class="o">=</span> <span class="n">next_level</span>       

        <span class="k">return</span> <span class="n">descendants</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_collider_path_nodes</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">W</span><span class="p">,</span> <span class="n">descendants</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get non-descendant collider path nodes and their parents of nodes in W up to time t.</span>
<span class="sd">        </span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">collider_path_nodes</span> <span class="o">=</span> <span class="nb">set</span><span class="p">([])</span>
        <span class="c1"># print(&quot;descendants &quot;, descendants)</span>
        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">W</span><span class="p">:</span>
            <span class="c1"># print(w)</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">w</span> 
            <span class="n">this_level</span> <span class="o">=</span> <span class="p">[</span><span class="n">w</span><span class="p">]</span>
            <span class="k">while</span> <span class="nb">len</span><span class="p">(</span><span class="n">this_level</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">next_level</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="k">for</span> <span class="n">varlag</span> <span class="ow">in</span> <span class="n">this_level</span><span class="p">:</span>
                    <span class="c1"># print(&quot;\t&quot;, varlag, self._get_spouses(varlag))</span>
                    <span class="k">for</span> <span class="n">spouse</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_spouses</span><span class="p">(</span><span class="n">varlag</span><span class="p">):</span>
                        <span class="c1"># print(&quot;\t\t&quot;, spouse)</span>
                        <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">spouse</span>
                        <span class="k">if</span> <span class="p">(</span><span class="n">spouse</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">collider_path_nodes</span>
                            <span class="ow">and</span> <span class="n">spouse</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">descendants</span> 
                            <span class="ow">and</span> <span class="p">(</span><span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">)):</span> <span class="c1"># or self.ignore_time_bounds)):</span>
                            <span class="n">collider_path_nodes</span> <span class="o">=</span> <span class="n">collider_path_nodes</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">spouse</span><span class="p">]))</span>
                            <span class="n">next_level</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">spouse</span><span class="p">)</span>

                <span class="n">this_level</span> <span class="o">=</span> <span class="n">next_level</span>       

        <span class="c1"># Add parents</span>
        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">collider_path_nodes</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">par</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_parents</span><span class="p">(</span><span class="n">w</span><span class="p">):</span>
                <span class="k">if</span> <span class="p">(</span><span class="n">par</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">collider_path_nodes</span>
                    <span class="ow">and</span> <span class="n">par</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">descendants</span>
                    <span class="ow">and</span> <span class="p">(</span><span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">)):</span> <span class="c1"># or self.ignore_time_bounds)):</span>
                    <span class="n">collider_path_nodes</span> <span class="o">=</span> <span class="n">collider_path_nodes</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="n">par</span><span class="p">]))</span>

        <span class="k">return</span> <span class="n">collider_path_nodes</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_adjacents_stationary_graph</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph</span><span class="p">,</span> <span class="n">node</span><span class="p">,</span> <span class="n">patterns</span><span class="p">,</span> 
        <span class="n">max_lag</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">exclude</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Find adjacencies of node matching patterns in a stationary graph.&quot;&quot;&quot;</span>
        
        <span class="c1"># graph = self.graph</span>

        <span class="c1"># Setup</span>
        <span class="n">i</span><span class="p">,</span> <span class="n">lag_i</span> <span class="o">=</span> <span class="n">node</span>
        <span class="k">if</span> <span class="n">exclude</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span> <span class="n">exclude</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">if</span> <span class="nb">type</span><span class="p">(</span><span class="n">patterns</span><span class="p">)</span> <span class="o">==</span> <span class="nb">str</span><span class="p">:</span>
            <span class="n">patterns</span> <span class="o">=</span> <span class="p">[</span><span class="n">patterns</span><span class="p">]</span>

        <span class="c1"># Init</span>
        <span class="n">adj</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="c1"># Find adjacencies going forward/contemp</span>
        <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">lag_ik</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,:,:])):</span>  
            <span class="n">matches</span> <span class="o">=</span> <span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="n">patt</span><span class="p">,</span> <span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">k</span><span class="p">,</span> <span class="n">lag_ik</span><span class="p">])</span> <span class="k">for</span> <span class="n">patt</span> <span class="ow">in</span> <span class="n">patterns</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">matches</span><span class="p">):</span>
                <span class="n">match</span> <span class="o">=</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="n">lag_i</span> <span class="o">+</span> <span class="n">lag_ik</span><span class="p">)</span>
                <span class="k">if</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="n">lag_i</span> <span class="o">+</span> <span class="n">lag_ik</span><span class="p">)</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">exclude</span> <span class="ow">and</span> <span class="p">(</span><span class="o">-</span><span class="n">max_lag</span> <span class="o">&lt;=</span> <span class="n">lag_i</span> <span class="o">+</span> <span class="n">lag_ik</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">):</span> <span class="c1"># or self.ignore_time_bounds):</span>
                    <span class="n">adj</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">k</span><span class="p">,</span> <span class="n">lag_ik</span><span class="p">],</span> <span class="n">match</span><span class="p">))</span>
        
        <span class="c1"># Find adjacencies going backward/contemp</span>
        <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">lag_ki</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">graph</span><span class="p">[:,</span><span class="n">i</span><span class="p">,:])):</span>  
            <span class="n">matches</span> <span class="o">=</span> <span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_reverse_link</span><span class="p">(</span><span class="n">patt</span><span class="p">),</span> <span class="n">graph</span><span class="p">[</span><span class="n">k</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">lag_ki</span><span class="p">])</span> <span class="k">for</span> <span class="n">patt</span> <span class="ow">in</span> <span class="n">patterns</span><span class="p">]</span>
            <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">matches</span><span class="p">):</span>
                <span class="n">match</span> <span class="o">=</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="n">lag_i</span> <span class="o">-</span> <span class="n">lag_ki</span><span class="p">)</span>
                <span class="k">if</span> <span class="p">(</span><span class="n">k</span><span class="p">,</span> <span class="n">lag_i</span> <span class="o">-</span> <span class="n">lag_ki</span><span class="p">)</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">exclude</span> <span class="ow">and</span> <span class="p">(</span><span class="o">-</span><span class="n">max_lag</span> <span class="o">&lt;=</span> <span class="n">lag_i</span> <span class="o">-</span> <span class="n">lag_ki</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">):</span> <span class="c1"># or self.ignore_time_bounds):</span>
                    <span class="n">adj</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">_reverse_link</span><span class="p">(</span><span class="n">graph</span><span class="p">[</span><span class="n">k</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">lag_ki</span><span class="p">]),</span> <span class="n">match</span><span class="p">))</span>         
        
        <span class="n">adj</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">adj</span><span class="p">))</span>
        <span class="k">return</span> <span class="n">adj</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_canonical_dag_from_graph</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">graph</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Constructs canonical DAG as links_coeffs dictionary from graph.</span>

<span class="sd">        For every &lt;-&gt; link further latent variables are added.</span>
<span class="sd">        This corresponds to a canonical DAG (Richardson Spirtes 2002).</span>

<span class="sd">        Can be used to evaluate d-separation.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">N</span><span class="p">,</span> <span class="n">N</span><span class="p">,</span> <span class="n">tau_maxplusone</span> <span class="o">=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span>
        <span class="n">tau_max</span> <span class="o">=</span> <span class="n">tau_maxplusone</span> <span class="o">-</span> <span class="mi">1</span>

        <span class="n">links</span> <span class="o">=</span> <span class="p">{</span><span class="n">j</span><span class="p">:</span> <span class="p">[]</span> <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">)}</span>

        <span class="c1"># Add further latent variables to accommodate &lt;-&gt; links</span>
        <span class="n">latent_index</span> <span class="o">=</span> <span class="n">N</span>
        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">graph</span><span class="p">)):</span>

            <span class="n">edge_type</span> <span class="o">=</span> <span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">tau</span><span class="p">]</span>

            <span class="c1"># Consider contemporaneous links only once</span>
            <span class="k">if</span> <span class="n">tau</span> <span class="o">==</span> <span class="mi">0</span> <span class="ow">and</span> <span class="n">j</span> <span class="o">&gt;</span> <span class="n">i</span><span class="p">:</span>
                <span class="k">continue</span>

            <span class="k">if</span> <span class="n">edge_type</span> <span class="o">==</span> <span class="s2">&quot;--&gt;&quot;</span><span class="p">:</span>
                <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">))</span>
            <span class="k">elif</span> <span class="n">edge_type</span> <span class="o">==</span> <span class="s2">&quot;&lt;--&quot;</span><span class="p">:</span>
                <span class="n">links</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">))</span>
            <span class="k">elif</span> <span class="n">edge_type</span> <span class="o">==</span> <span class="s2">&quot;&lt;-&gt;&quot;</span><span class="p">:</span>
                <span class="n">links</span><span class="p">[</span><span class="n">latent_index</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="n">links</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">latent_index</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
                <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">latent_index</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">))</span>
                <span class="n">latent_index</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="c1"># elif edge_type == &quot;---&quot;:</span>
            <span class="c1">#     links[latent_index] = []</span>
            <span class="c1">#     selection_vars.append(latent_index)</span>
            <span class="c1">#     links[latent_index].append((i, -tau))</span>
            <span class="c1">#     links[latent_index].append((j, 0))</span>
            <span class="c1">#     latent_index += 1</span>
            <span class="k">elif</span> <span class="n">edge_type</span> <span class="o">==</span> <span class="s2">&quot;+-&gt;&quot;</span><span class="p">:</span>
                <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">))</span>
                <span class="n">links</span><span class="p">[</span><span class="n">latent_index</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="n">links</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">latent_index</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
                <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">latent_index</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">))</span>
                <span class="n">latent_index</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">elif</span> <span class="n">edge_type</span> <span class="o">==</span> <span class="s2">&quot;&lt;-+&quot;</span><span class="p">:</span>
                <span class="n">links</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">))</span>
                <span class="n">links</span><span class="p">[</span><span class="n">latent_index</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
                <span class="n">links</span><span class="p">[</span><span class="n">i</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">latent_index</span><span class="p">,</span> <span class="mi">0</span><span class="p">))</span>
                <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">latent_index</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">))</span>
                <span class="n">latent_index</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="k">return</span> <span class="n">links</span>


    <span class="k">def</span><span class="w"> </span><span class="nf">_get_maximum_possible_lag</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">,</span> <span class="n">graph</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Construct maximum relevant time lag for d-separation in stationary graph.</span>

<span class="sd">        TO BE REVISED!</span>

<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">_repeating</span><span class="p">(</span><span class="n">link</span><span class="p">,</span> <span class="n">seen_path</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Returns True if a link or its time-shifted version is already</span>
<span class="sd">            included in seen_links.&quot;&quot;&quot;</span>
            <span class="n">i</span><span class="p">,</span> <span class="n">taui</span> <span class="o">=</span> <span class="n">link</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tauj</span> <span class="o">=</span> <span class="n">link</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>

            <span class="k">for</span> <span class="n">index</span><span class="p">,</span> <span class="n">seen_link</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">seen_path</span><span class="p">[:</span><span class="o">-</span><span class="mi">1</span><span class="p">]):</span>
                <span class="n">seen_i</span><span class="p">,</span> <span class="n">seen_taui</span> <span class="o">=</span> <span class="n">seen_link</span>
                <span class="n">seen_j</span><span class="p">,</span> <span class="n">seen_tauj</span> <span class="o">=</span> <span class="n">seen_path</span><span class="p">[</span><span class="n">index</span> <span class="o">+</span> <span class="mi">1</span><span class="p">]</span>

                <span class="k">if</span> <span class="p">(</span><span class="n">i</span> <span class="o">==</span> <span class="n">seen_i</span> <span class="ow">and</span> <span class="n">j</span> <span class="o">==</span> <span class="n">seen_j</span>
                    <span class="ow">and</span> <span class="nb">abs</span><span class="p">(</span><span class="n">tauj</span><span class="o">-</span><span class="n">taui</span><span class="p">)</span> <span class="o">==</span> <span class="nb">abs</span><span class="p">(</span><span class="n">seen_tauj</span><span class="o">-</span><span class="n">seen_taui</span><span class="p">)):</span>
                    <span class="k">return</span> <span class="kc">True</span>

            <span class="k">return</span> <span class="kc">False</span>

        <span class="c1"># TODO: does this work with PAGs?</span>
        <span class="c1"># if self.possible:</span>
        <span class="c1">#     patterns=[&#39;&lt;*-&#39;, &#39;&lt;*o&#39;, &#39;o*o&#39;] </span>
        <span class="c1"># else:</span>
        <span class="c1">#     patterns=[&#39;&lt;*-&#39;] </span>

        <span class="n">canonical_dag_links</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_canonical_dag_from_graph</span><span class="p">(</span><span class="n">graph</span><span class="p">)</span>

        <span class="n">max_lag</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">XYZ</span><span class="p">:</span>
            <span class="n">j</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">node</span>   <span class="c1"># tau &lt;= 0</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">max_lag</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">))</span>

            <span class="n">causal_path</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="n">queue</span> <span class="o">=</span> <span class="p">[(</span><span class="n">node</span><span class="p">,</span> <span class="n">causal_path</span><span class="p">)]</span>

            <span class="k">while</span> <span class="n">queue</span><span class="p">:</span>
                <span class="n">varlag</span><span class="p">,</span> <span class="n">causal_path</span> <span class="o">=</span> <span class="n">queue</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>
                <span class="n">causal_path</span> <span class="o">=</span> <span class="p">[</span><span class="n">varlag</span><span class="p">]</span> <span class="o">+</span> <span class="n">causal_path</span>

                <span class="n">var</span><span class="p">,</span> <span class="n">lag</span> <span class="o">=</span> <span class="n">varlag</span>
                <span class="k">for</span> <span class="n">partmp</span> <span class="ow">in</span> <span class="n">canonical_dag_links</span><span class="p">[</span><span class="n">var</span><span class="p">]:</span>
                    <span class="n">i</span><span class="p">,</span> <span class="n">tautmp</span> <span class="o">=</span> <span class="n">partmp</span>
                    <span class="c1"># Get shifted lag since canonical_dag_links is at t=0</span>
                    <span class="n">tau</span> <span class="o">=</span> <span class="n">tautmp</span> <span class="o">+</span> <span class="n">lag</span>
                    <span class="n">par</span> <span class="o">=</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">tau</span><span class="p">)</span>

                    <span class="k">if</span> <span class="p">(</span><span class="n">par</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">causal_path</span><span class="p">):</span>
                    
                        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">causal_path</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                            <span class="n">queue</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">par</span><span class="p">,</span> <span class="n">causal_path</span><span class="p">))</span>
                            <span class="k">continue</span>

                        <span class="k">if</span> <span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">causal_path</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">)</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">_repeating</span><span class="p">((</span><span class="n">par</span><span class="p">,</span> <span class="n">varlag</span><span class="p">),</span> <span class="n">causal_path</span><span class="p">):</span>
                            
                                <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">max_lag</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">tau</span><span class="p">))</span>
                                <span class="n">queue</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">par</span><span class="p">,</span> <span class="n">causal_path</span><span class="p">))</span>

        <span class="k">return</span> <span class="n">max_lag</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_get_latent_projection_graph</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">stationary</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;For DAGs/ADMGs uses the Latent projection operation (Pearl 2009).</span>

<span class="sd">           Assumes a normal or stationary graph with potentially unobserved nodes.</span>
<span class="sd">           Also allows particular time steps to be unobserved. By stationarity</span>
<span class="sd">           that pattern of unobserved nodes is repeated into -infinity.</span>

<span class="sd">           Latent projection operation for latents = nodes before t-tau_max or due to &lt;-&gt;:</span>
<span class="sd">           (i)  auxADMG contains (i, -taui) --&gt; (j, -tauj) iff there is a directed path </span>
<span class="sd">                (i, -taui) --&gt; ... --&gt; (j, -tauj) on which</span>
<span class="sd">                every non-endpoint vertex is in hidden variables (= not in observed_vars)</span>
<span class="sd">                here iff (i, -|taui-tauj|) --&gt; j in graph</span>
<span class="sd">           (ii) auxADMG contains (i, -taui) &lt;-&gt; (j, -tauj) iff there exists a path of the </span>
<span class="sd">                form (i, -taui) &lt;-- ... --&gt; (j, -tauj) on</span>
<span class="sd">                which every non-endpoint vertex is non-collider AND in L (=not in observed_vars)</span>
<span class="sd">                here iff (i, -|taui-tauj|) &lt;-&gt; j OR there is path </span>
<span class="sd">                (i, -taui) &lt;-- nodes before t-tau_max --&gt; (j, -tauj)</span>
<span class="sd">        &quot;&quot;&quot;</span>
        
        <span class="c1"># graph = self.graph</span>

        <span class="c1"># if self.hidden_variables is None:</span>
        <span class="c1">#     hidden_variables_here = []</span>
        <span class="c1"># else:</span>
        <span class="n">hidden_variables_here</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">hidden_variables</span>

        <span class="n">aux_graph</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;&lt;U3&#39;</span><span class="p">)</span>
        <span class="n">aux_graph</span><span class="p">[:]</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span>
        <span class="k">for</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">)</span> <span class="ow">in</span> <span class="n">itertools</span><span class="o">.</span><span class="n">product</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">),</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)):</span>
            <span class="k">for</span> <span class="n">jt</span><span class="p">,</span> <span class="n">tauj</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)):</span>
                <span class="k">for</span> <span class="n">it</span><span class="p">,</span> <span class="n">taui</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)):</span>
                    <span class="n">tau</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">taui</span> <span class="o">-</span> <span class="n">tauj</span><span class="p">)</span>
                    <span class="k">if</span> <span class="n">tau</span> <span class="o">==</span> <span class="mi">0</span> <span class="ow">and</span> <span class="n">j</span> <span class="o">==</span> <span class="n">i</span><span class="p">:</span>
                        <span class="k">continue</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">taui</span><span class="p">)</span> <span class="ow">in</span> <span class="n">hidden_variables_here</span> <span class="ow">or</span> <span class="p">(</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">tauj</span><span class="p">)</span> <span class="ow">in</span> <span class="n">hidden_variables_here</span><span class="p">:</span>
                        <span class="k">continue</span>
                    <span class="c1"># print(&quot;\n&quot;)</span>
                    <span class="c1"># print((i, -taui), (j, -tauj))</span>

                    <span class="n">cond_i_xy</span> <span class="o">=</span> <span class="p">(</span>
                            <span class="c1"># tau &lt;= graph_taumax </span>
                        <span class="c1"># and (graph[i, j, tau] == &#39;--&gt;&#39; or graph[i, j, tau] == &#39;+-&gt;&#39;) </span>
                        <span class="c1">#     )</span>
                          <span class="c1"># and </span>
                          <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span> <span class="c1">#graph=graph,</span>
                                                <span class="n">start</span><span class="o">=</span><span class="p">[(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">taui</span><span class="p">)],</span>
                                                 <span class="n">end</span><span class="o">=</span><span class="p">[(</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">tauj</span><span class="p">)],</span>
                                                 <span class="n">conditions</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                                 <span class="n">starts_with</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;-*&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;+*&gt;&#39;</span><span class="p">],</span>
                                                 <span class="n">ends_with</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;-*&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;+*&gt;&#39;</span><span class="p">],</span>
                                                 <span class="n">path_type</span><span class="o">=</span><span class="s1">&#39;causal&#39;</span><span class="p">,</span>
                                                 <span class="n">hidden_by_taumax</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                                 <span class="n">hidden_variables</span><span class="o">=</span><span class="n">hidden_variables_here</span><span class="p">,</span>
                                                 <span class="n">stationary_graph</span><span class="o">=</span><span class="n">stationary</span><span class="p">,</span>
                                                 <span class="p">))</span>
                    <span class="n">cond_i_yx</span> <span class="o">=</span> <span class="p">(</span>
                        <span class="c1"># tau &lt;= graph_taumax </span>
                        <span class="c1"># and (graph[i, j, tau] == &#39;&lt;--&#39; or graph[i, j, tau] == &#39;&lt;-+&#39;) </span>
                        <span class="c1">#     )</span>
                        <span class="c1"># and </span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span> <span class="c1">#graph=graph,</span>
                                              <span class="n">start</span><span class="o">=</span><span class="p">[(</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">tauj</span><span class="p">)],</span>
                                               <span class="n">end</span><span class="o">=</span><span class="p">[(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">taui</span><span class="p">)],</span>
                                               <span class="n">conditions</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                               <span class="n">starts_with</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;-*&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;+*&gt;&#39;</span><span class="p">],</span>
                                               <span class="n">ends_with</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;-*&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;+*&gt;&#39;</span><span class="p">],</span>
                                               <span class="n">path_type</span><span class="o">=</span><span class="s1">&#39;causal&#39;</span><span class="p">,</span>
                                               <span class="n">hidden_by_taumax</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                               <span class="n">hidden_variables</span><span class="o">=</span><span class="n">hidden_variables_here</span><span class="p">,</span>
                                               <span class="n">stationary_graph</span><span class="o">=</span><span class="n">stationary</span><span class="p">,</span>
                                               <span class="p">))</span>
                    <span class="k">if</span> <span class="n">stationary</span><span class="p">:</span>
                        <span class="n">hidden_by_taumax_here</span> <span class="o">=</span> <span class="kc">True</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="n">hidden_by_taumax_here</span> <span class="o">=</span> <span class="kc">False</span>

                    <span class="n">cond_ii</span> <span class="o">=</span> <span class="p">(</span>
                        <span class="c1"># tau &lt;= graph_taumax </span>
                                <span class="c1"># and </span>
                                <span class="p">(</span>
                                <span class="c1">#     graph[i, j, tau] == &#39;&lt;-&gt;&#39; </span>
                                <span class="c1"># or graph[i, j, tau] == &#39;+-&gt;&#39; or graph[i, j, tau] == &#39;&lt;-+&#39;)) </span>
                                    <span class="bp">self</span><span class="o">.</span><span class="n">_check_path</span><span class="p">(</span> <span class="c1">#graph=graph,</span>
                                                <span class="n">start</span><span class="o">=</span><span class="p">[(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">taui</span><span class="p">)],</span>
                                                 <span class="n">end</span><span class="o">=</span><span class="p">[(</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">tauj</span><span class="p">)],</span>
                                                 <span class="n">conditions</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                                 <span class="n">starts_with</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;&lt;**&#39;</span><span class="p">,</span> <span class="s1">&#39;+**&#39;</span><span class="p">],</span>
                                                 <span class="n">ends_with</span><span class="o">=</span><span class="p">[</span><span class="s1">&#39;**&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;**+&#39;</span><span class="p">],</span>
                                                 <span class="n">path_type</span><span class="o">=</span><span class="s1">&#39;any&#39;</span><span class="p">,</span>
                                                 <span class="n">hidden_by_taumax</span><span class="o">=</span><span class="n">hidden_by_taumax_here</span><span class="p">,</span>
                                                 <span class="n">hidden_variables</span><span class="o">=</span><span class="n">hidden_variables_here</span><span class="p">,</span>
                                                 <span class="n">stationary_graph</span><span class="o">=</span><span class="n">stationary</span><span class="p">,</span>
                                                 <span class="p">)))</span>

                    <span class="k">if</span> <span class="n">cond_i_xy</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">cond_i_yx</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">cond_ii</span><span class="p">:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">taui</span><span class="p">,</span> <span class="n">tauj</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;--&gt;&quot;</span>  <span class="c1">#graph[i, j, tau]</span>
                        <span class="c1"># if tau == 0:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tauj</span><span class="p">,</span> <span class="n">taui</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;&lt;--&quot;</span>  <span class="c1"># graph[j, i, tau]</span>
                    <span class="k">elif</span> <span class="ow">not</span> <span class="n">cond_i_xy</span> <span class="ow">and</span> <span class="n">cond_i_yx</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">cond_ii</span><span class="p">:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">taui</span><span class="p">,</span> <span class="n">tauj</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;&lt;--&quot;</span>  <span class="c1">#graph[i, j, tau]</span>
                        <span class="c1"># if tau == 0:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tauj</span><span class="p">,</span> <span class="n">taui</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;--&gt;&quot;</span>  <span class="c1"># graph[j, i, tau]</span>
                    <span class="k">elif</span> <span class="ow">not</span> <span class="n">cond_i_xy</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">cond_i_yx</span> <span class="ow">and</span> <span class="n">cond_ii</span><span class="p">:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">taui</span><span class="p">,</span> <span class="n">tauj</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;&lt;-&gt;&#39;</span>
                        <span class="c1"># if tau == 0:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tauj</span><span class="p">,</span> <span class="n">taui</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;&lt;-&gt;&#39;</span>
                    <span class="k">elif</span> <span class="n">cond_i_xy</span> <span class="ow">and</span> <span class="ow">not</span> <span class="n">cond_i_yx</span> <span class="ow">and</span> <span class="n">cond_ii</span><span class="p">:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">taui</span><span class="p">,</span> <span class="n">tauj</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;+-&gt;&#39;</span>
                        <span class="c1"># if tau == 0:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tauj</span><span class="p">,</span> <span class="n">taui</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;&lt;-+&#39;</span>                        
                    <span class="k">elif</span> <span class="ow">not</span> <span class="n">cond_i_xy</span> <span class="ow">and</span> <span class="n">cond_i_yx</span> <span class="ow">and</span> <span class="n">cond_ii</span><span class="p">:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">taui</span><span class="p">,</span> <span class="n">tauj</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;&lt;-+&#39;</span>
                        <span class="c1"># if tau == 0:</span>
                        <span class="n">aux_graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">tauj</span><span class="p">,</span> <span class="n">taui</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;+-&gt;&#39;</span> 
                    <span class="k">elif</span> <span class="n">cond_i_xy</span> <span class="ow">and</span> <span class="n">cond_i_yx</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Cycle between </span><span class="si">%s</span><span class="s2"> and </span><span class="si">%s</span><span class="s2">!&quot;</span> <span class="o">%</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">taui</span><span class="p">),</span> <span class="nb">str</span><span class="p">(</span><span class="n">j</span><span class="p">,</span> <span class="o">-</span><span class="n">tauj</span><span class="p">)))</span>
                    <span class="c1"># print(aux_graph[i, j, taui, tauj])</span>

                    <span class="c1"># print((i, -taui), (j, -tauj), cond_i_xy, cond_i_yx, cond_ii, aux_graph[i, j, taui, tauj], aux_graph[j, i, tauj, taui])</span>

        <span class="k">return</span> <span class="n">aux_graph</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_check_path</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> 
        <span class="c1"># graph, </span>
        <span class="n">start</span><span class="p">,</span> <span class="n">end</span><span class="p">,</span>
        <span class="n">conditions</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> 
        <span class="n">starts_with</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">ends_with</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">path_type</span><span class="o">=</span><span class="s1">&#39;any&#39;</span><span class="p">,</span>
        <span class="c1"># causal_children=None,</span>
        <span class="n">stationary_graph</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="n">hidden_by_taumax</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
        <span class="n">hidden_variables</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check whether an open/active path between start and end given conditions exists.</span>
<span class="sd">        </span>
<span class="sd">        Also allows to restrict start and end patterns and to consider causal/non-causal paths</span>

<span class="sd">        hidden_by_taumax and hidden_variables are relevant for the latent projection operation.</span>
<span class="sd">        &quot;&quot;&quot;</span>


        <span class="k">if</span> <span class="n">conditions</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">conditions</span> <span class="o">=</span> <span class="nb">set</span><span class="p">([])</span>
        <span class="c1"># if conditioned_variables is None:</span>
        <span class="c1">#     S = []</span>

        <span class="n">start</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">start</span><span class="p">)</span>
        <span class="n">end</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">end</span><span class="p">)</span>
        <span class="n">conditions</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">conditions</span><span class="p">)</span>
        
        <span class="c1"># Get maximal possible time lag of a connecting path</span>
        <span class="c1"># See Thm. XXXX - TO BE REVISED!</span>
        <span class="n">XYZ</span> <span class="o">=</span> <span class="n">start</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">end</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">conditions</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">stationary_graph</span><span class="p">:</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="mi">10</span><span class="o">*</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span>  <span class="c1"># TO BE REVISED! self._get_maximum_possible_lag(XYZ, self.graph)</span>
            <span class="n">causal_children</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_get_mediators_stationary_graph</span><span class="p">(</span><span class="n">start</span><span class="p">,</span> <span class="n">end</span><span class="p">,</span> <span class="n">max_lag</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">end</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="n">causal_children</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">get_mediators</span><span class="p">(</span><span class="n">start</span><span class="p">,</span> <span class="n">end</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">end</span><span class="p">))</span>
       
        <span class="c1"># if hidden_variables is None:</span>
        <span class="c1">#     hidden_variables = set([])</span>

        <span class="k">if</span> <span class="n">hidden_by_taumax</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">hidden_variables</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">hidden_variables</span> <span class="o">=</span> <span class="nb">set</span><span class="p">([])</span>
            <span class="n">hidden_variables</span> <span class="o">=</span> <span class="n">hidden_variables</span><span class="o">.</span><span class="n">union</span><span class="p">([(</span><span class="n">k</span><span class="p">,</span> <span class="o">-</span><span class="n">tauk</span><span class="p">)</span> <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)</span> 
                                            <span class="k">for</span> <span class="n">tauk</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span><span class="o">+</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_lag</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)])</span>

        <span class="c1"># print(&quot;causal_children &quot;, causal_children)</span>

        <span class="k">if</span> <span class="n">starts_with</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">starts_with</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;***&#39;</span><span class="p">]</span>
        <span class="k">elif</span> <span class="nb">type</span><span class="p">(</span><span class="n">starts_with</span><span class="p">)</span> <span class="o">==</span> <span class="nb">str</span><span class="p">:</span>
            <span class="n">starts_with</span> <span class="o">=</span> <span class="p">[</span><span class="n">starts_with</span><span class="p">]</span>

        <span class="k">if</span> <span class="n">ends_with</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">ends_with</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;***&#39;</span><span class="p">]</span>
        <span class="k">elif</span> <span class="nb">type</span><span class="p">(</span><span class="n">ends_with</span><span class="p">)</span> <span class="o">==</span> <span class="nb">str</span><span class="p">:</span>
            <span class="n">ends_with</span> <span class="o">=</span> <span class="p">[</span><span class="n">ends_with</span><span class="p">]</span>
        <span class="c1">#</span>
        <span class="c1"># Breadth-first search to find connection</span>
        <span class="c1">#</span>
        <span class="c1"># print(&quot;\nstart, starts_with, ends_with, end &quot;, start, starts_with, ends_with, end)</span>
        <span class="c1"># print(&quot;hidden_variables &quot;, hidden_variables)</span>
        <span class="n">start_from</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
        <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">start</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">stationary_graph</span><span class="p">:</span>
                <span class="n">link_neighbors</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_adjacents_stationary_graph</span><span class="p">(</span><span class="n">graph</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="p">,</span> <span class="n">node</span><span class="o">=</span><span class="n">x</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="n">starts_with</span><span class="p">,</span> 
                                        <span class="n">max_lag</span><span class="o">=</span><span class="n">max_lag</span><span class="p">,</span> <span class="n">exclude</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">start</span><span class="p">))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">link_neighbors</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_find_adj</span><span class="p">(</span><span class="n">node</span><span class="o">=</span><span class="n">x</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="n">starts_with</span><span class="p">,</span> <span class="n">exclude</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">start</span><span class="p">),</span> <span class="n">return_link</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            
            <span class="k">for</span> <span class="n">link_neighbor</span> <span class="ow">in</span> <span class="n">link_neighbors</span><span class="p">:</span>
                <span class="n">link</span><span class="p">,</span> <span class="n">neighbor</span> <span class="o">=</span> <span class="n">link_neighbor</span>

                <span class="c1"># if before_taumax and neighbor[1] &gt;= -self.tau_max:</span>
                <span class="c1">#     continue</span>

                <span class="k">if</span> <span class="p">(</span><span class="n">hidden_variables</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">neighbor</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">end</span>
                                    <span class="ow">and</span> <span class="n">neighbor</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">hidden_variables</span><span class="p">):</span>
                    <span class="k">continue</span>

                <span class="k">if</span> <span class="n">path_type</span> <span class="o">==</span> <span class="s1">&#39;non_causal&#39;</span><span class="p">:</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">neighbor</span> <span class="ow">in</span> <span class="n">causal_children</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="s1">&#39;-*&gt;&#39;</span><span class="p">,</span> <span class="n">link</span><span class="p">)</span> 
                        <span class="ow">and</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="s1">&#39;+*&gt;&#39;</span><span class="p">,</span> <span class="n">link</span><span class="p">)):</span>
                        <span class="k">continue</span>
                <span class="k">elif</span> <span class="n">path_type</span> <span class="o">==</span> <span class="s1">&#39;causal&#39;</span><span class="p">:</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">neighbor</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">causal_children</span><span class="p">):</span> <span class="c1"># or self._match_link(&#39;&lt;**&#39;, link)):</span>
                        <span class="k">continue</span>                    
                <span class="n">start_from</span><span class="o">.</span><span class="n">add</span><span class="p">((</span><span class="n">x</span><span class="p">,</span> <span class="n">link</span><span class="p">,</span> <span class="n">neighbor</span><span class="p">))</span>

        <span class="c1"># print(&quot;start, end, start_from &quot;, start, end, start_from)</span>

        <span class="n">visited</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
        <span class="k">for</span> <span class="p">(</span><span class="n">varlag_i</span><span class="p">,</span> <span class="n">link_ik</span><span class="p">,</span> <span class="n">varlag_k</span><span class="p">)</span> <span class="ow">in</span> <span class="n">start_from</span><span class="p">:</span>
            <span class="n">visited</span><span class="o">.</span><span class="n">add</span><span class="p">((</span><span class="n">link_ik</span><span class="p">,</span> <span class="n">varlag_k</span><span class="p">))</span>

        <span class="c1"># Traversing through motifs i *-* k *-* j</span>
        <span class="k">while</span> <span class="n">start_from</span><span class="p">:</span>

            <span class="c1"># print(&quot;Continue &quot;, start_from)</span>
            <span class="c1"># for (link_ik, varlag_k) in start_from:</span>
            <span class="n">removables</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="p">(</span><span class="n">varlag_i</span><span class="p">,</span> <span class="n">link_ik</span><span class="p">,</span> <span class="n">varlag_k</span><span class="p">)</span> <span class="ow">in</span> <span class="n">start_from</span><span class="p">:</span>

                <span class="c1"># print(&quot;varlag_k in end &quot;, varlag_k in end, link_ik)</span>
                <span class="k">if</span> <span class="n">varlag_k</span> <span class="ow">in</span> <span class="n">end</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">([</span><span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="n">patt</span><span class="p">,</span> <span class="n">link_ik</span><span class="p">)</span> <span class="k">for</span> <span class="n">patt</span> <span class="ow">in</span> <span class="n">ends_with</span><span class="p">]):</span>
                        <span class="c1"># print(&quot;Connected &quot;, varlag_i, link_ik, varlag_k)</span>
                        <span class="k">return</span> <span class="kc">True</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="n">removables</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">varlag_i</span><span class="p">,</span> <span class="n">link_ik</span><span class="p">,</span> <span class="n">varlag_k</span><span class="p">))</span>

            <span class="k">for</span> <span class="n">removable</span> <span class="ow">in</span> <span class="n">removables</span><span class="p">:</span>
                <span class="n">start_from</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="n">removable</span><span class="p">)</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">start_from</span><span class="p">)</span><span class="o">==</span><span class="mi">0</span><span class="p">:</span>
                <span class="k">return</span> <span class="kc">False</span>

            <span class="c1"># Get any neighbor from starting nodes</span>
            <span class="c1"># link_ik, varlag_k = start_from.pop()</span>
            <span class="n">varlag_i</span><span class="p">,</span> <span class="n">link_ik</span><span class="p">,</span> <span class="n">varlag_k</span> <span class="o">=</span> <span class="n">start_from</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>

            <span class="c1"># print(&quot;Get k = &quot;, link_ik, varlag_k)</span>
            <span class="c1"># print(&quot;start_from &quot;, start_from)</span>
            <span class="c1"># print(&quot;visited    &quot;, visited)</span>

            <span class="k">if</span> <span class="n">stationary_graph</span><span class="p">:</span>
                <span class="n">link_neighbors</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_adjacents_stationary_graph</span><span class="p">(</span><span class="n">graph</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">graph</span><span class="p">,</span> <span class="n">node</span><span class="o">=</span><span class="n">varlag_k</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="s1">&#39;***&#39;</span><span class="p">,</span> 
                                        <span class="n">max_lag</span><span class="o">=</span><span class="n">max_lag</span><span class="p">,</span> <span class="n">exclude</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">start</span><span class="p">))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">link_neighbors</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_find_adj</span><span class="p">(</span><span class="n">node</span><span class="o">=</span><span class="n">varlag_k</span><span class="p">,</span> <span class="n">patterns</span><span class="o">=</span><span class="s1">&#39;***&#39;</span><span class="p">,</span> <span class="n">exclude</span><span class="o">=</span><span class="nb">list</span><span class="p">(</span><span class="n">start</span><span class="p">),</span> <span class="n">return_link</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            
            <span class="c1"># print(&quot;link_neighbors &quot;, link_neighbors)</span>
            <span class="k">for</span> <span class="n">link_neighbor</span> <span class="ow">in</span> <span class="n">link_neighbors</span><span class="p">:</span>
                <span class="n">link_kj</span><span class="p">,</span> <span class="n">varlag_j</span> <span class="o">=</span> <span class="n">link_neighbor</span>
                <span class="c1"># print(&quot;Walk &quot;, link_ik, varlag_k, link_kj, varlag_j)</span>

                <span class="c1"># print (&quot;visited &quot;, (link_kj, varlag_j), visited)</span>
                <span class="k">if</span> <span class="p">(</span><span class="n">link_kj</span><span class="p">,</span> <span class="n">varlag_j</span><span class="p">)</span> <span class="ow">in</span> <span class="n">visited</span><span class="p">:</span>
                <span class="c1"># if (varlag_i, link_kj, varlag_j) in visited:</span>
                    <span class="c1"># print(&quot;in visited&quot;)</span>
                    <span class="k">continue</span>
                <span class="c1"># print(&quot;Not in visited&quot;)</span>

                <span class="k">if</span> <span class="n">path_type</span> <span class="o">==</span> <span class="s1">&#39;causal&#39;</span><span class="p">:</span>
                    <span class="k">if</span> <span class="ow">not</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="s1">&#39;-*&gt;&#39;</span><span class="p">,</span> <span class="n">link_kj</span><span class="p">)</span> <span class="ow">or</span> <span class="bp">self</span><span class="o">.</span><span class="n">_match_link</span><span class="p">(</span><span class="s1">&#39;+*&gt;&#39;</span><span class="p">,</span> <span class="n">link_kj</span><span class="p">)):</span>
                        <span class="k">continue</span> 

                <span class="c1"># If motif  i *-* k *-* j is open, </span>
                <span class="c1"># then add link_kj, varlag_j to visited and start_from</span>
                <span class="n">left_mark</span> <span class="o">=</span> <span class="n">link_ik</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span>
                <span class="n">right_mark</span> <span class="o">=</span> <span class="n">link_kj</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
                <span class="c1"># print(left_mark, right_mark)</span>

                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">definite_status</span><span class="p">:</span>
                    <span class="c1"># Exclude paths that are not definite_status implying that any of the following</span>
                    <span class="c1"># motifs occurs:</span>
                    <span class="c1"># i *-&gt; k o-* j</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">left_mark</span> <span class="o">==</span> <span class="s1">&#39;&gt;&#39;</span> <span class="ow">and</span> <span class="n">right_mark</span> <span class="o">==</span> <span class="s1">&#39;o&#39;</span><span class="p">):</span>
                        <span class="k">continue</span>
                    <span class="c1"># i *-o k &lt;-* j</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">left_mark</span> <span class="o">==</span> <span class="s1">&#39;o&#39;</span> <span class="ow">and</span> <span class="n">right_mark</span> <span class="o">==</span> <span class="s1">&#39;&lt;&#39;</span><span class="p">):</span>
                        <span class="k">continue</span>
                    <span class="c1"># i *-o k o-* j and i and j are adjacent</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">left_mark</span> <span class="o">==</span> <span class="s1">&#39;o&#39;</span> <span class="ow">and</span> <span class="n">right_mark</span> <span class="o">==</span> <span class="s1">&#39;o&#39;</span>
                        <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">_is_match</span><span class="p">(</span><span class="n">varlag_i</span><span class="p">,</span> <span class="n">varlag_j</span><span class="p">,</span> <span class="s2">&quot;***&quot;</span><span class="p">)):</span>
                        <span class="k">continue</span>

                    <span class="c1"># If k is in conditions and motif is *-o k o-*, then motif is blocked since</span>
                    <span class="c1"># i and j are non-adjacent due to the check above</span>
                    <span class="k">if</span> <span class="n">varlag_k</span> <span class="ow">in</span> <span class="n">conditions</span> <span class="ow">and</span> <span class="p">(</span><span class="n">left_mark</span> <span class="o">==</span> <span class="s1">&#39;o&#39;</span> <span class="ow">and</span> <span class="n">right_mark</span> <span class="o">==</span> <span class="s1">&#39;o&#39;</span><span class="p">):</span>
                        <span class="c1"># print(&quot;Motif closed &quot;, link_ik, varlag_k, link_kj, varlag_j )</span>
                        <span class="k">continue</span>  <span class="c1"># [(&#39;&gt;&#39;, &#39;&lt;&#39;), (&#39;&gt;&#39;, &#39;+&#39;), (&#39;+&#39;, &#39;&lt;&#39;), (&#39;+&#39;, &#39;+&#39;)]</span>

                <span class="c1"># If k is in conditions and left or right mark is tail &#39;-&#39;, then motif is blocked</span>
                <span class="k">if</span> <span class="n">varlag_k</span> <span class="ow">in</span> <span class="n">conditions</span> <span class="ow">and</span> <span class="p">(</span><span class="n">left_mark</span> <span class="o">==</span> <span class="s1">&#39;-&#39;</span> <span class="ow">or</span> <span class="n">right_mark</span> <span class="o">==</span> <span class="s1">&#39;-&#39;</span><span class="p">):</span>
                    <span class="c1"># print(&quot;Motif closed &quot;, link_ik, varlag_k, link_kj, varlag_j )</span>
                    <span class="k">continue</span>  <span class="c1"># [(&#39;&gt;&#39;, &#39;&lt;&#39;), (&#39;&gt;&#39;, &#39;+&#39;), (&#39;+&#39;, &#39;&lt;&#39;), (&#39;+&#39;, &#39;+&#39;)]</span>

                <span class="c1"># If k is not in conditions and left and right mark are heads &#39;&gt;&lt;&#39;, then motif is blocked</span>
                <span class="k">if</span> <span class="n">varlag_k</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">conditions</span> <span class="ow">and</span> <span class="p">(</span><span class="n">left_mark</span> <span class="o">==</span> <span class="s1">&#39;&gt;&#39;</span> <span class="ow">and</span> <span class="n">right_mark</span> <span class="o">==</span> <span class="s1">&#39;&lt;&#39;</span><span class="p">):</span>
                    <span class="c1"># print(&quot;Motif closed &quot;, link_ik, varlag_k, link_kj, varlag_j )</span>
                    <span class="k">continue</span>  <span class="c1"># [(&#39;&gt;&#39;, &#39;&lt;&#39;), (&#39;&gt;&#39;, &#39;+&#39;), (&#39;+&#39;, &#39;&lt;&#39;), (&#39;+&#39;, &#39;+&#39;)]</span>

                <span class="c1"># if (before_taumax and varlag_j not in end </span>
                <span class="c1">#     and varlag_j[1] &gt;= -self.tau_max):</span>
                <span class="c1">#     # print(&quot;before_taumax &quot;, varlag_j)</span>
                <span class="c1">#     continue</span>

                <span class="k">if</span> <span class="p">(</span><span class="n">hidden_variables</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">varlag_j</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">end</span>
                                    <span class="ow">and</span> <span class="n">varlag_j</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">hidden_variables</span><span class="p">):</span>
                    <span class="k">continue</span>

                <span class="c1"># Motif is open</span>
                <span class="c1"># print(&quot;Motif open &quot;, link_ik, varlag_k, link_kj, varlag_j )</span>
                <span class="c1"># start_from.add((link_kj, varlag_j))</span>
                <span class="n">visited</span><span class="o">.</span><span class="n">add</span><span class="p">((</span><span class="n">link_kj</span><span class="p">,</span> <span class="n">varlag_j</span><span class="p">))</span>
                <span class="n">start_from</span><span class="o">.</span><span class="n">add</span><span class="p">((</span><span class="n">varlag_k</span><span class="p">,</span> <span class="n">link_kj</span><span class="p">,</span> <span class="n">varlag_j</span><span class="p">))</span>
                <span class="c1"># visited.add((varlag_k, link_kj, varlag_j))</span>


        <span class="c1"># print(&quot;Separated&quot;)</span>
        <span class="k">return</span> <span class="kc">False</span>


    <span class="k">def</span><span class="w"> </span><span class="nf">_get_causal_paths</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">source_nodes</span><span class="p">,</span> <span class="n">target_nodes</span><span class="p">,</span>
        <span class="n">mediators</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">mediated_through</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">proper_paths</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
        <span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns causal paths via depth-first search.</span>

<span class="sd">        Allows to restrict paths through mediated_through.</span>

<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">source_nodes</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">source_nodes</span><span class="p">)</span>
        <span class="n">target_nodes</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">target_nodes</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">mediators</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">mediators</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">mediators</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">mediators</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">mediated_through</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">mediated_through</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">mediated_through</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">mediated_through</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">proper_paths</span><span class="p">:</span>
             <span class="n">inside_set</span> <span class="o">=</span> <span class="n">mediators</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">target_nodes</span><span class="p">)</span> <span class="o">-</span> <span class="n">source_nodes</span>
        <span class="k">else</span><span class="p">:</span>
             <span class="n">inside_set</span> <span class="o">=</span> <span class="n">mediators</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">target_nodes</span><span class="p">)</span><span class="o">.</span><span class="n">union</span><span class="p">(</span><span class="n">source_nodes</span><span class="p">)</span>

        <span class="n">all_causal_paths</span> <span class="o">=</span> <span class="p">{}</span>         
        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">source_nodes</span><span class="p">:</span>
            <span class="n">all_causal_paths</span><span class="p">[</span><span class="n">w</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
            <span class="k">for</span> <span class="n">z</span> <span class="ow">in</span> <span class="n">target_nodes</span><span class="p">:</span>
                <span class="n">all_causal_paths</span><span class="p">[</span><span class="n">w</span><span class="p">][</span><span class="n">z</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">source_nodes</span><span class="p">:</span>
            
            <span class="n">causal_path</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="n">queue</span> <span class="o">=</span> <span class="p">[(</span><span class="n">w</span><span class="p">,</span> <span class="n">causal_path</span><span class="p">)]</span>

            <span class="k">while</span> <span class="n">queue</span><span class="p">:</span>

                <span class="n">varlag</span><span class="p">,</span> <span class="n">causal_path</span> <span class="o">=</span> <span class="n">queue</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>
                <span class="n">causal_path</span> <span class="o">=</span> <span class="n">causal_path</span> <span class="o">+</span> <span class="p">[</span><span class="n">varlag</span><span class="p">]</span>
                <span class="n">suitable_nodes</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_get_children</span><span class="p">(</span><span class="n">varlag</span><span class="p">)</span>
                    <span class="p">)</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="n">inside_set</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">suitable_nodes</span><span class="p">:</span>
                    <span class="n">i</span><span class="p">,</span> <span class="n">tau</span> <span class="o">=</span> <span class="n">node</span>
                    <span class="k">if</span> <span class="p">((</span><span class="o">-</span><span class="bp">self</span><span class="o">.</span><span class="n">tau_max</span> <span class="o">&lt;=</span> <span class="n">tau</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">)</span> <span class="c1"># or self.ignore_time_bounds)</span>
                        <span class="ow">and</span> <span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">causal_path</span><span class="p">):</span>

                        <span class="n">queue</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">node</span><span class="p">,</span> <span class="n">causal_path</span><span class="p">))</span> 
 
                        <span class="k">if</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">target_nodes</span><span class="p">:</span>  
                            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">mediated_through</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="ow">and</span> <span class="nb">len</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">causal_path</span><span class="p">)</span><span class="o">.</span><span class="n">intersection</span><span class="p">(</span><span class="n">mediated_through</span><span class="p">))</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                                <span class="k">continue</span>
                            <span class="k">else</span><span class="p">:</span>
                                <span class="n">all_causal_paths</span><span class="p">[</span><span class="n">w</span><span class="p">][</span><span class="n">node</span><span class="p">]</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">causal_path</span> <span class="o">+</span> <span class="p">[</span><span class="n">node</span><span class="p">])</span> 

        <span class="k">return</span> <span class="n">all_causal_paths</span>

<div class="viewcode-block" id="Graphs.get_dict_from_graph">
<a class="viewcode-back" href="../../index.html#tigramite.graphs.Graphs.get_dict_from_graph">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_dict_from_graph</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">parents_only</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to convert graph to dictionary of links.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ---------</span>
<span class="sd">        graph : array of shape (N, N, tau_max+1)</span>
<span class="sd">            Matrix format of graph in string format.</span>

<span class="sd">        parents_only : bool</span>
<span class="sd">            Whether to only return parents (&#39;--&gt;&#39; in graph)</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        links : dict</span>
<span class="sd">            Dictionary of form {0:{(0, -1): o-o, ...}, 1:{...}, ...}.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">N</span> <span class="o">=</span> <span class="n">graph</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>

        <span class="n">links</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">([(</span><span class="n">j</span><span class="p">,</span> <span class="p">{})</span> <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">)])</span>

        <span class="k">if</span> <span class="n">parents_only</span><span class="p">:</span>
            <span class="k">for</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">tau</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">graph</span><span class="o">==</span><span class="s1">&#39;--&gt;&#39;</span><span class="p">)):</span>
                <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">][(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">)]</span> <span class="o">=</span> <span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span><span class="n">j</span><span class="p">,</span><span class="n">tau</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">for</span> <span class="p">(</span><span class="n">i</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">tau</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">(</span><span class="o">*</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">graph</span><span class="o">!=</span><span class="s1">&#39;&#39;</span><span class="p">)):</span>
                <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">][(</span><span class="n">i</span><span class="p">,</span> <span class="o">-</span><span class="n">tau</span><span class="p">)]</span> <span class="o">=</span> <span class="n">graph</span><span class="p">[</span><span class="n">i</span><span class="p">,</span><span class="n">j</span><span class="p">,</span><span class="n">tau</span><span class="p">]</span>

        <span class="k">return</span> <span class="n">links</span></div>


<div class="viewcode-block" id="Graphs.get_graph_from_dict">
<a class="viewcode-back" href="../../index.html#tigramite.graphs.Graphs.get_graph_from_dict">[docs]</a>
    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_graph_from_dict</span><span class="p">(</span><span class="n">links</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to convert dictionary of links to graph array format.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ---------</span>
<span class="sd">        links : dict</span>
<span class="sd">            Dictionary of form {0:[((0, -1), coeff, func), ...], 1:[...], ...}.</span>
<span class="sd">            Also format {0:[(0, -1), ...], 1:[...], ...} is allowed.</span>
<span class="sd">        tau_max : int or None</span>
<span class="sd">            Maximum lag. If None, the maximum lag in links is used.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        graph : array of shape (N, N, tau_max+1)</span>
<span class="sd">            Matrix format of graph with 1 for true links and 0 else.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">def</span><span class="w"> </span><span class="nf">_get_minmax_lag</span><span class="p">(</span><span class="n">links</span><span class="p">):</span>
<span class="w">            </span><span class="sd">&quot;&quot;&quot;Helper function to retrieve tau_min and tau_max from links.</span>
<span class="sd">            &quot;&quot;&quot;</span>

            <span class="n">N</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">links</span><span class="p">)</span>

            <span class="c1"># Get maximum time lag</span>
            <span class="n">min_lag</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">inf</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">N</span><span class="p">):</span>
                <span class="k">for</span> <span class="n">link_props</span> <span class="ow">in</span> <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">link_props</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">2</span><span class="p">:</span>
                        <span class="n">var</span><span class="p">,</span> <span class="n">lag</span> <span class="o">=</span> <span class="n">link_props</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
                        <span class="n">coeff</span> <span class="o">=</span> <span class="n">link_props</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
                        <span class="c1"># func = link_props[2]</span>
                        <span class="k">if</span> <span class="n">coeff</span> <span class="o">!=</span> <span class="mf">0.</span><span class="p">:</span>
                            <span class="n">min_lag</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">min_lag</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">lag</span><span class="p">))</span>
                            <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">max_lag</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">lag</span><span class="p">))</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="n">var</span><span class="p">,</span> <span class="n">lag</span> <span class="o">=</span> <span class="n">link_props</span>
                        <span class="n">min_lag</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">min_lag</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">lag</span><span class="p">))</span>
                        <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">max_lag</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">lag</span><span class="p">))</span>   

            <span class="k">return</span> <span class="n">min_lag</span><span class="p">,</span> <span class="n">max_lag</span>

        <span class="n">N</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">links</span><span class="p">)</span>

        <span class="c1"># Get maximum time lag</span>
        <span class="n">min_lag</span><span class="p">,</span> <span class="n">max_lag</span> <span class="o">=</span> <span class="n">_get_minmax_lag</span><span class="p">(</span><span class="n">links</span><span class="p">)</span>

        <span class="c1"># Set maximum lag</span>
        <span class="k">if</span> <span class="n">tau_max</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">tau_max</span> <span class="o">=</span> <span class="n">max_lag</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">max_lag</span> <span class="o">&gt;</span> <span class="n">tau_max</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;tau_max is smaller than maximum lag = </span><span class="si">%d</span><span class="s2"> &quot;</span>
                                 <span class="s2">&quot;found in links, use tau_max=None or larger &quot;</span>
                                 <span class="s2">&quot;value&quot;</span> <span class="o">%</span> <span class="n">max_lag</span><span class="p">)</span>

        <span class="n">graph</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">N</span><span class="p">,</span> <span class="n">N</span><span class="p">,</span> <span class="n">tau_max</span> <span class="o">+</span> <span class="mi">1</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;&lt;U3&#39;</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="n">links</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span>
            <span class="k">for</span> <span class="n">link_props</span> <span class="ow">in</span> <span class="n">links</span><span class="p">[</span><span class="n">j</span><span class="p">]:</span>
                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">link_props</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">2</span><span class="p">:</span>
                    <span class="n">var</span><span class="p">,</span> <span class="n">lag</span> <span class="o">=</span> <span class="n">link_props</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
                    <span class="n">coeff</span> <span class="o">=</span> <span class="n">link_props</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
                    <span class="k">if</span> <span class="n">coeff</span> <span class="o">!=</span> <span class="mf">0.</span><span class="p">:</span>
                        <span class="n">graph</span><span class="p">[</span><span class="n">var</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">lag</span><span class="p">)]</span> <span class="o">=</span> <span class="s2">&quot;--&gt;&quot;</span>
                        <span class="k">if</span> <span class="n">lag</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                            <span class="n">graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">var</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;&lt;--&quot;</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">var</span><span class="p">,</span> <span class="n">lag</span> <span class="o">=</span> <span class="n">link_props</span>
                    <span class="n">graph</span><span class="p">[</span><span class="n">var</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="nb">abs</span><span class="p">(</span><span class="n">lag</span><span class="p">)]</span> <span class="o">=</span> <span class="s2">&quot;--&gt;&quot;</span>
                    <span class="k">if</span> <span class="n">lag</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                        <span class="n">graph</span><span class="p">[</span><span class="n">j</span><span class="p">,</span> <span class="n">var</span><span class="p">,</span> <span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;&lt;--&quot;</span>

        <span class="k">return</span> <span class="n">graph</span></div>
</div>



<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    
    <span class="c1"># Consider some toy data</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.toymodels.structural_causal_processes</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">toys</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.data_processing</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">pp</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">tigramite.plotting</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">tp</span>
    <span class="kn">from</span><span class="w"> </span><span class="nn">matplotlib</span><span class="w"> </span><span class="kn">import</span> <span class="n">pyplot</span> <span class="k">as</span> <span class="n">plt</span>
    <span class="kn">import</span><span class="w"> </span><span class="nn">sys</span>

    <span class="c1"># # Use staticmethod to get graph</span>
    <span class="n">graph</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([[</span><span class="s1">&#39;&#39;</span><span class="p">,</span> <span class="s1">&#39;--&gt;&#39;</span><span class="p">],</span>
                      <span class="p">[</span><span class="s1">&#39;&lt;--&#39;</span><span class="p">,</span> <span class="s1">&#39;&#39;</span><span class="p">]],</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;&lt;U3&#39;</span><span class="p">)</span>
    

    <span class="c1"># # Initialize class as `stationary_dag`</span>
    <span class="n">causal_effects</span> <span class="o">=</span> <span class="n">Graphs</span><span class="p">(</span><span class="n">graph</span><span class="p">,</span> <span class="n">graph_type</span><span class="o">=</span><span class="s1">&#39;dag&#39;</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                                <span class="n">verbosity</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../index.html">Documentation overview</a><ul>
  <li><a href="../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &#169;2023, Jakob Runge.
      
      |
      Powered by <a href="https://www.sphinx-doc.org/">Sphinx 8.2.3</a>
      &amp; <a href="https://alabaster.readthedocs.io">Alabaster 0.7.16</a>
      
    </div>

    

    
  </body>
</html>