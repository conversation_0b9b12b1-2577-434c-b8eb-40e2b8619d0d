#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jupyter Notebook 翻译脚本
将英文的 Jupyter Notebook 文件翻译为中文
"""

import json
import os
import re
from pathlib import Path
import time

# 翻译字典 - 常见的技术术语和短语
TRANSLATION_DICT = {
    # 标题和章节
    "Causal discovery": "因果发现",
    "Causal Discovery": "因果发现",
    "Basic usage": "基本用法",
    "Plotting": "绘图",
    "Integrating": "整合",
    "expert assumptions": "专家假设",
    "assumptions about links": "关于链接的假设",
    "Benchmarking and validation": "基准测试和验证",
    "Causal effect estimation": "因果效应估计",
    "Dataset challenges": "数据集挑战",
    "Sliding window analysis": "滑动窗口分析",
    "Climate case study": "气候案例研究",
    "case study": "案例研究",
    "Case study": "案例研究",

    # 技术术语
    "time series": "时间序列",
    "Time series": "时间序列",
    "conditional independence": "条件独立性",
    "graphical models": "图形模型",
    "PCMCI framework": "PCMCI框架",
    "causal network": "因果网络",
    "causal inference": "因果推断",
    "correlation": "相关性",
    "causation": "因果关系",
    "lag": "滞后",
    "significance": "显著性",
    "p-value": "p值",
    "confidence interval": "置信区间",
    "bootstrap": "自助法",
    "aggregation": "聚合",
    "heteroskedastic": "异方差",
    "latent": "潜在",
    "pairwise": "成对",
    "multiple": "多重",
    "missing": "缺失",
    "masking": "掩码",
    "regime": "状态",
    "mediation": "中介",
    "linear": "线性",
    "general": "一般",
    "prediction": "预测",
    "feature learning": "特征学习",
    "climate science": "气候科学",
    "biogeosciences": "生物地球科学",
    "teleconnections": "遥相关",
    "circulation": "环流",

    # 常见短语和句子
    "This tutorial": "本教程",
    "explains the main features": "解释了主要功能",
    "walk-through examples": "演示示例",
    "It covers": "它涵盖了",
    "See the following paper": "请参阅以下论文",
    "theoretical background": "理论背景",
    "provides an overview": "提供了概述",
    "in general": "总体而言",
    "Last": "最后",
    "following": "以下",
    "paper": "论文",
    "overview": "概述",
    "example": "示例",
    "Example": "示例",
    "imports": "导入",
    "Imports": "导入",
    "import": "导入",
    "Import": "导入",
    "The two case studies": "这两个案例研究",
    "follow the": "遵循",
    "method-selection": "方法选择",
    "flow chart": "流程图",
    "Review paper": "综述论文",
    "A list of methods": "方法列表",
    "software": "软件",
    "address": "解决",
    "selected": "选定的",
    "problems": "问题",
    "appears": "出现",
    "at the end": "在末尾",
    "considers": "考虑",
    "where the": "其中",
    "physical mechanisms": "物理机制",
    "reasonably well understood": "相当好理解",
    "in terms of": "就...而言",
    "so-called": "所谓的",
    "more detailed discussion": "更详细的讨论",
    "The focus is on": "重点关注",
    "This branch": "这个分支",
    "describes": "描述",
    "large-scale": "大尺度",
    "time-mean": "时间平均",
    "falling air masses": "下沉气团",
    "surface": "表面",
    "rising air masses": "上升气团",
    "as illustrated": "如图所示",
    "schematic": "示意图",
    "is known to": "已知",
    "exhibit": "表现出",
    "regime-dependence": "状态依赖性",
    "strengthens": "加强",
    "during": "在...期间",
    "phases": "阶段",
    "defined by": "定义为",
    "anomalously": "异常地",
    "cool": "冷",
    "warm": "暖",
    "ocean temperatures": "海洋温度",
    "weakens": "减弱",
    "reverses": "逆转",
    "shifts": "移动",
    "eastward": "向东",
    "In addition": "此外",
    "also": "也",
    "seasonal": "季节性",
    "strength": "强度",
    "peaking": "达到峰值",
    "winter": "冬季",
    "below": "下面的",
    "analysis": "分析",
    "aims to": "旨在",
    "gain": "获得",
    "understanding": "理解",
    "anomalous": "异常的",
    "winter-time": "冬季",
    "neutral": "中性",
    "Let's start": "让我们开始",
    "with some": "一些",
    "standard": "标准",
    "python packages": "Python包",
    "as well as": "以及",
    "package": "包",

    # 代码注释常见词汇
    "Load": "加载",
    "Generate": "生成",
    "Create": "创建",
    "Plot": "绘制",
    "Run": "运行",
    "Test": "测试",
    "Note": "注意",
    "Warning": "警告",
    "Error": "错误",
    "Output": "输出",
    "Input": "输入",
    "Parameters": "参数",
    "Returns": "返回",
    "Function": "函数",
    "Class": "类",
    "Method": "方法",
    "Variable": "变量",
    "Data": "数据",
    "Dataset": "数据集",
    "Model": "模型",
    "Algorithm": "算法",
    "Result": "结果",
    "Results": "结果",
    "Analysis": "分析",
    "Visualization": "可视化",
    "Configuration": "配置",
    "Settings": "设置",
    "Options": "选项",
}

def translate_text(text):
    """
    翻译文本，使用简单的字典替换
    """
    if not text or not isinstance(text, str):
        return text

    # 跳过代码块（以```开头的行）
    if text.strip().startswith('```') or text.strip().startswith('`'):
        return text

    # 跳过纯代码行（包含常见编程关键字）
    code_keywords = ['import ', 'from ', 'def ', 'class ', 'if ', 'for ', 'while ', 'try:', 'except:', '=', 'print(', 'return ']
    if any(keyword in text for keyword in code_keywords):
        return text

    translated = text

    # 应用翻译字典
    for english, chinese in TRANSLATION_DICT.items():
        # 使用正则表达式进行单词边界匹配，避免部分匹配
        pattern = r'\b' + re.escape(english) + r'\b'
        translated = re.sub(pattern, chinese, translated, flags=re.IGNORECASE)

    return translated

def translate_notebook(notebook_path):
    """
    翻译单个 Jupyter Notebook 文件
    """
    print(f"正在翻译: {notebook_path}")

    try:
        # 读取notebook文件
        with open(notebook_path, 'r', encoding='utf-8') as f:
            notebook = json.load(f)

        # 遍历所有单元格
        for cell in notebook.get('cells', []):
            if cell.get('cell_type') == 'markdown':
                # 翻译markdown单元格的内容
                if 'source' in cell:
                    if isinstance(cell['source'], list):
                        cell['source'] = [translate_text(line) for line in cell['source']]
                    else:
                        cell['source'] = translate_text(cell['source'])

            elif cell.get('cell_type') == 'code':
                # 翻译代码单元格中的注释
                if 'source' in cell:
                    if isinstance(cell['source'], list):
                        translated_source = []
                        for line in cell['source']:
                            # 只翻译注释行（以#开头）
                            if line.strip().startswith('#'):
                                translated_source.append(translate_text(line))
                            else:
                                translated_source.append(line)
                        cell['source'] = translated_source
                    else:
                        # 处理单行源码
                        if cell['source'].strip().startswith('#'):
                            cell['source'] = translate_text(cell['source'])

        # 保存翻译后的文件
        with open(notebook_path, 'w', encoding='utf-8') as f:
            json.dump(notebook, f, ensure_ascii=False, indent=1)

        print(f"翻译完成: {notebook_path}")
        return True

    except Exception as e:
        print(f"翻译失败 {notebook_path}: {str(e)}")
        return False

def main():
    """
    主函数：遍历所有notebook文件并翻译
    """
    base_dir = Path("tutorials_chinese")

    if not base_dir.exists():
        print("错误：tutorials_chinese 文件夹不存在")
        return

    # 查找所有.ipynb文件
    notebook_files = list(base_dir.rglob("*.ipynb"))

    if not notebook_files:
        print("未找到任何 .ipynb 文件")
        return

    print(f"找到 {len(notebook_files)} 个 notebook 文件")

    success_count = 0
    for notebook_file in notebook_files:
        if translate_notebook(notebook_file):
            success_count += 1
        time.sleep(0.1)  # 短暂延迟避免过快处理

    print(f"\n翻译完成！成功翻译 {success_count}/{len(notebook_files)} 个文件")

if __name__ == "__main__":
    main()