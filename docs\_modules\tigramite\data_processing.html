
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.data_processing &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/alabaster.css" />
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/jquery.js"></script>
    <script src="../../_static/underscore.js"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../_static/doctools.js"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
   
  <link rel="stylesheet" href="../../_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.data_processing</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite data processing functions.&quot;&quot;&quot;</span>

<span class="c1"># Authors: <AUTHORS>
<span class="c1">#          Andreas Gerhardus &lt;<EMAIL>&gt;</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">print_function</span>
<span class="kn">from</span> <span class="nn">collections</span> <span class="kn">import</span> <span class="n">defaultdict</span><span class="p">,</span> <span class="n">OrderedDict</span>
<span class="kn">import</span> <span class="nn">sys</span>
<span class="kn">import</span> <span class="nn">warnings</span>
<span class="kn">from</span> <span class="nn">copy</span> <span class="kn">import</span> <span class="n">deepcopy</span>
<span class="kn">import</span> <span class="nn">math</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>
<span class="kn">import</span> <span class="nn">scipy.sparse</span>
<span class="kn">import</span> <span class="nn">scipy.sparse.linalg</span>
<span class="kn">from</span> <span class="nn">scipy</span> <span class="kn">import</span> <span class="n">stats</span>
<span class="kn">from</span> <span class="nn">numba</span> <span class="kn">import</span> <span class="n">jit</span>

<div class="viewcode-block" id="DataFrame"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.DataFrame">[docs]</a><span class="k">class</span> <span class="nc">DataFrame</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Data object containing single or multiple time series arrays and optional </span>
<span class="sd">    mask, as well as variable definitions.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    data : array-like</span>
<span class="sd">        if analysis_mode == &#39;single&#39;:</span>
<span class="sd">         Numpy array of shape (observations T, variables N)</span>
<span class="sd">         OR</span>
<span class="sd">         Dictionary with a single entry whose value is a numpy array of</span>
<span class="sd">         shape (observations T, variables N)</span>
<span class="sd">        if analysis_mode == &#39;multiple&#39;:</span>
<span class="sd">         Numpy array of shape (multiple datasets M, observations T,</span>
<span class="sd">         variables N)</span>
<span class="sd">         OR</span>
<span class="sd">         Dictionary whose values are numpy arrays of shape</span>
<span class="sd">         (observations T_i, variables N), where the number of observations</span>
<span class="sd">         T_i may vary across the multiple datasets but the number of variables</span>
<span class="sd">         N is fixed. </span>
<span class="sd">    mask : array-like, optional (default: None)</span>
<span class="sd">        Optional mask array, must be of same format and shape as data.</span>
<span class="sd">    data_type : array-like</span>
<span class="sd">        Binary data array of same shape as array which describes whether </span>
<span class="sd">        individual samples in a variable (or all samples) are continuous </span>
<span class="sd">        or discrete: 0s for continuous variables and 1s for discrete variables.</span>
<span class="sd">    missing_flag : number, optional (default: None)</span>
<span class="sd">        Flag for missing values in dataframe. Dismisses all time slices of</span>
<span class="sd">        samples where missing values occur in any variable. For</span>
<span class="sd">        remove_missing_upto_maxlag=True also flags samples for all lags up to</span>
<span class="sd">        2*tau_max (more precisely, this depends on the cut_off argument in</span>
<span class="sd">        self.construct_array(), see further below). This avoids biases, see</span>
<span class="sd">        section on masking in Supplement of Runge et al. SciAdv (2019).</span>
<span class="sd">    vector_vars : dict</span>
<span class="sd">        Dictionary of vector variables of the form,</span>
<span class="sd">        Eg. {0: [(0, 0), (1, 0)], 1: [(2, 0)], 2: [(3, 0)], 3: [(4, 0)]}</span>
<span class="sd">        The keys are the new vectorized variables and respective tuple values</span>
<span class="sd">        are the individual components of the vector variables. In the method of</span>
<span class="sd">        construct_array(), the individual components are parsed from vector_vars</span>
<span class="sd">        and added (accounting for lags) to the list that creates X, Y and Z for</span>
<span class="sd">        conditional independence test.</span>
<span class="sd">    var_names : list of strings, optional (default: range(N))</span>
<span class="sd">        Names of variables, must match the number of variables. If None is</span>
<span class="sd">        passed, variables are enumerated as [0, 1, ...]</span>
<span class="sd">    datatime : array-like, optional (default: None)</span>
<span class="sd">        Timelabel array. If None, range(T) is used.</span>
<span class="sd">    remove_missing_upto_maxlag : bool, optional (default: False)</span>
<span class="sd">        Whether to remove not only missing samples, but also all neighboring</span>
<span class="sd">        samples up to max_lag (as given by cut_off in construct_array).</span>
<span class="sd">    analysis_mode : string, optional (default: &#39;single&#39;)</span>
<span class="sd">        Must be &#39;single&#39; or &#39;multiple&#39;.</span>
<span class="sd">        Determines whether data contains a single (potentially multivariate)</span>
<span class="sd">        time series (--&gt; &#39;single&#39;) or multiple time series (--&gt; &#39;multiple&#39;).</span>
<span class="sd">    reference_points : None, int, or list (or 1D array) of integers,</span>
<span class="sd">        optional (default:None)</span>
<span class="sd">        Determines the time steps --- relative to the shared time axis as</span>
<span class="sd">        defined by the optional time_offset argument (see below) --- that are</span>
<span class="sd">        used to create samples for conditional independence testing.</span>
<span class="sd">        Set to [0, 1, ..., T_max-1] if None is passed, where T_max is</span>
<span class="sd">        self.largest_time_step, see below.</span>
<span class="sd">        All values smaller than 0 and bigger than T_max-1 will be ignored.</span>
<span class="sd">        At least one value must be in [0, 1, ..., T_max-1].</span>
<span class="sd">    time_offsets : None or dict, optional (default: None)</span>
<span class="sd">        if analysis_mode == &#39;single&#39;:</span>
<span class="sd">         Must be None.</span>
<span class="sd">         Shared time axis defined by the time indices of the single time series</span>
<span class="sd">        if analysis_mode == &#39;multiple&#39; and data is numpy array:</span>
<span class="sd">         Must be None.</span>
<span class="sd">         All datasets are assumed to be already aligned in time with</span>
<span class="sd">         respect to a shared time axis, which is the time axis of data</span>
<span class="sd">        if analysis_mode == &#39;multiple&#39; and data is dictionary:</span>
<span class="sd">         Must be dictionary of the form {key(m): time_offset(m), ...} whose</span>
<span class="sd">         set of keys agrees with the set of keys of data and whose values are</span>
<span class="sd">         non-negative integers, at least one of which is 0. The value</span>
<span class="sd">         time_offset(m) defines the time offset of dataset m with</span>
<span class="sd">         respect to a shared time axis.</span>

<span class="sd">    Attributes</span>
<span class="sd">    ----------</span>
<span class="sd">    self._initialized_from : string</span>
<span class="sd">        Specifies the data format in which data was given at instantiation.</span>
<span class="sd">        Possible values: &#39;2d numpy array&#39;, &#39;3d numpy array&#39;, &#39;dict&#39;.</span>
<span class="sd">    self.values : dictionary</span>
<span class="sd">        Dictionary holding the observations given by data internally mapped to a</span>
<span class="sd">        dictionary representation as follows:</span>
<span class="sd">        If analysis_mode == &#39;single&#39;: for self._initialized_from == &#39;2d numpy array&#39; this</span>
<span class="sd">        is {0: data} and for self._initialized_from == &#39;dict&#39; this is data.</span>
<span class="sd">        If analysis_mode == &#39;multiple&#39;: If self._initialized_from == &#39;3d numpy array&#39;, this is</span>
<span class="sd">        {m: data[m, :, :] for m in range(data.shape[0])} and for self._initialized_from == &#39;dict&#39; this</span>
<span class="sd">        is data.</span>
<span class="sd">    self.datasets: list</span>
<span class="sd">        List of the keys identifiying the multiple datasets, i.e.,</span>
<span class="sd">        list(self.values.keys())</span>
<span class="sd">    self.mask : dictionary</span>
<span class="sd">        Mask internally mapped to a dictionary representation in the same way as</span>
<span class="sd">        data is mapped to self.values</span>
<span class="sd">    self.data_type : array-like</span>
<span class="sd">        Binary data array of same shape as array which describes whether </span>
<span class="sd">        individual samples in a variable (or all samples) are continuous </span>
<span class="sd">        or discrete: 0s for continuous variables and 1s for discrete variables.</span>
<span class="sd">    self.missing_flag:</span>
<span class="sd">        Is missing_flag</span>
<span class="sd">    self.var_names:</span>
<span class="sd">        If var_names is not None:</span>
<span class="sd">            Is var_names</span>
<span class="sd">        If var_names is None:</span>
<span class="sd">            Is {i: i for i in range(self.N)}</span>
<span class="sd">    self.datatime : dictionary</span>
<span class="sd">        Time axis for each of the multiple datasets.</span>
<span class="sd">    self.analysis_mode : string</span>
<span class="sd">        Is analysis_mode</span>
<span class="sd">    self.reference_points: array-like</span>
<span class="sd">        If reference_points is not None:</span>
<span class="sd">            1D numpy array holding all specified reference_points, less those</span>
<span class="sd">            smaller than 0 and larger than self.largest_time_step-1</span>
<span class="sd">        If reference_points is None:</span>
<span class="sd">            Is np.array(self.largest_time_step)</span>
<span class="sd">    self.time_offsets : dictionary</span>
<span class="sd">        If time_offsets is not None:</span>
<span class="sd">            Is time_offsets</span>
<span class="sd">        If time_offsets is None:</span>
<span class="sd">            Is {key: 0 for key in self.values.keys()}</span>
<span class="sd">    self.M : int</span>
<span class="sd">        Number of datasets</span>
<span class="sd">    self.N : int</span>
<span class="sd">        Number of variables (constant across datasets)</span>
<span class="sd">    self.T : dictionary</span>
<span class="sd">        Dictionary {key(m): T(m), ...}, where T(m) is the time length of</span>
<span class="sd">        datasets m and key(m) its identifier as in self.values</span>
<span class="sd">    self.largest_time_step : int</span>
<span class="sd">        max_{0 &lt;= m &lt;= M} [ T(m) + time_offset(m)], i.e., the largest (latest)</span>
<span class="sd">        time step relative to the shared time axis for which at least one</span>
<span class="sd">        observation exists in the dataset.</span>
<span class="sd">    self.bootstrap : dictionary</span>
<span class="sd">        Whether to use bootstrap. Must be a dictionary with keys random_state,</span>
<span class="sd">        boot_samples, and boot_blocklength.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">,</span> <span class="n">mask</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">missing_flag</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">vector_vars</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">var_names</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">datatime</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">analysis_mode</span> <span class="o">=</span><span class="s1">&#39;single&#39;</span><span class="p">,</span> <span class="n">reference_points</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
        <span class="n">time_offsets</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">remove_missing_upto_maxlag</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>

        <span class="c1"># Check that a valid analysis mode, specified by the argument</span>
        <span class="c1"># &#39;analysis_mode&#39;, has been chosen</span>
        <span class="k">if</span> <span class="n">analysis_mode</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;single&#39;</span><span class="p">,</span> <span class="s1">&#39;multiple&#39;</span><span class="p">]:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">=</span> <span class="n">analysis_mode</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;&#39;analysis_mode&#39; is &#39;</span><span class="si">{}</span><span class="s2">&#39;, must be &#39;single&#39; or &quot;</span>\
                <span class="s2">&quot;&#39;multiple&#39;.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">analysis_mode</span><span class="p">))</span>

        <span class="c1"># Check for correct type and format of &#39;data&#39;, internally cast to the</span>
        <span class="c1"># analysis mode &#39;multiple&#39; case in dictionary representation</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">==</span> <span class="s1">&#39;single&#39;</span><span class="p">:</span>
            <span class="c1"># In this case the &#39;time_offset&#39; functionality must not be used</span>
            <span class="k">if</span> <span class="n">time_offsets</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;&#39;time_offsets&#39; must be None in analysis &quot;</span>\
                    <span class="s2">&quot;mode&#39;single&#39;.&quot;</span><span class="p">)</span>

            <span class="c1"># &#39;data&#39; must be either</span>
            <span class="c1"># - np.ndarray of shape (T, N)</span>
            <span class="c1"># - np.ndarray of shape (1, T, N)</span>
            <span class="c1"># - a dictionary with one element whose value is a np.ndarray of</span>
            <span class="c1"># shape (T, N)</span>
            
            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">):</span>
                <span class="n">_data_shape</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">shape</span>
                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">_data_shape</span><span class="p">)</span> <span class="o">==</span> <span class="mi">2</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">values</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">copy</span><span class="p">(</span><span class="n">data</span><span class="p">)}</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_initialized_from</span> <span class="o">=</span> <span class="s2">&quot;2d numpy array&quot;</span>
                <span class="k">elif</span> <span class="nb">len</span><span class="p">(</span><span class="n">_data_shape</span><span class="p">)</span> <span class="o">==</span> <span class="mi">3</span> <span class="ow">and</span> <span class="n">_data_shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">values</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">copy</span><span class="p">(</span><span class="n">data</span><span class="p">[</span><span class="mi">0</span><span class="p">,</span> <span class="p">:,</span> <span class="p">:])}</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_initialized_from</span> <span class="o">=</span> <span class="s2">&quot;3d numpy array&quot;</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;single&#39;, &#39;data&#39; given &quot;</span>\
                        <span class="s2">&quot;as np.ndarray. &#39;data&#39; is of shape </span><span class="si">{}</span><span class="s2">, must be of &quot;</span>\
                        <span class="s2">&quot;shape (T, N) or (1, T, N).&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">_data_shape</span><span class="p">))</span>

            <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                    <span class="n">_data</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="nb">iter</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">values</span><span class="p">()))</span>
                    <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">_data</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">):</span>
                        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">_data</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span> <span class="o">==</span> <span class="mi">2</span><span class="p">:</span>
                            <span class="bp">self</span><span class="o">.</span><span class="n">values</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
                            <span class="bp">self</span><span class="o">.</span><span class="n">_initialized_from</span> <span class="o">=</span> <span class="s2">&quot;dict&quot;</span>
                        <span class="k">else</span><span class="p">:</span>
                            <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;single&#39;, &quot;</span>\
                                <span class="s2">&quot;&#39;data&#39;given as dictionary. The single value &quot;</span>\
                                <span class="s2">&quot;is a np.ndarray of shape </span><span class="si">{}</span><span class="s2">, must be of &quot;</span>\
                                <span class="s2">&quot;shape (T, N).&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">_data</span><span class="o">.</span><span class="n">shape</span><span class="p">))</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;single&#39;, &#39;data&#39; &quot;</span>\
                            <span class="s2">&quot;given as dictionary. The single value is of type &quot;</span>\
                            <span class="s2">&quot;</span><span class="si">{}</span><span class="s2">, must be np.ndarray.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">_data</span><span class="p">)))</span>

                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;single&#39;, &#39;data&#39; given &quot;</span>\
                        <span class="s2">&quot;as dictionary. There are </span><span class="si">{}</span><span class="s2"> entries in &#39;data&#39;, there &quot;</span>\
                        <span class="s2">&quot;must be exactly one entry.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">)))</span>

            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;single&#39;. &#39;data&#39; is of type &quot;</span>\
                    <span class="s2">&quot;</span><span class="si">{}</span><span class="s2">, must be np.ndarray or dict.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">data</span><span class="p">)))</span>

        <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">==</span> <span class="s1">&#39;multiple&#39;</span><span class="p">:</span>
            <span class="c1"># &#39;data&#39; must either be a</span>
            <span class="c1"># - np.ndarray of shape (M, T, N)</span>
            <span class="c1"># - dict whose values of are np.ndarray of shape (T_i, N), where T_i</span>
            <span class="c1"># may vary across the values</span>

            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">):</span>
                <span class="n">_data_shape</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">shape</span>
                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">_data_shape</span><span class="p">)</span> <span class="o">==</span> <span class="mi">3</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">values</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">copy</span><span class="p">(</span><span class="n">data</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="p">:,</span> <span class="p">:])</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">_data_shape</span><span class="p">[</span><span class="mi">0</span><span class="p">])}</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_initialized_from</span> <span class="o">=</span> <span class="s2">&quot;3d numpy array&quot;</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;multiple&#39;, &#39;data&#39; &quot;</span>\
                        <span class="s2">&quot;given as np.ndarray. &#39;data&#39; is of shape </span><span class="si">{}</span><span class="s2">, must be &quot;</span>\
                        <span class="s2">&quot;of shape (M, T, N).&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">_data_shape</span><span class="p">))</span>

                <span class="c1"># In this case the &#39;time_offset&#39; functionality must not be used</span>
                <span class="k">if</span> <span class="n">time_offsets</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;multiple&#39;. Since &quot;</span>\
                        <span class="s2">&quot;&#39;data&#39; is given as np.ndarray, &#39;time_offsets&#39; must &quot;</span>\
                        <span class="s2">&quot;be None.&quot;</span><span class="p">)</span>

            <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
                <span class="n">_N_list</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
                <span class="k">for</span> <span class="n">dataset_key</span><span class="p">,</span> <span class="n">dataset_data</span> <span class="ow">in</span> <span class="n">data</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
                    <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">dataset_data</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">):</span>
                        <span class="n">_dataset_data_shape</span> <span class="o">=</span> <span class="n">dataset_data</span><span class="o">.</span><span class="n">shape</span>
                        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">_dataset_data_shape</span><span class="p">)</span> <span class="o">==</span> <span class="mi">2</span><span class="p">:</span>
                            <span class="n">_N_list</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">_dataset_data_shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
                        <span class="k">else</span><span class="p">:</span>
                            <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;multiple&#39;, &quot;</span>\
                                <span class="s2">&quot;&#39;data&#39; given as dictionary. &#39;data&#39;[</span><span class="si">{}</span><span class="s2">] is of &quot;</span>\
                                <span class="s2">&quot;shape </span><span class="si">{}</span><span class="s2">, must be of shape (T_i, N).&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span>
                                    <span class="n">dataset_key</span><span class="p">,</span> <span class="n">_dataset_data_shape</span><span class="p">))</span>

                    <span class="k">else</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;multiple&#39;, &#39;data&#39; &quot;</span>\
                            <span class="s2">&quot;given as dictionary. &#39;data&#39;[</span><span class="si">{}</span><span class="s2">] is of type </span><span class="si">{}</span><span class="s2">, &quot;</span>\
                            <span class="s2">&quot;must be np.ndarray.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">dataset_key</span><span class="p">,</span>
                                <span class="nb">type</span><span class="p">(</span><span class="n">dataset_data</span><span class="p">)))</span>

                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">_N_list</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">values</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">copy</span><span class="p">()</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">_initialized_from</span> <span class="o">=</span> <span class="s2">&quot;dict&quot;</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;multiple&#39;, &#39;data&#39; &quot;</span>\
                        <span class="s2">&quot;given as dictionary. All entries must be np.ndarrays &quot;</span>\
                        <span class="s2">&quot;of shape (T_i, N), where T_i may vary across the &quot;</span>\
                        <span class="s2">&quot;entries while N must not vary. In the given &#39;data&#39; N &quot;</span>\
                        <span class="s2">&quot;varies.&quot;</span><span class="p">)</span>

            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;multiple&#39;. &#39;data&#39; is of &quot;</span>\
                    <span class="s2">&quot;type </span><span class="si">{}</span><span class="s2">, must be np.ndarray or dict.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">data</span><span class="p">)))</span>

        <span class="c1"># Store the keys of the datasets in a separated attribute</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">datasets</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>

        <span class="c1"># Save the data format and check for NaNs:</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">M</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="p">)</span> <span class="c1"># (Number of datasets)</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">T</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span> <span class="c1"># (Time lengths of the individual datasets)</span>
        <span class="k">for</span> <span class="n">dataset_key</span><span class="p">,</span> <span class="n">dataset_data</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">dataset_data</span><span class="p">)</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;NaNs in the data.&quot;</span><span class="p">)</span>

            <span class="n">_dataset_data_shape</span> <span class="o">=</span> <span class="n">dataset_data</span><span class="o">.</span><span class="n">shape</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">T</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span> <span class="o">=</span> <span class="n">_dataset_data_shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">Ndata</span> <span class="o">=</span> <span class="n">_dataset_data_shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="c1"># (Number of variables) </span>
            <span class="c1"># N does not vary across the datasets</span>

        <span class="c1"># Setup dictionary of variables for vector mode</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">vector_vars</span> <span class="o">=</span> <span class="n">vector_vars</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">vector_vars</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">vector_vars</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">(</span><span class="nb">zip</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Ndata</span><span class="p">),</span> <span class="p">[[(</span><span class="n">i</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span> 
                                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">Ndata</span><span class="p">)]))</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">has_vector_data</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">has_vector_data</span> <span class="o">=</span> <span class="kc">True</span>


        <span class="c1"># TODO: check vector_vars!</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">N</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">vector_vars</span><span class="p">)</span>

        <span class="c1"># Warnings</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">==</span> <span class="s1">&#39;single&#39;</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">N</span> <span class="o">&gt;</span> <span class="nb">next</span><span class="p">(</span><span class="nb">iter</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">T</span><span class="o">.</span><span class="n">values</span><span class="p">())):</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;single&#39;, &#39;data&#39;.shape = (</span><span class="si">{}</span><span class="s2">, </span><span class="si">{}</span><span class="s2">);&quot;</span>\
                <span class="s2">&quot; is it of shape (observations, variables)?&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">T</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">))</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">==</span> <span class="s1">&#39;multiple&#39;</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;In analysis mode &#39;multiple&#39;. There is just a &quot;</span>\
                <span class="s2">&quot;single dataset, is this as intended?&#39;&quot;</span><span class="p">)</span>


        <span class="c1"># Save the variable names. If unspecified, use the default</span>
        <span class="k">if</span> <span class="n">var_names</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">var_names</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="n">i</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">N</span><span class="p">)}</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">var_names</span> <span class="o">=</span> <span class="n">var_names</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">mask</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="n">mask</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">mask</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_mask</span><span class="p">(</span><span class="n">mask</span> <span class="o">=</span> <span class="n">mask</span><span class="p">)</span>
            
        <span class="bp">self</span><span class="o">.</span><span class="n">data_type</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="k">if</span> <span class="n">data_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">data_type</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_mask</span><span class="p">(</span><span class="n">mask</span> <span class="o">=</span> <span class="n">data_type</span><span class="p">,</span> <span class="n">check_data_type</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

        <span class="c1"># Check and prepare the time offsets</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_check_and_set_time_offsets</span><span class="p">(</span><span class="n">time_offsets</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">time_offsets_is_none</span> <span class="o">=</span> <span class="n">time_offsets</span> <span class="ow">is</span> <span class="kc">None</span>

        <span class="c1"># Set the default datatime if unspecified</span>
        <span class="k">if</span> <span class="n">datatime</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">datatime</span> <span class="o">=</span> <span class="p">{</span><span class="n">m</span><span class="p">:</span> <span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span><span class="p">[</span><span class="n">m</span><span class="p">],</span> 
                <span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span><span class="p">[</span><span class="n">m</span><span class="p">]</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">T</span><span class="p">[</span><span class="n">m</span><span class="p">])</span> <span class="k">for</span> <span class="n">m</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">keys</span><span class="p">()}</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">datatime</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">datatime</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="n">datatime</span><span class="p">}</span>   
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">datatime</span> <span class="o">=</span> <span class="n">datatime</span>

        <span class="c1"># Save the largest/smallest relevant time step</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">largest_time_step</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">T</span><span class="o">.</span><span class="n">values</span><span class="p">())),</span> <span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span><span class="o">.</span><span class="n">values</span><span class="p">())))</span><span class="o">.</span><span class="n">max</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">smallest_time_step</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">T</span><span class="o">.</span><span class="n">values</span><span class="p">())),</span> <span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span><span class="o">.</span><span class="n">values</span><span class="p">())))</span><span class="o">.</span><span class="n">min</span><span class="p">()</span>

        <span class="c1"># Check and prepare the reference points</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_check_and_set_reference_points</span><span class="p">(</span><span class="n">reference_points</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">reference_points_is_none</span> <span class="o">=</span> <span class="n">reference_points</span> <span class="ow">is</span> <span class="kc">None</span>

        <span class="c1"># Save the &#39;missing_flag&#39; value</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">missing_flag</span> <span class="o">=</span> <span class="n">missing_flag</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">missing_flag</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">for</span> <span class="n">dataset_key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">][</span><span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">missing_flag</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">nan</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">remove_missing_upto_maxlag</span> <span class="o">=</span> <span class="n">remove_missing_upto_maxlag</span>

        <span class="c1"># If PCMCI.run_bootstrap_of is called, then the</span>
        <span class="c1"># bootstrap random draw can be set here</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap</span> <span class="o">=</span> <span class="kc">None</span>


    <span class="k">def</span> <span class="nf">_check_mask</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">mask</span><span class="p">,</span> <span class="n">check_data_type</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Checks that the mask is:</span>
<span class="sd">            * The same shape as the data</span>
<span class="sd">            * Is an numpy ndarray (or subtype)</span>
<span class="sd">            * Does not contain any NaN entries</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Check that there is a mask if required</span>
        <span class="n">_use_mask</span> <span class="o">=</span> <span class="n">mask</span>

        <span class="c1"># If we have a mask, check it</span>
        <span class="k">if</span> <span class="n">_use_mask</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># Check data type and generic format of &#39;mask&#39;, map to multiple datasets mode</span>
            <span class="c1"># dictionary representation</span>
            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">_use_mask</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">):</span>
                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">_use_mask</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span> <span class="o">==</span> <span class="mi">2</span><span class="p">:</span>
                    <span class="n">_use_mask_dict</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="n">_use_mask</span><span class="p">}</span>
                <span class="k">elif</span> <span class="nb">len</span><span class="p">(</span><span class="n">_use_mask</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span> <span class="o">==</span> <span class="mi">3</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">_use_mask</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span>
                        <span class="n">_use_mask_dict</span> <span class="o">=</span> <span class="p">{</span><span class="n">i</span><span class="p">:</span> <span class="n">_use_mask</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="p">:,</span> <span class="p">:]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">)}</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Shape mismatch: </span><span class="si">{}</span><span class="s2"> datasets &quot;</span>\
                            <span class="s2">&quot; in &#39;data&#39; but </span><span class="si">{}</span><span class="s2"> in &#39;mask&#39;, must be &quot;</span>\
                            <span class="s2">&quot;identical.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">,</span> <span class="n">_use_mask</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]))</span>

                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;&#39;data&#39; given as 3d np.ndarray. &quot;</span>\
                        <span class="s2">&quot;&#39;mask&#39; is np.ndarray of shape </span><span class="si">{}</span><span class="s2">, must be of &quot;</span>\
                        <span class="s2">&quot;shape (M, T, N).&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">_use_mask</span><span class="o">.</span><span class="n">shape</span><span class="p">))</span>

            <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">_use_mask</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">_use_mask</span><span class="p">)</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">dataset_key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span>
                        <span class="k">if</span> <span class="n">_use_mask</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">dataset_key</span><span class="p">)</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;&#39;data&#39; has key </span><span class="si">{}</span><span class="s2"> (type </span><span class="si">{}</span><span class="s2">) &quot;</span>\
                                <span class="s2">&quot;but &#39;mask&#39; does not, keys must be &quot;</span>\
                                <span class="s2">&quot;identical.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">dataset_key</span><span class="p">,</span>
                                    <span class="nb">type</span><span class="p">(</span><span class="n">dataset_key</span><span class="p">)))</span>

                    <span class="n">_use_mask_dict</span> <span class="o">=</span> <span class="n">_use_mask</span>

                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Shape mismatch: </span><span class="si">{}</span><span class="s2"> datasets &quot;</span>\
                        <span class="s2">&quot;in &#39;data&#39; but </span><span class="si">{}</span><span class="s2"> in &#39;mask&#39;, must be &quot;</span>\
                        <span class="s2">&quot;identical.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">_use_mask</span><span class="p">)))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;&#39;mask&#39; is of type &quot;</span>\
                    <span class="s2">&quot;</span><span class="si">{}</span><span class="s2">, must be dict or array.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">_use_mask</span><span class="p">)))</span>

            <span class="c1"># Check for consistency with shape of &#39;self.values&#39; and for NaNs</span>
            <span class="k">for</span> <span class="n">dataset_key</span><span class="p">,</span> <span class="n">dataset_data</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
                <span class="n">_use_mask_dict_data</span> <span class="o">=</span> <span class="n">_use_mask_dict</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span> 
                <span class="k">if</span> <span class="n">_use_mask_dict_data</span><span class="o">.</span><span class="n">shape</span> <span class="o">==</span> <span class="n">dataset_data</span><span class="o">.</span><span class="n">shape</span><span class="p">:</span>
                    <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">_use_mask_dict_data</span><span class="p">))</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;NaNs in the data mask&quot;</span><span class="p">)</span>
                    <span class="k">if</span> <span class="n">check_data_type</span><span class="p">:</span>
                        <span class="k">if</span> <span class="ow">not</span> <span class="nb">set</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">unique</span><span class="p">(</span><span class="n">_use_mask_dict_data</span><span class="p">))</span><span class="o">.</span><span class="n">issubset</span><span class="p">(</span><span class="nb">set</span><span class="p">([</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">])):</span>
                            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Type mask contains other values than 0 and 1&quot;</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">==</span> <span class="s1">&#39;single&#39;</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Shape mismatch: &#39;data&#39; is of shape &quot;</span>\
                            <span class="s2">&quot;</span><span class="si">{}</span><span class="s2">, &#39;mask&#39; is of shape </span><span class="si">{}</span><span class="s2">. Must be &quot;</span>\
                            <span class="s2">&quot;identical.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">dataset_data</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span>
                                <span class="n">_use_mask_dict_data</span><span class="o">.</span><span class="n">shape</span><span class="p">))</span>
                    <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">==</span> <span class="s1">&#39;multiple&#39;</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Shape mismatch: dataset </span><span class="si">{}</span><span class="s2"> &quot;</span>\
                            <span class="s2">&quot;is of shape </span><span class="si">{}</span><span class="s2"> in &#39;data&#39; and of shape </span><span class="si">{}</span><span class="s2"> in &quot;</span>\
                            <span class="s2">&quot;&#39;mask&#39;. Must be identical.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">dataset_key</span><span class="p">,</span>
                                <span class="n">dataset_data</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span>
                                <span class="n">_use_mask_dict_data</span><span class="o">.</span><span class="n">shape</span><span class="p">))</span>

            <span class="c1"># Return the mask in dictionary format</span>
            <span class="k">return</span> <span class="n">_use_mask_dict</span>

    <span class="k">def</span> <span class="nf">_check_and_set_time_offsets</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">time_offsets</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check the argument &#39;time_offsets&#39; for consistency and bring into</span>
<span class="sd">        canonical format&quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">time_offsets</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>

            <span class="k">assert</span> <span class="bp">self</span><span class="o">.</span><span class="n">analysis_mode</span> <span class="o">==</span> <span class="s1">&#39;multiple&#39;</span>
            <span class="k">assert</span> <span class="bp">self</span><span class="o">.</span><span class="n">_initialized_from</span> <span class="o">==</span> <span class="s1">&#39;dict&#39;</span>

            <span class="c1"># Check data type and generic format of &#39;time_offsets&#39;, map to</span>
            <span class="c1"># dictionary representation</span>
            <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">time_offsets</span><span class="p">,</span> <span class="nb">dict</span><span class="p">):</span>
                <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">time_offsets</span><span class="p">)</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">:</span>
                    <span class="k">for</span> <span class="n">dataset_key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span>
                        <span class="k">if</span> <span class="n">time_offsets</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">dataset_key</span><span class="p">)</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;&#39;data&#39; has key </span><span class="si">{}</span><span class="s2"> (type </span><span class="si">{}</span><span class="s2">) but &quot;</span>\
                                <span class="s2">&quot;&#39;time_offsets&#39; does not, keys must be &quot;</span>\
                                <span class="s2">&quot;identical.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">dataset_key</span><span class="p">,</span>
                                    <span class="nb">type</span><span class="p">(</span><span class="n">dataset_key</span><span class="p">)))</span>

                    <span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span> <span class="o">=</span> <span class="n">time_offsets</span>

                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Shape mismatch: </span><span class="si">{}</span><span class="s2"> datasets in &quot;</span>\
                        <span class="s2">&quot;&#39;data&#39; but </span><span class="si">{}</span><span class="s2"> in &#39;time_offsets&#39;, must be &quot;</span>\
                        <span class="s2">&quot;identical.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">M</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">time_offsets</span><span class="p">)))</span>

            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;&#39;time_offsets&#39; is of type </span><span class="si">{}</span><span class="s2">, must be &quot;</span>\
                    <span class="s2">&quot;dict.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">time_offsets</span><span class="p">)))</span>

            <span class="c1"># All time offsets must be non-negative integers, at least one of</span>
            <span class="c1"># which is zero</span>
            <span class="n">found_zero_time_offset</span> <span class="o">=</span> <span class="kc">False</span>
            <span class="k">for</span> <span class="n">time_offset</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span><span class="o">.</span><span class="n">values</span><span class="p">():</span>
                <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">issubdtype</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">time_offset</span><span class="p">),</span> <span class="n">np</span><span class="o">.</span><span class="n">integer</span><span class="p">):</span>
                    <span class="k">if</span> <span class="n">time_offset</span> <span class="o">&gt;=</span> <span class="mi">0</span><span class="p">:</span>
                        <span class="k">if</span> <span class="n">time_offset</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                            <span class="n">found_zero_time_offset</span> <span class="o">=</span> <span class="kc">True</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;A dataset has time offset &quot;</span>\
                            <span class="s2">&quot;</span><span class="si">{}</span><span class="s2">, must be non-negative.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="n">time_offset</span><span class="p">))</span>

                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;There is a time offset of type </span><span class="si">{}</span><span class="s2">, must &quot;</span>\
                        <span class="s2">&quot;be int.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">time_offset</span><span class="p">)))</span>

            <span class="k">if</span> <span class="ow">not</span> <span class="n">found_zero_time_offset</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;At least one time offset must be 0.&quot;</span><span class="p">)</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># If no time offsets are specified, all of them are zero</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span> <span class="o">=</span> <span class="p">{</span><span class="n">dataset_key</span><span class="p">:</span> <span class="mi">0</span> <span class="k">for</span> <span class="n">dataset_key</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">keys</span><span class="p">()}</span>

    <span class="k">def</span> <span class="nf">_check_and_set_reference_points</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">reference_points</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Check the argument &#39;reference_point&#39; for consistency and bring into</span>
<span class="sd">        canonical format&quot;&quot;&quot;</span>

        <span class="c1"># Check type of &#39;reference_points&#39; and its elements</span>
        <span class="k">if</span> <span class="n">reference_points</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># If no reference point is specified, use as many reference points</span>
            <span class="c1"># as possible</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">largest_time_step</span><span class="p">)</span>

        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">reference_points</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="c1"># If a single reference point is specified as an int, convert it to</span>
            <span class="c1"># a single element numpy array</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="n">reference_points</span><span class="p">])</span>

        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">reference_points</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">):</span>
            <span class="c1"># Check that all reference points are ints</span>
            <span class="k">for</span> <span class="n">ref_point</span> <span class="ow">in</span> <span class="n">reference_points</span><span class="p">:</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="n">np</span><span class="o">.</span><span class="n">issubdtype</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">ref_point</span><span class="p">),</span> <span class="n">np</span><span class="o">.</span><span class="n">integer</span><span class="p">):</span>
                    <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;All reference points must be integers.&quot;</span><span class="p">)</span>

            <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">=</span> <span class="n">reference_points</span>

        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">reference_points</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
            <span class="c1"># Check that all reference points are ints</span>
            <span class="k">for</span> <span class="n">ref_point</span> <span class="ow">in</span> <span class="n">reference_points</span><span class="p">:</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">ref_point</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
                    <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;All reference points must be integers.&quot;</span><span class="p">)</span>

            <span class="c1"># If given as a list, cast to numpy array</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="n">reference_points</span><span class="p">)</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">TypeError</span><span class="p">(</span><span class="s2">&quot;Unsupported data type of &#39;reference_points&#39;: Is &quot;</span>\
                <span class="s2">&quot;</span><span class="si">{}</span><span class="s2">, must be None or int or a list or np.ndarray of &quot;</span>\
                <span class="s2">&quot;ints.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="nb">type</span><span class="p">(</span><span class="n">reference_points</span><span class="p">)))</span>

        <span class="c1"># Remove negative reference points</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;Some reference points were negative. These are &quot;</span>\
                <span class="s2">&quot;removed.&quot;</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">&gt;=</span> <span class="mi">0</span><span class="p">]</span>

        <span class="c1"># Remove reference points that are larger than the largest time step</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">largest_time_step</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;Some reference points were larger than the largest &quot;</span>\
                <span class="s2">&quot;relevant time step, which here is </span><span class="si">{}</span><span class="s2">. These are &quot;</span>\
                <span class="s2">&quot;removed.&quot;</span><span class="o">.</span><span class="n">format</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">largest_time_step</span> <span class="o">-</span> <span class="mi">1</span><span class="p">))</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">&lt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">largest_time_step</span><span class="p">]</span>

        <span class="c1"># Raise an error if no valid reference points was specified</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;No valid reference point.&quot;</span><span class="p">)</span> 


<div class="viewcode-block" id="DataFrame.construct_array"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.DataFrame.construct_array">[docs]</a>    <span class="k">def</span> <span class="nf">construct_array</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span>
                        <span class="n">extraZ</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                        <span class="n">mask</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                        <span class="n">mask_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                        <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                        <span class="n">return_cleaned_xyz</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                        <span class="n">do_checks</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                        <span class="n">remove_overlaps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                        <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;2xtau_max&#39;</span><span class="p">,</span>
                        <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Constructs array from variables X, Y, Z from data.</span>
<span class="sd">        Data is of shape (T, N) if analysis_mode == &#39;single&#39;, where T is the</span>
<span class="sd">        time series length and N the number of variables, and of (n_ens, T, N)</span>
<span class="sd">        if analysis_mode == &#39;multiple&#39;.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        X, Y, Z, extraZ : list of tuples</span>
<span class="sd">            For a dependence measure I(X;Y|Z), X, Y, Z can be multivariate of</span>
<span class="sd">            the form [(var1, -lag), (var2, -lag), ...]. At least one varlag in Y </span>
<span class="sd">            has to be at lag zero. extraZ is only used in CausalEffects class.</span>
<span class="sd">        tau_max : int</span>
<span class="sd">            Maximum time lag. This may be used to make sure that estimates for</span>
<span class="sd">            different lags in X and Z all have the same sample size.</span>
<span class="sd">        mask : array-like, optional (default: None)</span>
<span class="sd">            Optional mask array, must be of same shape as data.  If it is set,</span>
<span class="sd">            then it overrides the self.mask assigned to the dataframe. If it is</span>
<span class="sd">            None, then the self.mask is used, if it exists.</span>
<span class="sd">        mask_type : {None, &#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">            Masking mode: Indicators for which variables in the dependence</span>
<span class="sd">            measure I(X; Y | Z) the samples should be masked. If None, the mask</span>
<span class="sd">            is not used. Explained in tutorial on masking and missing values.</span>
<span class="sd">        data_type : array-like</span>
<span class="sd">            Binary data array of same shape as array which describes whether </span>
<span class="sd">            individual samples in a variable (or all samples) are continuous </span>
<span class="sd">            or discrete: 0s for continuous variables and 1s for discrete variables.</span>
<span class="sd">            If it is set, then it overrides the self.data_type assigned to the dataframe.</span>
<span class="sd">        return_cleaned_xyz : bool, optional (default: False)</span>
<span class="sd">            Whether to return cleaned X,Y,Z, where possible duplicates are</span>
<span class="sd">            removed.</span>
<span class="sd">        do_checks : bool, optional (default: True)</span>
<span class="sd">            Whether to perform sanity checks on input X,Y,Z</span>
<span class="sd">        remove_overlaps : bool, optional (default: True)</span>
<span class="sd">            Whether to remove variables from Z/extraZ if they overlap with X or Y.</span>
<span class="sd">        cut_off : {&#39;2xtau_max&#39;, &#39;tau_max&#39;, &#39;max_lag&#39;, &#39;max_lag_or_tau_max&#39;, 2xtau_max_future}</span>
<span class="sd">            If cut_off == &#39;2xtau_max&#39;:</span>
<span class="sd">                - 2*tau_max samples are cut off at the beginning of the time</span>
<span class="sd">                  series (&#39;beginning&#39; here refers to the temporally first</span>
<span class="sd">                  time steps). This guarantees that (as long as no mask is</span>
<span class="sd">                  used) all MCI tests are conducted on the same samples,</span>
<span class="sd">                  independent of X, Y, and Z.</span>

<span class="sd">                - If at time step t_missing a data value is missing, then the</span>
<span class="sd">                  time steps t_missing, ..., t_missing + 2*tau_max are cut</span>
<span class="sd">                  out. The latter part only holds if</span>
<span class="sd">                  remove_missing_upto_maxlag=True.</span>

<span class="sd">            If cut_off ==  &#39;max_lag&#39;:</span>
<span class="sd">                - max_lag(X, Y, Z) samples are cut off at the beginning of the</span>
<span class="sd">                  time series, where max_lag(X, Y, Z) is the maximum lag of</span>
<span class="sd">                  all nodes in X, Y, and Z. These are all samples that can in</span>
<span class="sd">                  principle be used.</span>

<span class="sd">                - If at time step t_missing a data value is missing, then the</span>
<span class="sd">                  time steps t_missing, ..., t_missing + max_lag(X, Y, Z) are</span>
<span class="sd">                  cut out. The latter part only holds if</span>
<span class="sd">                  remove_missing_upto_maxlag=True.</span>

<span class="sd">            If cut_off == &#39;max_lag_or_tau_max&#39;:</span>
<span class="sd">                - max(max_lag(X, Y, Z), tau_max) are cut off at the beginning.</span>
<span class="sd">                  This may be useful for modeling by comparing multiple</span>
<span class="sd">                  models on the same samples. </span>

<span class="sd">                - If at time step t_missing a data value is missing, then the</span>
<span class="sd">                  time steps t_missing, ..., t_missing + max(max_lag(X, Y,</span>
<span class="sd">                  Z), tau_max) are cut out. The latter part only holds if</span>
<span class="sd">                  remove_missing_upto_maxlag=True.</span>

<span class="sd">            If cut_off == &#39;tau_max&#39;:</span>
<span class="sd">                - tau_max samples are cut off at the beginning. This may be</span>
<span class="sd">                  useful for modeling by comparing multiple models on the</span>
<span class="sd">                  same samples. </span>

<span class="sd">                - If at time step t_missing a data value is missing, then the</span>
<span class="sd">                  time steps t_missing, ..., t_missing + max(max_lag(X, Y,</span>
<span class="sd">                  Z), tau_max) are cut out. The latter part only holds if</span>
<span class="sd">                  remove_missing_upto_maxlag=True.</span>
<span class="sd">                </span>
<span class="sd">            If cut_off == &#39;2xtau_max_future&#39;:</span>
<span class="sd">                First, the relevant time steps are determined as for cut_off ==</span>
<span class="sd">                &#39;max_lag&#39;. Then, the temporally latest time steps are removed</span>
<span class="sd">                such that the same number of time steps remains as there would</span>
<span class="sd">                be for cut_off == &#39;2xtau_max&#39;. This may be useful when one is</span>
<span class="sd">                mostly interested in the temporally first time steps and would</span>
<span class="sd">                like all MCI tests to be performed on the same *number* of</span>
<span class="sd">                samples. Note, however, that while the *number* of samples is</span>
<span class="sd">                the same for all MCI tests, the samples themselves may be</span>
<span class="sd">                different.</span>
<span class="sd">        verbosity : int, optional (default: 0)</span>
<span class="sd">            Level of verbosity.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        array, xyz [,XYZ], data_type : Tuple of data array of shape (dim, n_samples),</span>
<span class="sd">            xyz identifier array of shape (dim,) identifying which row in array</span>
<span class="sd">            corresponds to X, Y, and Z, and the type mask that indicates which samples</span>
<span class="sd">            are continuous or discrete. For example: X = [(0, -1)],</span>
<span class="sd">            Y = [(1, 0)], Z = [(1, -1), (0, -2)] yields an array of shape</span>
<span class="sd">            (4, n_samples) and xyz is xyz = numpy.array([0,1,2,2]). If</span>
<span class="sd">            return_cleaned_xyz is True, also outputs the cleaned XYZ lists.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="c1"># # This version does not yet work with bootstrap</span>
        <span class="c1"># try:</span>
        <span class="c1">#     assert self.bootstrap is None</span>
        <span class="c1"># except AssertionError:</span>
        <span class="c1">#     print(&quot;This version does not yet work with bootstrap.&quot;)</span>
        <span class="c1">#     raise</span>

        <span class="k">if</span> <span class="n">extraZ</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">extraZ</span> <span class="o">=</span> <span class="p">[]</span>
        
        <span class="k">if</span> <span class="n">Z</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="c1"># If vector-valued variables exist, add them</span>
        <span class="k">def</span> <span class="nf">vectorize</span><span class="p">(</span><span class="n">varlag</span><span class="p">):</span>     
            <span class="n">vectorized_var</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="p">(</span><span class="n">var</span><span class="p">,</span> <span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="n">varlag</span><span class="p">:</span>
                <span class="k">for</span> <span class="p">(</span><span class="n">vector_var</span><span class="p">,</span> <span class="n">vector_lag</span><span class="p">)</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">vector_vars</span><span class="p">[</span><span class="n">var</span><span class="p">]:</span>
                    <span class="n">vectorized_var</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">vector_var</span><span class="p">,</span> <span class="n">vector_lag</span> <span class="o">+</span> <span class="n">lag</span><span class="p">))</span>
            <span class="k">return</span> <span class="n">vectorized_var</span>

        <span class="n">X</span> <span class="o">=</span> <span class="n">vectorize</span><span class="p">(</span><span class="n">X</span><span class="p">)</span> 
        <span class="n">Y</span> <span class="o">=</span> <span class="n">vectorize</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span> 
        <span class="n">Z</span> <span class="o">=</span> <span class="n">vectorize</span><span class="p">(</span><span class="n">Z</span><span class="p">)</span> 
        <span class="n">extraZ</span> <span class="o">=</span> <span class="n">vectorize</span><span class="p">(</span><span class="n">extraZ</span><span class="p">)</span> 

        <span class="c1"># Remove duplicates in X, Y, Z, extraZ</span>
        <span class="n">X</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">OrderedDict</span><span class="o">.</span><span class="n">fromkeys</span><span class="p">(</span><span class="n">X</span><span class="p">))</span>
        <span class="n">Y</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">OrderedDict</span><span class="o">.</span><span class="n">fromkeys</span><span class="p">(</span><span class="n">Y</span><span class="p">))</span>
        <span class="n">Z</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">OrderedDict</span><span class="o">.</span><span class="n">fromkeys</span><span class="p">(</span><span class="n">Z</span><span class="p">))</span>
        <span class="n">extraZ</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">OrderedDict</span><span class="o">.</span><span class="n">fromkeys</span><span class="p">(</span><span class="n">extraZ</span><span class="p">))</span>

        <span class="k">if</span> <span class="n">remove_overlaps</span><span class="p">:</span>
            <span class="c1"># If a node in Z occurs already in X or Y, remove it from Z</span>
            <span class="n">Z</span> <span class="o">=</span> <span class="p">[</span><span class="n">node</span> <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">Z</span> <span class="k">if</span> <span class="p">(</span><span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">X</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">Y</span><span class="p">)]</span>
            <span class="n">extraZ</span> <span class="o">=</span> <span class="p">[</span><span class="n">node</span> <span class="k">for</span> <span class="n">node</span> <span class="ow">in</span> <span class="n">extraZ</span> <span class="k">if</span> <span class="p">(</span><span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">X</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">Y</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">node</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">Z</span><span class="p">)]</span>

        <span class="n">XYZ</span> <span class="o">=</span> <span class="n">X</span> <span class="o">+</span> <span class="n">Y</span> <span class="o">+</span> <span class="n">Z</span> <span class="o">+</span> <span class="n">extraZ</span>
        <span class="n">dim</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)</span>

        <span class="c1"># Check that all lags are non-positive and indices are in [0,N-1]</span>
        <span class="k">if</span> <span class="n">do_checks</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_check_nodes</span><span class="p">(</span><span class="n">Y</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">Ndata</span><span class="p">,</span> <span class="n">dim</span><span class="p">)</span>

        <span class="c1"># Use the mask, override if needed</span>
        <span class="n">_mask</span> <span class="o">=</span> <span class="n">mask</span>
        <span class="k">if</span> <span class="n">_mask</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">_mask</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">_mask</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_mask</span><span class="p">(</span><span class="n">mask</span> <span class="o">=</span> <span class="n">_mask</span><span class="p">)</span>
            
        <span class="n">_data_type</span> <span class="o">=</span> <span class="n">data_type</span>
        <span class="k">if</span> <span class="n">_data_type</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">_data_type</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">data_type</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">_data_type</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_check_mask</span><span class="p">(</span><span class="n">mask</span> <span class="o">=</span> <span class="n">_data_type</span><span class="p">,</span> <span class="n">check_data_type</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

        <span class="c1"># Figure out what cut off we will be using</span>
        <span class="k">if</span> <span class="n">cut_off</span> <span class="o">==</span> <span class="s1">&#39;2xtau_max&#39;</span><span class="p">:</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="mi">2</span><span class="o">*</span><span class="n">tau_max</span>
        <span class="k">elif</span> <span class="n">cut_off</span> <span class="o">==</span> <span class="s1">&#39;max_lag&#39;</span><span class="p">:</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)[:,</span> <span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">min</span><span class="p">())</span>
        <span class="k">elif</span> <span class="n">cut_off</span> <span class="o">==</span> <span class="s1">&#39;tau_max&#39;</span><span class="p">:</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="n">tau_max</span>
        <span class="k">elif</span> <span class="n">cut_off</span> <span class="o">==</span> <span class="s1">&#39;max_lag_or_tau_max&#39;</span><span class="p">:</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="nb">abs</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)[:,</span> <span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">min</span><span class="p">()),</span> <span class="n">tau_max</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">cut_off</span> <span class="o">==</span> <span class="s1">&#39;2xtau_max_future&#39;</span><span class="p">:</span>
            <span class="c1">## TODO: CHECK THIS</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">abs</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)[:,</span> <span class="mi">1</span><span class="p">]</span><span class="o">.</span><span class="n">min</span><span class="p">())</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;max_lag must be in {&#39;2xtau_max&#39;, &#39;tau_max&#39;, &#39;max_lag&#39;, &quot;</span>\
                <span class="s2">&quot;&#39;max_lag_or_tau_max&#39;, &#39;2xtau_max_future&#39;}&quot;</span><span class="p">)</span>

        <span class="c1"># Setup XYZ identifier</span>
        <span class="n">index_code</span> <span class="o">=</span> <span class="p">{</span><span class="s1">&#39;x&#39;</span> <span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
                      <span class="s1">&#39;y&#39;</span> <span class="p">:</span> <span class="mi">1</span><span class="p">,</span>
                      <span class="s1">&#39;z&#39;</span> <span class="p">:</span> <span class="mi">2</span><span class="p">,</span>
                      <span class="s1">&#39;e&#39;</span> <span class="p">:</span> <span class="mi">3</span><span class="p">}</span>
        <span class="n">xyz</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="n">index_code</span><span class="p">[</span><span class="n">name</span><span class="p">]</span>
                        <span class="k">for</span> <span class="n">var</span><span class="p">,</span> <span class="n">name</span> <span class="ow">in</span> <span class="nb">zip</span><span class="p">([</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">extraZ</span><span class="p">],</span> <span class="p">[</span><span class="s1">&#39;x&#39;</span><span class="p">,</span> <span class="s1">&#39;y&#39;</span><span class="p">,</span> <span class="s1">&#39;z&#39;</span><span class="p">,</span> <span class="s1">&#39;e&#39;</span><span class="p">])</span>
                        <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="n">var</span><span class="p">])</span>

        <span class="c1"># Run through all datasets and fill a dictionary holding the</span>
        <span class="c1"># samples taken from the individual datasets</span>
        <span class="n">samples_datasets</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="n">data_types</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">use_indices_dataset_dict</span> <span class="o">=</span> <span class="nb">dict</span><span class="p">()</span>

        <span class="k">for</span> <span class="n">dataset_key</span><span class="p">,</span> <span class="n">dataset_data</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">values</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>

            <span class="c1"># Apply time offset to the reference points</span>
            <span class="n">ref_points_here</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span>

            <span class="c1"># Remove reference points that are out of bounds or are to be</span>
            <span class="c1"># excluded given the choice of &#39;cut_off&#39;</span>
            <span class="n">ref_points_here</span> <span class="o">=</span> <span class="n">ref_points_here</span><span class="p">[</span><span class="n">ref_points_here</span> <span class="o">&gt;=</span> <span class="n">max_lag</span><span class="p">]</span>
            <span class="n">ref_points_here</span> <span class="o">=</span> <span class="n">ref_points_here</span><span class="p">[</span><span class="n">ref_points_here</span> <span class="o">&lt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">T</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]]</span>

            <span class="c1"># Keep track of which reference points would have remained for</span>
            <span class="c1"># max_lag == 2*tau_max</span>
            <span class="k">if</span> <span class="n">cut_off</span> <span class="o">==</span> <span class="s1">&#39;2xtau_max_future&#39;</span><span class="p">:</span>
                <span class="n">ref_points_here_2_tau_max</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">reference_points</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">time_offsets</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span>
                <span class="n">ref_points_here_2_tau_max</span> <span class="o">=</span> <span class="n">ref_points_here_2_tau_max</span><span class="p">[</span><span class="n">ref_points_here_2_tau_max</span>  <span class="o">&gt;=</span> <span class="mi">2</span><span class="o">*</span><span class="n">tau_max</span><span class="p">]</span>
                <span class="n">ref_points_here_2_tau_max</span> <span class="o">=</span> <span class="n">ref_points_here_2_tau_max</span><span class="p">[</span><span class="n">ref_points_here_2_tau_max</span>  <span class="o">&lt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">T</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]]</span>

            <span class="c1"># Sort the valid reference points (not needed, but might be useful</span>
            <span class="c1"># for detailed debugging)</span>
            <span class="n">ref_points_here</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)</span>

            <span class="c1"># For cut_off == &#39;2xtau_max_future&#39; reduce the samples size the</span>
            <span class="c1"># number of samples that would have been obtained for cut_off ==</span>
            <span class="c1"># &#39;2xtau_max&#39;, removing the temporally latest ones</span>
            <span class="k">if</span> <span class="n">cut_off</span> <span class="o">==</span> <span class="s1">&#39;2xtau_max_future&#39;</span><span class="p">:</span>
                <span class="n">n_to_cut_off</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)</span> <span class="o">-</span> <span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here_2_tau_max</span><span class="p">)</span>
                <span class="k">assert</span> <span class="n">n_to_cut_off</span> <span class="o">&gt;=</span> <span class="mi">0</span>
                <span class="k">if</span> <span class="n">n_to_cut_off</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="n">ref_points_here</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)</span>
                    <span class="n">ref_points_here</span> <span class="o">=</span> <span class="n">ref_points_here</span><span class="p">[:</span><span class="o">-</span><span class="n">n_to_cut_off</span><span class="p">]</span>

            <span class="c1"># If no valid reference points are left, continue with the next dataset</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">continue</span>

            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>

                <span class="n">boot_blocklength</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">bootstrap</span><span class="p">[</span><span class="s1">&#39;boot_blocklength&#39;</span><span class="p">]</span>

                <span class="k">if</span> <span class="n">boot_blocklength</span> <span class="o">==</span> <span class="s1">&#39;cube_root&#39;</span><span class="p">:</span>
                    <span class="n">boot_blocklength</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="nb">int</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)</span><span class="o">**</span><span class="p">(</span><span class="mi">1</span><span class="o">/</span><span class="mi">3</span><span class="p">)))</span>
                <span class="c1"># elif boot_blocklength == &#39;from_autocorrelation&#39;:</span>
                <span class="c1">#     boot_blocklength = \</span>
                <span class="c1">#         get_block_length(overlapping_residuals.T, xyz=np.zeros(N), mode=&#39;confidence&#39;)</span>
                <span class="k">elif</span> <span class="nb">type</span><span class="p">(</span><span class="n">boot_blocklength</span><span class="p">)</span> <span class="ow">is</span> <span class="nb">int</span> <span class="ow">and</span> <span class="n">boot_blocklength</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="k">pass</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;boot_blocklength must be integer &gt; 0, &#39;cube_root&#39;, or &#39;from_autocorrelation&#39;&quot;</span><span class="p">)</span>

                <span class="c1"># Chooses THE SAME random seed for every dataset, maybe that&#39;s what we want...</span>
                <span class="c1"># If the reference points are all the same, this will give the same bootstrap</span>
                <span class="c1"># draw. However, if they are NOT the same, they will differ. </span>
                <span class="c1"># TODO: Decide whether bootstrap draws should be the same for each dataset and</span>
                <span class="c1"># how to achieve that if the reference points differ...</span>
                <span class="c1"># random_state = self.bootstrap[&#39;random_state&#39;]</span>
                <span class="n">random_state</span> <span class="o">=</span> <span class="n">deepcopy</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">bootstrap</span><span class="p">[</span><span class="s1">&#39;random_state&#39;</span><span class="p">])</span>

                <span class="c1"># Determine the number of blocks total, rounding up for non-integer</span>
                <span class="c1"># amounts</span>
                <span class="n">n_blks</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">math</span><span class="o">.</span><span class="n">ceil</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">))</span><span class="o">/</span><span class="n">boot_blocklength</span><span class="p">))</span>

                <span class="k">if</span> <span class="n">n_blks</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">:</span>
                    <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Only </span><span class="si">%d</span><span class="s2"> block(s) for block-sampling,&quot;</span>  <span class="o">%</span><span class="n">n_blks</span> <span class="o">+</span>
                                     <span class="s2">&quot; choose smaller boot_blocklength!&quot;</span><span class="p">)</span>

                <span class="c1"># Get the starting indices for the blocks</span>
                <span class="n">blk_strt</span> <span class="o">=</span> <span class="n">random_state</span><span class="o">.</span><span class="n">choice</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)</span> <span class="o">-</span> <span class="n">boot_blocklength</span><span class="p">),</span> <span class="n">size</span><span class="o">=</span><span class="n">n_blks</span><span class="p">,</span> <span class="n">replace</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
                <span class="c1"># Get the empty array of block resampled values</span>
                <span class="n">boot_draw</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">n_blks</span><span class="o">*</span><span class="n">boot_blocklength</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int&#39;</span><span class="p">)</span>
                <span class="c1"># Fill the array of block resamples</span>
                <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">boot_blocklength</span><span class="p">):</span>
                    <span class="n">boot_draw</span><span class="p">[</span><span class="n">i</span><span class="p">::</span><span class="n">boot_blocklength</span><span class="p">]</span> <span class="o">=</span> <span class="n">ref_points_here</span><span class="p">[</span><span class="n">blk_strt</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span>
                <span class="c1"># Cut to proper length</span>
                <span class="n">ref_points_here</span> <span class="o">=</span> <span class="n">boot_draw</span><span class="p">[:</span><span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)]</span>

            <span class="c1"># Construct the data array holding the samples taken from the</span>
            <span class="c1"># current dataset</span>
            <span class="n">samples_datasets</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">dim</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)),</span> <span class="n">dtype</span> <span class="o">=</span> <span class="n">dataset_data</span><span class="o">.</span><span class="n">dtype</span><span class="p">)</span>
            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">var</span><span class="p">,</span> <span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">XYZ</span><span class="p">):</span>
                <span class="n">samples_datasets</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">][</span><span class="n">i</span><span class="p">,</span> <span class="p">:]</span> <span class="o">=</span> <span class="n">dataset_data</span><span class="p">[</span><span class="n">ref_points_here</span> <span class="o">+</span> <span class="n">lag</span><span class="p">,</span> <span class="n">var</span><span class="p">]</span>

            <span class="c1"># Build the mask array corresponding to this dataset</span>
            <span class="k">if</span> <span class="n">_mask</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">mask_dataset</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">dim</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)),</span> <span class="n">dtype</span> <span class="o">=</span> <span class="s1">&#39;bool&#39;</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">var</span><span class="p">,</span> <span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">XYZ</span><span class="p">):</span>
                    <span class="n">mask_dataset</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="p">:]</span> <span class="o">=</span> <span class="n">_mask</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">][</span><span class="n">ref_points_here</span> <span class="o">+</span> <span class="n">lag</span><span class="p">,</span> <span class="n">var</span><span class="p">]</span>

            <span class="c1"># Take care of masking</span>
            <span class="n">use_indices_dataset</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">),</span> <span class="n">dtype</span> <span class="o">=</span> <span class="s1">&#39;int&#39;</span><span class="p">)</span>

            <span class="c1"># Build the type mask array corresponding to this dataset</span>
            <span class="k">if</span> <span class="n">_data_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">data_type_dataset</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">dim</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)),</span> <span class="n">dtype</span> <span class="o">=</span> <span class="s1">&#39;bool&#39;</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="p">(</span><span class="n">var</span><span class="p">,</span> <span class="n">lag</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">XYZ</span><span class="p">):</span>
                    <span class="n">data_type_dataset</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="p">:]</span> <span class="o">=</span> <span class="n">_data_type</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">][</span><span class="n">ref_points_here</span> <span class="o">+</span> <span class="n">lag</span><span class="p">,</span> <span class="n">var</span><span class="p">]</span>
                <span class="n">data_types</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span> <span class="o">=</span> <span class="n">data_type_dataset</span>
            
            <span class="c1"># Remove all values that have missing value flag, and optionally as well the time</span>
            <span class="c1"># slices that occur up to max_lag after</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">missing_flag</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">missing_anywhere</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">samples_datasets</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]),</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">))[</span><span class="mi">0</span><span class="p">])</span>

                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">remove_missing_upto_maxlag</span><span class="p">:</span>
                    <span class="n">idx_to_remove</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">idx</span> <span class="o">+</span> <span class="n">tau</span> <span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="n">missing_anywhere</span> <span class="k">for</span> <span class="n">tau</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">max_lag</span> <span class="o">+</span> <span class="mi">1</span><span class="p">))</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">idx_to_remove</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">idx</span> <span class="k">for</span> <span class="n">idx</span> <span class="ow">in</span> <span class="n">missing_anywhere</span><span class="p">)</span>
                
                <span class="n">use_indices_dataset</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="nb">list</span><span class="p">(</span><span class="n">idx_to_remove</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int&#39;</span><span class="p">)]</span> <span class="o">=</span> <span class="mi">0</span>
            
            <span class="k">if</span> <span class="n">_mask</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="c1"># Remove samples with mask == 1 conditional on which mask_type</span>
                <span class="c1"># is used</span>

                <span class="c1"># Iterate over defined mapping from letter index to number index,</span>
                <span class="c1"># i.e. &#39;x&#39; -&gt; 0, &#39;y&#39; -&gt; 1, &#39;z&#39;-&gt; 2, &#39;e&#39;-&gt; 3</span>
                <span class="k">for</span> <span class="n">idx</span><span class="p">,</span> <span class="n">cde</span> <span class="ow">in</span> <span class="n">index_code</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
                    <span class="c1"># Check if the letter index is in the mask type</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">mask_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">and</span> <span class="p">(</span><span class="n">idx</span> <span class="ow">in</span> <span class="n">mask_type</span><span class="p">):</span>
                        <span class="c1"># If so, check if any of the data that correspond to the</span>
                        <span class="c1"># letter index is masked by taking the product along the</span>
                        <span class="c1"># node-data to return a time slice selection, where 0</span>
                        <span class="c1"># means the time slice will not be used</span>
                        <span class="n">slice_select</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">prod</span><span class="p">(</span><span class="n">mask_dataset</span><span class="p">[</span><span class="n">xyz</span> <span class="o">==</span> <span class="n">cde</span><span class="p">,</span> <span class="p">:]</span> <span class="o">==</span> <span class="kc">False</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
                        <span class="n">use_indices_dataset</span> <span class="o">*=</span> <span class="n">slice_select</span>

            <span class="c1"># Accordingly update the data array</span>
            <span class="n">samples_datasets</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span> <span class="o">=</span> <span class="n">samples_datasets</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">][:,</span> <span class="n">use_indices_dataset</span> <span class="o">==</span> <span class="mi">1</span><span class="p">]</span>

        <span class="c1">## end for dataset_key, dataset_data in self.values.items()</span>

        <span class="c1"># Save used indices as attribute</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">ref_points_here</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">use_indices_dataset_dict</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span> <span class="o">=</span> <span class="n">ref_points_here</span><span class="p">[</span><span class="n">use_indices_dataset</span><span class="o">==</span><span class="mi">1</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">use_indices_dataset_dict</span><span class="p">[</span><span class="n">dataset_key</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="c1"># Concatenate the arrays of all datasets</span>
        <span class="n">array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">concatenate</span><span class="p">(</span><span class="nb">tuple</span><span class="p">(</span><span class="n">samples_datasets</span><span class="o">.</span><span class="n">values</span><span class="p">()),</span> <span class="n">axis</span> <span class="o">=</span> <span class="mi">1</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">_data_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">type_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">concatenate</span><span class="p">(</span><span class="nb">tuple</span><span class="p">(</span><span class="n">data_types</span><span class="o">.</span><span class="n">values</span><span class="p">()),</span> <span class="n">axis</span> <span class="o">=</span> <span class="mi">1</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">type_array</span> <span class="o">=</span> <span class="kc">None</span>
        
        <span class="c1"># print(np.where(np.isnan(array)))</span>
        <span class="c1"># print(array.shape)</span>

        <span class="c1"># Check whether there is any valid sample</span>
        <span class="k">if</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;No valid samples&quot;</span><span class="p">)</span>

        <span class="c1"># Print information about the constructed array</span>
        <span class="k">if</span> <span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">2</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">print_array_info</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">missing_flag</span><span class="p">,</span> <span class="n">mask_type</span><span class="p">,</span> <span class="n">type_array</span><span class="p">,</span> <span class="n">extraZ</span><span class="p">)</span>

        <span class="c1"># Return the array and xyz and optionally (X, Y, Z)</span>
        <span class="k">if</span> <span class="n">return_cleaned_xyz</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">),</span> <span class="n">type_array</span>

        <span class="k">return</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">type_array</span></div>

    <span class="k">def</span> <span class="nf">_check_nodes</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">,</span> <span class="n">N</span><span class="p">,</span> <span class="n">dim</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Checks that:</span>
<span class="sd">        * The requests XYZ nodes have the correct shape</span>
<span class="sd">        * All lags are non-positive</span>
<span class="sd">        * All indices are less than N</span>
<span class="sd">        * One of the Y nodes has zero lag</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        Y : list of tuples</span>
<span class="sd">            Of the form [(var, -tau)], where var specifies the variable</span>
<span class="sd">            index and tau the time lag.</span>
<span class="sd">        XYZ : list of tuples</span>
<span class="sd">            List of nodes chosen for current independence test</span>
<span class="sd">        N : int</span>
<span class="sd">            Total number of listed nodes</span>
<span class="sd">        dim : int</span>
<span class="sd">            Number of nodes excluding repeated nodes</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)</span><span class="o">.</span><span class="n">shape</span> <span class="o">!=</span> <span class="p">(</span><span class="n">dim</span><span class="p">,</span> <span class="mi">2</span><span class="p">):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;X, Y, Z must be lists of tuples in format&quot;</span>
                             <span class="s2">&quot; [(var, -lag),...], eg., [(2, -2), (1, 0), ...]&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)[:,</span> <span class="mi">1</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;nodes are </span><span class="si">%s</span><span class="s2">, &quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)</span> <span class="o">+</span>
                             <span class="s2">&quot;but all lags must be non-positive&quot;</span><span class="p">)</span>
        <span class="k">if</span> <span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)[:,</span> <span class="mi">0</span><span class="p">]</span> <span class="o">&gt;=</span> <span class="n">N</span><span class="p">)</span>
                <span class="ow">or</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)[:,</span> <span class="mi">0</span><span class="p">]</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">)):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;var indices </span><span class="si">%s</span><span class="s2">,&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">(</span><span class="n">XYZ</span><span class="p">)[:,</span> <span class="mi">0</span><span class="p">])</span> <span class="o">+</span>
                             <span class="s2">&quot; but must be in [0, </span><span class="si">%d</span><span class="s2">]&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">N</span> <span class="o">-</span> <span class="mi">1</span><span class="p">))</span>
        <span class="c1"># if np.all(np.array(Y)[:, 1] != 0):</span>
        <span class="c1">#     raise ValueError(&quot;Y-nodes are %s, &quot; % str(Y) +</span>
        <span class="c1">#                      &quot;but one of the Y-nodes must have zero lag&quot;)</span>

<div class="viewcode-block" id="DataFrame.print_array_info"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.DataFrame.print_array_info">[docs]</a>    <span class="k">def</span> <span class="nf">print_array_info</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">missing_flag</span><span class="p">,</span> <span class="n">mask_type</span><span class="p">,</span> <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">extraZ</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Print info about the constructed array</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        array : Data array of shape (dim, T)</span>
<span class="sd">            Data array.</span>
<span class="sd">        X, Y, Z, extraZ : list of tuples</span>
<span class="sd">            For a dependence measure I(X;Y|Z), Y is of the form [(varY, 0)],</span>
<span class="sd">            where var specifies the variable index. X typically is of the form</span>
<span class="sd">            [(varX, -tau)] with tau denoting the time lag and Z can be</span>
<span class="sd">            multivariate [(var1, -lag), (var2, -lag), ...] .</span>
<span class="sd">        missing_flag : number, optional (default: None)</span>
<span class="sd">            Flag for missing values. Dismisses all time slices of samples where</span>
<span class="sd">            missing values occur in any variable and also flags samples for all</span>
<span class="sd">            lags up to 2*tau_max. This avoids biases, see section on masking in</span>
<span class="sd">            Supplement of [1]_.</span>
<span class="sd">        mask_type : {&#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">            Masking mode: Indicators for which variables in the dependence</span>
<span class="sd">            measure I(X; Y | Z) the samples should be masked. If None, the mask</span>
<span class="sd">            is not used. Explained in tutorial on masking and missing values.</span>
<span class="sd">        data_type : array-like</span>
<span class="sd">            Binary data array of same shape as array which describes whether </span>
<span class="sd">            individual samples in a variable (or all samples) are continuous </span>
<span class="sd">            or discrete: 0s for continuous variables and 1s for discrete variables.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">extraZ</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">extraZ</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">indt</span> <span class="o">=</span> <span class="s2">&quot; &quot;</span> <span class="o">*</span> <span class="mi">12</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">indt</span> <span class="o">+</span> <span class="s2">&quot;Constructed array of shape </span><span class="si">%s</span><span class="s2"> from&quot;</span><span class="o">%</span><span class="nb">str</span><span class="p">(</span><span class="n">array</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span> <span class="o">+</span>
              <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">+</span> <span class="n">indt</span> <span class="o">+</span> <span class="s2">&quot;X = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">X</span><span class="p">)</span> <span class="o">+</span>
              <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">+</span> <span class="n">indt</span> <span class="o">+</span> <span class="s2">&quot;Y = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span> <span class="o">+</span>
              <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">&quot;</span> <span class="o">+</span> <span class="n">indt</span> <span class="o">+</span> <span class="s2">&quot;Z = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">Z</span><span class="p">))</span>
        <span class="k">if</span> <span class="n">extraZ</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>  
            <span class="nb">print</span><span class="p">(</span><span class="n">indt</span> <span class="o">+</span> <span class="s2">&quot;extraZ = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="nb">str</span><span class="p">(</span><span class="n">extraZ</span><span class="p">))</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">mask_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="n">indt</span><span class="o">+</span><span class="s2">&quot;with masked samples in </span><span class="si">%s</span><span class="s2"> removed&quot;</span> <span class="o">%</span> <span class="n">mask_type</span><span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">data_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="n">indt</span><span class="o">+</span><span class="s2">&quot;with </span><span class="si">%s</span><span class="s2"> </span><span class="si">% d</span><span class="s2">iscrete values&quot;</span> <span class="o">%</span> <span class="n">np</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">data_type</span><span class="p">)</span><span class="o">/</span><span class="n">data_type</span><span class="o">.</span><span class="n">size</span><span class="p">)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">missing_flag</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="n">indt</span><span class="o">+</span><span class="s2">&quot;with missing values = </span><span class="si">%s</span><span class="s2"> removed&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">missing_flag</span><span class="p">)</span></div></div>


<div class="viewcode-block" id="get_acf"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.get_acf">[docs]</a><span class="k">def</span> <span class="nf">get_acf</span><span class="p">(</span><span class="n">series</span><span class="p">,</span> <span class="n">max_lag</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns autocorrelation function.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    series : 1D-array</span>
<span class="sd">        data series to compute autocorrelation from</span>

<span class="sd">    max_lag : int, optional (default: None)</span>
<span class="sd">        maximum lag for autocorrelation function. If None is passed, 10% of</span>
<span class="sd">        the data series length are used.</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    autocorr : array of shape (max_lag + 1,)</span>
<span class="sd">        Autocorrelation function.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Set the default max lag</span>
    <span class="k">if</span> <span class="n">max_lag</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="nb">max</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mf">0.1</span><span class="o">*</span><span class="nb">len</span><span class="p">(</span><span class="n">series</span><span class="p">)))</span>
    <span class="c1"># Initialize the result</span>
    <span class="n">autocorr</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">max_lag</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span>
    <span class="c1"># Iterate over possible lags</span>
    <span class="k">for</span> <span class="n">lag</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_lag</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
        <span class="c1"># Set the values</span>
        <span class="n">y1_vals</span> <span class="o">=</span> <span class="n">series</span><span class="p">[</span><span class="n">lag</span><span class="p">:]</span>
        <span class="n">y2_vals</span> <span class="o">=</span> <span class="n">series</span><span class="p">[:</span><span class="nb">len</span><span class="p">(</span><span class="n">series</span><span class="p">)</span> <span class="o">-</span> <span class="n">lag</span><span class="p">]</span>
        <span class="c1"># Calculate the autocorrelation</span>
        <span class="n">autocorr</span><span class="p">[</span><span class="n">lag</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">corrcoef</span><span class="p">(</span><span class="n">y1_vals</span><span class="p">,</span> <span class="n">y2_vals</span><span class="p">,</span> <span class="n">ddof</span><span class="o">=</span><span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">]</span>
    <span class="k">return</span> <span class="n">autocorr</span></div>

<div class="viewcode-block" id="get_block_length"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.get_block_length">[docs]</a><span class="k">def</span> <span class="nf">get_block_length</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">mode</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns optimal block length for significance and confidence tests.</span>

<span class="sd">    Determine block length using approach in Mader (2013) [Eq. (6)] which</span>
<span class="sd">    improves the method of Pfeifer (2005) with non-overlapping blocks In</span>
<span class="sd">    case of multidimensional X, the max is used. Further details in [1]_.</span>
<span class="sd">    Two modes are available. For mode=&#39;significance&#39;, only the indices</span>
<span class="sd">    corresponding to X are shuffled in array. For mode=&#39;confidence&#39; all</span>
<span class="sd">    variables are jointly shuffled. If the autocorrelation curve fit fails,</span>
<span class="sd">    a block length of 5% of T is used. The block length is limited to a</span>
<span class="sd">    maximum of 10% of T.</span>

<span class="sd">    Mader et al., Journal of Neuroscience Methods,</span>
<span class="sd">    Volume 219, Issue 2, 15 October 2013, Pages 285-291</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    array : array-like</span>
<span class="sd">        data array with X, Y, Z in rows and observations in columns</span>

<span class="sd">    xyz : array of ints</span>
<span class="sd">        XYZ identifier array of shape (dim,).</span>

<span class="sd">    mode : str</span>
<span class="sd">        Which mode to use.</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    block_len : int</span>
<span class="sd">        Optimal block length.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="c1"># Inject a dependency on siganal, optimize</span>
    <span class="kn">from</span> <span class="nn">scipy</span> <span class="kn">import</span> <span class="n">signal</span><span class="p">,</span> <span class="n">optimize</span>
    <span class="c1"># Get the shape of the array</span>
    <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>
    <span class="c1"># Initiailize the indices</span>
    <span class="n">indices</span> <span class="o">=</span> <span class="nb">range</span><span class="p">(</span><span class="n">dim</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">mode</span> <span class="o">==</span> <span class="s1">&#39;significance&#39;</span><span class="p">:</span>
        <span class="n">indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

    <span class="c1"># Maximum lag for autocov estimation</span>
    <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="mf">0.1</span><span class="o">*</span><span class="n">T</span><span class="p">)</span>
    <span class="c1"># Define the function to optimize against</span>
    <span class="k">def</span> <span class="nf">func</span><span class="p">(</span><span class="n">x_vals</span><span class="p">,</span> <span class="n">a_const</span><span class="p">,</span> <span class="n">decay</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">a_const</span> <span class="o">*</span> <span class="n">decay</span><span class="o">**</span><span class="n">x_vals</span>

    <span class="c1"># Calculate the block length</span>
    <span class="n">block_len</span> <span class="o">=</span> <span class="mi">1</span>
    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">:</span>
        <span class="c1"># Get decay rate of envelope of autocorrelation functions</span>
        <span class="c1"># via hilbert trafo</span>
        <span class="n">autocov</span> <span class="o">=</span> <span class="n">get_acf</span><span class="p">(</span><span class="n">series</span><span class="o">=</span><span class="n">array</span><span class="p">[</span><span class="n">i</span><span class="p">],</span> <span class="n">max_lag</span><span class="o">=</span><span class="n">max_lag</span><span class="p">)</span>
        <span class="n">autocov</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="mf">1.</span>
        <span class="n">hilbert</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">signal</span><span class="o">.</span><span class="n">hilbert</span><span class="p">(</span><span class="n">autocov</span><span class="p">))</span>
        <span class="c1"># Try to fit the curve</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">popt</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">optimize</span><span class="o">.</span><span class="n">curve_fit</span><span class="p">(</span>
                <span class="n">f</span><span class="o">=</span><span class="n">func</span><span class="p">,</span>
                <span class="n">xdata</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">max_lag</span><span class="o">+</span><span class="mi">1</span><span class="p">),</span>
                <span class="n">ydata</span><span class="o">=</span><span class="n">hilbert</span><span class="p">,</span>
            <span class="p">)</span>
            <span class="n">phi</span> <span class="o">=</span> <span class="n">popt</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
            <span class="c1"># Formula of Pfeifer (2005) assuming non-overlapping blocks</span>
            <span class="n">l_opt</span> <span class="o">=</span> <span class="p">(</span><span class="mf">4.</span> <span class="o">*</span> <span class="n">T</span> <span class="o">*</span> <span class="p">(</span><span class="n">phi</span> <span class="o">/</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">phi</span><span class="p">)</span> <span class="o">+</span> <span class="n">phi</span><span class="o">**</span><span class="mi">2</span> <span class="o">/</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">phi</span><span class="p">)</span><span class="o">**</span><span class="mi">2</span><span class="p">)</span><span class="o">**</span><span class="mi">2</span>
                     <span class="o">/</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">+</span> <span class="mf">2.</span> <span class="o">*</span> <span class="n">phi</span> <span class="o">/</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">phi</span><span class="p">))</span><span class="o">**</span><span class="mi">2</span><span class="p">)</span><span class="o">**</span><span class="p">(</span><span class="mf">1.</span> <span class="o">/</span> <span class="mf">3.</span><span class="p">)</span>
            <span class="n">block_len</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">block_len</span><span class="p">,</span> <span class="nb">int</span><span class="p">(</span><span class="n">l_opt</span><span class="p">))</span>
        <span class="k">except</span> <span class="ne">RuntimeError</span><span class="p">:</span>
            <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;Error - curve_fit failed for estimating block_shuffle length, using&quot;</span>
                  <span class="s2">&quot; block_len = </span><span class="si">%d</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="mf">.05</span> <span class="o">*</span> <span class="n">T</span><span class="p">)))</span>
            <span class="c1"># block_len = max(int(.05 * T), block_len)</span>
    <span class="c1"># Limit block length to a maximum of 10% of T</span>
    <span class="n">block_len</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">block_len</span><span class="p">,</span> <span class="nb">int</span><span class="p">(</span><span class="mf">0.1</span> <span class="o">*</span> <span class="n">T</span><span class="p">))</span>
    <span class="k">return</span> <span class="n">block_len</span></div>


<div class="viewcode-block" id="lowhighpass_filter"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.lowhighpass_filter">[docs]</a><span class="k">def</span> <span class="nf">lowhighpass_filter</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">cutperiod</span><span class="p">,</span> <span class="n">pass_periods</span><span class="o">=</span><span class="s1">&#39;low&#39;</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Butterworth low- or high pass filter.</span>

<span class="sd">    This function applies a linear filter twice, once forward and once</span>
<span class="sd">    backwards. The combined filter has linear phase.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    data : array</span>
<span class="sd">        Data array of shape (time, variables).</span>
<span class="sd">    cutperiod : int</span>
<span class="sd">        Period of cutoff.</span>
<span class="sd">    pass_periods : str, optional (default: &#39;low&#39;)</span>
<span class="sd">        Either &#39;low&#39; or &#39;high&#39; to act as a low- or high-pass filter</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    data : array</span>
<span class="sd">        Filtered data array.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="kn">from</span> <span class="nn">scipy.signal</span> <span class="kn">import</span> <span class="n">butter</span><span class="p">,</span> <span class="n">filtfilt</span>
    <span class="k">except</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s1">&#39;Could not import scipy.signal for butterworth filtering!&#39;</span><span class="p">)</span>

    <span class="n">fs</span> <span class="o">=</span> <span class="mf">1.</span>
    <span class="n">order</span> <span class="o">=</span> <span class="mi">3</span>
    <span class="n">ws</span> <span class="o">=</span> <span class="mf">1.</span> <span class="o">/</span> <span class="n">cutperiod</span> <span class="o">/</span> <span class="p">(</span><span class="mf">0.5</span> <span class="o">*</span> <span class="n">fs</span><span class="p">)</span>
    <span class="n">b</span><span class="p">,</span> <span class="n">a</span> <span class="o">=</span> <span class="n">butter</span><span class="p">(</span><span class="n">order</span><span class="p">,</span> <span class="n">ws</span><span class="p">,</span> <span class="n">pass_periods</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
        <span class="n">data</span> <span class="o">=</span> <span class="n">filtfilt</span><span class="p">(</span><span class="n">b</span><span class="p">,</span> <span class="n">a</span><span class="p">,</span> <span class="n">data</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]):</span>
            <span class="n">data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">filtfilt</span><span class="p">(</span><span class="n">b</span><span class="p">,</span> <span class="n">a</span><span class="p">,</span> <span class="n">data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">])</span>

    <span class="k">return</span> <span class="n">data</span></div>


<div class="viewcode-block" id="smooth"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.smooth">[docs]</a><span class="k">def</span> <span class="nf">smooth</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">smooth_width</span><span class="p">,</span> <span class="n">kernel</span><span class="o">=</span><span class="s1">&#39;gaussian&#39;</span><span class="p">,</span>
           <span class="n">mask</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">residuals</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns either smoothed time series or its residuals.</span>

<span class="sd">    the difference between the original and the smoothed time series</span>
<span class="sd">    (=residuals) of a kernel smoothing with gaussian (smoothing kernel width =</span>
<span class="sd">    twice the sigma!) or heaviside window, equivalent to a running mean.</span>

<span class="sd">    Assumes data of shape (T, N) or (T,)</span>
<span class="sd">    :rtype: array</span>
<span class="sd">    :returns: smoothed/residual data</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    data : array</span>
<span class="sd">        Data array of shape (time, variables).</span>
<span class="sd">    smooth_width : float</span>
<span class="sd">        Window width of smoothing, 2*sigma for a gaussian.</span>
<span class="sd">    kernel : str, optional (default: &#39;gaussian&#39;)</span>
<span class="sd">        Smoothing kernel, &#39;gaussian&#39; or &#39;heaviside&#39; for a running mean.</span>
<span class="sd">    mask : bool array, optional (default: None)</span>
<span class="sd">        Data mask where True labels masked samples.</span>
<span class="sd">    residuals : bool, optional (default: False)</span>
<span class="sd">        True if residuals should be returned instead of smoothed data.</span>
<span class="sd">    verbosity : int, optional (default: 0)</span>
<span class="sd">        Level of verbosity.</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    data : array-like</span>
<span class="sd">        Smoothed/residual data.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">if</span> <span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> </span><span class="si">%s</span><span class="s2"> smoothing with &quot;</span> <span class="o">%</span> <span class="p">({</span><span class="kc">True</span><span class="p">:</span> <span class="s2">&quot;Take residuals of a &quot;</span><span class="p">,</span>
                                      <span class="kc">False</span><span class="p">:</span> <span class="s2">&quot;&quot;</span><span class="p">}[</span><span class="n">residuals</span><span class="p">],</span> <span class="n">kernel</span><span class="p">)</span> <span class="o">+</span>
          <span class="s2">&quot;window width </span><span class="si">%.2f</span><span class="s2"> (=2*sigma for a gaussian!)&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">smooth_width</span><span class="p">))</span>

    <span class="n">totaltime</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">kernel</span> <span class="o">==</span> <span class="s1">&#39;gaussian&#39;</span><span class="p">:</span>
        <span class="n">window</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">exp</span><span class="p">(</span><span class="o">-</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="n">totaltime</span><span class="p">)</span><span class="o">.</span><span class="n">reshape</span><span class="p">((</span><span class="mi">1</span><span class="p">,</span> <span class="n">totaltime</span><span class="p">))</span> <span class="o">-</span>
                             <span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="n">totaltime</span><span class="p">)</span><span class="o">.</span><span class="n">reshape</span><span class="p">((</span><span class="n">totaltime</span><span class="p">,</span> <span class="mi">1</span><span class="p">))</span>
                             <span class="p">)</span> <span class="o">**</span> <span class="mi">2</span> <span class="o">/</span> <span class="p">((</span><span class="mf">2.</span> <span class="o">*</span> <span class="n">smooth_width</span> <span class="o">/</span> <span class="mf">2.</span><span class="p">)</span> <span class="o">**</span> <span class="mi">2</span><span class="p">))</span>
    <span class="k">elif</span> <span class="n">kernel</span> <span class="o">==</span> <span class="s1">&#39;heaviside&#39;</span><span class="p">:</span>
        <span class="kn">import</span> <span class="nn">scipy.linalg</span>
        <span class="n">wtmp</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">totaltime</span><span class="p">)</span>
        <span class="n">wtmp</span><span class="p">[:</span><span class="nb">int</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">ceil</span><span class="p">(</span><span class="n">smooth_width</span> <span class="o">/</span> <span class="mf">2.</span><span class="p">))]</span> <span class="o">=</span> <span class="mi">1</span>
        <span class="n">window</span> <span class="o">=</span> <span class="n">scipy</span><span class="o">.</span><span class="n">linalg</span><span class="o">.</span><span class="n">toeplitz</span><span class="p">(</span><span class="n">wtmp</span><span class="p">)</span>

    <span class="k">if</span> <span class="n">mask</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="n">smoothed_data</span> <span class="o">=</span> <span class="p">(</span><span class="n">data</span> <span class="o">*</span> <span class="n">window</span><span class="p">)</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span> <span class="o">/</span> <span class="n">window</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">smoothed_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]):</span>
                <span class="n">smoothed_data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span>
                    <span class="n">data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span> <span class="o">*</span> <span class="n">window</span><span class="p">)</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span> <span class="o">/</span> <span class="n">window</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="n">smoothed_data</span> <span class="o">=</span> <span class="p">((</span><span class="n">data</span> <span class="o">*</span> <span class="n">window</span> <span class="o">*</span> <span class="p">(</span><span class="n">mask</span><span class="o">==</span><span class="kc">False</span><span class="p">))</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span> <span class="o">/</span>
                             <span class="p">(</span><span class="n">window</span> <span class="o">*</span> <span class="p">(</span><span class="n">mask</span><span class="o">==</span><span class="kc">False</span><span class="p">))</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">))</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">smoothed_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]):</span>
                <span class="n">smoothed_data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="p">((</span>
                    <span class="n">data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span> <span class="o">*</span> <span class="n">window</span> <span class="o">*</span> <span class="p">(</span><span class="n">mask</span><span class="o">==</span><span class="kc">False</span><span class="p">)[:,</span> <span class="n">i</span><span class="p">])</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span> <span class="o">/</span>
                    <span class="p">(</span><span class="n">window</span> <span class="o">*</span> <span class="p">(</span><span class="n">mask</span><span class="o">==</span><span class="kc">False</span><span class="p">)[:,</span> <span class="n">i</span><span class="p">])</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">))</span>

    <span class="k">if</span> <span class="n">residuals</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">data</span> <span class="o">-</span> <span class="n">smoothed_data</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">smoothed_data</span></div>


<div class="viewcode-block" id="weighted_avg_and_std"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.weighted_avg_and_std">[docs]</a><span class="k">def</span> <span class="nf">weighted_avg_and_std</span><span class="p">(</span><span class="n">values</span><span class="p">,</span> <span class="n">axis</span><span class="p">,</span> <span class="n">weights</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns the weighted average and standard deviation.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ---------</span>
<span class="sd">    values : array</span>
<span class="sd">        Data array of shape (time, variables).</span>
<span class="sd">    axis : int</span>
<span class="sd">        Axis to average/std about</span>
<span class="sd">    weights : array</span>
<span class="sd">        Weight array of shape (time, variables).</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    (average, std) : tuple of arrays</span>
<span class="sd">        Tuple of weighted average and standard deviation along axis.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">values</span><span class="p">[</span><span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">values</span><span class="p">)]</span> <span class="o">=</span> <span class="mf">0.</span>
    <span class="n">average</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ma</span><span class="o">.</span><span class="n">average</span><span class="p">(</span><span class="n">values</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="n">axis</span><span class="p">,</span> <span class="n">weights</span><span class="o">=</span><span class="n">weights</span><span class="p">)</span>

    <span class="n">variance</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">weights</span> <span class="o">*</span> <span class="p">(</span><span class="n">values</span> <span class="o">-</span> <span class="n">np</span><span class="o">.</span><span class="n">expand_dims</span><span class="p">(</span><span class="n">average</span><span class="p">,</span> <span class="n">axis</span><span class="p">)</span>
                                    <span class="p">)</span> <span class="o">**</span> <span class="mi">2</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="n">axis</span><span class="p">)</span> <span class="o">/</span> <span class="n">weights</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="n">axis</span><span class="p">)</span>

    <span class="k">return</span> <span class="p">(</span><span class="n">average</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">sqrt</span><span class="p">(</span><span class="n">variance</span><span class="p">))</span></div>


<div class="viewcode-block" id="time_bin_with_mask"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.time_bin_with_mask">[docs]</a><span class="k">def</span> <span class="nf">time_bin_with_mask</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">time_bin_length</span><span class="p">,</span> <span class="n">mask</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns time binned data where only about non-masked values is averaged.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    data : array</span>
<span class="sd">        Data array of shape (time, variables).</span>
<span class="sd">    time_bin_length : int</span>
<span class="sd">        Length of time bin.</span>
<span class="sd">    mask : bool array, optional (default: None)</span>
<span class="sd">        Data mask where True labels masked samples.</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    (bindata, T) : tuple of array and int</span>
<span class="sd">        Tuple of time-binned data array and new length of array.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">T</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>

    <span class="n">time_bin_length</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">time_bin_length</span><span class="p">)</span>

    <span class="k">if</span> <span class="n">mask</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">sample_selector</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="c1"># Invert mask</span>
        <span class="n">sample_selector</span> <span class="o">=</span> <span class="p">(</span><span class="n">mask</span> <span class="o">==</span> <span class="kc">False</span><span class="p">)</span>

    <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">==</span> <span class="mf">1.</span><span class="p">:</span>
        <span class="n">data</span><span class="o">.</span><span class="n">shape</span> <span class="o">=</span> <span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">mask</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">mask</span><span class="o">.</span><span class="n">shape</span> <span class="o">=</span> <span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">sample_selector</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>

    <span class="n">bindata</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span>
        <span class="p">(</span><span class="n">T</span> <span class="o">//</span> <span class="n">time_bin_length</span><span class="p">,)</span> <span class="o">+</span> <span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">:],</span> <span class="n">dtype</span><span class="o">=</span><span class="s2">&quot;float32&quot;</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">index</span><span class="p">,</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">T</span> <span class="o">-</span> <span class="n">time_bin_length</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span>
                                    <span class="n">time_bin_length</span><span class="p">)):</span>
        <span class="c1"># print weighted_avg_and_std(fulldata[i:i+time_bin_length], axis=0,</span>
        <span class="c1"># weights=sample_selector[i:i+time_bin_length])[0]</span>
        <span class="n">bindata</span><span class="p">[</span><span class="n">index</span><span class="p">]</span> <span class="o">=</span> <span class="n">weighted_avg_and_std</span><span class="p">(</span><span class="n">data</span><span class="p">[</span><span class="n">i</span><span class="p">:</span><span class="n">i</span> <span class="o">+</span> <span class="n">time_bin_length</span><span class="p">],</span>
                                              <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                                              <span class="n">weights</span><span class="o">=</span><span class="n">sample_selector</span><span class="p">[</span><span class="n">i</span><span class="p">:</span><span class="n">i</span> <span class="o">+</span>
                                              <span class="n">time_bin_length</span><span class="p">])[</span><span class="mi">0</span><span class="p">]</span>

    <span class="n">T</span><span class="p">,</span> <span class="n">grid_size</span> <span class="o">=</span> <span class="n">bindata</span><span class="o">.</span><span class="n">shape</span>

    <span class="k">return</span> <span class="p">(</span><span class="n">bindata</span><span class="o">.</span><span class="n">squeeze</span><span class="p">(),</span> <span class="n">T</span><span class="p">)</span></div>

<div class="viewcode-block" id="trafo2normal"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.trafo2normal">[docs]</a><span class="k">def</span> <span class="nf">trafo2normal</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">mask</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">thres</span><span class="o">=</span><span class="mf">0.001</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Transforms input data to standard normal marginals.</span>

<span class="sd">    Assumes data.shape = (T, dim)</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    data : array</span>
<span class="sd">        Data array of shape (time, variables).</span>
<span class="sd">    thres : float</span>
<span class="sd">        Set outer points in CDF to this value.</span>
<span class="sd">    mask : bool array, optional (default: None)</span>
<span class="sd">        Data mask where True labels masked samples.</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    normal_data : array-like</span>
<span class="sd">        data with standard normal marginals.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span> <span class="nf">trafo</span><span class="p">(</span><span class="n">xi</span><span class="p">):</span>
        <span class="n">xisorted</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">xi</span><span class="p">)</span>
        <span class="n">yi</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">linspace</span><span class="p">(</span><span class="mf">1.</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">xi</span><span class="p">),</span> <span class="mi">1</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">xi</span><span class="p">))</span>
        <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">interp</span><span class="p">(</span><span class="n">xi</span><span class="p">,</span> <span class="n">xisorted</span><span class="p">,</span> <span class="n">yi</span><span class="p">)</span>

    <span class="n">normal_data</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">copy</span><span class="p">(</span><span class="n">data</span><span class="p">)</span>

    <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">mask</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">nonmasked</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">==</span> <span class="kc">False</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">nonmasked</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">((</span><span class="n">mask</span><span class="o">==</span><span class="mi">0</span><span class="p">)</span><span class="o">*</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">data</span><span class="p">)</span> <span class="o">==</span> <span class="kc">False</span><span class="p">))</span>

        <span class="n">u</span> <span class="o">=</span> <span class="n">trafo</span><span class="p">(</span><span class="n">data</span><span class="p">[</span><span class="n">nonmasked</span><span class="p">])</span>
        <span class="n">u</span><span class="p">[</span><span class="n">u</span><span class="o">==</span><span class="mf">0.</span><span class="p">]</span> <span class="o">=</span> <span class="n">thres</span>
        <span class="n">u</span><span class="p">[</span><span class="n">u</span><span class="o">==</span><span class="mf">1.</span><span class="p">]</span> <span class="o">=</span> <span class="mf">1.</span> <span class="o">-</span> <span class="n">thres</span>
        <span class="n">normal_data</span><span class="p">[</span><span class="n">nonmasked</span><span class="p">]</span> <span class="o">=</span> <span class="n">stats</span><span class="o">.</span><span class="n">norm</span><span class="o">.</span><span class="n">ppf</span><span class="p">(</span><span class="n">u</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]):</span>
            <span class="k">if</span> <span class="n">mask</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">nonmasked</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">data</span><span class="p">[:,</span><span class="n">i</span><span class="p">])</span> <span class="o">==</span> <span class="kc">False</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">nonmasked</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">((</span><span class="n">mask</span><span class="p">[:,</span> <span class="n">i</span><span class="p">]</span><span class="o">==</span><span class="mi">0</span><span class="p">)</span><span class="o">*</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">])</span> <span class="o">==</span> <span class="kc">False</span><span class="p">))</span>
                <span class="c1"># nonmasked = np.where(mask[:, i]==0)</span>
            <span class="c1"># print(data[:, i].shape, nonmasked.shape)</span>
            <span class="n">uniform</span> <span class="o">=</span> <span class="n">trafo</span><span class="p">(</span><span class="n">data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">][</span><span class="n">nonmasked</span><span class="p">])</span>
            
            <span class="c1"># print(data[-3:, i][nonmasked])</span>

            <span class="n">uniform</span><span class="p">[</span><span class="n">uniform</span><span class="o">==</span><span class="mf">0.</span><span class="p">]</span> <span class="o">=</span> <span class="n">thres</span>
            <span class="n">uniform</span><span class="p">[</span><span class="n">uniform</span><span class="o">==</span><span class="mf">1.</span><span class="p">]</span> <span class="o">=</span> <span class="mf">1.</span> <span class="o">-</span> <span class="n">thres</span>
            <span class="n">normal_data</span><span class="p">[:,</span> <span class="n">i</span><span class="p">][</span><span class="n">nonmasked</span><span class="p">]</span> <span class="o">=</span> <span class="n">stats</span><span class="o">.</span><span class="n">norm</span><span class="o">.</span><span class="n">ppf</span><span class="p">(</span><span class="n">uniform</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">normal_data</span></div>

<span class="nd">@jit</span><span class="p">(</span><span class="n">nopython</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">_get_patterns</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">array_mask</span><span class="p">,</span> <span class="n">patt</span><span class="p">,</span> <span class="n">patt_mask</span><span class="p">,</span> <span class="n">weights</span><span class="p">,</span> <span class="n">dim</span><span class="p">,</span> <span class="n">step</span><span class="p">,</span> <span class="n">fac</span><span class="p">,</span> <span class="n">N</span><span class="p">,</span> <span class="n">T</span><span class="p">):</span>
    <span class="n">v</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">dim</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;float&#39;</span><span class="p">)</span>

    <span class="n">start</span> <span class="o">=</span> <span class="n">step</span> <span class="o">*</span> <span class="p">(</span><span class="n">dim</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">n</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">N</span><span class="p">):</span>
        <span class="k">for</span> <span class="n">t</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">start</span><span class="p">,</span> <span class="n">T</span><span class="p">):</span>
            <span class="n">mask</span> <span class="o">=</span> <span class="mi">1</span>
            <span class="n">ave</span> <span class="o">=</span> <span class="mf">0.</span>
            <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">dim</span><span class="p">):</span>
                <span class="n">tau</span> <span class="o">=</span> <span class="n">k</span> <span class="o">*</span> <span class="n">step</span>
                <span class="n">v</span><span class="p">[</span><span class="n">k</span><span class="p">]</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">t</span> <span class="o">-</span> <span class="n">tau</span><span class="p">,</span> <span class="n">n</span><span class="p">]</span>
                <span class="n">ave</span> <span class="o">+=</span> <span class="n">v</span><span class="p">[</span><span class="n">k</span><span class="p">]</span>
                <span class="n">mask</span> <span class="o">*=</span> <span class="n">array_mask</span><span class="p">[</span><span class="n">t</span> <span class="o">-</span> <span class="n">tau</span><span class="p">,</span> <span class="n">n</span><span class="p">]</span>
            <span class="n">ave</span> <span class="o">/=</span> <span class="n">dim</span>
            <span class="n">var</span> <span class="o">=</span> <span class="mf">0.</span>
            <span class="k">for</span> <span class="n">k</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">dim</span><span class="p">):</span>
                <span class="n">var</span> <span class="o">+=</span> <span class="p">(</span><span class="n">v</span><span class="p">[</span><span class="n">k</span><span class="p">]</span> <span class="o">-</span> <span class="n">ave</span><span class="p">)</span> <span class="o">**</span> <span class="mi">2</span>
            <span class="n">var</span> <span class="o">/=</span> <span class="n">dim</span>
            <span class="n">weights</span><span class="p">[</span><span class="n">t</span> <span class="o">-</span> <span class="n">start</span><span class="p">,</span> <span class="n">n</span><span class="p">]</span> <span class="o">=</span> <span class="n">var</span>
            <span class="k">if</span> <span class="p">(</span><span class="n">v</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">&lt;</span> <span class="n">v</span><span class="p">[</span><span class="mi">1</span><span class="p">]):</span>
                <span class="n">p</span> <span class="o">=</span> <span class="mi">1</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">p</span> <span class="o">=</span> <span class="mi">0</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="n">dim</span><span class="p">):</span>
                <span class="k">for</span> <span class="n">j</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">i</span><span class="p">):</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">v</span><span class="p">[</span><span class="n">j</span><span class="p">]</span> <span class="o">&lt;</span> <span class="n">v</span><span class="p">[</span><span class="n">i</span><span class="p">]):</span>
                        <span class="n">p</span> <span class="o">+=</span> <span class="n">fac</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>
            <span class="n">patt</span><span class="p">[</span><span class="n">t</span> <span class="o">-</span> <span class="n">start</span><span class="p">,</span> <span class="n">n</span><span class="p">]</span> <span class="o">=</span> <span class="n">p</span>
            <span class="n">patt_mask</span><span class="p">[</span><span class="n">t</span> <span class="o">-</span> <span class="n">start</span><span class="p">,</span> <span class="n">n</span><span class="p">]</span> <span class="o">=</span> <span class="n">mask</span>

    <span class="k">return</span> <span class="n">patt</span><span class="p">,</span> <span class="n">patt_mask</span><span class="p">,</span> <span class="n">weights</span>

<div class="viewcode-block" id="ordinal_patt_array"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.ordinal_patt_array">[docs]</a><span class="k">def</span> <span class="nf">ordinal_patt_array</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">array_mask</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">dim</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">step</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span>
                        <span class="n">weights</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns symbolified array of ordinal patterns.</span>

<span class="sd">    Each data vector (X_t, ..., X_t+(dim-1)*step) is converted to its rank</span>
<span class="sd">    vector. E.g., (0.2, -.6, 1.2) --&gt; (1,0,2) which is then assigned to a</span>
<span class="sd">    unique integer (see Article). There are faculty(dim) possible rank vectors.</span>

<span class="sd">    Note that the symb_array is step*(dim-1) shorter than the original array!</span>

<span class="sd">    Reference: B. Pompe and J. Runge (2011). Momentary information transfer as</span>
<span class="sd">    a coupling measure of time series. Phys. Rev. E, 83(5), 1-12.</span>
<span class="sd">    doi:10.1103/PhysRevE.83.051122</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    array : array-like</span>
<span class="sd">        Data array of shape (time, variables).</span>
<span class="sd">    array_mask : bool array</span>
<span class="sd">        Data mask where True labels masked samples.</span>
<span class="sd">    dim : int, optional (default: 2)</span>
<span class="sd">        Pattern dimension</span>
<span class="sd">    step : int, optional (default: 1)</span>
<span class="sd">        Delay of pattern embedding vector.</span>
<span class="sd">    weights : bool, optional (default: False)</span>
<span class="sd">        Whether to return array of variances of embedding vectors as weights.</span>
<span class="sd">    verbosity : int, optional (default: 0)</span>
<span class="sd">        Level of verbosity.</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    patt, patt_mask [, patt_time] : tuple of arrays</span>
<span class="sd">        Tuple of converted pattern array and new length</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="kn">from</span> <span class="nn">scipy.misc</span> <span class="kn">import</span> <span class="n">factorial</span>

    <span class="n">array</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;float64&#39;</span><span class="p">)</span>

    <span class="k">if</span> <span class="n">array_mask</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="k">assert</span> <span class="n">array_mask</span><span class="o">.</span><span class="n">dtype</span> <span class="o">==</span> <span class="s1">&#39;int32&#39;</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">array_mask</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">array</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>


    <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">array</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
        <span class="n">T</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">array</span><span class="p">)</span>
        <span class="n">array</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
        <span class="n">array_mask</span> <span class="o">=</span> <span class="n">array_mask</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>

    <span class="c1"># Add noise to destroy ties...</span>
    <span class="n">array</span> <span class="o">+=</span> <span class="p">(</span><span class="mf">1E-6</span> <span class="o">*</span> <span class="n">array</span><span class="o">.</span><span class="n">std</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
              <span class="o">*</span> <span class="n">random_state</span><span class="o">.</span><span class="n">random</span><span class="p">((</span><span class="n">array</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]))</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;float64&#39;</span><span class="p">))</span>

    <span class="n">patt_time</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">array</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">-</span> <span class="n">step</span> <span class="o">*</span> <span class="p">(</span><span class="n">dim</span> <span class="o">-</span> <span class="mi">1</span><span class="p">))</span>
    <span class="n">T</span><span class="p">,</span> <span class="n">N</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>

    <span class="k">if</span> <span class="n">dim</span> <span class="o">&lt;=</span> <span class="mi">1</span> <span class="ow">or</span> <span class="n">patt_time</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Dim mist be &gt; 1 and length of delay vector smaller &quot;</span>
                         <span class="s2">&quot;array length.&quot;</span><span class="p">)</span>

    <span class="n">patt</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">patt_time</span><span class="p">,</span> <span class="n">N</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>
    <span class="n">weights_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">patt_time</span><span class="p">,</span> <span class="n">N</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;float64&#39;</span><span class="p">)</span>

    <span class="n">patt_mask</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">patt_time</span><span class="p">,</span> <span class="n">N</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>

    <span class="c1"># Precompute factorial for c-code... patterns of dimension</span>
    <span class="c1"># larger than 10 are not supported</span>
    <span class="n">fac</span> <span class="o">=</span> <span class="n">factorial</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="mi">10</span><span class="p">))</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>

    <span class="c1"># _get_patterns assumes mask=0 to be a masked value</span>
    <span class="n">array_mask</span> <span class="o">=</span> <span class="p">(</span><span class="n">array_mask</span> <span class="o">==</span> <span class="kc">False</span><span class="p">)</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>

    <span class="p">(</span><span class="n">patt</span><span class="p">,</span> <span class="n">patt_mask</span><span class="p">,</span> <span class="n">weights_array</span><span class="p">)</span> <span class="o">=</span> <span class="n">_get_patterns</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">array_mask</span><span class="p">,</span> <span class="n">patt</span><span class="p">,</span> <span class="n">patt_mask</span><span class="p">,</span> <span class="n">weights_array</span><span class="p">,</span> <span class="n">dim</span><span class="p">,</span> <span class="n">step</span><span class="p">,</span> <span class="n">fac</span><span class="p">,</span> <span class="n">N</span><span class="p">,</span> <span class="n">T</span><span class="p">)</span>

    <span class="n">weights_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="n">weights_array</span><span class="p">)</span>
    <span class="n">patt</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="n">patt</span><span class="p">)</span>
    <span class="c1"># Transform back to mask=1 implying a masked value</span>
    <span class="n">patt_mask</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="n">patt_mask</span><span class="p">)</span> <span class="o">==</span> <span class="kc">False</span>

    <span class="k">if</span> <span class="n">weights</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">patt</span><span class="p">,</span> <span class="n">patt_mask</span><span class="p">,</span> <span class="n">patt_time</span><span class="p">,</span> <span class="n">weights_array</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="k">return</span> <span class="n">patt</span><span class="p">,</span> <span class="n">patt_mask</span><span class="p">,</span> <span class="n">patt_time</span></div>


<div class="viewcode-block" id="quantile_bin_array"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.quantile_bin_array">[docs]</a><span class="k">def</span> <span class="nf">quantile_bin_array</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">bins</span><span class="o">=</span><span class="mi">6</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns symbolified array with equal-quantile binning.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    data : array</span>
<span class="sd">        Data array of shape (time, variables).</span>
<span class="sd">    bins : int, optional (default: 6)</span>
<span class="sd">        Number of bins.</span>

<span class="sd">    Returns</span>
<span class="sd">    -------</span>
<span class="sd">    symb_array : array</span>
<span class="sd">        Converted data of integer type.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">T</span><span class="p">,</span> <span class="n">N</span> <span class="o">=</span> <span class="n">data</span><span class="o">.</span><span class="n">shape</span>

    <span class="c1"># get the bin quantile steps</span>
    <span class="n">bin_edge</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">ceil</span><span class="p">(</span><span class="n">T</span> <span class="o">/</span> <span class="nb">float</span><span class="p">(</span><span class="n">bins</span><span class="p">)))</span>

    <span class="n">symb_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">T</span><span class="p">,</span> <span class="n">N</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span>

    <span class="c1"># get the lower edges of the bins for every time series</span>
    <span class="n">edges</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)[::</span><span class="n">bin_edge</span><span class="p">,</span> <span class="p">:]</span><span class="o">.</span><span class="n">T</span>
    <span class="n">bins</span> <span class="o">=</span> <span class="n">edges</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>

    <span class="c1"># This gives the symbolic time series</span>
    <span class="n">symb_array</span> <span class="o">=</span> <span class="p">(</span><span class="n">data</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="n">T</span><span class="p">,</span> <span class="n">N</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="n">edges</span><span class="o">.</span><span class="n">reshape</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">N</span><span class="p">,</span> <span class="n">bins</span><span class="p">))</span><span class="o">.</span><span class="n">sum</span><span class="p">(</span>
        <span class="n">axis</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span> <span class="o">-</span> <span class="mi">1</span>

    <span class="k">return</span> <span class="n">symb_array</span><span class="o">.</span><span class="n">astype</span><span class="p">(</span><span class="s1">&#39;int32&#39;</span><span class="p">)</span></div>


<div class="viewcode-block" id="var_process"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.var_process">[docs]</a><span class="k">def</span> <span class="nf">var_process</span><span class="p">(</span><span class="n">parents_neighbors_coeffs</span><span class="p">,</span> <span class="n">T</span><span class="o">=</span><span class="mi">1000</span><span class="p">,</span> <span class="n">use</span><span class="o">=</span><span class="s1">&#39;inv_inno_cov&#39;</span><span class="p">,</span>
                <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">initial_values</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns a vector-autoregressive process with correlated innovations.</span>

<span class="sd">    Wrapper around var_network with possibly more user-friendly input options.</span>

<span class="sd">    DEPRECATED. Will be removed in future.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;data generating models are now in toymodels folder: &quot;</span>
         <span class="s2">&quot;from tigramite.toymodels import structural_causal_processes as toys.&quot;</span><span class="p">)</span>
    <span class="k">return</span> <span class="kc">None</span></div>

<div class="viewcode-block" id="structural_causal_process"><a class="viewcode-back" href="../../index.html#tigramite.data_processing.structural_causal_process">[docs]</a><span class="k">def</span> <span class="nf">structural_causal_process</span><span class="p">(</span><span class="n">links</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">noises</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> 
                        <span class="n">intervention</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">intervention_type</span><span class="o">=</span><span class="s1">&#39;hard&#39;</span><span class="p">,</span>
                        <span class="n">seed</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Returns a structural causal process with contemporaneous and lagged</span>
<span class="sd">    dependencies.</span>

<span class="sd">    DEPRECATED. Will be removed in future.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;data generating models are now in toymodels folder: &quot;</span>
         <span class="s2">&quot;from tigramite.toymodels import structural_causal_processes as toys.&quot;</span><span class="p">)</span>
    <span class="k">return</span> <span class="kc">None</span></div>


<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s1">&#39;__main__&#39;</span><span class="p">:</span>
    
    <span class="kn">from</span> <span class="nn">tigramite.toymodels.structural_causal_processes</span> <span class="kn">import</span> <span class="n">structural_causal_process</span>
    <span class="c1">## Generate some time series from a structural causal process</span>
    <span class="k">def</span> <span class="nf">lin_f</span><span class="p">(</span><span class="n">x</span><span class="p">):</span> <span class="k">return</span> <span class="n">x</span>
    <span class="k">def</span> <span class="nf">nonlin_f</span><span class="p">(</span><span class="n">x</span><span class="p">):</span> <span class="k">return</span> <span class="p">(</span><span class="n">x</span> <span class="o">+</span> <span class="mf">5.</span> <span class="o">*</span> <span class="n">x</span><span class="o">**</span><span class="mi">2</span> <span class="o">*</span> <span class="n">np</span><span class="o">.</span><span class="n">exp</span><span class="p">(</span><span class="o">-</span><span class="n">x</span><span class="o">**</span><span class="mi">2</span> <span class="o">/</span> <span class="mf">20.</span><span class="p">))</span>

    <span class="n">links</span> <span class="o">=</span> <span class="p">{</span><span class="mi">0</span><span class="p">:</span> <span class="p">[((</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.9</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">)],</span>
             <span class="mi">1</span><span class="p">:</span> <span class="p">[((</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.8</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">),</span> <span class="p">((</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.3</span><span class="p">,</span> <span class="n">nonlin_f</span><span class="p">)],</span>
             <span class="mi">2</span><span class="p">:</span> <span class="p">[((</span><span class="mi">2</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">),</span> <span class="mf">0.7</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">),</span> <span class="p">((</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="o">-</span><span class="mf">0.2</span><span class="p">,</span> <span class="n">lin_f</span><span class="p">)],</span>
             <span class="p">}</span>

    <span class="n">random_state_1</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">random_state_2</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
    <span class="n">random_state_3</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="o">=</span><span class="mi">3</span><span class="p">)</span>

    <span class="n">noises</span> <span class="o">=</span> <span class="p">[</span><span class="n">random_state_1</span><span class="o">.</span><span class="n">standard_normal</span><span class="p">,</span> <span class="n">random_state_2</span><span class="o">.</span><span class="n">standard_normal</span><span class="p">,</span> <span class="n">random_state_3</span><span class="o">.</span><span class="n">standard_normal</span><span class="p">]</span>

    <span class="n">ens</span> <span class="o">=</span> <span class="mi">3</span>
    <span class="n">data_ens</span> <span class="o">=</span> <span class="p">{}</span>
    <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">ens</span><span class="p">):</span>
        <span class="n">data</span><span class="p">,</span> <span class="n">nonstat</span> <span class="o">=</span> <span class="n">structural_causal_process</span><span class="p">(</span><span class="n">links</span><span class="p">,</span>
                <span class="n">T</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">noises</span><span class="o">=</span><span class="n">noises</span><span class="p">)</span>
        <span class="n">data</span><span class="p">[</span><span class="mi">10</span><span class="p">,</span> <span class="mi">1</span><span class="p">]</span> <span class="o">==</span> <span class="mf">999.</span>
        <span class="n">data_ens</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">data</span>
    <span class="c1"># print(data.shape)</span>

    <span class="n">frame</span> <span class="o">=</span> <span class="n">DataFrame</span><span class="p">(</span><span class="n">data_ens</span><span class="p">,</span> <span class="n">missing_flag</span><span class="o">=</span><span class="mf">999.</span><span class="p">,</span>
        <span class="n">analysis_mode</span> <span class="o">=</span> <span class="s1">&#39;multiple&#39;</span><span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="n">frame</span><span class="o">.</span><span class="n">T</span><span class="p">)</span>

    <span class="n">X</span><span class="o">=</span><span class="p">[(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span>
    <span class="n">Y</span><span class="o">=</span><span class="p">[(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)]</span>
    <span class="n">Z</span><span class="o">=</span><span class="p">[(</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="mi">3</span><span class="p">)]</span>
    <span class="n">tau_max</span><span class="o">=</span><span class="mi">5</span>
    <span class="n">frame</span><span class="o">.</span><span class="n">construct_array</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">tau_max</span><span class="p">,</span>
                        <span class="n">extraZ</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                        <span class="n">mask</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                        <span class="n">mask_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                        <span class="n">return_cleaned_xyz</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                        <span class="n">do_checks</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                        <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;2xtau_max&#39;</span><span class="p">,</span>
                        <span class="n">verbosity</span><span class="o">=</span><span class="mi">4</span><span class="p">)</span>
</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../index.html">Documentation overview</a><ul>
  <li><a href="../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;2023, Jakob Runge.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 5.0.2</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
    </div>

    

    
  </body>
</html>