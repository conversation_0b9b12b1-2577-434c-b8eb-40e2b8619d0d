
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>tigramite.independence_tests.independence_tests_base &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/alabaster.css" />
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
   
  <link rel="stylesheet" href="../../../_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>Source code for tigramite.independence_tests.independence_tests_base</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;Tigramite causal discovery for time series.&quot;&quot;&quot;</span>

<span class="c1"># Author: Jakob Runge &lt;<EMAIL>&gt;</span>
<span class="c1">#</span>
<span class="c1"># License: GNU General Public License v3.0</span>

<span class="kn">from</span> <span class="nn">__future__</span> <span class="kn">import</span> <span class="n">print_function</span>
<span class="kn">import</span> <span class="nn">warnings</span>
<span class="kn">import</span> <span class="nn">math</span>
<span class="kn">import</span> <span class="nn">abc</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>
<span class="kn">import</span> <span class="nn">six</span>
<span class="kn">from</span> <span class="nn">hashlib</span> <span class="kn">import</span> <span class="n">sha1</span>


<div class="viewcode-block" id="CondIndTest"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest">[docs]</a><span class="nd">@six</span><span class="o">.</span><span class="n">add_metaclass</span><span class="p">(</span><span class="n">abc</span><span class="o">.</span><span class="n">ABCMeta</span><span class="p">)</span>
<span class="k">class</span> <span class="nc">CondIndTest</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Base class of conditional independence tests.</span>

<span class="sd">    Provides useful general functions for different independence tests such as</span>
<span class="sd">    shuffle significance testing and bootstrap confidence estimation. Also</span>
<span class="sd">    handles masked samples. Other test classes can inherit from this class.</span>

<span class="sd">    Parameters</span>
<span class="sd">    ----------</span>
<span class="sd">    seed : int, optional(default = 42)</span>
<span class="sd">        Seed for RandomState (default_rng)</span>

<span class="sd">    mask_type : str, optional (default = None)</span>
<span class="sd">        Must be in {None, &#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">        Masking mode: Indicators for which variables in the dependence measure</span>
<span class="sd">        I(X; Y | Z) the samples should be masked. If None, the mask is not used. </span>
<span class="sd">        Explained in tutorial on masking and missing values.</span>

<span class="sd">    significance : str, optional (default: &#39;analytic&#39;)</span>
<span class="sd">        Type of significance test to use. In this package &#39;analytic&#39;,</span>
<span class="sd">        &#39;fixed_thres&#39; and &#39;shuffle_test&#39; are available.</span>

<span class="sd">    fixed_thres : float, optional (default: 0.1)</span>
<span class="sd">        Deprecated.</span>

<span class="sd">    sig_samples : int, optional (default: 500)</span>
<span class="sd">        Number of samples for shuffle significance test.</span>

<span class="sd">    sig_blocklength : int, optional (default: None)</span>
<span class="sd">        Block length for block-shuffle significance test. If None, the</span>
<span class="sd">        block length is determined from the decay of the autocovariance as</span>
<span class="sd">        explained in [1]_.</span>

<span class="sd">    confidence : str, optional (default: None)</span>
<span class="sd">        Specify type of confidence estimation. If False, numpy.nan is returned.</span>
<span class="sd">        &#39;bootstrap&#39; can be used with any test, for ParCorr also &#39;analytic&#39; is</span>
<span class="sd">        implemented.</span>

<span class="sd">    conf_lev : float, optional (default: 0.9)</span>
<span class="sd">        Two-sided confidence interval.</span>

<span class="sd">    conf_samples : int, optional (default: 100)</span>
<span class="sd">        Number of samples for bootstrap.</span>

<span class="sd">    conf_blocklength : int, optional (default: None)</span>
<span class="sd">        Block length for block-bootstrap. If None, the block length is</span>
<span class="sd">        determined from the decay of the autocovariance as explained in [1]_.</span>

<span class="sd">    recycle_residuals : bool, optional (default: False)</span>
<span class="sd">        Specifies whether residuals should be stored. This may be faster, but</span>
<span class="sd">        can cost considerable memory.</span>

<span class="sd">    verbosity : int, optional (default: 0)</span>
<span class="sd">        Level of verbosity.</span>
<span class="sd">    &quot;&quot;&quot;</span>
<div class="viewcode-block" id="CondIndTest.get_dependence_measure"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_dependence_measure">[docs]</a>    <span class="nd">@abc</span><span class="o">.</span><span class="n">abstractmethod</span>
    <span class="k">def</span> <span class="nf">get_dependence_measure</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Abstract function that all concrete classes must instantiate.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">pass</span></div>

    <span class="nd">@abc</span><span class="o">.</span><span class="n">abstractproperty</span>
    <span class="k">def</span> <span class="nf">measure</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Abstract property to store the type of independence test.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">pass</span>

    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span>
                 <span class="n">seed</span><span class="o">=</span><span class="mi">42</span><span class="p">,</span>
                 <span class="n">mask_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">significance</span><span class="o">=</span><span class="s1">&#39;analytic&#39;</span><span class="p">,</span>
                 <span class="n">fixed_thres</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">sig_samples</span><span class="o">=</span><span class="mi">500</span><span class="p">,</span>
                 <span class="n">sig_blocklength</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">confidence</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">conf_lev</span><span class="o">=</span><span class="mf">0.9</span><span class="p">,</span>
                 <span class="n">conf_samples</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
                 <span class="n">conf_blocklength</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                 <span class="n">recycle_residuals</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                 <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
        <span class="c1"># Set the dataframe to None for now, will be reset during pcmci call</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="c1"># Set the options</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">random</span><span class="o">.</span><span class="n">default_rng</span><span class="p">(</span><span class="n">seed</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">significance</span> <span class="o">=</span> <span class="n">significance</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">sig_samples</span> <span class="o">=</span> <span class="n">sig_samples</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">sig_blocklength</span> <span class="o">=</span> <span class="n">sig_blocklength</span>
        <span class="k">if</span> <span class="n">fixed_thres</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;fixed_thres is replaced by providing alpha_or_thres in run_test&quot;</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">=</span> <span class="n">verbosity</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">cached_ci_results</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">ci_results</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="c1"># If we recycle residuals, then set up a residual cache</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span> <span class="o">=</span> <span class="n">recycle_residuals</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">residuals</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="c1"># If we use a mask, we cannot recycle residuals</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">set_mask_type</span><span class="p">(</span><span class="n">mask_type</span><span class="p">)</span>

        <span class="c1"># Set the confidence type and details</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span> <span class="o">=</span> <span class="n">confidence</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">conf_lev</span> <span class="o">=</span> <span class="n">conf_lev</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">conf_samples</span> <span class="o">=</span> <span class="n">conf_samples</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">conf_blocklength</span> <span class="o">=</span> <span class="n">conf_blocklength</span>

        <span class="c1"># Print information about the</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">print_info</span><span class="p">()</span>

<div class="viewcode-block" id="CondIndTest.set_mask_type"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.set_mask_type">[docs]</a>    <span class="k">def</span> <span class="nf">set_mask_type</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">mask_type</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Setter for mask type to ensure that this option does not clash with</span>
<span class="sd">        recycle_residuals.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        mask_type : str</span>
<span class="sd">            Must be in {None, &#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">            Masking mode: Indicators for which variables in the dependence measure</span>
<span class="sd">            I(X; Y | Z) the samples should be masked. If None, the mask is not used. </span>
<span class="sd">            Explained in tutorial on masking and missing values.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Set the mask type</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="o">=</span> <span class="n">mask_type</span>
        <span class="c1"># Check if this clashes with residual recycling</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span> <span class="ow">is</span> <span class="kc">True</span><span class="p">:</span>
                <span class="n">warnings</span><span class="o">.</span><span class="n">warn</span><span class="p">(</span><span class="s2">&quot;Using a mask disables recycling residuals.&quot;</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="c1"># Check the mask type is keyed correctly</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_check_mask_type</span><span class="p">()</span></div>

<div class="viewcode-block" id="CondIndTest.print_info"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.print_info">[docs]</a>    <span class="k">def</span> <span class="nf">print_info</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Print information about the conditional independence test parameters</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">info_str</span> <span class="o">=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2"># Initialize conditional independence test</span><span class="se">\n\n</span><span class="s2">Parameters:&quot;</span>
        <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">independence test = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">measure</span>
        <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">significance = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span>
        <span class="c1"># Check if we are using a shuffle test</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span> <span class="o">==</span> <span class="s1">&#39;shuffle_test&#39;</span><span class="p">:</span>
            <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">sig_samples = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">sig_samples</span>
            <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">sig_blocklength = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">sig_blocklength</span>
        <span class="c1"># # Check if we are using a fixed threshold</span>
        <span class="c1"># elif self.significance == &#39;fixed_thres&#39;:</span>
        <span class="c1">#     info_str += &quot;\nfixed_thres = %s&quot; % self.fixed_thres</span>
        <span class="c1"># Check if we have a confidence type</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span><span class="p">:</span>
            <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">confidence = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span>
            <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">conf_lev = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">conf_lev</span>
        <span class="c1"># Check if this confidence type is boostrapping</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span> <span class="o">==</span> <span class="s1">&#39;bootstrap&#39;</span><span class="p">:</span>
            <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">conf_samples = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">conf_samples</span>
            <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">conf_blocklength = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span><span class="bp">self</span><span class="o">.</span><span class="n">conf_blocklength</span>
        <span class="c1"># Check if we use a non-trivial mask type</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">mask_type = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span>
        <span class="c1"># Check if we are recycling residuals or not</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span><span class="p">:</span>
            <span class="n">info_str</span> <span class="o">+=</span> <span class="s2">&quot;</span><span class="se">\n</span><span class="s2">recycle_residuals = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span>
        <span class="c1"># Print the information string</span>
        <span class="nb">print</span><span class="p">(</span><span class="n">info_str</span><span class="p">)</span></div>

    <span class="k">def</span> <span class="nf">_check_mask_type</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        mask_type : str, optional (default = None)</span>
<span class="sd">            Must be in {None, &#39;y&#39;,&#39;x&#39;,&#39;z&#39;,&#39;xy&#39;,&#39;xz&#39;,&#39;yz&#39;,&#39;xyz&#39;}</span>
<span class="sd">            Masking mode: Indicators for which variables in the dependence measure</span>
<span class="sd">            I(X; Y | Z) the samples should be masked. If None, the mask is not used. </span>
<span class="sd">            Explained in tutorial on masking and missing values.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">mask_set</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">)</span> <span class="o">-</span> <span class="nb">set</span><span class="p">([</span><span class="s1">&#39;x&#39;</span><span class="p">,</span> <span class="s1">&#39;y&#39;</span><span class="p">,</span> <span class="s1">&#39;z&#39;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">mask_set</span><span class="p">:</span>
                <span class="n">err_msg</span> <span class="o">=</span> <span class="s2">&quot;mask_type = </span><span class="si">%s</span><span class="s2">,&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="o">+</span> <span class="s2">&quot; but must be&quot;</span> <span class="o">+</span>\
                          <span class="s2">&quot; list containing &#39;x&#39;,&#39;y&#39;,&#39;z&#39;, or any combination&quot;</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="n">err_msg</span><span class="p">)</span>


<div class="viewcode-block" id="CondIndTest.get_analytic_confidence"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_analytic_confidence">[docs]</a>    <span class="k">def</span> <span class="nf">get_analytic_confidence</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">df</span><span class="p">,</span> <span class="n">conf_lev</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Base class assumption that this is not implemented.  Concrete classes</span>
<span class="sd">        should override when possible.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">raise</span> <span class="ne">NotImplementedError</span><span class="p">(</span><span class="s2">&quot;Analytic confidence not&quot;</span><span class="o">+</span>\
                                  <span class="s2">&quot; implemented for </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">measure</span><span class="p">)</span></div>

<div class="viewcode-block" id="CondIndTest.get_model_selection_criterion"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_model_selection_criterion">[docs]</a>    <span class="k">def</span> <span class="nf">get_model_selection_criterion</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">j</span><span class="p">,</span> <span class="n">parents</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Base class assumption that this is not implemented.  Concrete classes</span>
<span class="sd">        should override when possible.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">raise</span> <span class="ne">NotImplementedError</span><span class="p">(</span><span class="s2">&quot;Model selection not&quot;</span><span class="o">+</span>\
                                  <span class="s2">&quot; implemented for </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">measure</span><span class="p">)</span></div>

<div class="viewcode-block" id="CondIndTest.get_analytic_significance"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_analytic_significance">[docs]</a>    <span class="k">def</span> <span class="nf">get_analytic_significance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">dim</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Base class assumption that this is not implemented.  Concrete classes</span>
<span class="sd">        should override when possible.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">raise</span> <span class="ne">NotImplementedError</span><span class="p">(</span><span class="s2">&quot;Analytic significance not&quot;</span><span class="o">+</span>\
                                  <span class="s2">&quot; implemented for </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">measure</span><span class="p">)</span></div>

<div class="viewcode-block" id="CondIndTest.get_shuffle_significance"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_shuffle_significance">[docs]</a>    <span class="k">def</span> <span class="nf">get_shuffle_significance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span>
                                 <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                 <span class="n">return_null_dist</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Base class assumption that this is not implemented.  Concrete classes</span>
<span class="sd">        should override when possible.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">raise</span> <span class="ne">NotImplementedError</span><span class="p">(</span><span class="s2">&quot;Shuffle significance not&quot;</span><span class="o">+</span>\
                                  <span class="s2">&quot; implemented for </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">measure</span><span class="p">)</span></div>

    <span class="k">def</span> <span class="nf">_get_single_residuals</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">target_var</span><span class="p">,</span>
                              <span class="n">standardize</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">return_means</span><span class="o">=</span><span class="kc">False</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Base class assumption that this is not implemented.  Concrete classes</span>
<span class="sd">        should override when possible.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">raise</span> <span class="ne">NotImplementedError</span><span class="p">(</span><span class="s2">&quot;Residual calculation not&quot;</span><span class="o">+</span>\
                                  <span class="s2">&quot; implemented for </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">measure</span><span class="p">)</span>

<div class="viewcode-block" id="CondIndTest.set_dataframe"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.set_dataframe">[docs]</a>    <span class="k">def</span> <span class="nf">set_dataframe</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">dataframe</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Initialize and check the dataframe.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        dataframe : data object</span>
<span class="sd">            Set tigramite dataframe object. It must have the attributes</span>
<span class="sd">            dataframe.values yielding a numpy array of shape (observations T,</span>
<span class="sd">            variables N) and optionally a mask of the same shape and a missing</span>
<span class="sd">            values flag.</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span> <span class="o">=</span> <span class="n">dataframe</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">dataframe</span><span class="o">.</span><span class="n">mask</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;mask_type is not None, but no mask in dataframe.&quot;</span><span class="p">)</span>
            <span class="n">dataframe</span><span class="o">.</span><span class="n">_check_mask</span><span class="p">(</span><span class="n">dataframe</span><span class="o">.</span><span class="n">mask</span><span class="p">)</span></div>

    <span class="k">def</span> <span class="nf">_keyfy</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">z</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to make lists unique.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="p">(</span><span class="nb">tuple</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">x</span><span class="p">)),</span> <span class="nb">tuple</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">z</span><span class="p">)))</span>

    <span class="k">def</span> <span class="nf">_get_array</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;2xtau_max&#39;</span><span class="p">,</span>
                   <span class="n">remove_constant_data</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                   <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Convencience wrapper around construct_array.&quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">measure</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;par_corr&#39;</span><span class="p">,</span> <span class="s1">&#39;par_corr_wls&#39;</span><span class="p">,</span> <span class="s1">&#39;robust_par_corr&#39;</span><span class="p">,</span> <span class="s1">&#39;regressionCI&#39;</span><span class="p">,</span> 
                            <span class="s1">&#39;gsquared&#39;</span><span class="p">,</span> <span class="s1">&#39;gp_dc&#39;</span><span class="p">]:</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">X</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="n">Y</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;X and Y for </span><span class="si">%s</span><span class="s2"> must be univariate.&quot;</span> <span class="o">%</span>
                                        <span class="bp">self</span><span class="o">.</span><span class="n">measure</span><span class="p">)</span>
        <span class="c1"># Call the wrapped function</span>
        <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">,</span> <span class="n">type_array</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">dataframe</span><span class="o">.</span><span class="n">construct_array</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="o">=</span><span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="n">Z</span><span class="p">,</span>
                                              <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                                              <span class="n">mask_type</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">mask_type</span><span class="p">,</span>
                                              <span class="n">return_cleaned_xyz</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                              <span class="n">do_checks</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                              <span class="n">remove_overlaps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                              <span class="n">cut_off</span><span class="o">=</span><span class="n">cut_off</span><span class="p">,</span>
                                              <span class="n">verbosity</span><span class="o">=</span><span class="n">verbosity</span><span class="p">)</span>
        
        <span class="k">if</span> <span class="n">remove_constant_data</span><span class="p">:</span>
            <span class="n">zero_components</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">array</span><span class="o">.</span><span class="n">std</span><span class="p">(</span><span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span><span class="o">==</span><span class="mf">0.</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

            <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span> <span class="o">=</span> <span class="n">XYZ</span>
            <span class="n">x_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">newX</span> <span class="o">=</span> <span class="p">[</span><span class="n">X</span><span class="p">[</span><span class="n">entry</span><span class="p">]</span> <span class="k">for</span> <span class="n">entry</span><span class="p">,</span> <span class="n">ind</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">x_indices</span><span class="p">)</span> <span class="k">if</span> <span class="n">ind</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">zero_components</span><span class="p">]</span>

            <span class="n">y_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">1</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">newY</span> <span class="o">=</span> <span class="p">[</span><span class="n">Y</span><span class="p">[</span><span class="n">entry</span><span class="p">]</span> <span class="k">for</span> <span class="n">entry</span><span class="p">,</span> <span class="n">ind</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">y_indices</span><span class="p">)</span> <span class="k">if</span> <span class="n">ind</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">zero_components</span><span class="p">]</span>

            <span class="n">z_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">2</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
            <span class="n">newZ</span> <span class="o">=</span> <span class="p">[</span><span class="n">Z</span><span class="p">[</span><span class="n">entry</span><span class="p">]</span> <span class="k">for</span> <span class="n">entry</span><span class="p">,</span> <span class="n">ind</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">z_indices</span><span class="p">)</span> <span class="k">if</span> <span class="n">ind</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">zero_components</span><span class="p">]</span>

            <span class="n">nonzero_XYZ</span> <span class="o">=</span> <span class="p">(</span><span class="n">newX</span><span class="p">,</span> <span class="n">newY</span><span class="p">,</span> <span class="n">newZ</span><span class="p">)</span>

            <span class="n">nonzero_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">zero_components</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="n">nonzero_xyz</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="n">xyz</span><span class="p">,</span> <span class="n">zero_components</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">type_array</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">nonzero_type_array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="n">type_array</span><span class="p">,</span> <span class="n">zero_components</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">nonzero_type_array</span> <span class="o">=</span> <span class="kc">None</span>

            <span class="k">return</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">,</span> <span class="n">type_array</span><span class="p">,</span> <span class="n">nonzero_array</span><span class="p">,</span> <span class="n">nonzero_xyz</span><span class="p">,</span> <span class="n">nonzero_XYZ</span><span class="p">,</span> <span class="n">nonzero_type_array</span>

        <span class="k">return</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">,</span> <span class="n">type_array</span>

    
    <span class="k">def</span> <span class="nf">_get_array_hash</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Helper function to get hash of array.</span>

<span class="sd">        For a CI test X _|_ Y | Z the order of variables within X or Y or Z </span>
<span class="sd">        does not matter and also the order X and Y can be swapped.</span>
<span class="sd">        Hence, to compare hashes of the whole array, we order accordingly</span>
<span class="sd">        to create a unique, order-independent hash. </span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        array : Data array of shape (dim, T)</span>
<span class="sd">            Data array.</span>
<span class="sd">        xyz : array</span>
<span class="sd">            Identifier array of shape (dim,) identifying which row in array</span>
<span class="sd">            corresponds to X, Y, and Z</span>
<span class="sd">        XYZ : list of tuples</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        combined_hash : str</span>
<span class="sd">            Hash that identifies uniquely an array of XYZ      </span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span> <span class="o">=</span> <span class="n">XYZ</span>

        <span class="c1"># First check whether CI result was already computed</span>
        <span class="c1"># by checking whether hash of (xyz, array) already exists</span>
        <span class="c1"># Individually sort X, Y, Z since for a CI test it does not matter</span>
        <span class="c1"># how they are aranged</span>
        <span class="n">x_orderd</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">X</span><span class="p">)),</span> <span class="n">key</span><span class="o">=</span><span class="n">X</span><span class="o">.</span><span class="fm">__getitem__</span><span class="p">)</span>
        <span class="n">arr_x</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">xyz</span><span class="o">==</span><span class="mi">0</span><span class="p">][</span><span class="n">x_orderd</span><span class="p">]</span>
        <span class="n">x_hash</span> <span class="o">=</span> <span class="n">sha1</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">ascontiguousarray</span><span class="p">(</span><span class="n">arr_x</span><span class="p">))</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>

        <span class="n">y_orderd</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">Y</span><span class="p">)),</span> <span class="n">key</span><span class="o">=</span><span class="n">Y</span><span class="o">.</span><span class="fm">__getitem__</span><span class="p">)</span>
        <span class="n">arr_y</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">xyz</span><span class="o">==</span><span class="mi">1</span><span class="p">][</span><span class="n">y_orderd</span><span class="p">]</span>
        <span class="n">y_hash</span> <span class="o">=</span> <span class="n">sha1</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">ascontiguousarray</span><span class="p">(</span><span class="n">arr_y</span><span class="p">))</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>

        <span class="n">z_orderd</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span><span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">Z</span><span class="p">)),</span> <span class="n">key</span><span class="o">=</span><span class="n">Z</span><span class="o">.</span><span class="fm">__getitem__</span><span class="p">)</span>
        <span class="n">arr_z</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">xyz</span><span class="o">==</span><span class="mi">2</span><span class="p">][</span><span class="n">z_orderd</span><span class="p">]</span>
        <span class="n">z_hash</span> <span class="o">=</span> <span class="n">sha1</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">ascontiguousarray</span><span class="p">(</span><span class="n">arr_z</span><span class="p">))</span><span class="o">.</span><span class="n">hexdigest</span><span class="p">()</span>

        <span class="n">sorted_xy</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">([</span><span class="n">x_hash</span><span class="p">,</span> <span class="n">y_hash</span><span class="p">])</span>
        <span class="n">combined_hash</span> <span class="o">=</span> <span class="p">(</span><span class="n">sorted_xy</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">sorted_xy</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="n">z_hash</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">combined_hash</span>


<div class="viewcode-block" id="CondIndTest.run_test"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.run_test">[docs]</a>    <span class="k">def</span> <span class="nf">run_test</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">cut_off</span><span class="o">=</span><span class="s1">&#39;2xtau_max&#39;</span><span class="p">,</span> <span class="n">alpha_or_thres</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Perform conditional independence test.</span>

<span class="sd">        Calls the dependence measure and significance test functions. The child</span>
<span class="sd">        classes must specify a function get_dependence_measure and either or</span>
<span class="sd">        both functions get_analytic_significance and  get_shuffle_significance.</span>
<span class="sd">        If recycle_residuals is True, also _get_single_residuals must be</span>
<span class="sd">        available.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        X, Y, Z : list of tuples</span>
<span class="sd">            X,Y,Z are of the form [(var, -tau)], where var specifies the</span>
<span class="sd">            variable index and tau the time lag.</span>
<span class="sd">        tau_max : int, optional (default: 0)</span>
<span class="sd">            Maximum time lag. This may be used to make sure that estimates for</span>
<span class="sd">            different lags in X, Z, all have the same sample size.</span>
<span class="sd">        cut_off : {&#39;2xtau_max&#39;, &#39;max_lag&#39;, &#39;max_lag_or_tau_max&#39;}</span>
<span class="sd">            How many samples to cutoff at the beginning. The default is</span>
<span class="sd">            &#39;2xtau_max&#39;, which guarantees that MCI tests are all conducted on</span>
<span class="sd">            the same samples. For modeling, &#39;max_lag_or_tau_max&#39; can be used,</span>
<span class="sd">            which uses the maximum of tau_max and the conditions, which is</span>
<span class="sd">            useful to compare multiple models on the same sample.  Last,</span>
<span class="sd">            &#39;max_lag&#39; uses as much samples as possible.</span>
<span class="sd">        alpha_or_thres : float (optional)</span>
<span class="sd">            Significance level (if significance=&#39;analytic&#39; or &#39;shuffle_test&#39;) or</span>
<span class="sd">            threshold (if significance=&#39;fixed_thres&#39;). If given, run_test returns</span>
<span class="sd">            the test decision dependent=True/False.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        val, pval, [dependent] : Tuple of floats and bool</span>
<span class="sd">            The test statistic value and the p-value. If alpha_or_thres is</span>
<span class="sd">            given, run_test also returns the test decision dependent=True/False.      </span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span> <span class="o">==</span> <span class="s1">&#39;fixed_thres&#39;</span> <span class="ow">and</span> <span class="n">alpha_or_thres</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;significance == &#39;fixed_thres&#39; requires setting alpha_or_thres&quot;</span><span class="p">)</span>

        <span class="c1"># Get the array to test on</span>
        <span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">,</span> <span class="n">data_type</span><span class="p">,</span> 
         <span class="n">nonzero_array</span><span class="p">,</span> <span class="n">nonzero_xyz</span><span class="p">,</span> <span class="n">nonzero_XYZ</span><span class="p">,</span> <span class="n">nonzero_data_type</span><span class="p">)</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_array</span><span class="p">(</span>
                                            <span class="n">X</span><span class="o">=</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="o">=</span><span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="n">Z</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span> <span class="n">cut_off</span><span class="o">=</span><span class="n">cut_off</span><span class="p">,</span>
                                            <span class="n">remove_constant_data</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>
        <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span> <span class="o">=</span> <span class="n">XYZ</span>
        <span class="n">nonzero_X</span><span class="p">,</span> <span class="n">nonzero_Y</span><span class="p">,</span> <span class="n">nonzero_Z</span> <span class="o">=</span> <span class="n">nonzero_XYZ</span>

        <span class="c1"># Record the dimensions</span>
        <span class="c1"># dim, T = array.shape</span>

        <span class="c1"># Ensure it is a valid array</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">any</span><span class="p">(</span><span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">array</span><span class="p">)):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;nans in the array!&quot;</span><span class="p">)</span>

        <span class="n">combined_hash</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_array_hash</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">XYZ</span><span class="p">)</span>

        <span class="c1"># Get test statistic value and p-value [cached if possible]</span>
        <span class="k">if</span> <span class="n">combined_hash</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">cached_ci_results</span><span class="o">.</span><span class="n">keys</span><span class="p">():</span>
            <span class="n">cached</span> <span class="o">=</span> <span class="kc">True</span>
            <span class="n">val</span><span class="p">,</span> <span class="n">pval</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">cached_ci_results</span><span class="p">[</span><span class="n">combined_hash</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">cached</span> <span class="o">=</span> <span class="kc">False</span>

            <span class="c1"># If all X or all Y are zero, then return pval=1, val=0, dependent=False</span>
            <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">nonzero_X</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="n">nonzero_Y</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">val</span> <span class="o">=</span> <span class="mf">0.</span>
                <span class="n">pval</span> <span class="o">=</span> <span class="kc">None</span> <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span> <span class="o">==</span> <span class="s1">&#39;fixed_thres&#39;</span> <span class="k">else</span> <span class="mf">1.</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># Get the dependence measure, reycling residuals if need be</span>
                <span class="n">val</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_dependence_measure_recycle</span><span class="p">(</span><span class="n">nonzero_X</span><span class="p">,</span> <span class="n">nonzero_Y</span><span class="p">,</span> <span class="n">nonzero_Z</span><span class="p">,</span> 
                                            <span class="n">nonzero_xyz</span><span class="p">,</span> <span class="n">nonzero_array</span><span class="p">,</span> <span class="n">nonzero_data_type</span><span class="p">)</span>
                <span class="c1"># Get the p-value (None if significance = &#39;fixed_thres&#39;)</span>
                <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">nonzero_array</span><span class="o">.</span><span class="n">shape</span>
                <span class="n">pval</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_p_value</span><span class="p">(</span><span class="n">val</span><span class="o">=</span><span class="n">val</span><span class="p">,</span> <span class="n">array</span><span class="o">=</span><span class="n">nonzero_array</span><span class="p">,</span> <span class="n">xyz</span><span class="o">=</span><span class="n">nonzero_xyz</span><span class="p">,</span> <span class="n">T</span><span class="o">=</span><span class="n">T</span><span class="p">,</span> <span class="n">dim</span><span class="o">=</span><span class="n">dim</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">cached_ci_results</span><span class="p">[</span><span class="n">combined_hash</span><span class="p">]</span> <span class="o">=</span> <span class="p">(</span><span class="n">val</span><span class="p">,</span> <span class="n">pval</span><span class="p">)</span>

        <span class="c1"># Make test decision</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">nonzero_X</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="n">nonzero_Y</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">dependent</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="k">else</span><span class="p">:</span> 
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span> <span class="o">==</span> <span class="s1">&#39;fixed_thres&#39;</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">two_sided</span><span class="p">:</span>
                    <span class="n">dependent</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">val</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">alpha_or_thres</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">dependent</span> <span class="o">=</span> <span class="n">val</span> <span class="o">&gt;=</span> <span class="n">alpha_or_thres</span>
                <span class="n">pval</span> <span class="o">=</span> <span class="mf">0.</span> <span class="k">if</span> <span class="n">dependent</span> <span class="k">else</span> <span class="mf">1.</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">alpha_or_thres</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="n">dependent</span> <span class="o">=</span> <span class="kc">None</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">dependent</span> <span class="o">=</span> <span class="n">pval</span> <span class="o">&lt;=</span> <span class="n">alpha_or_thres</span>
                
        <span class="bp">self</span><span class="o">.</span><span class="n">ci_results</span><span class="p">[(</span><span class="nb">tuple</span><span class="p">(</span><span class="n">X</span><span class="p">),</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">Y</span><span class="p">),</span><span class="nb">tuple</span><span class="p">(</span><span class="n">Z</span><span class="p">))]</span> <span class="o">=</span> <span class="p">(</span><span class="n">val</span><span class="p">,</span> <span class="n">pval</span><span class="p">,</span> <span class="n">dependent</span><span class="p">)</span>

        <span class="c1"># Return the calculated value(s)</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">1</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_print_cond_ind_results</span><span class="p">(</span><span class="n">val</span><span class="o">=</span><span class="n">val</span><span class="p">,</span> <span class="n">pval</span><span class="o">=</span><span class="n">pval</span><span class="p">,</span> <span class="n">cached</span><span class="o">=</span><span class="n">cached</span><span class="p">,</span> <span class="n">dependent</span><span class="o">=</span><span class="n">dependent</span><span class="p">,</span>
                                         <span class="n">conf</span><span class="o">=</span><span class="kc">None</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">alpha_or_thres</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">val</span><span class="p">,</span> <span class="n">pval</span>
        <span class="k">else</span><span class="p">:</span>              
            <span class="k">return</span> <span class="n">val</span><span class="p">,</span> <span class="n">pval</span><span class="p">,</span> <span class="n">dependent</span></div>


<div class="viewcode-block" id="CondIndTest.run_test_raw"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.run_test_raw">[docs]</a>    <span class="k">def</span> <span class="nf">run_test_raw</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">z</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">x_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">y_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">z_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">alpha_or_thres</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Perform conditional independence test directly on input arrays x, y, z.</span>

<span class="sd">        Calls the dependence measure and signficicance test functions. The child</span>
<span class="sd">        classes must specify a function get_dependence_measure and either or</span>
<span class="sd">        both functions get_analytic_significance and  get_shuffle_significance.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x, y, z : arrays</span>
<span class="sd">            x,y,z are of the form (samples, dimension).</span>

<span class="sd">        x_type, y_type, z_type : array-like</span>
<span class="sd">            data arrays of same shape as x, y and z respectively, which describes whether variables</span>
<span class="sd">            are continuous or discrete: 0s for continuous variables and</span>
<span class="sd">            1s for discrete variables</span>

<span class="sd">        alpha_or_thres : float (optional)</span>
<span class="sd">            Significance level (if significance=&#39;analytic&#39; or &#39;shuffle_test&#39;) or</span>
<span class="sd">            threshold (if significance=&#39;fixed_thres&#39;). If given, run_test returns</span>
<span class="sd">            the test decision dependent=True/False.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        val, pval, [dependent] : Tuple of floats and bool</span>
<span class="sd">            The test statistic value and the p-value. If alpha_or_thres is</span>
<span class="sd">            given, run_test also returns the test decision dependent=True/False.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">x</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">2</span> <span class="ow">or</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">y</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">2</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;x,y must be arrays of shape (samples, dimension)&quot;</span>
                             <span class="s2">&quot; where dimension can be 1.&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">z</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">z</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">2</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;z must be array of shape (samples, dimension)&quot;</span>
                             <span class="s2">&quot; where dimension can be 1.&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">x_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">or</span> <span class="n">y_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">or</span> <span class="n">z_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">has_data_type</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">has_data_type</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="k">if</span> <span class="n">x_type</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">has_data_type</span><span class="p">:</span>
            <span class="n">x_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">x</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int&#39;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">y_type</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">has_data_type</span><span class="p">:</span>
            <span class="n">y_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">y</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int&#39;</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">z</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="c1"># Get the array to test on</span>
            <span class="n">array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">vstack</span><span class="p">((</span><span class="n">x</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">y</span><span class="o">.</span><span class="n">T</span><span class="p">))</span>
            <span class="k">if</span> <span class="n">has_data_type</span><span class="p">:</span>
                <span class="n">data_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">vstack</span><span class="p">((</span><span class="n">x_type</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">y_type</span><span class="o">.</span><span class="n">T</span><span class="p">))</span>

            <span class="c1"># xyz is the dimension indicator</span>
            <span class="n">xyz</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mi">0</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">x</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])]</span> <span class="o">+</span>
                           <span class="p">[</span><span class="mi">1</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">y</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])])</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Get the array to test on</span>
            <span class="n">array</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">vstack</span><span class="p">((</span><span class="n">x</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">y</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">z</span><span class="o">.</span><span class="n">T</span><span class="p">))</span>
            <span class="k">if</span> <span class="n">z_type</span> <span class="ow">is</span> <span class="kc">None</span> <span class="ow">and</span> <span class="n">has_data_type</span><span class="p">:</span>
                <span class="n">z_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">z</span><span class="o">.</span><span class="n">shape</span><span class="p">,</span> <span class="n">dtype</span><span class="o">=</span><span class="s1">&#39;int&#39;</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">has_data_type</span><span class="p">:</span>
                <span class="n">data_type</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">vstack</span><span class="p">((</span><span class="n">x_type</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">y_type</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">z_type</span><span class="o">.</span><span class="n">T</span><span class="p">))</span>
            <span class="c1"># xyz is the dimension indicator</span>
            <span class="n">xyz</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mi">0</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">x</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])]</span> <span class="o">+</span>
                           <span class="p">[</span><span class="mi">1</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">y</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])]</span> <span class="o">+</span>
                           <span class="p">[</span><span class="mi">2</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">z</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">])])</span>
        
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span> <span class="o">==</span> <span class="s1">&#39;fixed_thres&#39;</span> <span class="ow">and</span> <span class="n">alpha_or_thres</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;significance == &#39;fixed_thres&#39; requires setting alpha_or_thres&quot;</span><span class="p">)</span>

        <span class="c1"># Record the dimensions</span>
        <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>
        <span class="c1"># Ensure it is a valid array</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">array</span><span class="p">)</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;nans in the array!&quot;</span><span class="p">)</span>
        <span class="c1"># Get the dependence measure</span>
        <span class="k">if</span> <span class="n">has_data_type</span><span class="p">:</span>
            <span class="n">val</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">data_type</span><span class="o">=</span><span class="n">data_type</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">val</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">)</span>


        <span class="c1"># Get the p-value (returns None if significance=&#39;fixed_thres&#39;)</span>
        <span class="k">if</span> <span class="n">has_data_type</span><span class="p">:</span>
            <span class="n">pval</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_p_value</span><span class="p">(</span><span class="n">val</span><span class="o">=</span><span class="n">val</span><span class="p">,</span> <span class="n">array</span><span class="o">=</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="o">=</span><span class="n">xyz</span><span class="p">,</span>
                    <span class="n">T</span><span class="o">=</span><span class="n">T</span><span class="p">,</span> <span class="n">dim</span><span class="o">=</span><span class="n">dim</span><span class="p">,</span> <span class="n">data_type</span><span class="o">=</span><span class="n">data_type</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">pval</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_p_value</span><span class="p">(</span><span class="n">val</span><span class="o">=</span><span class="n">val</span><span class="p">,</span> <span class="n">array</span><span class="o">=</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="o">=</span><span class="n">xyz</span><span class="p">,</span>
                    <span class="n">T</span><span class="o">=</span><span class="n">T</span><span class="p">,</span> <span class="n">dim</span><span class="o">=</span><span class="n">dim</span><span class="p">)</span>

        <span class="c1"># Make test decision</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span> <span class="o">==</span> <span class="s1">&#39;fixed_thres&#39;</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">two_sided</span><span class="p">:</span>
                <span class="n">dependent</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">val</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">alpha_or_thres</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">dependent</span> <span class="o">=</span> <span class="n">val</span> <span class="o">&gt;=</span> <span class="n">alpha_or_thres</span>
            <span class="n">pval</span> <span class="o">=</span> <span class="mf">0.</span> <span class="k">if</span> <span class="n">dependent</span> <span class="k">else</span> <span class="mf">1.</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">alpha_or_thres</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
                <span class="n">dependent</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">dependent</span> <span class="o">=</span> <span class="n">pval</span> <span class="o">&lt;=</span> <span class="n">alpha_or_thres</span>

        <span class="c1"># Return the value and the pvalue</span>
        <span class="k">if</span> <span class="n">alpha_or_thres</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="n">val</span><span class="p">,</span> <span class="n">pval</span>
        <span class="k">else</span><span class="p">:</span>              
            <span class="k">return</span> <span class="n">val</span><span class="p">,</span> <span class="n">pval</span><span class="p">,</span> <span class="n">dependent</span></div>

    <span class="k">def</span> <span class="nf">_get_dependence_measure_recycle</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Get the dependence_measure, optionally recycling residuals</span>

<span class="sd">        If self.recycle_residuals is True, also _get_single_residuals must be</span>
<span class="sd">        available.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        X, Y, Z : list of tuples</span>
<span class="sd">            X,Y,Z are of the form [(var, -tau)], where var specifies the</span>
<span class="sd">            variable index and tau the time lag.</span>

<span class="sd">        xyz : array of ints</span>
<span class="sd">            XYZ identifier array of shape (dim,).</span>

<span class="sd">        array : array</span>
<span class="sd">            Data array of shape (dim, T)</span>

<span class="sd">       data_type : array-like</span>
<span class="sd">            Binary data array of same shape as array which describes whether </span>
<span class="sd">            individual samples in a variable (or all samples) are continuous </span>
<span class="sd">            or discrete: 0s for continuous variables and 1s for discrete variables.</span>

<span class="sd">        Return</span>
<span class="sd">        ------</span>
<span class="sd">        val : float</span>
<span class="sd">            Test statistic</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Check if we are recycling residuals</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">recycle_residuals</span><span class="p">:</span>
            <span class="c1"># Get or calculate the cached residuals</span>
            <span class="n">x_resid</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_cached_residuals</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">y_resid</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_cached_residuals</span><span class="p">(</span><span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
            <span class="c1"># Make a new residual array</span>
            <span class="n">array_resid</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="n">x_resid</span><span class="p">,</span> <span class="n">y_resid</span><span class="p">])</span>
            <span class="n">xyz_resid</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">array</span><span class="p">([</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">])</span>
            <span class="c1"># Return the dependence measure</span>
            <span class="c1"># data type can only be continuous in this case</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span><span class="p">(</span><span class="n">array_resid</span><span class="p">,</span> <span class="n">xyz_resid</span><span class="p">)</span>

        <span class="c1"># If not, return the dependence measure on the array and xyz</span>
        <span class="k">if</span> <span class="n">data_type</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> 
                                        <span class="n">data_type</span><span class="o">=</span><span class="n">data_type</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">_get_cached_residuals</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x_nodes</span><span class="p">,</span> <span class="n">z_nodes</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">target_var</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Retrieve or calculate the cached residuals for the given node sets.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">            x_nodes : list of tuples</span>
<span class="sd">                List of nodes, X or Y normally. Used to key the residual cache</span>
<span class="sd">                during lookup</span>

<span class="sd">            z_nodes : list of tuples</span>
<span class="sd">                List of nodes, Z normally</span>

<span class="sd">            target_var : int</span>
<span class="sd">                Key to differentiate X from Y.</span>
<span class="sd">                x_nodes == X =&gt; 0, x_nodes == Y =&gt; 1</span>

<span class="sd">            array : array</span>
<span class="sd">                Data array of shape (dim, T)</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">            x_resid : array</span>
<span class="sd">                Residuals calculated by _get_single_residual</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Check if we have calculated these residuals</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_keyfy</span><span class="p">(</span><span class="n">x_nodes</span><span class="p">,</span> <span class="n">z_nodes</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">list</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">residuals</span><span class="p">):</span>
            <span class="n">x_resid</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">residuals</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_keyfy</span><span class="p">(</span><span class="n">x_nodes</span><span class="p">,</span> <span class="n">z_nodes</span><span class="p">)]</span>
        <span class="c1"># If not, calculate the residuals</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">x_resid</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_single_residuals</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">target_var</span><span class="o">=</span><span class="n">target_var</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">z_nodes</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">residuals</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">_keyfy</span><span class="p">(</span><span class="n">x_nodes</span><span class="p">,</span> <span class="n">z_nodes</span><span class="p">)]</span> <span class="o">=</span> <span class="n">x_resid</span>
        <span class="c1"># Return these residuals</span>
        <span class="k">return</span> <span class="n">x_resid</span>

    <span class="k">def</span> <span class="nf">_get_p_value</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">val</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">T</span><span class="p">,</span> <span class="n">dim</span><span class="p">,</span>
                         <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                         <span class="n">sig_override</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Returns the p-value from whichever significance function is specified</span>
<span class="sd">        for this test. If an override is used, then it will call a different</span>
<span class="sd">        function then specified by self.significance</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        val : float</span>
<span class="sd">            Test statistic value.</span>

<span class="sd">        array : array-like</span>
<span class="sd">            data array with X, Y, Z in rows and observations in columns</span>

<span class="sd">        xyz : array of ints</span>
<span class="sd">            XYZ identifier array of shape (dim,).</span>

<span class="sd">        T : int</span>
<span class="sd">            Sample length</span>

<span class="sd">        dim : int</span>
<span class="sd">            Dimensionality, ie, number of features.</span>
<span class="sd">            </span>
<span class="sd">       data_type : array-like</span>
<span class="sd">            Binary data array of same shape as array which describes whether </span>
<span class="sd">            individual samples in a variable (or all samples) are continuous </span>
<span class="sd">            or discrete: 0s for continuous variables and 1s for discrete variables.</span>

<span class="sd">        sig_override : string</span>
<span class="sd">            Must be in &#39;analytic&#39;, &#39;shuffle_test&#39;, &#39;fixed_thres&#39;</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        pval : float or numpy.nan</span>
<span class="sd">            P-value.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Defaults to the self.significance member value</span>
        <span class="n">use_sig</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span>
        <span class="k">if</span> <span class="n">sig_override</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">use_sig</span> <span class="o">=</span> <span class="n">sig_override</span>
        <span class="c1"># Check if we are using the analytic significance</span>
        <span class="k">if</span> <span class="n">use_sig</span> <span class="o">==</span> <span class="s1">&#39;analytic&#39;</span><span class="p">:</span>
            <span class="n">pval</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_analytic_significance</span><span class="p">(</span><span class="n">value</span><span class="o">=</span><span class="n">val</span><span class="p">,</span> <span class="n">T</span><span class="o">=</span><span class="n">T</span><span class="p">,</span> <span class="n">dim</span><span class="o">=</span><span class="n">dim</span><span class="p">,</span> <span class="n">xyz</span><span class="o">=</span><span class="n">xyz</span><span class="p">)</span>
        <span class="c1"># Check if we are using the shuffle significance</span>
        <span class="k">elif</span> <span class="n">use_sig</span> <span class="o">==</span> <span class="s1">&#39;shuffle_test&#39;</span><span class="p">:</span>
            <span class="n">pval</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_shuffle_significance</span><span class="p">(</span><span class="n">array</span><span class="o">=</span><span class="n">array</span><span class="p">,</span>
                                                 <span class="n">xyz</span><span class="o">=</span><span class="n">xyz</span><span class="p">,</span>
                                                 <span class="n">value</span><span class="o">=</span><span class="n">val</span><span class="p">)</span>
        <span class="c1"># Check if we are using the fixed_thres significance</span>
        <span class="k">elif</span> <span class="n">use_sig</span> <span class="o">==</span> <span class="s1">&#39;fixed_thres&#39;</span><span class="p">:</span>
            <span class="c1"># Determined outside then</span>
            <span class="n">pval</span> <span class="o">=</span> <span class="kc">None</span>
            <span class="c1"># if self.two_sided:</span>
            <span class="c1">#     dependent = np.abs(val) &gt;= np.abs(alpha_or_thres)</span>
            <span class="c1"># else:</span>
            <span class="c1">#     dependent = val &gt;= alpha_or_thres</span>
            <span class="c1"># pval = 0. if dependent else 1.</span>
            <span class="c1"># # pval = self.get_fixed_thres_significance(</span>
            <span class="c1"># #         value=val,</span>
            <span class="c1"># #         fixed_thres=self.fixed_thres)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> not known.&quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">significance</span><span class="p">)</span>

        <span class="c1"># # Return the calculated value(s)</span>
        <span class="c1"># if alpha_or_thres is not None:</span>
        <span class="c1">#     if use_sig != &#39;fixed_thres&#39;: </span>
        <span class="c1">#         dependent = pval &lt;= alpha_or_thres </span>
        <span class="c1">#     return pval, dependent </span>
        <span class="c1"># else:</span>
        <span class="k">return</span> <span class="n">pval</span>

<div class="viewcode-block" id="CondIndTest.get_measure"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_measure">[docs]</a>    <span class="k">def</span> <span class="nf">get_measure</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> 
                    <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Estimate dependence measure.</span>

<span class="sd">        Calls the dependence measure function. The child classes must specify</span>
<span class="sd">        a function get_dependence_measure.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        X, Y [, Z] : list of tuples</span>
<span class="sd">            X,Y,Z are of the form [(var, -tau)], where var specifies the</span>
<span class="sd">            variable index and tau the time lag.</span>

<span class="sd">        tau_max : int, optional (default: 0)</span>
<span class="sd">            Maximum time lag. This may be used to make sure that estimates for</span>
<span class="sd">            different lags in X, Z, all have the same sample size.</span>
<span class="sd">        </span>
<span class="sd">       data_type : array-like</span>
<span class="sd">            Binary data array of same shape as array which describes whether </span>
<span class="sd">            individual samples in a variable (or all samples) are continuous </span>
<span class="sd">            or discrete: 0s for continuous variables and 1s for discrete variables.</span>


<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        val : float</span>
<span class="sd">            The test statistic value.</span>

<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Make the array</span>
        <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">),</span> <span class="n">_</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_array</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="o">=</span><span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="n">Z</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                                            <span class="n">remove_constant_data</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
        <span class="n">D</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>
        <span class="c1"># Check it is valid</span>
        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">array</span><span class="p">)</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;nans in the array!&quot;</span><span class="p">)</span>
        <span class="c1"># Return the dependence measure</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_dependence_measure_recycle</span><span class="p">(</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">array</span><span class="p">)</span></div>

<div class="viewcode-block" id="CondIndTest.get_confidence"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_confidence">[docs]</a>    <span class="k">def</span> <span class="nf">get_confidence</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span>
                       <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Perform confidence interval estimation.</span>

<span class="sd">        Calls the dependence measure and confidence test functions. The child</span>
<span class="sd">        classes can specify a function get_dependence_measure and</span>
<span class="sd">        get_analytic_confidence or get_bootstrap_confidence. If confidence is</span>
<span class="sd">        False, (numpy.nan, numpy.nan) is returned.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        X, Y, Z : list of tuples</span>
<span class="sd">            X,Y,Z are of the form [(var, -tau)], where var specifies the</span>
<span class="sd">            variable index and tau the time lag.</span>

<span class="sd">        tau_max : int, optional (default: 0)</span>
<span class="sd">            Maximum time lag. This may be used to make sure that estimates for</span>
<span class="sd">            different lags in X, Z, all have the same sample size.</span>
<span class="sd">            </span>
<span class="sd">       data_type : array-like</span>
<span class="sd">            Binary data array of same shape as array which describes whether </span>
<span class="sd">            individual samples in a variable (or all samples) are continuous </span>
<span class="sd">            or discrete: 0s for continuous variables and 1s for discrete variables.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        (conf_lower, conf_upper) : Tuple of floats</span>
<span class="sd">            Upper and lower confidence bound of confidence interval.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Check if a confidence type has been defined</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span><span class="p">:</span>
            <span class="c1"># Ensure the confidence level given makes sense</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">conf_lev</span> <span class="o">&lt;</span> <span class="mf">.5</span> <span class="ow">or</span> <span class="bp">self</span><span class="o">.</span><span class="n">conf_lev</span> <span class="o">&gt;=</span> <span class="mf">1.</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;conf_lev = </span><span class="si">%.2f</span><span class="s2">, &quot;</span> <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">conf_lev</span> <span class="o">+</span>
                                 <span class="s2">&quot;but must be between 0.5 and 1&quot;</span><span class="p">)</span>
            <span class="n">half_conf</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">conf_samples</span> <span class="o">*</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">conf_lev</span><span class="p">)</span><span class="o">/</span><span class="mf">2.</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span> <span class="o">==</span> <span class="s1">&#39;bootstrap&#39;</span> <span class="ow">and</span>  <span class="n">half_conf</span> <span class="o">&lt;</span> <span class="mf">1.</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;conf_samples*(1.-conf_lev)/2 is </span><span class="si">%.2f</span><span class="s2">&quot;</span>
                                 <span class="o">%</span> <span class="n">half_conf</span> <span class="o">+</span> <span class="s2">&quot;, must be &gt;&gt; 1&quot;</span><span class="p">)</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span><span class="p">:</span>
            <span class="c1"># Make and check the array</span>
            <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">data_type</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_array</span><span class="p">(</span><span class="n">X</span><span class="o">=</span><span class="n">X</span><span class="p">,</span> <span class="n">Y</span><span class="o">=</span><span class="n">Y</span><span class="p">,</span> <span class="n">Z</span><span class="o">=</span><span class="n">Z</span><span class="p">,</span> <span class="n">tau_max</span><span class="o">=</span><span class="n">tau_max</span><span class="p">,</span>
                                            <span class="n">remove_constant_data</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
            <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>
            <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">isnan</span><span class="p">(</span><span class="n">array</span><span class="p">)</span><span class="o">.</span><span class="n">sum</span><span class="p">()</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;nans in the array!&quot;</span><span class="p">)</span>

            <span class="c1"># Check if we are using analytic confidence or bootstrapping it</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span> <span class="o">==</span> <span class="s1">&#39;analytic&#39;</span><span class="p">:</span>
                <span class="n">val</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">)</span>
                <span class="p">(</span><span class="n">conf_lower</span><span class="p">,</span> <span class="n">conf_upper</span><span class="p">)</span> <span class="o">=</span> \
                        <span class="bp">self</span><span class="o">.</span><span class="n">get_analytic_confidence</span><span class="p">(</span><span class="n">df</span><span class="o">=</span><span class="n">T</span><span class="o">-</span><span class="n">dim</span><span class="p">,</span>
                                                     <span class="n">value</span><span class="o">=</span><span class="n">val</span><span class="p">,</span>
                                                     <span class="n">conf_lev</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">conf_lev</span><span class="p">)</span>
            <span class="k">elif</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span> <span class="o">==</span> <span class="s1">&#39;bootstrap&#39;</span><span class="p">:</span>
                <span class="c1"># Overwrite analytic values</span>
                <span class="p">(</span><span class="n">conf_lower</span><span class="p">,</span> <span class="n">conf_upper</span><span class="p">)</span> <span class="o">=</span> \
                        <span class="bp">self</span><span class="o">.</span><span class="n">get_bootstrap_confidence</span><span class="p">(</span>
                            <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span>
                            <span class="n">conf_samples</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">conf_samples</span><span class="p">,</span>
                            <span class="n">conf_blocklength</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">conf_blocklength</span><span class="p">,</span>
                            <span class="n">conf_lev</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">conf_lev</span><span class="p">,</span> <span class="n">verbosity</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">verbosity</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;</span><span class="si">%s</span><span class="s2"> confidence estimation not implemented&quot;</span>
                                 <span class="o">%</span> <span class="bp">self</span><span class="o">.</span><span class="n">confidence</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span>

        <span class="c1"># Cache the confidence interval</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">conf</span> <span class="o">=</span> <span class="p">(</span><span class="n">conf_lower</span><span class="p">,</span> <span class="n">conf_upper</span><span class="p">)</span>
        <span class="c1"># Return the confidence interval</span>
        <span class="k">return</span> <span class="p">(</span><span class="n">conf_lower</span><span class="p">,</span> <span class="n">conf_upper</span><span class="p">)</span></div>

    <span class="k">def</span> <span class="nf">_print_cond_ind_results</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">val</span><span class="p">,</span> <span class="n">pval</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">cached</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">dependent</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">conf</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Print results from conditional independence test.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        val : float</span>
<span class="sd">            Test stastistic value.</span>

<span class="sd">        pval : float, optional (default: None)</span>
<span class="sd">            p-value</span>

<span class="sd">        dependent : bool</span>
<span class="sd">            Test decision.</span>

<span class="sd">        conf : tuple of floats, optional (default: None)</span>
<span class="sd">            Confidence bounds.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">printstr</span> <span class="o">=</span> <span class="s2">&quot;        val = </span><span class="si">% .3f</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">val</span><span class="p">)</span>      
        <span class="k">if</span> <span class="n">pval</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">printstr</span> <span class="o">+=</span> <span class="s2">&quot; | pval = </span><span class="si">%.5f</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">pval</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">dependent</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">printstr</span> <span class="o">+=</span> <span class="s2">&quot; | dependent = </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">dependent</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">conf</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">printstr</span> <span class="o">+=</span> <span class="s2">&quot; | conf bounds = (</span><span class="si">%.3f</span><span class="s2">, </span><span class="si">%.3f</span><span class="s2">)&quot;</span> <span class="o">%</span> <span class="p">(</span>
                <span class="n">conf</span><span class="p">[</span><span class="mi">0</span><span class="p">],</span> <span class="n">conf</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
        <span class="k">if</span> <span class="n">cached</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">printstr</span> <span class="o">+=</span> <span class="s2">&quot; </span><span class="si">%s</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">({</span><span class="mi">0</span><span class="p">:</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="mi">1</span><span class="p">:</span><span class="s2">&quot;[cached]&quot;</span><span class="p">}[</span><span class="n">cached</span><span class="p">])</span>

        <span class="nb">print</span><span class="p">(</span><span class="n">printstr</span><span class="p">)</span>

<div class="viewcode-block" id="CondIndTest.get_bootstrap_confidence"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_bootstrap_confidence">[docs]</a>    <span class="k">def</span> <span class="nf">get_bootstrap_confidence</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">dependence_measure</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                 <span class="n">conf_samples</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">conf_blocklength</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                 <span class="n">conf_lev</span><span class="o">=</span><span class="mf">.95</span><span class="p">,</span> 
                                 <span class="n">data_type</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                                 <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Perform bootstrap confidence interval estimation.</span>

<span class="sd">        With conf_blocklength &gt; 1 or None a block-bootstrap is performed.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        array : array-like</span>
<span class="sd">            data array with X, Y, Z in rows and observations in columns</span>

<span class="sd">        xyz : array of ints</span>
<span class="sd">            XYZ identifier array of shape (dim,).</span>

<span class="sd">        dependence_measure : function (default = self.get_dependence_measure)</span>
<span class="sd">            Dependence measure function must be of form</span>
<span class="sd">            dependence_measure(array, xyz) and return a numeric value</span>

<span class="sd">        conf_lev : float, optional (default: 0.9)</span>
<span class="sd">            Two-sided confidence interval.</span>

<span class="sd">        conf_samples : int, optional (default: 100)</span>
<span class="sd">            Number of samples for bootstrap.</span>

<span class="sd">        conf_blocklength : int, optional (default: None)</span>
<span class="sd">            Block length for block-bootstrap. If None, the block length is</span>
<span class="sd">            determined from the decay of the autocovariance as explained in</span>
<span class="sd">            [1]_.</span>

<span class="sd">       data_type : array-like</span>
<span class="sd">            Binary data array of same shape as array which describes whether </span>
<span class="sd">            individual samples in a variable (or all samples) are continuous </span>
<span class="sd">            or discrete: 0s for continuous variables and 1s for discrete variables.</span>

<span class="sd">        verbosity : int, optional (default: 0)</span>
<span class="sd">            Level of verbosity.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        (conf_lower, conf_upper) : Tuple of floats</span>
<span class="sd">            Upper and lower confidence bound of confidence interval.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="c1"># Check if a dependence measure if provided or if to use default</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">dependence_measure</span><span class="p">:</span>
            <span class="n">dependence_measure</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dependence_measure</span>

        <span class="c1"># confidence interval is two-sided</span>
        <span class="n">c_int</span> <span class="o">=</span> <span class="mf">1.</span> <span class="o">-</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">conf_lev</span><span class="p">)</span><span class="o">/</span><span class="mf">2.</span>
        <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>

        <span class="c1"># If not block length is given, determine the optimal block length.</span>
        <span class="c1"># This has a maximum of 10% of the time sample length</span>
        <span class="k">if</span> <span class="n">conf_blocklength</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">conf_blocklength</span> <span class="o">=</span> \
                    <span class="bp">self</span><span class="o">.</span><span class="n">_get_block_length</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">mode</span><span class="o">=</span><span class="s1">&#39;confidence&#39;</span><span class="p">)</span>
        <span class="c1"># Determine the number of blocks total, rounding up for non-integer</span>
        <span class="c1"># amounts</span>
        <span class="n">n_blks</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">math</span><span class="o">.</span><span class="n">ceil</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="n">T</span><span class="p">)</span><span class="o">/</span><span class="n">conf_blocklength</span><span class="p">))</span>

        <span class="c1"># Print some information</span>
        <span class="k">if</span> <span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">2</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;            block_bootstrap confidence intervals&quot;</span>
                  <span class="s2">&quot; with block-length = </span><span class="si">%d</span><span class="s2"> ...&quot;</span> <span class="o">%</span> <span class="n">conf_blocklength</span><span class="p">)</span>

        <span class="c1"># Generate the block bootstrapped distribution</span>
        <span class="n">bootdist</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">conf_samples</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">smpl</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">conf_samples</span><span class="p">):</span>
            <span class="c1"># Get the starting indices for the blocks</span>
            <span class="n">blk_strt</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span><span class="o">.</span><span class="n">integers</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">T</span> <span class="o">-</span> <span class="n">conf_blocklength</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="n">n_blks</span><span class="p">)</span>
            <span class="c1"># Get the empty array of block resampled values</span>
            <span class="n">array_bootstrap</span> <span class="o">=</span> \
                    <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">dim</span><span class="p">,</span> <span class="n">n_blks</span><span class="o">*</span><span class="n">conf_blocklength</span><span class="p">),</span> <span class="n">dtype</span><span class="o">=</span><span class="n">array</span><span class="o">.</span><span class="n">dtype</span><span class="p">)</span>
            <span class="c1"># Fill the array of block resamples</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">conf_blocklength</span><span class="p">):</span>
                <span class="n">array_bootstrap</span><span class="p">[:,</span> <span class="n">i</span><span class="p">::</span><span class="n">conf_blocklength</span><span class="p">]</span> <span class="o">=</span> <span class="n">array</span><span class="p">[:,</span> <span class="n">blk_strt</span> <span class="o">+</span> <span class="n">i</span><span class="p">]</span>
            <span class="c1"># Cut to proper length</span>
            <span class="n">array_bootstrap</span> <span class="o">=</span> <span class="n">array_bootstrap</span><span class="p">[:,</span> <span class="p">:</span><span class="n">T</span><span class="p">]</span>

            <span class="n">bootdist</span><span class="p">[</span><span class="n">smpl</span><span class="p">]</span> <span class="o">=</span> <span class="n">dependence_measure</span><span class="p">(</span><span class="n">array_bootstrap</span><span class="p">,</span> <span class="n">xyz</span><span class="p">)</span>

        <span class="c1"># Sort and get quantile</span>
        <span class="n">bootdist</span><span class="o">.</span><span class="n">sort</span><span class="p">()</span>
        <span class="n">conf_lower</span> <span class="o">=</span> <span class="n">bootdist</span><span class="p">[</span><span class="nb">int</span><span class="p">((</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">c_int</span><span class="p">)</span> <span class="o">*</span> <span class="n">conf_samples</span><span class="p">)]</span>
        <span class="n">conf_upper</span> <span class="o">=</span> <span class="n">bootdist</span><span class="p">[</span><span class="nb">int</span><span class="p">(</span><span class="n">c_int</span> <span class="o">*</span> <span class="n">conf_samples</span><span class="p">)]</span>
        <span class="c1"># Return the confidance limits as a tuple</span>
        <span class="k">return</span> <span class="p">(</span><span class="n">conf_lower</span><span class="p">,</span> <span class="n">conf_upper</span><span class="p">)</span></div>

    <span class="k">def</span> <span class="nf">_get_acf</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">series</span><span class="p">,</span> <span class="n">max_lag</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns autocorrelation function.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        series : 1D-array</span>
<span class="sd">            data series to compute autocorrelation from</span>

<span class="sd">        max_lag : int, optional (default: None)</span>
<span class="sd">            maximum lag for autocorrelation function. If None is passed, 10% of</span>
<span class="sd">            the data series length are used.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        autocorr : array of shape (max_lag + 1,)</span>
<span class="sd">            Autocorrelation function.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Set the default max lag</span>
        <span class="k">if</span> <span class="n">max_lag</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="nb">max</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mf">0.1</span><span class="o">*</span><span class="nb">len</span><span class="p">(</span><span class="n">series</span><span class="p">)))</span>
        <span class="c1"># Initialize the result</span>
        <span class="n">autocorr</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">ones</span><span class="p">(</span><span class="n">max_lag</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span>
        <span class="c1"># Iterate over possible lags</span>
        <span class="k">for</span> <span class="n">lag</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_lag</span> <span class="o">+</span> <span class="mi">1</span><span class="p">):</span>
            <span class="c1"># Set the values</span>
            <span class="n">y1_vals</span> <span class="o">=</span> <span class="n">series</span><span class="p">[</span><span class="n">lag</span><span class="p">:]</span>
            <span class="n">y2_vals</span> <span class="o">=</span> <span class="n">series</span><span class="p">[:</span><span class="nb">len</span><span class="p">(</span><span class="n">series</span><span class="p">)</span> <span class="o">-</span> <span class="n">lag</span><span class="p">]</span>
            <span class="c1"># Calculate the autocorrelation</span>
            <span class="n">autocorr</span><span class="p">[</span><span class="n">lag</span><span class="p">]</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">corrcoef</span><span class="p">(</span><span class="n">y1_vals</span><span class="p">,</span> <span class="n">y2_vals</span><span class="p">,</span> <span class="n">ddof</span><span class="o">=</span><span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">]</span>
        <span class="k">return</span> <span class="n">autocorr</span>

    <span class="k">def</span> <span class="nf">_get_block_length</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">mode</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns optimal block length for significance and confidence tests.</span>

<span class="sd">        Determine block length using approach in Mader (2013) [Eq. (6)] which</span>
<span class="sd">        improves the method of Peifer (2005) with non-overlapping blocks In</span>
<span class="sd">        case of multidimensional X, the max is used. Further details in [1]_.</span>
<span class="sd">        Two modes are available. For mode=&#39;significance&#39;, only the indices</span>
<span class="sd">        corresponding to X are shuffled in array. For mode=&#39;confidence&#39; all</span>
<span class="sd">        variables are jointly shuffled. If the autocorrelation curve fit fails,</span>
<span class="sd">        a block length of 5% of T is used. The block length is limited to a</span>
<span class="sd">        maximum of 10% of T.</span>

<span class="sd">        Mader et al., Journal of Neuroscience Methods,</span>
<span class="sd">        Volume 219, Issue 2, 15 October 2013, Pages 285-291</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        array : array-like</span>
<span class="sd">            data array with X, Y, Z in rows and observations in columns</span>

<span class="sd">        xyz : array of ints</span>
<span class="sd">            XYZ identifier array of shape (dim,).</span>

<span class="sd">        mode : str</span>
<span class="sd">            Which mode to use.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        block_len : int</span>
<span class="sd">            Optimal block length.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="c1"># Inject a dependency on siganal, optimize</span>
        <span class="kn">from</span> <span class="nn">scipy</span> <span class="kn">import</span> <span class="n">signal</span><span class="p">,</span> <span class="n">optimize</span>
        <span class="c1"># Get the shape of the array</span>
        <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>
        <span class="c1"># Initiailize the indices</span>
        <span class="n">indices</span> <span class="o">=</span> <span class="nb">range</span><span class="p">(</span><span class="n">dim</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">mode</span> <span class="o">==</span> <span class="s1">&#39;significance&#39;</span><span class="p">:</span>
            <span class="n">indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>

        <span class="c1"># Maximum lag for autocov estimation</span>
        <span class="n">max_lag</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="mf">0.1</span><span class="o">*</span><span class="n">T</span><span class="p">)</span>
        <span class="c1"># Define the function to optimize against</span>
        <span class="k">def</span> <span class="nf">func</span><span class="p">(</span><span class="n">x_vals</span><span class="p">,</span> <span class="n">a_const</span><span class="p">,</span> <span class="n">decay</span><span class="p">):</span>
            <span class="k">return</span> <span class="n">a_const</span> <span class="o">*</span> <span class="n">decay</span><span class="o">**</span><span class="n">x_vals</span>

        <span class="c1"># Calculate the block length</span>
        <span class="n">block_len</span> <span class="o">=</span> <span class="mi">1</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">indices</span><span class="p">:</span>
            <span class="c1"># Get decay rate of envelope of autocorrelation functions</span>
            <span class="c1"># via hilbert trafo</span>
            <span class="n">autocov</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_acf</span><span class="p">(</span><span class="n">series</span><span class="o">=</span><span class="n">array</span><span class="p">[</span><span class="n">i</span><span class="p">],</span> <span class="n">max_lag</span><span class="o">=</span><span class="n">max_lag</span><span class="p">)</span>
            <span class="n">autocov</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="mf">1.</span>
            <span class="n">hilbert</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">abs</span><span class="p">(</span><span class="n">signal</span><span class="o">.</span><span class="n">hilbert</span><span class="p">(</span><span class="n">autocov</span><span class="p">))</span>
            <span class="c1"># Try to fit the curve</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">popt</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="n">optimize</span><span class="o">.</span><span class="n">curve_fit</span><span class="p">(</span>
                    <span class="n">f</span><span class="o">=</span><span class="n">func</span><span class="p">,</span>
                    <span class="n">xdata</span><span class="o">=</span><span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">max_lag</span><span class="o">+</span><span class="mi">1</span><span class="p">),</span>
                    <span class="n">ydata</span><span class="o">=</span><span class="n">hilbert</span><span class="p">,</span>
                <span class="p">)</span>
                <span class="n">phi</span> <span class="o">=</span> <span class="n">popt</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>
                <span class="c1"># Formula assuming non-overlapping blocks</span>
                <span class="n">l_opt</span> <span class="o">=</span> <span class="p">(</span><span class="mf">4.</span> <span class="o">*</span> <span class="n">T</span> <span class="o">*</span> <span class="p">(</span><span class="n">phi</span> <span class="o">/</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">phi</span><span class="p">)</span> <span class="o">+</span> <span class="n">phi</span><span class="o">**</span><span class="mi">2</span> <span class="o">/</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">phi</span><span class="p">)</span><span class="o">**</span><span class="mi">2</span><span class="p">)</span><span class="o">**</span><span class="mi">2</span>
                         <span class="o">/</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">+</span> <span class="mf">2.</span> <span class="o">*</span> <span class="n">phi</span> <span class="o">/</span> <span class="p">(</span><span class="mf">1.</span> <span class="o">-</span> <span class="n">phi</span><span class="p">))</span><span class="o">**</span><span class="mi">2</span><span class="p">)</span><span class="o">**</span><span class="p">(</span><span class="mf">1.</span> <span class="o">/</span> <span class="mf">3.</span><span class="p">)</span>
                <span class="n">block_len</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="n">block_len</span><span class="p">,</span> <span class="nb">int</span><span class="p">(</span><span class="n">l_opt</span><span class="p">))</span>
            <span class="k">except</span> <span class="ne">RuntimeError</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Error - curve_fit failed in block_shuffle, using&quot;</span>
                      <span class="s2">&quot; block_len = </span><span class="si">%d</span><span class="s2">&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="mf">.05</span> <span class="o">*</span> <span class="n">T</span><span class="p">)))</span>
                <span class="c1"># block_len = max(int(.05 * T), block_len)</span>
        <span class="c1"># Limit block length to a maximum of 10% of T</span>
        <span class="n">block_len</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">block_len</span><span class="p">,</span> <span class="nb">int</span><span class="p">(</span><span class="mf">0.1</span> <span class="o">*</span> <span class="n">T</span><span class="p">))</span>
        <span class="k">return</span> <span class="n">block_len</span>

    <span class="k">def</span> <span class="nf">_get_shuffle_dist</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span> <span class="n">dependence_measure</span><span class="p">,</span>
                          <span class="n">sig_samples</span><span class="p">,</span> <span class="n">sig_blocklength</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
                          <span class="n">verbosity</span><span class="o">=</span><span class="mi">0</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Returns shuffle distribution of test statistic.</span>

<span class="sd">        The rows in array corresponding to the X-variable are shuffled using</span>
<span class="sd">        a block-shuffle approach.</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        array : array-like</span>
<span class="sd">            data array with X, Y, Z in rows and observations in columns</span>

<span class="sd">        xyz : array of ints</span>
<span class="sd">            XYZ identifier array of shape (dim,).</span>

<span class="sd">       dependence_measure : object</span>
<span class="sd">           Dependence measure function must be of form</span>
<span class="sd">           dependence_measure(array, xyz) and return a numeric value</span>

<span class="sd">        sig_samples : int, optional (default: 100)</span>
<span class="sd">            Number of samples for shuffle significance test.</span>

<span class="sd">        sig_blocklength : int, optional (default: None)</span>
<span class="sd">            Block length for block-shuffle significance test. If None, the</span>
<span class="sd">            block length is determined from the decay of the autocovariance as</span>
<span class="sd">            explained in [1]_.</span>

<span class="sd">        verbosity : int, optional (default: 0)</span>
<span class="sd">            Level of verbosity.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        null_dist : array of shape (sig_samples,)</span>
<span class="sd">            Contains the sorted test statistic values estimated from the</span>
<span class="sd">            shuffled arrays.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">dim</span><span class="p">,</span> <span class="n">T</span> <span class="o">=</span> <span class="n">array</span><span class="o">.</span><span class="n">shape</span>

        <span class="n">x_indices</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">where</span><span class="p">(</span><span class="n">xyz</span> <span class="o">==</span> <span class="mi">0</span><span class="p">)[</span><span class="mi">0</span><span class="p">]</span>
        <span class="n">dim_x</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">x_indices</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">sig_blocklength</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">sig_blocklength</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_block_length</span><span class="p">(</span><span class="n">array</span><span class="p">,</span> <span class="n">xyz</span><span class="p">,</span>
                                                     <span class="n">mode</span><span class="o">=</span><span class="s1">&#39;significance&#39;</span><span class="p">)</span>

        <span class="n">n_blks</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">math</span><span class="o">.</span><span class="n">floor</span><span class="p">(</span><span class="nb">float</span><span class="p">(</span><span class="n">T</span><span class="p">)</span><span class="o">/</span><span class="n">sig_blocklength</span><span class="p">))</span>
        <span class="c1"># print &#39;n_blks &#39;, n_blks</span>
        <span class="k">if</span> <span class="n">verbosity</span> <span class="o">&gt;</span> <span class="mi">2</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;            Significance test with block-length = </span><span class="si">%d</span><span class="s2"> &quot;</span>
                  <span class="s2">&quot;...&quot;</span> <span class="o">%</span> <span class="p">(</span><span class="n">sig_blocklength</span><span class="p">))</span>

        <span class="n">array_shuffled</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">copy</span><span class="p">(</span><span class="n">array</span><span class="p">)</span>
        <span class="n">block_starts</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">arange</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">T</span> <span class="o">-</span> <span class="n">sig_blocklength</span> <span class="o">+</span> <span class="mi">1</span><span class="p">,</span> <span class="n">sig_blocklength</span><span class="p">)</span>

        <span class="c1"># Dividing the array up into n_blks of length sig_blocklength may</span>
        <span class="c1"># leave a tail. This tail is later randomly inserted</span>
        <span class="n">tail</span> <span class="o">=</span> <span class="n">array</span><span class="p">[</span><span class="n">x_indices</span><span class="p">,</span> <span class="n">n_blks</span><span class="o">*</span><span class="n">sig_blocklength</span><span class="p">:]</span>

        <span class="n">null_dist</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">(</span><span class="n">sig_samples</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">sam</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">sig_samples</span><span class="p">):</span>

            <span class="n">blk_starts</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span><span class="o">.</span><span class="n">permutation</span><span class="p">(</span><span class="n">block_starts</span><span class="p">)[:</span><span class="n">n_blks</span><span class="p">]</span>

            <span class="n">x_shuffled</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">zeros</span><span class="p">((</span><span class="n">dim_x</span><span class="p">,</span> <span class="n">n_blks</span><span class="o">*</span><span class="n">sig_blocklength</span><span class="p">),</span>
                                  <span class="n">dtype</span><span class="o">=</span><span class="n">array</span><span class="o">.</span><span class="n">dtype</span><span class="p">)</span>

            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">index</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">x_indices</span><span class="p">):</span>
                <span class="k">for</span> <span class="n">blk</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">sig_blocklength</span><span class="p">):</span>
                    <span class="n">x_shuffled</span><span class="p">[</span><span class="n">i</span><span class="p">,</span> <span class="n">blk</span><span class="p">::</span><span class="n">sig_blocklength</span><span class="p">]</span> <span class="o">=</span> \
                            <span class="n">array</span><span class="p">[</span><span class="n">index</span><span class="p">,</span> <span class="n">blk_starts</span> <span class="o">+</span> <span class="n">blk</span><span class="p">]</span>

            <span class="c1"># Insert tail randomly somewhere</span>
            <span class="k">if</span> <span class="n">tail</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">insert_tail_at</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">random_state</span><span class="o">.</span><span class="n">choice</span><span class="p">(</span><span class="n">block_starts</span><span class="p">)</span>
                <span class="n">x_shuffled</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="n">x_shuffled</span><span class="p">,</span> <span class="n">insert_tail_at</span><span class="p">,</span>
                                       <span class="n">tail</span><span class="o">.</span><span class="n">T</span><span class="p">,</span> <span class="n">axis</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">index</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">x_indices</span><span class="p">):</span>
                <span class="n">array_shuffled</span><span class="p">[</span><span class="n">index</span><span class="p">]</span> <span class="o">=</span> <span class="n">x_shuffled</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>

            <span class="n">null_dist</span><span class="p">[</span><span class="n">sam</span><span class="p">]</span> <span class="o">=</span> <span class="n">dependence_measure</span><span class="p">(</span><span class="n">array</span><span class="o">=</span><span class="n">array_shuffled</span><span class="p">,</span>
                                                <span class="n">xyz</span><span class="o">=</span><span class="n">xyz</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">null_dist</span>

<div class="viewcode-block" id="CondIndTest.get_fixed_thres_significance"><a class="viewcode-back" href="../../../index.html#tigramite.independence_tests.independence_tests_base.CondIndTest.get_fixed_thres_significance">[docs]</a>    <span class="k">def</span> <span class="nf">get_fixed_thres_significance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">value</span><span class="p">,</span> <span class="n">fixed_thres</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;DEPRECATED Returns signficance for thresholding test.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;fixed_thres is replaced by alpha_or_thres in run_test.&quot;</span><span class="p">)</span></div>
        <span class="c1"># if np.abs(value) &lt; np.abs(fixed_thres):</span>
        <span class="c1">#     pval = 1.</span>
        <span class="c1"># else:</span>
        <span class="c1">#     pval = 0.</span>

        <span class="c1"># return pval</span>

    <span class="k">def</span> <span class="nf">_trafo2uniform</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">x</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Transforms input array to uniform marginals.</span>

<span class="sd">        Assumes x.shape = (dim, T)</span>

<span class="sd">        Parameters</span>
<span class="sd">        ----------</span>
<span class="sd">        x : array-like</span>
<span class="sd">            Input array.</span>

<span class="sd">        Returns</span>
<span class="sd">        -------</span>
<span class="sd">        u : array-like</span>
<span class="sd">            array with uniform marginals.</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="k">def</span> <span class="nf">trafo</span><span class="p">(</span><span class="n">xi</span><span class="p">):</span>
            <span class="n">xisorted</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">xi</span><span class="p">)</span>
            <span class="n">yi</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">linspace</span><span class="p">(</span><span class="mf">1.</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">xi</span><span class="p">),</span> <span class="mi">1</span><span class="p">,</span> <span class="nb">len</span><span class="p">(</span><span class="n">xi</span><span class="p">))</span>
            <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">interp</span><span class="p">(</span><span class="n">xi</span><span class="p">,</span> <span class="n">xisorted</span><span class="p">,</span> <span class="n">yi</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">np</span><span class="o">.</span><span class="n">ndim</span><span class="p">(</span><span class="n">x</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
            <span class="n">u</span> <span class="o">=</span> <span class="n">trafo</span><span class="p">(</span><span class="n">x</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">u</span> <span class="o">=</span> <span class="n">np</span><span class="o">.</span><span class="n">empty</span><span class="p">(</span><span class="n">x</span><span class="o">.</span><span class="n">shape</span><span class="p">)</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">x</span><span class="o">.</span><span class="n">shape</span><span class="p">[</span><span class="mi">0</span><span class="p">]):</span>
                <span class="n">u</span><span class="p">[</span><span class="n">i</span><span class="p">]</span> <span class="o">=</span> <span class="n">trafo</span><span class="p">(</span><span class="n">x</span><span class="p">[</span><span class="n">i</span><span class="p">])</span>
        <span class="k">return</span> <span class="n">u</span></div>
</pre></div>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../../../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../../../index.html">Documentation overview</a><ul>
  <li><a href="../../index.html">Module code</a><ul>
  </ul></li>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;2023, Jakob Runge.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 5.0.2</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
    </div>

    

    
  </body>
</html>