
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Overview: module code &#8212; Tigramite 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../_static/alabaster.css" />
    <script data-url_root="../" id="documentation_options" src="../_static/documentation_options.js"></script>
    <script src="../_static/jquery.js"></script>
    <script src="../_static/underscore.js"></script>
    <script src="../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../_static/doctools.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
   
  <link rel="stylesheet" href="../_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <h1>All modules for which code is available</h1>
<ul><li><a href="abc.html">abc</a></li>
<li><a href="tigramite/causal_effects.html">tigramite.causal_effects</a></li>
<li><a href="tigramite/data_processing.html">tigramite.data_processing</a></li>
<li><a href="tigramite/independence_tests/cmiknn.html">tigramite.independence_tests.cmiknn</a></li>
<li><a href="tigramite/independence_tests/cmisymb.html">tigramite.independence_tests.cmisymb</a></li>
<li><a href="tigramite/independence_tests/gpdc.html">tigramite.independence_tests.gpdc</a></li>
<li><a href="tigramite/independence_tests/gpdc_torch.html">tigramite.independence_tests.gpdc_torch</a></li>
<li><a href="tigramite/independence_tests/gsquared.html">tigramite.independence_tests.gsquared</a></li>
<li><a href="tigramite/independence_tests/independence_tests_base.html">tigramite.independence_tests.independence_tests_base</a></li>
<li><a href="tigramite/independence_tests/oracle_conditional_independence.html">tigramite.independence_tests.oracle_conditional_independence</a></li>
<li><a href="tigramite/independence_tests/parcorr.html">tigramite.independence_tests.parcorr</a></li>
<li><a href="tigramite/independence_tests/parcorr_mult.html">tigramite.independence_tests.parcorr_mult</a></li>
<li><a href="tigramite/independence_tests/parcorr_wls.html">tigramite.independence_tests.parcorr_wls</a></li>
<li><a href="tigramite/independence_tests/regressionCI.html">tigramite.independence_tests.regressionCI</a></li>
<li><a href="tigramite/independence_tests/robust_parcorr.html">tigramite.independence_tests.robust_parcorr</a></li>
<li><a href="tigramite/jpcmciplus.html">tigramite.jpcmciplus</a></li>
<li><a href="tigramite/lpcmci.html">tigramite.lpcmci</a></li>
<li><a href="tigramite/models.html">tigramite.models</a></li>
<li><a href="tigramite/pcmci.html">tigramite.pcmci</a></li>
<li><a href="tigramite/plotting.html">tigramite.plotting</a></li>
<li><a href="tigramite/rpcmci.html">tigramite.rpcmci</a></li>
<li><a href="tigramite/toymodels/structural_causal_processes.html">tigramite.toymodels.structural_causal_processes</a></li>
</ul>

          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="../index.html">Tigramite</a></h1>








<h3>Navigation</h3>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="../index.html">Documentation overview</a><ul>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;2023, Jakob Runge.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 5.0.2</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
    </div>

    

    
  </body>
</html>